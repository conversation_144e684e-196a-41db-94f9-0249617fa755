package com.volvo.maintain.interfaces.controller;


import com.volvo.maintain.application.maintainlead.service.DeliverOrderService;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/deliverOrder")
@Api(value = "送修人工单处理", tags = {"送修人工单处理"})
@Slf4j
public class DeliverOrderController {

    @Autowired
    DeliverOrderService deliverOrderService;

    /**
     * 记录异常送修人工单
     *
     * @param roNo      工单号
     * @param traceTime 回访时间类型
     * @return map 返回结果
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "roNo", value = "工单号", required = true),
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "traceTime", value = "回访时间类型", required = true)
    })
    @ApiOperation(value = "记录异常送修人工单", notes = "记录异常送修人工单", httpMethod = "PUT")
    @RequestMapping(value = "/alterTraceTime/{roNo}/{traceTime}", method = RequestMethod.PUT)
    @ResponseBody
    public Map<String, Object> deliveryOrderExceptionRecord(@PathVariable("roNo") String roNo, @PathVariable("traceTime") String traceTime) {
        if (StringUtils.isBlank(roNo) && StringUtils.isBlank(traceTime)) {
            throw new ServiceBizException("参数异常！");
        }
        return deliverOrderService.deliveryOrderExceptionRecord(roNo, traceTime);
    }

    /**
     * 记录异常送修人工单 (取消交车)
     * @param roNo      工单号
     * @return map 返回结果
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "roNo", value = "工单号", required = true)
    })
    @ApiOperation(value = "记录异常送修人工单 (取消交车)", notes = "记录异常送修人工单 (取消交车)", httpMethod = "PUT")
    @RequestMapping(value = "/getVehicle/{roNo}", method = RequestMethod.PUT)
    @ResponseBody
    public Map<String, Object> cancelDeliverExceptionRecord(@PathVariable("roNo") String roNo) {
        if (StringUtils.isBlank(roNo)) {
            throw new ServiceBizException("参数异常！");
        }
        return deliverOrderService.cancelDeliverExceptionRecord(roNo);
    }

    /**
     * 查询送修人手机号异常
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "mobile", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "查询送修人手机号异常", notes = "查询送修人手机号异常", httpMethod = "GET")
    @GetMapping(value = "/queryDeliverMobileException", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String queryDeliverMobileException(@RequestParam("mobile") String mobile,@RequestParam("ownerCode") String ownerCode,@RequestParam("vin") String vin) {
        return deliverOrderService.queryDeliverMobileException(mobile, ownerCode, vin);
    }

}
