package com.volvo.maintain.interfaces.controller.customerProfile;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.VipCustomPageReqDTO;
import com.volvo.maintain.application.maintainlead.dto.VipGroupImportDTO;
import com.volvo.maintain.application.maintainlead.dto.VipImportDto;
import com.volvo.maintain.application.maintainlead.service.EM90ServiceLeadService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.VipCustomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/vipCustom")
@Api(value = "VIP客户")
public class VipCustomController {
    @Autowired
    private VipCustomService vipCustomService;

    @Autowired
    private EM90ServiceLeadService em90ServiceLeadService;

    /**
     * 添加Vip客户
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<VipImportDto>", name = "vipImportDto", value = "", required = true)
    })
    @ApiOperation(value = "添加Vip客户", notes = "添加Vip客户", httpMethod = "POST")
    @PostMapping("/increasVip")
    public void increasVip(@RequestBody List<VipImportDto> vipImportDto) {
        vipCustomService.increasVip(vipImportDto);
    }

    /**
     * Vip提醒推送
     */
    @ApiOperation(value = "Vip提醒推送", notes = "Vip提醒推送", httpMethod = "POST")
    @PostMapping("/vipEntranceEmail")
    public void vipEntranceEmail() {
        vipCustomService.vipEntranceEmail();
    }

    /**
     * Vip提醒推送
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "message", value = "", required = true)
    })
    @ApiOperation(value = "Vip提醒推送", notes = "Vip提醒推送", httpMethod = "POST")
    @PostMapping("/entranceReceptionVehicle")
    public void entranceReceptionVehicle(@RequestParam String message) {

        vipCustomService.saveEntranceReceptionVehicle(message);

    }


    /**
     * 根据code查名称
     * @param dealerCode 经销商code
     * @return 经销商名称
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "经销商code", required = true)
    })
    @ApiOperation(value = "根据code查名称", notes = "根据code查名称", httpMethod = "GET")
    @GetMapping("/queryDealerName")
    public String queryDealerName(@RequestParam("dealerCode") String dealerCode) {
        return em90ServiceLeadService.queryDealerName(dealerCode);
    }

    /**
     * 页面导入VIP客户群组
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<VipGroupImportDTO>", name = "importDTOList", value = "", required = true)
    })
    @ApiOperation(value = "页面导入VIP客户群组", notes = "页面导入VIP客户群组", httpMethod = "POST")
    @PostMapping("/vipGroup/import")
    public String importVipGroup(@RequestBody List<VipGroupImportDTO> importDTOList) {
        return vipCustomService.importVipGroup(importDTOList);
    }

    /**
     * 页面查询VIP客户群组
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VipCustomPageReqDTO", name = "pageReqDTO", value = "", required = true)
    })
    @ApiOperation(value = "页面查询VIP客户群组", notes = "页面查询VIP客户群组", httpMethod = "POST")
    @PostMapping("/vipGroup/page")
    public Page<VipGroupImportDTO> vipGroupPage(@RequestBody VipCustomPageReqDTO pageReqDTO) {
        return vipCustomService.vipGroupPage(pageReqDTO);
    }

    /**
     * 页面导出VIP客户群组
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VipCustomPageReqDTO", name = "pageReqDTO", value = "", required = true)
    })
    @ApiOperation(value = "页面导出VIP客户群组", notes = "页面导出VIP客户群组", httpMethod = "POST")
    @PostMapping("/vipGroup/export")
    public void vipGroupExport(@RequestBody VipCustomPageReqDTO pageReqDTO) {
        vipCustomService.vipGroupExport(pageReqDTO);
    }

    /**
     * 页面导出VIP客户群组
     */
    @ApiOperation(value = "页面导出VIP客户群组", notes = "页面导出VIP客户群组", httpMethod = "GET")
    @GetMapping("/vipGroup/export/callback")
    public List<VipGroupImportDTO> exportCallback(VipCustomPageReqDTO pageReqDTO) {
        return vipCustomService.exportCallback(pageReqDTO);
    }

}
