package com.volvo.maintain.interfaces.controller.vhc;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.vhc.NoRepairItemDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.QueryVhcDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcDetailsDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcInfoDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcItemPoDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcPricesheetDetailsDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcQueryLabourDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcQueryLabourVo;
import com.volvo.maintain.application.maintainlead.service.vhc.VhcRepairService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(tags = "vhc工单相关控制层")
@RestController
@RequestMapping("/vhcRepair/v1")
public class VhcRepairController {


    @Autowired
    private VhcRepairService vhcRepairService;


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VhcInfoDTO", name = "vhcInfoDTO", value = "", required = true)
    })
    @ApiOperation(value = "创建车辆健康检查", notes = "", httpMethod = "POST")
    @PostMapping("/createdVhcInfo")
    public void createdVhcInfo(@RequestBody VhcInfoDTO vhcInfoDTO){
        vhcRepairService.createdVhcInfo(vhcInfoDTO);
    }



    /**
     * 校验工单组是否存在vhc套餐code
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "setCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "校验工单组是否存在vhc套餐code", notes = "校验工单组是否存在vhc套餐code", httpMethod = "GET")
    @GetMapping("/verifyOrderGroupVHC")
    public boolean verifyOrderGroupVHC(
            @RequestParam(value = "ownerCode")String ownerCode,
            @RequestParam(value = "roNo",required = false)String roNo,
            @RequestParam(value = "setCode")String setCode,
            @RequestParam(value = "vin")String vin){
        return vhcRepairService.verifyOrderGroupVHC(ownerCode, roNo, setCode,vin);
    }

    /**
     * 检查页面查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryVhcDto", name = "queryVhcDto", value = "", required = true)
    })
    @ApiOperation(value = "检查页面查询", notes = "检查页面查询", httpMethod = "POST")
    @PostMapping("/selectVhcList")
    public Page<VhcDetailsDTO> selectVhcList(@RequestBody QueryVhcDto queryVhcDto){
        return vhcRepairService.selectVhcList(queryVhcDto);
    }

    /**
     * 报价页面查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryVhcDto", name = "queryVhcDto", value = "", required = true)
    })
    @ApiOperation(value = "报价页面查询", notes = "报价页面查询", httpMethod = "POST")
    @PostMapping("/selectVhcPricesheetList")
    public Page<VhcPricesheetDetailsDTO> selectVhcPricesheetList(@RequestBody QueryVhcDto queryVhcDto){
        return vhcRepairService.selectVhcPricesheetList(queryVhcDto);
    }

    /**
     * 根据车架号经销商查询最近一次检查未修项
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = "")
    })
    @ApiOperation(value = "根据车架号经销商查询最近一次检查未修项", notes = "根据车架号经销商查询最近一次检查未修项", httpMethod = "GET")
    @GetMapping("/selectNoRepairItem")
    public List<NoRepairItemDto> selectNoRepairItem(@RequestParam(value = "ownerCode")String ownerCode,
                                                    @RequestParam(value = "vin") String vin,
                                                    @RequestParam(value = "roNo",required = false) String roNo){
        return vhcRepairService.selectNoRepairItem(ownerCode, vin,roNo);
    }

    /**
     * 360客户画像车辆健康检查
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "360客户画像车辆健康检查", notes = "360客户画像车辆健康检查", httpMethod = "GET")
    @GetMapping("/select360VhcItem")
    public Map<String, List<VhcItemPoDTO>> select360VhcItem(@RequestParam(value = "ownerCode",required = false)String ownerCode,
                                                            @RequestParam(value = "vin") String vin){
        return vhcRepairService.select360VhcItem(ownerCode, vin);

    }

    /**
     * 查询工时信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VhcQueryLabourDto", name = "vhcQueryLabourDto", value = "", required = true)
    })
    @ApiOperation(value = "查询工时信息", notes = "查询工时信息", httpMethod = "POST")
    @PostMapping("/queryLabourList")
    public Page<VhcQueryLabourVo> queryLabourList(@RequestBody VhcQueryLabourDto vhcQueryLabourDto){
        return vhcRepairService.queryLabourList(vhcQueryLabourDto);
    }

    /**
     * 预约单关联未修项目
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "bookingOrderNo", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "预约单关联未修项目", notes = "预约单关联未修项目", httpMethod = "GET")
    @GetMapping("/vhcConnectBookOrder")
    public void vhcConnectBookOrder(@RequestParam(value = "ownerCode")String ownerCode,
                                    @RequestParam(value = "bookingOrderNo")String bookingOrderNo,
                                    @RequestParam(value = "vin")String vin){
        vhcRepairService.vhcConnectBookOrder(ownerCode,bookingOrderNo,vin);
    }

    /**
     * 查询预约单关联未修项目
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "bookingOrderNo", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "预约单关联未修项目", notes = "查询预约单关联未修项目", httpMethod = "GET")
    @GetMapping("/selectVhcConnectBookOrder")
    public List<NoRepairItemDto> selectVhcConnectBookOrder(@RequestParam(value = "ownerCode")String ownerCode,
                                                           @RequestParam(value = "bookingOrderNo")String bookingOrderNo,
                                                           @RequestParam(value = "vin")String vin){
        return vhcRepairService.selectVhcConnectBookOrder(ownerCode, bookingOrderNo, vin);
    }


}
