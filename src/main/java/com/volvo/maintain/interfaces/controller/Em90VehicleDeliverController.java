package com.volvo.maintain.interfaces.controller;

import com.volvo.maintain.application.maintainlead.dto.TaskDeliverCarRequestDto;
import com.volvo.maintain.application.maintainlead.dto.TaskDeliverCarResponseDto;
import com.volvo.maintain.application.maintainlead.dto.VehicleDeliveryStatusChangeDTO;
import com.volvo.maintain.application.maintainlead.service.Em90VehicleDeliverService;
import com.volvo.utils.JSONUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/Em90VehicleDeliver")
@Api(value = "Em90VehicleDeliver", tags = {"Em90VehicleDeliver"})
@Slf4j
public class Em90VehicleDeliverController {
    @Autowired
    Em90VehicleDeliverService em90VehicleDeliverService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "message", value = "", required = true)
    })
    @ApiOperation(value = "取送车通知", notes = "", httpMethod = "GET")
    @GetMapping("/deliver")
    public void deliver(@RequestParam("message") String message) {
        em90VehicleDeliverService.em90Deliver(message);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VehicleDeliveryStatusChangeDTO", name = "message", value = "", required = true)
    })
    @ApiOperation(value = "取送车通知", notes = "", httpMethod = "POST")
    @PostMapping("/deliverPost")
    public void deliverPost(@RequestBody VehicleDeliveryStatusChangeDTO message) {
        String str=JSONUtil.objectToJson(message);
        em90VehicleDeliverService.em90Deliver(str);
    }
    /**
     * 创建取送车订单
     * @param taskDeliverCarRequestDto 取送车信息
     * @return TaskDeliverCarResponseDto
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "TaskDeliverCarRequestDto", name = "taskDeliverCarRequestDto", value = "取送车信息", required = true)
    })
    @ApiOperation(value = "创建取送车订单", notes = "创建取送车订单", httpMethod = "POST")
    @PostMapping("/vehicleDeliver")
    public TaskDeliverCarResponseDto vehicleDeliver(@RequestBody TaskDeliverCarRequestDto taskDeliverCarRequestDto) {
        if (StringUtils.isBlank(taskDeliverCarRequestDto.getPickupAreaCode()) ||StringUtils.isBlank(taskDeliverCarRequestDto.getReturnAreaCode())){
            throw new ServiceBizException("取送车地区code不可为空！！");
        }
        TaskDeliverCarResponseDto taskDeliverCarResponseDto =  em90VehicleDeliverService.vehicleDeliver(taskDeliverCarRequestDto);
        return taskDeliverCarResponseDto;
    }

    /**
     * 编辑取送车订单
     * @param taskDeliverCarRequestDto 取送车信息
     * @return id
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "TaskDeliverCarRequestDto", name = "taskDeliverCarRequestDto", value = "取送车信息", required = true)
    })
    @ApiOperation(value = "编辑取送车订单", notes = "编辑取送车订单", httpMethod = "POST")
    @PostMapping("/updateVehicleDeliver")
    public Integer updateVehicleDeliver(@RequestBody TaskDeliverCarRequestDto taskDeliverCarRequestDto) {
        Integer result =  em90VehicleDeliverService.updateVehicleDeliver(taskDeliverCarRequestDto);
        return result;
    }


    /**
     * 取送车未确认推送订单
     * @param oneId 订单id
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "oneId", value = "订单id", required = true)
    })
    @ApiOperation(value = "取送车未确认推送订单", notes = "取送车未确认推送订单", httpMethod = "POST")
    @PostMapping("/em90TakeDeliverCarUnconfirmedPush")
    public void em90TakeDeliverCarUnconfirmedPush(@RequestParam("oneId") String oneId) {
        em90VehicleDeliverService.em90TakeDeliverCarUnconfirmedPush(oneId);
    }



}
