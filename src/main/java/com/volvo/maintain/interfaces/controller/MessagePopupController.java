package com.volvo.maintain.interfaces.controller;


import com.volvo.maintain.application.maintainlead.dto.MessageConfirmDTO;
import com.volvo.maintain.application.maintainlead.dto.MessageResultDTO;
import com.volvo.maintain.application.maintainlead.service.MessagePopupService;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 功能描述：消息提醒adapter层
 */
@RestController
@RequestMapping("/message/popup")
@Api(value = "消息提醒adapter层", tags = {"消息提醒adapter层"})
@Slf4j
public class MessagePopupController {
    @Resource
    private MessagePopupService messagePopupService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "businessType", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "businessId", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/selectPopupRecord")
    public MessageResultDTO selectPopupRecord(@RequestParam("ownerCode")String ownerCode,
                                                          @RequestParam("vin")String vin,
                                                          @RequestParam("businessType")String businessType,
                                                          @RequestParam("businessId") String businessId,
                                              @RequestParam(value ="mileage",required = false) BigDecimal mileage) {
        if (StringUtils.isBlank(vin) || StringUtils.isBlank(businessType)) {
            throw new ServiceBizException("参数异常！");
        }
        return messagePopupService.selectPopupRecord(ownerCode, vin, businessType, businessId,mileage);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "MessageConfirmDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping("/confirm")
    public void confirmMessage(@RequestBody MessageConfirmDTO dto) {
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getBusinessType()) || StringUtils.isBlank(dto.getBusinessId())) {
            throw new ServiceBizException("参数异常！");
        }
        messagePopupService.confirmMessage(dto);
    }

}
