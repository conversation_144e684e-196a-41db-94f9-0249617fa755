package com.volvo.maintain.interfaces.controller.icup;

import com.volvo.maintain.application.maintainlead.dto.icup.IcupMileageDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcItemConfigInfoDto;
import com.volvo.maintain.application.maintainlead.service.Icup.IcupService;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/icup")
@Api(value = "icup车型", tags = {"icup车型"})
@Slf4j
public class IcupController {

    @Autowired
    public IcupService icupService;

    @ApiOperation("查询icup车型里程")
    @GetMapping("/getIcupMileageByVin")
    public IcupMileageDto getIcupMileageByVin(@RequestParam("vin") String vin) {
        if (StringUtils.isEmpty(vin)){
            throw new ServiceBizException("车架号为空");
        }
        return icupService.getIcupMileageByVin(vin);
    }


}
