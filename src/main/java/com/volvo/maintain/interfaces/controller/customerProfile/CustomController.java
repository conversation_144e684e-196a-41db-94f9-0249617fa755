package com.volvo.maintain.interfaces.controller.customerProfile;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.CustomerInfoDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CustomerService;
import com.volvo.maintain.interfaces.vo.RepairOrderHistoryParamsVo;
import com.volvo.maintain.interfaces.vo.RepairOrderHistoryResultVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/customInfo")
@Api(value = "客户信息")
public class CustomController {
    private static final Logger logger = LoggerFactory.getLogger(CustomController.class);


    @Autowired
    private CustomerService customerService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "mobile", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "flag", value = "", required = true)
    })
    @ApiOperation(value = " 去重手机号批量查询 cdp 客户属性信息", notes = "", httpMethod = "GET")
    @GetMapping("/queryCustomInfoListByMobile")
    public List<CustomerInfoDto> queryCustomInfoListByMobile(@RequestParam(value = "vin") String vin ,
                                                             @RequestParam(value = "mobile" ,required = false) String mobile,
                                                             @RequestParam(value = "ownerCode") String ownerCode,
                                                             @RequestParam(value = "flag") String flag){
        logger.info("CustomController->queryCustomInfoListByMobileVin:{},mobile:{}",vin,mobile);
        if(StringUtils.isBlank(vin)) {
            throw new ServiceBizException("vin不能为空");
        }
        return customerService.queryCustomInfoListByMobile(vin, mobile, ownerCode);
    }

    /**
     * 查询客户旅程
     * @param current
     * @param size
     * @param paramsVo
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "current", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "size", value = "", required = true)
    })
    @ApiOperation(value = "查询客户旅程", notes = "查询客户旅程", httpMethod = "GET")
    @GetMapping(value = "/queryCustomerJourney")
    public Page<RepairOrderHistoryResultVo> queryCustomerJourney(@RequestParam("current") int current,
                                                                 @RequestParam("size") int size,
                                                                 RepairOrderHistoryParamsVo paramsVo) {
        return customerService.queryCustomerJourney(current, size, paramsVo);
    }


}
