package com.volvo.maintain.interfaces.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cache")
@Api("缓存")
@Slf4j
public class CacheController {
    /**
     * redis key : test::id_{id}
     * unless = "#result == null"  如果返回结果为空，则不缓存:
     *
     */
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("cache/add")
    @Cacheable(value = "test", key = "'id_'",unless = "#result == null")
    public String addCache(@RequestParam(value = "id",required = false)String id) {
        System.out.println("addCache");
        return StrUtil.toString(id);
    }

    /**
     * redis key : test::id_{id}
     * allEntries = true  删除所有缓存
     *
     */
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/cache/del")
    @CacheEvict(value = "test", key = "'id'+#id")
    public String delCache(@RequestParam(value = "id",required = false)String id) {
        System.out.println("delCache");
        return StrUtil.toString(id);
    }
}
