package com.volvo.maintain.interfaces.controller.vhc;

import com.volvo.maintain.application.maintainlead.dto.vhc.VhcConfirmRepairDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcMaintanceReqDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcNotRepairReqDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcQuotedDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcRemindDTO;
import com.volvo.maintain.application.maintainlead.service.vhc.VhcWorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;


@Api(tags = "vhc工单相关控制层")
@RestController
@RequestMapping("/workOrder/v1")
public class WorkOrderController {

    @Autowired
    private VhcWorkOrderService vhcWorkOrderService;


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vhcNo", value = "", required = true)
    })
    @ApiOperation(value = "获取报价单维修项目信息", notes = "", httpMethod = "GET")
    @GetMapping(value = "/queryMaintenanceItems")
    public VhcQuotedDTO queryMaintenanceItems(@RequestParam(value = "vhcNo") String vhcNo) {
        return vhcWorkOrderService.queryMaintenanceItems(vhcNo);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "partNo", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "BigDecimal", name = "practicalQuantity", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "storageCode", value = "", required = true)
    })
    @ApiOperation(value = "检查零件是否缺料", notes = "", httpMethod = "GET")
    @GetMapping(value = "/checkedPartShort")
    public Integer checkedPartShort(@RequestParam(value = "partNo") String partNo, @RequestParam(value = "practicalQuantity") BigDecimal practicalQuantity, @RequestParam(value = "storageCode") String storageCode) {
        return vhcWorkOrderService.checkedPartShort(partNo, practicalQuantity, storageCode);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "partNo", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "labourCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = "", required = true)
    })
    @ApiOperation(value = "检查零件是否在工单", notes = "", httpMethod = "GET")
    @GetMapping(value = "/checkedWorkOrderHavePart")
    public Integer checkedWorkOrderHavePart(@RequestParam(value = "partNo") String partNo, @RequestParam(value = "labourCode") String labourCode, @RequestParam(value = "roNo") String roNo) {
        return vhcWorkOrderService.checkedWorkOrderHavePart(partNo, labourCode, roNo);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VhcMaintanceReqDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "保存草稿&报价完成", notes = "", httpMethod = "POST")
    @PostMapping(value = "/saveMaintenanceItems")
    public void saveMaintenanceItems(@RequestBody VhcMaintanceReqDTO dto) {
        vhcWorkOrderService.saveMaintenanceItems(dto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vhcNo", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "flag", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "itemIds", value = "")
    })
    @ApiOperation(value = "推送用户", notes = "", httpMethod = "POST")
    @PostMapping(value = "/pushCustomer")
    public void pushCustomer(@RequestParam(value = "vhcNo") String vhcNo, @RequestParam(value = "roNo") String roNo, @RequestParam(value = "flag") Integer flag, @RequestParam(value = "itemIds", required = false) String itemIds) {
        vhcWorkOrderService.pushCustomer(vhcNo, roNo, flag, itemIds);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "userId", value = "", required = true)
    })
    @ApiOperation(value = "vhc健康检查消息提醒查询", notes = "", httpMethod = "GET")
    @GetMapping(value = "/queryVhcRemind")
    public List<VhcRemindDTO> queryVhcRemind(@RequestParam(value = "userId") Long userId) {
        return vhcWorkOrderService.queryVhcRemind(userId);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "VHC已读根据id删除消息", notes = "", httpMethod = "GET")
    @GetMapping("/removeInfoById")
    public void removeInfoById(@RequestParam("id") Integer id) {
        vhcWorkOrderService.removeInfoById(id);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VhcConfirmRepairDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "代客户反向确认维修", notes = "", httpMethod = "POST")
    @PostMapping(value = "/confirmRepair")
    public void confirmRepair(@RequestBody VhcConfirmRepairDTO dto) {
        vhcWorkOrderService.confirmRepair(dto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "isPad", value = "", required = true)
    })
    @ApiOperation(value = "维修质检校验按钮", notes = "", httpMethod = "GET")
    @GetMapping(value = "/verification")
    public void verification(@RequestParam("roNo") String roNo, @RequestParam("ownerCode") String ownerCode, @RequestParam("isPad") String isPad) {
        vhcWorkOrderService.verification(roNo, ownerCode, isPad);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VhcNotRepairReqDTO", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "保存不修原因", notes = "", httpMethod = "POST")
    @PostMapping(value = "/saveNotRepair")
    public void saveNotRepair(@RequestBody VhcNotRepairReqDTO dto){
        vhcWorkOrderService.saveNotRepair(dto);
    }

}
