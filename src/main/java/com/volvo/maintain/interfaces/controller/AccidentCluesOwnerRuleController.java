package com.volvo.maintain.interfaces.controller;

import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.OwnerRuleDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.SaveOwnerRuleDto;
import com.volvo.maintain.application.maintainlead.service.AccidentCluesOwnerRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/accidentCluesOwnerRule")
@Api(value = "企微事故线索-配置规则adapter层", tags = {"企微事故线索-配置规则adapter层"})
@Slf4j
public class AccidentCluesOwnerRuleController {
	@Autowired
	private AccidentCluesOwnerRuleService accidentCluesOwnerRuleService;

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "JSONObject", name = "jsonObject", value = "", required = true)
	})
	@ApiOperation(value = "", notes = "", httpMethod = "POST")
	@PostMapping("/selectOwnerRules/interf")
	public void selectOwnerRules(@RequestBody JSONObject jsonObject) {
		Objects.requireNonNull(jsonObject, "未获取到jsonObject");
		List<String> ownerCodes = jsonObject.getObject("ownerCodes", List.class);
		Integer ruleType = jsonObject.getInteger("ruleType");
		accidentCluesOwnerRuleService.selectOwnerRules(ownerCodes, ruleType);
	}

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ownerCode", value = "", required = true),
			@ApiImplicitParam(paramType = "path", dataType = "int", name = "ruleType", value = "", required = true)
	})
	@ApiOperation(value = "", notes = "", httpMethod = "GET")
	@GetMapping("/selectOwnerRule/{ownerCode}/{ruleType}")
	public List<OwnerRuleDto> selectOwnerRule(@PathVariable("ownerCode") String ownerCode, @PathVariable("ruleType") Integer ruleType) {
		return accidentCluesOwnerRuleService.selectOwnerRule(ownerCode, ruleType);
	}

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ids", value = "", required = true)
	})
	@ApiOperation(value = "", notes = "", httpMethod = "GET")
	@GetMapping("/deleteOwnerRule/{ids}")
	public Integer deleteOwnerRule(@PathVariable("ids") String ids) {
		return accidentCluesOwnerRuleService.deleteOwnerRule(ids);
	}

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "SaveOwnerRuleDto", name = "saveOwnerRuleDto", value = "", required = true)
	})
	@ApiOperation(value = "", notes = "", httpMethod = "POST")
	@PostMapping("/saveOwnerRule")
	public void saveOwnerRule(@RequestBody SaveOwnerRuleDto saveOwnerRuleDto) {
		accidentCluesOwnerRuleService.saveOwnerRule(saveOwnerRuleDto);
	}

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ownerCode", value = "", required = true)
	})
	@ApiOperation(value = "", notes = "", httpMethod = "GET")
	@GetMapping("/selectAllocatedInfo/interf/{ownerCode}")
	public OwnerRuleDto selectAllocatedInfo(@PathVariable("ownerCode") String ownerCode) {
		return accidentCluesOwnerRuleService.selectAllocatedInfo(ownerCode);
	}

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "string", name = "ownerCode", value = "", required = true)
	})
	@ApiOperation(value = "", notes = "", httpMethod = "GET")
	@GetMapping("/selectMessageInfo/interf/{ownerCode}")
	public List<OwnerRuleDto> selectMessageInfo(@PathVariable("ownerCode") String ownerCode) {
		return accidentCluesOwnerRuleService.selectMessageInfo(ownerCode);
	}
}
