package com.volvo.maintain.interfaces.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.RequestDto;
import com.volvo.maintain.application.maintainlead.dto.SettlementDocConfirmCompensateDto;
import com.volvo.maintain.application.maintainlead.dto.order.CancelOrderDto;
import com.volvo.maintain.application.maintainlead.dto.order.DriverDto;
import com.volvo.maintain.application.maintainlead.dto.order.PlaceOrderDto;
import com.volvo.maintain.application.maintainlead.dto.order.PriceDto;
import com.volvo.maintain.application.maintainlead.dto.order.QueryVehicleDeliverDto;
import com.volvo.maintain.application.maintainlead.dto.order.SaveVehicleDeliverDto;
import com.volvo.maintain.application.maintainlead.dto.order.ShopVehicleDeliverDto;
import com.volvo.maintain.application.maintainlead.service.OrderService;
import com.volvo.maintain.application.maintainlead.vo.order.DetailVo;
import com.volvo.maintain.application.maintainlead.vo.order.DriverInfoVo;
import com.volvo.maintain.application.maintainlead.vo.order.DriverVo;
import com.volvo.maintain.application.maintainlead.vo.order.PlaceOrderVo;
import com.volvo.maintain.application.maintainlead.vo.order.PriceVo;
import com.volvo.maintain.application.maintainlead.vo.order.SaveVehicleDeliverVo;
import com.volvo.maintain.application.maintainlead.vo.order.ShopVehicleDeliverVo;
import com.volvo.maintain.application.maintainlead.vo.order.TraceVo;
import com.volvo.maintain.application.maintainlead.vo.order.VehicleDeliverDetailVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;


@Api(tags = "取送车管理web")
@RestController
@RequestMapping("order")
@Validated
public class OrderController {

	@Autowired
	private OrderService orderService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RequestDto<ShopVehicleDeliverDto>", name = "shopVehicleDeliverDto", value = "", required = true)
    })
    @ApiOperation(value = "店端查询取送车订单(分页)", notes = "", httpMethod = "POST")
    @PostMapping(value = "/vehicleDeliver/shopSearchVehiicleDeliverPage")
    public Page<ShopVehicleDeliverVo> shopSearchVehiicleDeliverPage(@RequestBody RequestDto<ShopVehicleDeliverDto> shopVehicleDeliverDto) {
        return orderService.shopSearchVehiicleDeliverPage(shopVehicleDeliverDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RequestDto<CancelOrderDto>", name = "cancelOrderDto", value = "", required = true)
    })
    @ApiOperation(value = "取送车订单取消", notes = "", httpMethod = "POST")
    @PostMapping(value = "/vehicleDeliver/cancelVehicleOrder")
    public boolean cancelVehicleOrder(@RequestBody RequestDto<CancelOrderDto> cancelOrderDto) {
        return orderService.cancelVehicleOrder(cancelOrderDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RequestDto<PlaceOrderDto>", name = "placeOrderDto", value = "", required = true)
    })
    @ApiOperation(value = "e代驾下单", notes = "", httpMethod = "POST")
    @PostMapping(value = "/vehicleDeliver/placeOrder")
    public PlaceOrderVo placeOrder(@RequestBody RequestDto<PlaceOrderDto> placeOrderDto) {
        return orderService.placeOrder(placeOrderDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RequestDto<ShopVehicleDeliverDto>", name = "shopVehicleDeliverDto", value = "", required = true)
    })
    @ApiOperation(value = "店端查询取送车订单(无分页)", notes = "", httpMethod = "POST")
    @PostMapping(value = "/vehicleDeliver/shopSearchVehiicleDeliverList")
    public List<ShopVehicleDeliverVo> shopSearchVehiicleDeliverList(@RequestBody RequestDto<ShopVehicleDeliverDto> shopVehicleDeliverDto) {
        return orderService.shopSearchVehiicleDeliverList(shopVehicleDeliverDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RequestDto<QueryVehicleDeliverDto>", name = "queryVehicleDeliverDto", value = "", required = true)
    })
    @ApiOperation(value = "查询取送车订单详情(所有)", notes = "", httpMethod = "POST")
    @PostMapping(value = "/vehicleDeliver/allDetail")
    public List<VehicleDeliverDetailVo> allDetail(@RequestBody RequestDto<QueryVehicleDeliverDto> queryVehicleDeliverDto) {
        return orderService.allDetail(queryVehicleDeliverDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "SaveVehicleDeliverDto", name = "saveVehicleDeliverDto", value = "", required = true)
    })
    @ApiOperation(value = "创建取送车订单(老DMS)", notes = "创建取送车订单(老DMS)", httpMethod = "POST")
    @PostMapping(path = "/vehicleDeliver/addVehicleDeliverOldDms")
    public SaveVehicleDeliverVo addVehicleDeliverOldDms(@RequestBody SaveVehicleDeliverDto saveVehicleDeliverDto, BindingResult bindingResult) {
        return orderService.addVehicleDeliverOldDms(saveVehicleDeliverDto,bindingResult);
    }

    //////////////////////////////////////////////////////////////////////////////////////////////////////


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "customerId", value = "", required = true)
    })
    @ApiOperation(value = "获取商户的账号余额", notes = "////////////////////////////////////////////////////////////////////////////////////////////////////", httpMethod = "GET")
    @GetMapping(value = "/business/balance/customerId/{customerId}")
    public BigDecimal balanceCustomerId(@ApiParam(name = "customerId", value = "customerId", required = true) @PathVariable String customerId) {
        return orderService.balanceCustomerId(customerId);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RequestDto<DriverDto>", name = "driverDto", value = "", required = true)
    })
    @ApiOperation(value = "获取附近驻店司机列表", notes = "", httpMethod = "POST")
    @PostMapping(value = "/business/getFixList")
    public List<DriverVo> getFixList(@RequestBody RequestDto<DriverDto> driverDto) {
        return orderService.getFixList(driverDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RequestDto<PriceDto>", name = "priceDto", value = "", required = true)
    })
    @ApiOperation(value = "获取预估价格", notes = "", httpMethod = "POST")
    @PostMapping(value = "/business/price")
    public PriceVo price(@RequestBody RequestDto<PriceDto> priceDto) {
        return orderService.price(priceDto);
    }



    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////




    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "orderId", value = "", required = true)
    })
    @ApiOperation(value = "获取订单详情", notes = "/////////////////////////////////////////////////////////////////////////////////////////////////////////////////", httpMethod = "GET")
    @GetMapping(value = "/order/detail/orderId/{orderId}")
    public DetailVo detailOrderId(@ApiParam(name = "orderId", value = "订单id", required = true) @PathVariable String orderId) {
        return orderService.detailOrderId(orderId);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "orderId", value = "", required = true),
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "type", value = "", required = true)
    })
    @ApiOperation(value = "获取司机代驾轨迹", notes = "", httpMethod = "GET")
    @GetMapping(value = "/order/trace/orderId/{orderId}/type/{type}")
    public TraceVo traceOrderId(@ApiParam(name = "orderId", value = "订单id", required = true) @PathVariable String orderId,
                                            @ApiParam(name = "type", value = "代驾类型 Int 否 1-取车 ") @PathVariable Integer type) {
        return orderService.traceOrderId(orderId,type);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "orderId", value = "", required = true),
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "type", value = "", required = true)
    })
    @ApiOperation(value = "获取司机信息", notes = "", httpMethod = "GET")
    @GetMapping(value = "/order/driverInfo/orderId/{orderId}/type/{type}")
    public DriverInfoVo driverInfoOrderId(@ApiParam(name = "orderId", value = "订单id", required = true) @PathVariable String orderId,
                                                 @ApiParam(name = "type", value = "代驾类型 Int 否 1-取车 ") @PathVariable Integer type) {
        return orderService.driverInfoOrderId(orderId,type);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "orderId", value = "", required = true),
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "daijiaType", value = "", required = true)
    })
    @ApiOperation(value = "获取车辆信息照片", notes = "", httpMethod = "GET")
    @GetMapping(value = "/order/getCarPhotos/orderId/{orderId}/daijiaType/{daijiaType}")
    public Map<String, Object> getCarPhotosOrderId(@ApiParam(name = "orderId", value = "订单号", required = true) @PathVariable String orderId,
                                                   @ApiParam(name = "daijiaType", value = "代驾类型(1:取车，2：还车)", required = true) @PathVariable Integer daijiaType) {
        return orderService.getCarPhotosOrderId(orderId,daijiaType);
    }
    @PostMapping(value = "/v1/settlementDocConfirmCompensate")
    public void settlementDocConfirmCompensate(@RequestBody SettlementDocConfirmCompensateDto parseObject) {
        orderService.settlementDocConfirmCompensate(parseObject);
    }

}
