/*
 * Copyright (c) Volvo CAR Distribution (SHANGHAI) Co., Ltd. 2023. All rights reserved.
 */
package com.volvo.maintain.interfaces.controller;

import com.volvo.maintain.application.maintainlead.service.customerProfile.BookingRegisterService;
import com.volvo.maintain.interfaces.vo.booking.BookingEm90Vo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 功能描述：养修线索控制层
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@RestController
@RequestMapping("/bookingRegister")
@Api(value = "养修预约adapter层", tags = {"养修预约adapter层"})
@Slf4j
public class BookingRegisterController {

    @Autowired
    private BookingRegisterService bookingRegisterService;

    /**
     * 功能描述：EM90预约单提醒
     *
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "beginDate", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "endDate", value = "", required = true)
    })
    @ApiOperation(value = "EM90预约单提醒", notes = "功能描述：EM90预约单提醒", httpMethod = "GET")
    @GetMapping("/em90BookingOrderPush")
    public void em90BookingOrderPush(@RequestParam("beginDate") int beginDate,@RequestParam("endDate") int endDate) {
        bookingRegisterService.em90BookingOrderPush(beginDate, endDate);
    }

    /**
     * 功能描述：EM90预约单提醒
     *
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<BookingEm90Vo>", name = "bookingEm90VoList", value = "", required = true)
    })
    @ApiOperation(value = "EM90预约单提醒", notes = "功能描述：EM90预约单提醒", httpMethod = "POST")
    @PostMapping ("/em90BookingOrderPushCs")
    public void em90BookingOrderPushCs(@RequestBody List<BookingEm90Vo> bookingEm90VoList) {
        bookingRegisterService.em90BookingOrderPushCs(bookingEm90VoList);
    }

}
