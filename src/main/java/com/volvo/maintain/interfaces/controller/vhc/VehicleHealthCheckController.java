package com.volvo.maintain.interfaces.controller.vhc;

import com.volvo.maintain.application.maintainlead.dto.vhc.VehicleHealthCheckDetailDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VehicleHealthCheckDetailParamDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VehicleHealthCheckInfoDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcItemConfigInfoDto;
import com.volvo.maintain.application.maintainlead.service.vhc.VehicleHealthCheckDeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @Description 车辆健康检查控制
 * @Date 2024/9/25 14:36
 */
@RestController
@RequestMapping("/vehicleHealthCheck/v1")
@Api(value = "车辆健康检查", tags = {"车辆健康检查"})
@Slf4j
@AllArgsConstructor
public class VehicleHealthCheckController {

    private final VehicleHealthCheckDeService vehicleHealthCheckService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VehicleHealthCheckDetailParamDto", name = "vehicleHealthCheckDetailParamDto", value = "", required = true)
    })
    @ApiOperation(value = "查询车辆健康检查详情", notes = "", httpMethod = "POST")
    @PostMapping("/getVehicleHealthCheckDetail")
    public VehicleHealthCheckDetailDto getVehicleHealthCheckDetail(@RequestBody VehicleHealthCheckDetailParamDto vehicleHealthCheckDetailParamDto){
      return vehicleHealthCheckService.getVehicleHealthCheckDetail(vehicleHealthCheckDetailParamDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "classId", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "configClassId", value = "", required = true)
    })
    @ApiOperation(value = "根据大类id查询小类信息", notes = "", httpMethod = "GET")
    @GetMapping("/getVhcItemInfoByClassId")
    public List<VhcItemConfigInfoDto> getVhcItemInfoByClassId(@RequestParam("classId") Integer classId, @RequestParam("configClassId") String configClassId){
        //根据大类配置表id查询大类
        return vehicleHealthCheckService.getVhcItemInfoByClassId(classId, configClassId);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VehicleHealthCheckInfoDto", name = "vehicleHealthCheckInfoDto", value = "", required = true)
    })
    @ApiOperation(value = "保存车辆健康检查信息", notes = "", httpMethod = "POST")
    @PostMapping("/saveVehicleHealthCheckInfo")
    public void saveVehicleHealthCheckInfo(@RequestBody VehicleHealthCheckInfoDto vehicleHealthCheckInfoDto){
        vehicleHealthCheckService.saveVehicleHealthCheckInfo(vehicleHealthCheckInfoDto);
    }
}
