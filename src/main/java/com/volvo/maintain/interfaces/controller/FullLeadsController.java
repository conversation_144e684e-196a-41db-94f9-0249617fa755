package com.volvo.maintain.interfaces.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.FullLeadQueryDto;
import com.volvo.maintain.application.maintainlead.dto.FullLeadsCallDto;
import com.volvo.maintain.application.maintainlead.dto.FullLeadsFollowDto;
import com.volvo.maintain.application.maintainlead.service.FullLeadsService;
import com.volvo.maintain.application.maintainlead.vo.CallDetailVo;
import com.volvo.maintain.application.maintainlead.vo.FullLeadVo;
import com.volvo.maintain.application.maintainlead.vo.FullLeadsTagVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 功能描述：全量线索控制层
 *
 * <AUTHOR>
 * @since 2023/12/19
 */
@RestController
@RequestMapping("/fullLeads")
@Api(value = "全量线索应用层", tags = {"全量线索应用层"})
@Slf4j
public class FullLeadsController {

    private final FullLeadsService fullLeadsService;

    public FullLeadsController(FullLeadsService fullLeadsService) {
        this.fullLeadsService = fullLeadsService;
    }

    /**
     * 功能描述：查询全量线索列表
     *
     * @param queryParams 全量线索查询dto对象
     * @return IPage<FullLeadVo> 全量线索列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "FullLeadQueryDto", name = "queryParams", value = "全量线索查询dto对象", required = true)
    })
    @ApiOperation(value = "查询全量线索列表", notes = "功能描述：查询全量线索列表", httpMethod = "POST")
    @PostMapping("/list")
    public Page<FullLeadVo> queryFullLeadsList(@RequestBody FullLeadQueryDto queryParams) {
        log.info("queryFullLeadsList enter args:{}", JSON.toJSONString(queryParams));
        return fullLeadsService.queryFullLeadsList(queryParams);
    }

    /**
     * 功能描述：查询全量线索列表
     *
     * @param queryParams 全量线索查询dto对象
     * @return IPage<FullLeadVo> 全量线索列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "FullLeadQueryDto", name = "queryParams", value = "全量线索查询dto对象", required = true)
    })
    @ApiOperation(value = "查询全量线索列表(厂端)", notes = "功能描述：查询全量线索列表", httpMethod = "POST")
    @PostMapping("/oemList")
    public Page<FullLeadVo> queryOemFullLeadsList(@RequestBody FullLeadQueryDto queryParams) {
        log.info("queryOemFullLeadsList enter args:{}", JSON.toJSONString(queryParams));
        return fullLeadsService.queryOemFullLeadsList(queryParams);
    }

    /**
     * 功能描述：全量线索列表导出
     *
     * @param queryParams 全量线索查询dto对象
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "FullLeadQueryDto", name = "queryParams", value = "全量线索查询dto对象", required = true)
    })
    @ApiOperation(value = "全量线索列表导出", notes = "功能描述：全量线索列表导出", httpMethod = "POST")
    @PostMapping("/export")
    public void exportList(@RequestBody FullLeadQueryDto queryParams) {
        log.info("exportList enter args:{}", JSON.toJSONString(queryParams));
        fullLeadsService.exportList(queryParams);
    }

    /**
     * 功能描述：全量线索列表导出回调
     *
     * @return IPage<FullLeadVo> 全量线索列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "licensePlateNum", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "List<Integer>", name = "inviteTypeList", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "List<Integer>", name = "followStatusList", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "List<Long>", name = "saIdList", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "saName", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "planFollowDateBegin", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "planFollowDateEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "adviseInDateBegin", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "adviseInDateEnd", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "twiceMonth", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "largeAreaId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "areaId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "currentPage", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "pageSize", value = "")
    })
    @ApiOperation(value = "全量线索列表导出回调", notes = "功能描述：全量线索列表导出回调", httpMethod = "GET")
    @GetMapping("/exportCallback")
    public List<FullLeadVo> exportCallback(
            @RequestParam(value = "dealerCode", required = false) String dealerCode,
            @RequestParam(value = "licensePlateNum", required = false) String licensePlateNum,
            @RequestParam(value = "vin", required = false) String vin,
            @RequestParam(value = "inviteTypeList", required = false) List<Integer> inviteTypeList,
            @RequestParam(value = "followStatusList", required = false) List<Integer> followStatusList,
            @RequestParam(value = "saIdList", required = false) List<Long> saIdList,
            @RequestParam(value = "saName", required = false) String saName,
            @RequestParam(value = "planFollowDateBegin", required = false) String planFollowDateBegin,
            @RequestParam(value = "planFollowDateEnd", required = false) String planFollowDateEnd,
            @RequestParam(value = "adviseInDateBegin", required = false) String adviseInDateBegin,
            @RequestParam(value = "adviseInDateEnd", required = false) String adviseInDateEnd,
            @RequestParam(value = "twiceMonth", required = false) String twiceMonth,
            @RequestParam(value = "largeAreaId", required = false) String largeAreaId,
            @RequestParam(value = "areaId", required = false) String areaId,
            @RequestParam(value = "currentPage", required = false) Long currentPage,
            @RequestParam(value = "pageSize", required = false) Long pageSize
    ) {
        FullLeadQueryDto queryParams = new FullLeadQueryDto();
        queryParams.setDealerCode(dealerCode)
                .setLargeAreaId(largeAreaId)
                .setAreaId(areaId)
                .setLicensePlateNum(licensePlateNum)
                .setVin(vin)
                .setInviteTypeList(inviteTypeList)
                .setFollowStatusList(followStatusList)
                .setSaIdList(saIdList)
                .setSaName(saName)
                .setPlanFollowDateBegin(planFollowDateBegin)
                .setPlanFollowDateEnd(planFollowDateEnd)
                .setAdviseInDateBegin(adviseInDateBegin)
                .setAdviseInDateEnd(adviseInDateEnd)
                .setTwiceMonth(twiceMonth)
                .setCurrentPage(currentPage)
                .setPageSize(pageSize);
        log.info("exportCallback enter args:{}", JSON.toJSONString(queryParams));
        return fullLeadsService.exportCallback(queryParams);
    }

    /**
     * 功能描述：全量线索tag
     *
     * @param id         线索id
     * @param inviteType 线索类型
     * @param vin        vin
     * @param dealerCode 经销商代码
     * @return FullLeadsTagVo
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "线索id"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "inviteType", value = "线索类型"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "vin", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dealerCode", value = "经销商代码", required = true)
    })
    @ApiOperation(value = "查询全量线索tag列表", notes = "功能描述：全量线索tag", httpMethod = "GET")
    @GetMapping("/tagList")
    public FullLeadsTagVo queryTagList(@RequestParam(value = "id", required = false) Long id,
                                       @RequestParam(value = "inviteType", required = false) Integer inviteType,
                                       @RequestParam("vin") String vin, @RequestParam("dealerCode") String dealerCode) {
        log.info("queryTagList enter args: id={}, inviteType={}, vin={}", id, inviteType, vin);
        FullLeadsTagVo fullLeadsTagVo = fullLeadsService.queryTagList(id, inviteType, vin, dealerCode);
        log.info("查询全量线索tag列表返回结果：{}", JSON.toJSONString(fullLeadsTagVo));
        return fullLeadsTagVo;
    }

    /**
     * 功能描述：全量线索通话记录保存
     *
     * @param params 通话记录保存参数对象
     * @return List<FullLeadsCallDto> 返回结果
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<FullLeadsCallDto>", name = "params", value = "通话记录保存参数对象", required = true)
    })
    @ApiOperation(value = "功能描述：全量线索通话记录保存", notes = "功能描述：全量线索通话记录保存", httpMethod = "POST")
    @PostMapping("/saveCallRecords")
    public List<FullLeadsCallDto> saveCallRecords(@RequestBody List<FullLeadsCallDto> params) {
        log.info("saveCallRecords enter args:{}", JSON.toJSONString(params));
        return fullLeadsService.saveCallRecordList(params);
    }

    /**
     * 功能描述：全量线索跟进
     *
     * @param followList 全量线索dto对象
     * @return IPage<FullLeadVo> 全量线索列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<FullLeadsFollowDto>", name = "followList", value = "全量线索dto对象", required = true)
    })
    @ApiOperation(value = "全量线索跟进", notes = "功能描述：全量线索跟进", httpMethod = "POST")
    @PostMapping("/saveFollowRecords")
    public Boolean saveFollowRecords(@RequestBody List<FullLeadsFollowDto> followList) {
        log.info("saveFollowRecords enter args:{}", JSON.toJSONString(followList));
        return fullLeadsService.saveFollowRecords(followList);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<FullLeadsFollowDto>", name = "followList", value = "", required = true)
    })
    @ApiOperation(value = "全量线索通话校验", notes = "", httpMethod = "POST")
    @PostMapping("/checkFollowAI")
    public List<Integer> checkFollowAI(@RequestBody List<FullLeadsFollowDto> followList) {
        log.info("checkFollowAI enter args:{}", JSON.toJSONString(followList));
        return fullLeadsService.checkFollowAI(followList);
    }

    /**
     * 邀约通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "detailId", value = "跟进记录id", required = true)
    })
    @ApiOperation(value = "邀约类型线索通话记录查询", notes = "邀约通话记录查询", httpMethod = "GET")
    @GetMapping("/inviteCallList")
    public List<CallDetailVo> inviteCallList(@RequestParam("detailId") Long detailId) {
        log.info("inviteCallList enter args: detailId={}", detailId);
        return fullLeadsService.inviteCallList(detailId);
    }

    /**
     * 续保通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "detailId", value = "跟进记录id", required = true)
    })
    @ApiOperation(value = "续保类型线索通话记录查询", notes = "续保通话记录查询", httpMethod = "GET")
    @GetMapping("/insuranceCallList")
    public List<CallDetailVo> insuranceCallList(@RequestParam("detailId") Long detailId) {
        log.info("insuranceCallList enter args: detailId={}", detailId);
        return fullLeadsService.insuranceCallList(detailId);
    }

    /**
     * 故障灯通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "detailId", value = "跟进记录id", required = true)
    })
    @ApiOperation(value = "故障灯线索通话记录查询", notes = "故障灯通话记录查询", httpMethod = "GET")
    @GetMapping("/faultLightCallList")
    public List<CallDetailVo> faultLightCallList(@RequestParam("detailId") Long detailId) {
        log.info("faultLightCallList enter args: detailId={}", detailId);
        return fullLeadsService.faultLightCallList(detailId);
    }

    /**
     * 功能描述：自建邀约线索删除功能
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "Map<String, Object>", name = "param", value = "", required = true)
    })
    @ApiOperation(value = "自建邀约线索删除功能", notes = "功能描述：自建邀约线索删除功能", httpMethod = "POST")
    @PostMapping("/oem/delete")
    public void deleteSelfCreateInvitation(@RequestBody Map<String, Object> param) {
        log.info("自建邀约线索删除功能入参{}", param);
        fullLeadsService.deleteSelfCreateInvitation(param);
    }
}
