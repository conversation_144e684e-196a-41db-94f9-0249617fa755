package com.volvo.maintain.interfaces.controller.customerProfile;

import com.volvo.maintain.application.maintainlead.dto.PrecheckOrderTagDto;
import com.volvo.maintain.application.maintainlead.dto.PrecheckOrderTagParamsDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.PrecheckOrderTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/precheckOrderTag")
@Api(value = "环检单标签")
public class PrecheckOrderTagController {

    @Autowired
    private PrecheckOrderTagService precheckOrderTagService;


    /**
     * 环检单标签查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "PrecheckOrderTagParamsDto", name = "precheckOrderTagParamsDto", value = "", required = true)
    })
    @ApiOperation(value = "环检单标签查询", notes = "环检单标签查询", httpMethod = "POST")
    @PostMapping("/queryPrecheckOrderTag")
    public List<PrecheckOrderTagDto> queryPrecheckOrderTag(@RequestBody PrecheckOrderTagParamsDto precheckOrderTagParamsDto) {
        List<PrecheckOrderTagDto> precheckOrderTagList = precheckOrderTagService.queryPrecheckOrderTag(precheckOrderTagParamsDto.getOwnerCode(), precheckOrderTagParamsDto.getVinLicenseDtos(), precheckOrderTagParamsDto.getRecords());
        return precheckOrderTagList;
    }

}
