package com.volvo.maintain.interfaces.controller;

import com.volvo.maintain.application.maintainlead.dto.AccidentCluesDto;
import com.volvo.maintain.application.maintainlead.dto.AccidentCluesFollowStatusChangeTaskDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.*;
import com.volvo.maintain.application.maintainlead.service.AccidentCluesService;
import com.volvo.maintain.application.maintainlead.vo.AccidentCluesVo;
import com.volvo.maintain.application.maintainlead.vo.DealerVO;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/accidentClues")
@Api(value = "企微事故线索adapter层", tags = {"企微事故线索adapter层"})
@Slf4j
public class AccidentCluesController {

    private final AccidentCluesService accidentCluesService;

    public AccidentCluesController(AccidentCluesService accidentCluesService) {
        this.accidentCluesService = accidentCluesService;
    }

    /**
     * 通过保单号和vin 获取续保相关信息
     *
     * @param insuranceNo 保单号
     * @param vin vin
     * @return 续保信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "vin"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "insuranceNo", value = "保单号")
    })
    @ApiOperation(value = "通过保单号和vin 获取续保相关信息", notes = "通过保单号和vin 获取续保相关信息", httpMethod = "GET")
    @GetMapping("insurance/dealer/vin/insuranceNo/interf")
    public InsuranceDealerDto getInsuranceDealer(@RequestParam(value = "vin",required = false) String vin,
        @RequestParam(value = "insuranceNo", required = false) String insuranceNo) {
        if (StringUtils.isBlank(vin)) {
            throw new ServiceBizException("VIN参数缺失");
        }
        return accidentCluesService.getInsuranceDealer(insuranceNo, vin);
    }

    /**
     * 接受下发线索
     * @param dto liteCrm下发线索
     * @return 布尔成功失败
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "LeadOperationResultDto", name = "dto", value = "liteCrm下发线索", required = true)
    })
    @ApiOperation(value = "接受下发线索", notes = "接受下发线索", httpMethod = "POST")
    @PostMapping("/inviteClue/clueDataSynchro/interf")
    public LiteCrmClueResultDTO crmToNewbieClueDistribute(@RequestBody LeadOperationResultDto dto) {
        log.info("crmToNewbieClueDistribute.start:{}",dto.getId());
        if (Objects.isNull(dto)) {
            throw new ServiceBizException("参数有误！");
        }
        LiteCrmClueResultDTO liteCrmClueResultDTO = accidentCluesService.crmToNewbieClueDistribute(dto);
        log.info("crmToNewbieClueDistribute.end:{}",dto.getId());
        return liteCrmClueResultDTO;
    }
    /**
     * 事故线索超时未跟进
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesFollowStatusChangeTaskDto", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "事故线索超时未跟进", notes = "事故线索超时未跟进", httpMethod = "POST")
    @PostMapping("/applicationSyncAccidentCluesTimeOut")
    public void syncAccidentCluesTimeOut(@RequestBody AccidentCluesFollowStatusChangeTaskDto dto) {
    	log.info("applicationSyncAccidentCluesTimeOut--start:{}", dto.toString());
    	if(null != dto && dto.getRangeTime() != null && dto.getConditionTime() != null) {
    		accidentCluesService.updateAccidentCluesTimeOutJob(dto);
    	}
    }
    /**
     * 事故线索超时关闭
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesFollowStatusChangeTaskDto", name = "dto", value = "", required = true)
    })
    @ApiOperation(value = "事故线索超时关闭", notes = "事故线索超时关闭", httpMethod = "POST")
    @PostMapping("/applicationSyncAccidentCluesClose")
    public void syncAccidentCluesClose(@RequestBody AccidentCluesFollowStatusChangeTaskDto dto) {
    	log.info("applicationSyncAccidentCluesClose--start:{}", dto.toString());
    	if(null != dto && dto.getRangeTime() != null && dto.getConditionTime() != null) {
    		accidentCluesService.updateAccidentCluesCloseJob(dto);
    	}
    }

    /**
     * 事故线索集合
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "AccidentCluesDto", name = "query", value = "", required = true)
    })
    @ApiOperation(value = "事故线索集合", notes = "事故线索集合", httpMethod = "POST")
    @PostMapping("list")
    public List<AccidentCluesVo> list(@RequestBody AccidentCluesDto query){
        log.info("accident clue list query:{}",query);
        return accidentCluesService.list(query);
    }
    /**
     * 根据acId,dealerPhone查询虚拟手机号
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "acId", value = "", required = true)
    })
    @ApiOperation(value = "根据acId,dealerPhone查询虚拟手机号", notes = "根据acId,dealerPhone查询虚拟手机号", httpMethod = "POST")
    @PostMapping("/getVirtualNumber")
    public String getVirtualNumberById(@RequestParam(value = "acId") Long acId){
        log.info("getVirtualNumberById:{}", acId);
        return accidentCluesService.getVirtualNumberById(acId);
    }


    /**
     * 导出数据查询
     * <p>
     * 下载中心回调
     */
    @ApiOperation(value = "导出数据查询 <p> 下载中心回调", notes = "导出数据查询 <p> 下载中心回调", httpMethod = "GET")
    @GetMapping("export/oss")
    public List<AccidentCluesExportDto> exportOss(AccidentCluesExportQueryDto query){
        return accidentCluesService.exportOss(query);
    }

    /**
     * 查询全网经销商
     * @param params
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DealerDTO", name = "params", value = "", required = true)
    })
    @ApiOperation(value = "查询全网经销商", notes = "查询全网经销商", httpMethod = "POST")
    @PostMapping("/getDealerList")
    public DealerVO getDealerList(@RequestBody DealerDTO params){
        log.info("application-getDealerList:{}", params);
        return accidentCluesService.getDealerList(params);
    }

    /**
     * 厂端线索分配经销商
     * @param params
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<AllotDealerDTO>", name = "params", value = "", required = true)
    })
    @ApiOperation(value = "厂端线索分配经销商", notes = "厂端线索分配经销商", httpMethod = "POST")
    @PostMapping("/allotDealer")
    void allotDealer(@RequestBody List<AllotDealerDTO> params){
        log.info("application-allotDealer:{}", params);
        accidentCluesService.allotDealer(params);
    }

    /**
     * 线索池，事故线索数据统计
     * @param params
     * @return
     */
    @PostMapping("/cluePool/getSumAccidentInfo")
    public AccidentCluesSumInfoDTO getSumAccidentInfo(@RequestBody AccidentCluesExportQueryDto params){
        log.info("application-getSumAccidentInfo:{}", params);
        return accidentCluesService.getSumAccidentInfo(params);
    }

}
