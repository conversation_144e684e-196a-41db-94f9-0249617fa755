package com.volvo.maintain.interfaces.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.part.PartDto;
import com.volvo.maintain.application.maintainlead.dto.part.ShortPartReplenishmentDTO;
import com.volvo.maintain.application.maintainlead.dto.part.ShortPartResultDTO;
import com.volvo.maintain.application.maintainlead.service.ReplenishmentService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "缺料补货")
@RestController
@RequestMapping("/replenishment")
public class ReplenishmentController {
    @Autowired
    private ReplenishmentService replenishmentService;
    /**
     * 校验零件是否缺料
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<PartDto>", name = "parts", value = "", required = true)
    })
    @ApiOperation(value = "检查是否缺料", notes = "检查是否缺料", httpMethod = "POST")
    @RequestMapping(value = "/checkeReplenishment", method = RequestMethod.POST)
    public List<ShortPartResultDTO> checkReplenishment(@RequestBody List<PartDto> parts){
        return replenishmentService.checkReplenishment(parts, true);
    }
    /**
     * 缺料明细生成及自动补货接口
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ShortPartReplenishmentDTO", name = "shortPartDto", value = "", required = true)
    })
    @ApiOperation(value = "缺料补货", notes = "缺料补货", httpMethod = "POST")
    @RequestMapping(value = "/shortageReplenishment", method = RequestMethod.POST)
    public Long shortageReplenishment(@RequestBody ShortPartReplenishmentDTO shortPartDto){
        return replenishmentService.shortageReplenishment(shortPartDto);
    }
    
    /**
	 * 维修业务主查询
	 */
	@ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "Map<String, String>", name = "queryParams", value = "", required = true)
    })
    @ApiOperation(value = "维修业务查询", notes = "获取系统中满足查询条件的维修业务数据", httpMethod = "GET")
    @GetMapping(value = "/repairBusiness/findAll")
    public Page<Map<String,String>> findAll(@RequestParam Map<String,String> queryParams) {
		return replenishmentService.findAll(queryParams);
	}
    /** shortType
     * 缺料明细导出
     * @param map 导出参数查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "Map<String, String>", name = "map", value = "导出参数查询", required = true)
    })
    @ApiOperation(value = "缺料明细导出", notes = "缺料明细导出", httpMethod = "GET")
    @GetMapping(value = "/web/export")
    public void queryShortPartExport(@RequestParam Map<String, String> map) {
        log.info("queryShortPartExport:{}", map);
        replenishmentService.queryShortPartExport(map);
    }
    /**
     * 缺料明细导出--下载中心调用
     * @param map 导出参数查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "Map<String, String>", name = "map", value = "导出参数查询", required = true)
    })
    @ApiOperation(value = "缺料明细导出--下载中心调用", notes = "缺料明细导出--下载中心调用", httpMethod = "GET")
    @GetMapping(value = "/download/shortpartExport")
    public List<Map> queryShortPart(@RequestParam Map<String, String> map) {
        log.info("queryShortPart:{}", map);
        return replenishmentService.queryShortPart(map);
    }
    @ApiOperation(value = "/v1/getUpdateMaterialShortageInfo", notes = "刷新按钮检查是否缺料", httpMethod = "GET")
    @RequestMapping(value = "/v1/getUpdateMaterialShortageInfo", method = RequestMethod.GET)
    public List<ShortPartResultDTO> checkReplenishmentNew(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo,@ApiParam(hidden = true) @RequestParam("orderFlag") Integer orderFlag){
        log.info("application-checkReplenishmentNew:{},{},{}", ownerCode, roNo, orderFlag);
        return replenishmentService.checkReplenishmentNew(ownerCode, roNo, orderFlag);
    }
    /**
     * 刷新按钮缺料明细生成及自动补货接口
     */
    @ApiOperation(value = "/v1/updateMaterialShortageInfo", notes = "刷新按钮缺料补货", httpMethod = "POST")
    @RequestMapping(value = "/v1/updateMaterialShortageInfo", method = RequestMethod.POST)
    public Long shortageReplenishmentNew(@RequestBody ShortPartReplenishmentDTO shortPartDto){
        return replenishmentService.shortageReplenishmentNew(shortPartDto);
    }
}
