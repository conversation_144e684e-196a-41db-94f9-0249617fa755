package com.volvo.maintain.interfaces.controller.workshop;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.BeginOrderDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.DeliveryDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.QueryParamDto;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.SignQuantityDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.StatusCountDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.WorkShopBalanceOrderDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.WorkShopRepairOrderDTO;
import com.volvo.maintain.application.maintainlead.service.workshop.LucencyWorkShopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/lucencyWorkShop/v1")
@Api(value = "透明车间列表查询", tags = {"透明车间列表查询"})
@Slf4j
@AllArgsConstructor
public class LucencyWorkShopController {


    @Autowired
    private LucencyWorkShopService lucencyWorkshopService;


    /**
     * 开单页面查询
     * @param queryParamDto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryParamDto", name = "queryParamDto", value = "", required = true)
    })
    @ApiOperation(value = "开单页面查询", notes = "开单页面查询", httpMethod = "POST")
    @PostMapping(value = "/selectBeginOrderList")
    @ResponseBody
    public IPage<BeginOrderDTO> selectBeginOrderList(@RequestBody QueryParamDto queryParamDto) {
        return lucencyWorkshopService.selectBeginOrderList(queryParamDto);

    }

    /**
     * 查询派工数量
     * @param queryParamDto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryParamDto", name = "queryParamDto", value = "", required = true)
    })
    @ApiOperation(value = "查询派工数量", notes = "查询派工数量", httpMethod = "POST")
    @PostMapping(value = "/selectAssignStatusCount")
    @ResponseBody
    public List<StatusCountDTO> selectAssignStatusCount(@RequestBody QueryParamDto queryParamDto){
        return lucencyWorkshopService.selectAssignStatusCount(queryParamDto);
    }

    /**
     * 查询维修页面
     * @param queryParamDto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryParamDto", name = "queryParamDto", value = "", required = true)
    })
    @ApiOperation(value = "查询维修页面", notes = "查询维修页面", httpMethod = "POST")
    @PostMapping(value = "/selectWorkshopRepairOrder")
    @ResponseBody
    public IPage<WorkShopRepairOrderDTO> selectWorkshopRepairOrder(@RequestBody QueryParamDto queryParamDto){
        return lucencyWorkshopService.selectWorkshopRepairOrder(queryParamDto);
    }

    /**
     * 查询打卡数量
     * @param queryParamDto
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryParamDto", name = "queryParamDto", value = "", required = true)
    })
    @ApiOperation(value = "查询打卡数量", notes = "查询打卡数量", httpMethod = "POST")
    @PostMapping(value = "/selectIsPunchCount")
    @ResponseBody
    public List<StatusCountDTO> selectIsPunchCount(@RequestBody QueryParamDto queryParamDto){
        return lucencyWorkshopService.selectIsPunchCount(queryParamDto);
    }

    /**
     * 查询结算页面
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryParamDto", name = "queryParamDto", value = "", required = true)
    })
    @ApiOperation(value = "查询结算页面", notes = "查询结算页面", httpMethod = "POST")
    @PostMapping(value = "/selectWorkShopBalanceOrder")
    @ResponseBody
    public IPage<WorkShopBalanceOrderDTO> selectWorkShopBalanceOrder(@RequestBody QueryParamDto queryParamDto){
        return lucencyWorkshopService.selectWorkShopBalanceOrder(queryParamDto);
    }

    /**
     * 查询交车数量
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryParamDto", name = "queryParamDto", value = "", required = true)
    })
    @ApiOperation(value = "查询交车数量", notes = "查询交车数量", httpMethod = "POST")
    @PostMapping(value = "/selectDeliveryTagCount")
    @ResponseBody
    public List<StatusCountDTO> selectDeliveryTagCount(@RequestBody QueryParamDto queryParamDto){
        return lucencyWorkshopService.selectDeliveryTagCount(queryParamDto);
    }


    /**
     * 看版交车列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryParamDto", name = "queryParamDto", value = "", required = true)
    })
    @ApiOperation(value = "看版交车列表", notes = "看版交车列表", httpMethod = "POST")
    @PostMapping(value = "/selectDeliveryList")
    @ResponseBody
    public IPage<DeliveryDTO> selectDeliveryList(@RequestBody QueryParamDto queryParamDto){
        return lucencyWorkshopService.selectDeliveryList(queryParamDto);
    }


    /**
     * 企微菜单上的角标数量查询
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "beginDate", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "endDate", value = "")
    })
    @ApiOperation(value = "企微菜单上的角标数量查询", notes = "企微菜单上的角标数量查询", httpMethod = "GET")
    @GetMapping(value = "/signQuantity")
    public List<SignQuantityDTO> signQuantity(@RequestParam(value = "ownerCode",required = false)String ownerCode,
                                              @RequestParam(value = "beginDate",required = false)String beginDate,
                                              @RequestParam(value = "endDate",required = false)String endDate){
        return lucencyWorkshopService.signQuantity(ownerCode, beginDate, endDate);

    }



}
