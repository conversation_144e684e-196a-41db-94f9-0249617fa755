package com.volvo.maintain.interfaces.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsConfigDto;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsConfigLogDto;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsTransparencyDto;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsTransparencyQueryDto;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceAssigPersonImportDTO;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceRecordImportDTO;
import com.volvo.maintain.application.maintainlead.service.clues.RenewalOfInsuranceService;
import com.volvo.maintain.infrastructure.gateway.response.ImportTempResult;
import com.volvo.maintain.infrastructure.util.DateUtil;
import com.volvo.maintain.interfaces.vo.clues.InsuranceLeadsConfigVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import static com.volvo.maintain.infrastructure.util.DateUtil.SIMPLE_DATE_FORMAT;

@RestController
@RequestMapping("/renewalOfInsurance")
@Api(value = "续保线索adapter层", tags = {"续保线索adapter层"})
public class RenewalOfInsuranceController {

    private final Logger logger = LoggerFactory.getLogger(AccidentCluesController.class);
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final RenewalOfInsuranceService renewalOfInsuranceService;

    public RenewalOfInsuranceController(RenewalOfInsuranceService renewalOfInsuranceService) {
        this.renewalOfInsuranceService = renewalOfInsuranceService;
    }

    /**
     * 初始化经销商配置（单店保护，）
     * @param ownerCode 经销商
     * @return 返回字符串 成功失败 文字提示
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "经销商"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "allocationType", value = "")
    })
    @ApiOperation(value = "初始化经销商配置（单店保护，）", notes = "初始化经销商配置（单店保护，）", httpMethod = "POST")
    @PostMapping("/config/init")
    public String init(@RequestParam(value = "ownerCode", required = false) String ownerCode, @RequestParam(value = "allocationType", required = false) String allocationType) {
        String currentTime = LocalDateTime.now().format(formatter);
        logger.info("Request Time: {} - Performing an action for ownerCode init :{}", currentTime, ownerCode);
        return renewalOfInsuranceService.initConfig(ownerCode, allocationType);
    }


    /**
     *  根据参数查询配置信息
     * @param afterBigAreaId 大区Id
     * @param afterSmallAreaId 小区Id
     * @param ownerCode 经销商
     * @return 根据参数查询配置信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "afterBigAreaId", value = "大区Id"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "afterSmallAreaId", value = "小区Id"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "经销商"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageNum", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "")
    })
    @ApiOperation(value = "根据参数查询配置信息", notes = "根据参数查询配置信息", httpMethod = "GET")
    @GetMapping("/config/list")
    public Page<InsuranceLeadsConfigDto> queryCompanyConfig(@RequestParam(value = "afterBigAreaId", required = false) String afterBigAreaId,
                                                            @RequestParam(value = "afterSmallAreaId", required = false) String afterSmallAreaId,
                                                            @RequestParam(value = "ownerCode", required = false) String ownerCode,
                                                            @RequestParam(value = "pageNum", required = false) Integer pageNum,
                                                            @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        return renewalOfInsuranceService.queryCompanyConfig(afterBigAreaId, afterSmallAreaId, ownerCode, pageNum, pageSize);
    }


    /**
     * 根据对应参数进行修改数据
     * @param InsuranceLeadsConfigVo 经销商信息
     * @return 返回修改的数据集合
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InsuranceLeadsConfigVo", name = "InsuranceLeadsConfigVo", value = "经销商信息", required = true)
    })
    @ApiOperation(value = "根据对应参数进行修改数据", notes = "根据对应参数进行修改数据", httpMethod = "POST")
    @PostMapping("/config/update")
    public List<InsuranceLeadsConfigDto> modifyOwnerCodeConfig(@RequestBody InsuranceLeadsConfigVo InsuranceLeadsConfigVo) {
        return renewalOfInsuranceService.modifyOwnerCodeConfig(InsuranceLeadsConfigVo);
    }


    /**
     * 导入经销商配置文件
     * @param importFile excel文件
     * @return 解析数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "excel文件", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "boolean", name = "tab", value = "")
    })
    @ApiOperation(value = "导入经销商配置文件", notes = "导入经销商配置文件", httpMethod = "POST")
    @PostMapping(value = "/config/import")
    @ResponseBody
    public ImportTempResult<InsuranceLeadsConfigDto> importDealerConfigInfo(@RequestParam(value = "file") MultipartFile importFile,
                                                                            @RequestParam(value = "tab", required = false) Boolean tab) {
        //校验文件
        if (importFile.isEmpty()) {
            throw new ServiceBizException("importFile is empty");
        }
        return renewalOfInsuranceService.importDealerConfigInfo(importFile, tab);
    }

    /**
     * 导出经销商规则配置数据
     * @param dto 导出参数查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InsuranceLeadsConfigDto", name = "dto", value = "导出参数查询", required = true)
    })
    @ApiOperation(value = "导出经销商规则配置数据", notes = "导出经销商规则配置数据", httpMethod = "POST")
    @PostMapping(value = "/config/export")
    public void partSupplyResultsExport(@RequestBody InsuranceLeadsConfigDto dto) {
        renewalOfInsuranceService.dataResultExport(dto);
    }
    /**
     * 店端设置提前出单日期
     * @param advanceDays 提前出单天数
     * @return 返回修改的数据集合
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "advanceDays", value = "提前出单天数", required = true)
    })
    @ApiOperation(value = "店端设置提前出单日期", notes = "店端设置提前出单日期", httpMethod = "POST")
    @PostMapping("/config/advance/update")
    public List<InsuranceLeadsConfigDto> modifyOwnerCodeConfigDate(@RequestParam("advanceDays") Integer advanceDays) {
        return renewalOfInsuranceService.modifyOwnerCodeConfigDate(advanceDays);
    }
    /**
     *  查询经销商规则提前出单日期修改记录
     * @param ownerCode 经销商
     * @return 根据参数查询配置信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "经销商", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "flag", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageNum", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询经销商规则提前出单日期修改记录", notes = "查询经销商规则提前出单日期修改记录", httpMethod = "GET")
    @GetMapping("/config/queryInsuranceLeadsModifyLog")
    public Page<InsuranceLeadsConfigLogDto> queryInsuranceLeadsModifyLog(@RequestParam(value = "ownerCode") String ownerCode,
                                                                         @RequestParam("flag") Integer flag,
                                                                         @RequestParam(value = "pageNum") Integer pageNum,
                                                                         @RequestParam(value = "pageSize") Integer pageSize) {
        return renewalOfInsuranceService.queryInsuranceLeadsModifyLog(ownerCode, flag, pageNum, pageSize);
    }

    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/download/data")
    @ResponseBody
    public List<InsuranceLeadsConfigDto> queryInsuranceLeadsExport(InsuranceLeadsConfigVo vo) {
        logger.info("queryInsuranceLeadsExport:{}", JSON.toJSONString(vo));
        if (ObjectUtils.isEmpty(vo) || null == vo.getCurrentPage() || null == vo.getPageSize()) {
            throw new ServiceBizException("下载中心回调经销商规则查询失败！");
        }
        return renewalOfInsuranceService.queryInsuranceLeadsExport(vo);
    }
    /**
     *店端-保险跟进-导入线索
     * @param importFile excel文件
     * @return 解析数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "excel文件", required = true)
    })
    @ApiOperation(value = "店端-保险跟进-导入线索", notes = "店端-保险跟进-导入线索", httpMethod = "POST")
    @PostMapping(value = "/renewalOfInsurance/import")
    @ResponseBody
    public void importRenewalOfInsurance(@RequestParam(value = "file") MultipartFile importFile) {
        //校验文件
        if (importFile.isEmpty()) {
            throw new ServiceBizException("importFile is empty");
        }
        renewalOfInsuranceService.importRenewalOfInsurance(importFile);
    }
    /**
     * 店端-保险跟进-查询导入到临时表的数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "flag", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageNum", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "店端-保险跟进-查询导入到临时表的数据", notes = "店端-保险跟进-查询导入到临时表的数据", httpMethod = "GET")
    @GetMapping("/renewalOfInsurance/importQuery")
    public Page<InsuranceRecordImportDTO> importQuery(@RequestParam(value = "flag") Integer flag,
                                                          @RequestParam(value = "pageNum") Integer pageNum,
                                                          @RequestParam(value = "pageSize") Integer pageSize) {
        return renewalOfInsuranceService.importQuery(flag, pageNum, pageSize);
    }
    /**
     * 续保线索-初始化经销商配置
     */
    @ApiOperation(value = "续保线索-初始化经销商配置", notes = "续保线索-初始化经销商配置", httpMethod = "POST")
    @PostMapping("/config/initJob")
    public String initConfigJob() {
        return renewalOfInsuranceService.initConfigJob();
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "adviseInDateStr", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/closeRenewalLead")
    public void closeRenewalLead(@RequestParam(value = "adviseInDateStr") String adviseInDateStr){
        Date adviseInDate = null;
        if(StringUtils.isNotEmpty(adviseInDateStr)){
            adviseInDate = DateUtil.parseDate(adviseInDateStr,SIMPLE_DATE_FORMAT);
        }
        renewalOfInsuranceService.closeRenewalLead(adviseInDate);
    }

    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/assignClue")
    public void assignClue(){
        renewalOfInsuranceService.assignClue();
    }
    /**
     *  店端-保险跟进-执行线索导入
     * @return 解析数据
     */
    @ApiOperation(value = "店端-保险跟进-执行线索导入", notes = "店端-保险跟进-执行线索导入", httpMethod = "POST")
    @PostMapping("/renewalOfInsurance/executeImport")
    void executeImportRenewalOfInsurance(){
        renewalOfInsuranceService.executeImportRenewalOfInsurance();
    }
    /**
     *店端-续保任务分配-导入跟进人员
     * @param importFile excel文件
     * @return 解析数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "excel文件", required = true)
    })
    @ApiOperation(value = "店端-续保任务分配-导入跟进人员", notes = "店端-续保任务分配-导入跟进人员", httpMethod = "POST")
    @PostMapping(value = "/importAssigPerson")
    public void importAssigPerson(@RequestParam(value = "file") MultipartFile importFile) {
        //校验文件
        if (importFile.isEmpty()) {
            throw new ServiceBizException("importFile is empty");
        }
        renewalOfInsuranceService.importAssigPerson(importFile);
    }
    /**
     * 店端-续保分配-查询导入分配人到临时表的数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "flag", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageNum", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "店端-续保分配-查询导入分配人到临时表的数据", notes = "店端-续保分配-查询导入分配人到临时表的数据", httpMethod = "GET")
    @GetMapping("/queryImportAssigPerson")
    public Page<InsuranceAssigPersonImportDTO> queryImportAssigPerson(@RequestParam(value = "flag", required = false) Integer flag,
                                                                      @RequestParam(value = "pageNum") Integer pageNum,
                                                                      @RequestParam(value = "pageSize") Integer pageSize) {
        return renewalOfInsuranceService.queryImportAssigPerson(flag, pageNum, pageSize);
    }
    /**
     *  店端-续保分配-分配人导入执行
     * @return 解析数据
     */
    @ApiOperation(value = "店端-续保分配-分配人导入执行", notes = "店端-续保分配-分配人导入执行", httpMethod = "POST")
    @PostMapping("/executeImportAssigPerson")
    void executeImportAssigPerson(){
        renewalOfInsuranceService.executeImportAssigPerson();
    }

    /**
     * 续保线索跟进透明查询接口
     */
    @PostMapping("/v1/queryTransparency")
    public Page<InsuranceLeadsTransparencyDto> queryTransparency(@RequestBody InsuranceLeadsTransparencyQueryDto queryDto) {
        return renewalOfInsuranceService.queryTransparency(queryDto);
    }

    /**
     * 续保线索跟进透明导出接口
     */
    @PostMapping("/v1/exportTransparency")
    public void exportTransparency(@RequestBody InsuranceLeadsTransparencyQueryDto queryDto) {
        renewalOfInsuranceService.exportTransparency(queryDto);
    }

    /**
     * 功能描述：续保线索跟进透明导出回调
     *
     * @return IPage<FullLeadVo> 全量线索列表
     */
    @GetMapping("/v1/exportCallback")
    @ApiOperation(value = "续保线索跟进列表导出回调")
    public List<InsuranceLeadsTransparencyDto> exportCallback(
            @RequestParam(value = "afterBigareaId", required = false) String afterBigareaId,
            @RequestParam(value = "afterSmallareaId", required = false) String  afterSmallareaId,
            @RequestParam(value = "ownerCode", required = false) List<String>  ownerCode,
            @RequestParam(value = "vin", required = false) String vin,
            @RequestParam(value = "licensePlate", required = false) String licensePlate,
            @RequestParam(value = "insuranceExpiryDateStart", required = false) String insuranceExpiryDateStart,
            @RequestParam(value = "insuranceExpiryDateEnd", required = false) String insuranceExpiryDateEnd,
            @RequestParam(value = "followUpTimeStart", required = false) String followUpTimeStart,
            @RequestParam(value = "followUpTimeEnd", required = false) String followUpTimeEnd,
            @RequestParam(value = "renewCustomerType", required = false) String renewCustomerType,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "currentPage", required = false) Long currentPage,
            @RequestParam(value = "pageSize", required = false) Long pageSize
    ) {
        InsuranceLeadsTransparencyQueryDto queryParams = new InsuranceLeadsTransparencyQueryDto();
        queryParams.setAfterBigareaId(afterBigareaId)
                .setAfterSmallareaId(afterSmallareaId)
                .setOwnerCode(ownerCode)
                .setVin(vin)
                .setLicensePlate(licensePlate)
                .setInsuranceExpiryDateStart(insuranceExpiryDateStart)
                .setInsuranceExpiryDateEnd(insuranceExpiryDateEnd)
                .setFollowUpTimeStart(followUpTimeStart)
                .setFollowUpTimeEnd(followUpTimeEnd)
                .setRenewCustomerType(renewCustomerType)
                .setType(type)
                .setCurrentPage(currentPage)
                .setPageSize(pageSize);
        logger.info("exportCallback enter args:{}", JSON.toJSONString(queryParams));
        return renewalOfInsuranceService.exportCallback(queryParams);
    }
}
