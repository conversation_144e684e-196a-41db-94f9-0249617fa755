package com.volvo.maintain.interfaces.controller;

import com.volvo.maintain.application.maintainlead.service.BalanceAccountsPrintService;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@Api(tags = "结算单导出Controller")
@RestController
@RequestMapping("/BalanceAccountsPrint")
@Slf4j
public class BalanceAccountsPrintController {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Autowired
    private BalanceAccountsPrintService balanceAccountsPrintService;

    /**
     * 结算单导出
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "Map<String, String>", name = "queryParams", value = "", required = true)
    })
    @ApiOperation(value = "结算单导出", notes = "结算单导出", httpMethod = "GET")
    @RequestMapping(value = "/exportFile",method = RequestMethod.GET)
    @ResponseBody
    public void exportFile(@RequestParam Map<String,String> queryParams){
        log.info("BalanceAccountsPrint exportFile:{}",queryParams);
        balanceAccountsPrintService.exportFile(queryParams);
    }



}
