package com.volvo.maintain.interfaces.controller.repairAssign;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderReqDTO;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderReqV2DTO;
import com.volvo.maintain.application.maintainlead.dto.repairAssign.TtRoAssignDTO;
import com.volvo.maintain.application.maintainlead.service.repairAssign.RepairAssignV2Service;
import com.volvo.maintain.application.maintainlead.vo.FinalInspectionContentVO;
import com.volvo.maintain.application.maintainlead.vo.FinalInspectionTreeVO;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderV2VO;
import com.volvo.maintain.application.maintainlead.vo.RoHandRepairProjectVO;
import com.volvo.maintain.interfaces.vo.CheckFinalInspectionReqVO;
import com.volvo.maintain.interfaces.vo.CheckFinalInspectionRespVO;
import com.volvo.maintain.interfaces.vo.FinalInspectionAttVO;
import com.yonyou.cyx.framework.dto.ResponseDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Api("/repairAssign/v2")
@Controller
@RequestMapping("/repairAssign/v2")
public class RepairAssignV2Controller {

    @Resource
    private RepairAssignV2Service repairAssignV2Service;

    /**
     * 查询工单组根据工单
     *
     */
    @ApiOperation(value = "查询工单组根据工单信息", notes = "查询工单组根据工单", httpMethod = "POST")
    @PostMapping("/queryRepairOrderList")
    @ResponseBody
    public ResponseDTO<List<RepairOrderV2VO>> queryRepairOrders(@RequestBody RepairOrderReqV2DTO repairOrderReq){
    	log.info("RepairAssignV2Controller queryRepairOrders : {}", JSON.toJSONString(repairOrderReq));
        return new ResponseDTO().successData(repairAssignV2Service.queryRepairOrders(repairOrderReq));
    }
    
    
    /**
     * 根据经销商+工单号批量查询工单信息
     *
     */
    @ApiOperation(value = "根据经销商+工单号批量查询工单信息", notes = "根据经销商+工单号批量查询工单信息", httpMethod = "POST")
    @PostMapping("/qualityInspection")
    @ResponseBody
    public List<RoHandRepairProjectVO> qualityInspection(@RequestBody List<RepairOrderReqDTO> repairOrderListReq){
    	log.info("RepairAssignV2Controller qualityInspection : {}", JSON.toJSONString(repairOrderListReq));
        return repairAssignV2Service.qualityInspection(repairOrderListReq);
    }
    
    
    /**
     * 根据维修类型 筛选出 维修部位&维修现象下拉选
     *
     */
    @ApiOperation(value = "根据维修类型 筛选出 维修部位&维修现象下拉选", notes = "根据维修类型 筛选出 维修部位&维修现象下拉选", httpMethod = "GET")
    @GetMapping("/queryRepairAnalysisByType")
    @ResponseBody
    public FinalInspectionTreeVO queryRepairAnalysisByType(@RequestParam("repairTypeCode") String repairTypeCode){
    	log.info("RepairAssignV2Controller queryRepairAnalysisByType : {}", repairTypeCode);
        return repairAssignV2Service.queryRepairAnalysisByType(repairTypeCode);
    }
    
    
    /**
     * 新增根据交修项目维修类型集合反查终检内容清单
     *
     */
    @ApiOperation(value = "新增根据交修项目维修类型集合反查终检内容清单", notes = "新增根据交修项目维修类型集合反查终检内容清单", httpMethod = "POST")
    @PostMapping("/queryFinalInspectionContentByType")
    @ResponseBody
    public List<FinalInspectionContentVO> queryFinalInspectionContentByType(@RequestBody List<String> repairTypeCodeList){
    	log.info("RepairAssignV2Controller queryFinalInspectionContentByType : {}", JSON.toJSONString(repairTypeCodeList));
        return repairAssignV2Service.queryFinalInspectionContentByType(repairTypeCodeList);
    }
    
    
    /**
     * 竣工,整單派工
     */
    @ApiOperation(value = "竣工,整單派工", notes = "竣工,整單派工", httpMethod = "PUT")
    @PutMapping(value = "/maintainRepair")
    @ResponseBody
    public void maintainRepairAssignComplete(@RequestBody List<TtRoAssignDTO> dtoList) {
    	repairAssignV2Service.maintainRepairAssignCompleteV2(dtoList);
    }
    
    /**
     * 维修项目校验
     *
     */
    @ApiOperation(value = "维修项目校验", notes = "维修项目校验", httpMethod = "POST")
    @PostMapping("/checkFinalInspection")
    @ResponseBody
    public List<CheckFinalInspectionRespVO> checkFinalInspection(@RequestBody List<CheckFinalInspectionReqVO> checkFinalInspectionReqList){
    	log.info("RepairAssignV2Controller checkFinalInspection : {}", JSON.toJSONString(checkFinalInspectionReqList));
        return repairAssignV2Service.checkFinalInspection(checkFinalInspectionReqList);
    }
    
    /**
     * 查询附加项目 根据 经销商+工单号
     *
     */
    @ApiOperation(value = "查询附加项目 根据 经销商+工单号", notes = "查询附加项目 根据 经销商+工单号", httpMethod = "GET")
    @GetMapping("/queryFinalInspectionAttByRoNo")
    @ResponseBody
    public List<FinalInspectionAttVO> queryFinalInspectionAttByRoNo(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo){
    	log.info("RepairAssignV2Controller queryFinalInspectionAttByRoNo ownerCode : {} , roNo : {}", ownerCode, roNo);
        return repairAssignV2Service.queryFinalInspectionAttByRoNo(ownerCode, roNo);
    }
    
}
