package com.volvo.maintain.interfaces.controller.whitelistManagement;

import com.volvo.maintain.application.maintainlead.dto.white.CheckWhiteListDto;
import com.volvo.maintain.application.maintainlead.service.whitelistManagement.WhitelistCheckService;
import com.volvo.maintain.interfaces.vo.white.CheckWhiteListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "白名单检查")
@RestController
@RequestMapping("/check")
public class WhitelistCheckController {

    @Autowired
    private WhitelistCheckService whitelistCheckService;


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "CheckWhiteListVo", name = "checkWhiteListVo", value = "", required = true)
    })
    @ApiOperation(value = "检查经销商白名单", notes = "检查经销商白名单 ", httpMethod = "POST")
    @PostMapping (path = "/whitelist/interf")
    public List<CheckWhiteListDto> checkWhitelist(@RequestBody CheckWhiteListVo checkWhiteListVo) {
        return whitelistCheckService.checkWhitelist(checkWhiteListVo);
    }

}
