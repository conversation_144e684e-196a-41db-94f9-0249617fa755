package com.volvo.maintain.interfaces.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.volvo.maintain.application.maintainlead.dto.ContractPurchaseGiveDto;
import com.volvo.maintain.application.maintainlead.dto.ExtWarPurgiveDataVO;
import com.volvo.maintain.application.maintainlead.dto.rights.GiveRecordRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.GiveRecordResponseDto;
import com.volvo.maintain.application.maintainlead.dto.rights.GiveStatusSyncDto;
import com.volvo.maintain.application.maintainlead.dto.rights.OrderActivationDetailsRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.OrderActivationDetailsResponseDto;
import com.volvo.maintain.application.maintainlead.dto.rights.PurchaseEligibilityCheckRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.PurchaseEligibilityCheckResponseDto;
import com.volvo.maintain.application.maintainlead.dto.rights.RightsProductDto;
import com.volvo.maintain.application.maintainlead.dto.rights.UsageDetailsRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.UsageDetailsResponseDto;
import com.volvo.maintain.application.maintainlead.service.strategy.RightsStrategyChoose;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/product/v1/")
@Api(value = "权益商品控制层", tags = {"权益商品控制层"})
public class RightsProductController {

    @Autowired
    private RightsStrategyChoose rightsStrategyChoose;
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "productType", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "productNo", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "productName", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageNum", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/mall-items")
    public IPage<RightsProductDto> productList(@RequestParam Integer productType,
                                               @RequestParam(required = false) String productNo,
                                               @RequestParam(required = false) String productName,
                                               @RequestParam int pageNum,
                                               @RequestParam int pageSize){
        log.info("RightsProductController productList");
        RightsProductDto rightsProductDto = new RightsProductDto();
        rightsProductDto.setProductType(productType);
        rightsProductDto.setProductName(productName);
        rightsProductDto.setProductNo(productNo);
        rightsProductDto.setPageNum(pageNum);
        rightsProductDto.setPageSize(pageSize);
        return rightsStrategyChoose.productList(rightsProductDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<GiveStatusSyncDto>", name = "statusSyncDtos", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping("/sync-give-status")
    public List<GiveStatusSyncDto> giveSync(@RequestBody List<GiveStatusSyncDto> statusSyncDtos){
        log.info("RightsProductController giveSync");
        return rightsStrategyChoose.giveSync(statusSyncDtos);
    }

    /**
     * C端购买上架产品保存接口(新增 通用接口支持延保/保养套餐)
     * @param contractPurchaseGiveDto 购买入参
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ContractPurchaseGiveDto", name = "contractPurchaseGiveDto", value = "购买入参", required = true)
    })
    @ApiOperation(value = "C端购买上架产品保存接口(新增 通用接口支持延保/保养套餐)", notes = "C端购买上架产品保存接口(新增 通用接口支持延保/保养套餐)", httpMethod = "POST")
    @PostMapping("/give")
    public void give(@RequestBody ContractPurchaseGiveDto contractPurchaseGiveDto){
        log.info("RightsProductController give");
        rightsStrategyChoose.give(contractPurchaseGiveDto);
    }

    /**
     * C端购买上架产品限制查询接口(新增 通用接口支持延保/保养套餐)
     * @param purchaseEligibilityCheckRequestDto 校验购买限制入参
     * @return 根据入参返回结果
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "PurchaseEligibilityCheckRequestDto", name = "purchaseEligibilityCheckRequestDto", value = "校验购买限制入参", required = true)
    })
    @ApiOperation(value = "C端购买上架产品限制查询接口(新增 通用接口支持延保/保养套餐)", notes = "C端购买上架产品限制查询接口(新增 通用接口支持延保/保养套餐)", httpMethod = "POST")
    @PostMapping("/buy-valid")
    public List<PurchaseEligibilityCheckResponseDto> buyValid(@RequestBody PurchaseEligibilityCheckRequestDto purchaseEligibilityCheckRequestDto){
        log.info("RightsProductController buyValid");
        if (ObjectUtils.isEmpty(purchaseEligibilityCheckRequestDto)) {
            return Lists.newArrayList();
        }
        return rightsStrategyChoose.buyValid(purchaseEligibilityCheckRequestDto);
    }

    /**
     * 提供C端退款查询激活明细接口(退款用)
     * @param orderActivationDetailsRequestDto 查询激活明细入参
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "OrderActivationDetailsRequestDto", name = "orderActivationDetailsRequestDto", value = "查询激活明细入参", required = true)
    })
    @ApiOperation(value = "提供C端退款查询激活明细接口(退款用)", notes = "提供C端退款查询激活明细接口(退款用)", httpMethod = "POST")
    @PostMapping("/give-act-list")
    public List<OrderActivationDetailsResponseDto> giveActList(@RequestBody OrderActivationDetailsRequestDto orderActivationDetailsRequestDto){
        return rightsStrategyChoose.giveActList(orderActivationDetailsRequestDto);
    }

    /**
     * 保养套餐使用明细
     * @param usageDetailsDto 保养套餐使用明细入参
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "UsageDetailsRequestDto", name = "usageDetailsDto", value = "保养套餐使用明细入参", required = true)
    })
    @ApiOperation(value = "保养套餐使用明细", notes = "保养套餐使用明细", httpMethod = "POST")
    @PostMapping("/use-list")
    public List<UsageDetailsResponseDto> useList(@RequestBody UsageDetailsRequestDto usageDetailsDto){
        return rightsStrategyChoose.useList(usageDetailsDto);
    }

    /**
     * 保养套餐购买记录
     * @param giveRecordRequestDto 保养套餐购买记录入参
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "GiveRecordRequestDto", name = "giveRecordRequestDto", value = "保养套餐购买记录入参", required = true)
    })
    @ApiOperation(value = "保养套餐购买记录", notes = "保养套餐购买记录", httpMethod = "POST")
    @PostMapping("/give-list")
    public IPage<GiveRecordResponseDto> givelist(@RequestBody GiveRecordRequestDto giveRecordRequestDto){
        return rightsStrategyChoose.givelist(giveRecordRequestDto);
    }
    
    
    @ApiOperation(value = "车主车辆页面延保信息", notes = "车主车辆页面延保信息 ", httpMethod = "GET")
    @GetMapping(value = "/queryGiveList")
    public List<ExtWarPurgiveDataVO> queryGiveList(@RequestParam("vin") String vin){
        return rightsStrategyChoose.queryGiveList(vin);
    }
}
