package com.volvo.maintain.interfaces.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CareBoughtQueryDTO;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CheckedCareBuyeDUseScopeVo;
import com.volvo.maintain.application.maintainlead.service.carebuyed.CareBuyedService;
import com.volvo.maintain.application.maintainlead.vo.CareBoughtVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/carebuyed")
@Api("保养活动")
@Slf4j
public class CareBuyedController {

    @Autowired
    private CareBuyedService careBuyedService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping(value = "/checkCareBuyedUseScope/{id}")
    public CheckedCareBuyeDUseScopeVo checkUseScope(@PathVariable Long id){
        return careBuyedService.checkUseScope(id);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "CareBoughtQueryDTO", name = "queryCareBoughtDTO", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping("/equity/resource")
    public Page<CareBoughtVO> queryEquityDetail(@RequestBody CareBoughtQueryDTO queryCareBoughtDTO){
        return careBuyedService.queryEquityDetail(queryCareBoughtDTO);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "CareBoughtQueryDTO", name = "queryCareBoughtDTO", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "POST")
    @PostMapping("/equity/export/excel")
    public void exportCareBoughtData(@RequestBody CareBoughtQueryDTO queryCareBoughtDTO){
        careBuyedService.exportCareBoughtData(queryCareBoughtDTO);
    }
}
