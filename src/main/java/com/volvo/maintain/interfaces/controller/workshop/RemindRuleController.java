package com.volvo.maintain.interfaces.controller.workshop;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.QueryCustomInfoDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.RemindRuleDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.RuleInfoDto;
import com.volvo.maintain.application.maintainlead.service.workshop.RemindRuleService;
import com.volvo.maintain.application.maintainlead.vo.workshop.CustomUserInfoVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.RemindRuleDetailVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.RemindRuleVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.SpecialVehicleConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 规则提醒接口
 * @Date 2024/11/14 13:33
 */
@Api(value = "/remindRule/v1", tags = {"规则提醒接口"})
@RestController
@RequestMapping("/remindRule/v1")
@Slf4j
@AllArgsConstructor
public class RemindRuleController {

    private final RemindRuleService remindRuleService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RemindRuleDto", name = "remindRuleDto", value = "", required = true)
    })
    @ApiOperation(value = "分页查询规则列表", notes = "", httpMethod = "POST")
    @PostMapping("/queryRulePage")
    public Page<RemindRuleVo> queryRulePage(@RequestBody @Valid RemindRuleDto remindRuleDto) {
        return remindRuleService.queryRulePage(remindRuleDto);
    }

    @ApiOperation(value = "查询规则列表", notes = "", httpMethod = "GET")
    @GetMapping("/queryRuleList")
    public List<RemindRuleVo> queryRuleList(RemindRuleDto remindRuleDto) {
       return remindRuleService.queryRuleList(remindRuleDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RemindRuleDto", name = "queryParams", value = "", required = true)
    })
    @ApiOperation(value = "规则列表导出", notes = "", httpMethod = "POST")
    @PostMapping("/exportRuleList")
    public void exportRuleList(@RequestBody RemindRuleDto queryParams) {
        remindRuleService.exportRuleList(queryParams);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "查询规则详情", notes = "", httpMethod = "GET")
    @GetMapping("/queryRuleDetail")
    public RemindRuleDetailVo queryRuleDetail(@RequestParam("id") Integer id) {
        return remindRuleService.queryRuleDetail(id);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryCustomInfoDto", name = "queryCustomInfoDto", value = "", required = true)
    })
    @ApiOperation(value = "分页查询自定义人员信息", notes = "", httpMethod = "POST")
    @PostMapping("/queryCustomUserInfoList")
    public Page<CustomUserInfoVo> queryCustomUserInfoList(@RequestBody QueryCustomInfoDto queryCustomInfoDto) {
        return remindRuleService.queryCustomUserInfoList(queryCustomInfoDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryCustomInfoDto", name = "queryCustomInfoDto", value = "", required = true)
    })
    @ApiOperation(value = "分页查询特殊车辆配置信息", notes = "", httpMethod = "POST")
    @PostMapping("/querySpecialVehicleConfigList")
    public Page<SpecialVehicleConfigVo> querySpecialVehicleConfigList(@RequestBody QueryCustomInfoDto queryCustomInfoDto) {
        return remindRuleService.querySpecialVehicleConfigList(queryCustomInfoDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "RuleInfoDto", name = "ruleInfoDto", value = "", required = true)
    })
    @ApiOperation(value = "保存规则", notes = "", httpMethod = "POST")
    @PostMapping("/saveRuleInfo")
    public void saveRuleInfo(@RequestBody @Valid RuleInfoDto ruleInfoDto) {
        remindRuleService.saveRuleInfo(ruleInfoDto);
    }

    @ApiOperation(value = "获取规则相关通用配置下拉", notes = "", httpMethod = "GET")
    @GetMapping("/queryRuleCommonConfig")
    public Map<String, List<CommonConfigDto>> queryCommonConfigList() {
        return remindRuleService.queryCommonConfigList();
    }
}
