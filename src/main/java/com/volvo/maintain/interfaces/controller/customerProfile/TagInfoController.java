package com.volvo.maintain.interfaces.controller.customerProfile;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.ChangeTagInfoDto;
import com.volvo.maintain.application.maintainlead.dto.FieldTranslationDto;
import com.volvo.maintain.application.maintainlead.dto.TagInfoDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.TagInfoService;
import com.volvo.maintain.interfaces.vo.CompleteTagInfoVo;
import com.volvo.maintain.interfaces.vo.FieldTranslationVo;
import com.volvo.maintain.interfaces.vo.TagInfoVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 功能描述：标签管理控制层
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@RestController
@RequestMapping("/tag")
@Api(value = "标签管理")
public class TagInfoController {
    @Autowired
    private TagInfoService tagInfoService;

    /**
     * 功能描述：查询标签信息列表
     *
     * @param tagInfoDTO 标签信息dto对象
     * @return Page<TagInfoVO> 标签信息列表
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "TagInfoDto", name = "tagInfoDTO", value = "标签信息dto对象", required = true)
    })
    @ApiOperation(value = "查询标签信息列表", notes = "功能描述：查询标签信息列表", httpMethod = "POST")
    @PostMapping("/queryTagInfo")
    public Page<TagInfoVo> queryTagInfo(@RequestBody TagInfoDto tagInfoDTO) {
        return tagInfoService.queryTagInfo(tagInfoDTO);
    }

    /**
     * 功能描述：查询标签详情
     *
     * @param id 标签id
     * @return TagInfoVO 标签信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "标签id", required = true)
    })
    @ApiOperation(value = "获取标签详情", notes = "功能描述：查询标签详情", httpMethod = "GET")
    @GetMapping(value = "/queryTagInfoById")
    public TagInfoVo queryTagInfoById(@RequestParam("id") Long id) {
        TagInfoVo tagInfoVO = tagInfoService.queryTagInfoById(id);
        return tagInfoVO;
    }

    /**
     * 功能描述：更新标签信息
     *
     * @param changeTagInfoDTO 标签入参
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ChangeTagInfoDto", name = "changeTagInfoDTO", value = "标签入参", required = true)
    })
    @ApiOperation(value = "更新标签信息", notes = "功能描述：更新标签信息", httpMethod = "POST")
    @PostMapping(value = "/updateTagInfo")
    public Integer updateTagInfo(@RequestBody ChangeTagInfoDto changeTagInfoDTO) {
       return tagInfoService.updateTagInfo(changeTagInfoDTO);
    }

    /**
     * 功能描述：同步CDP标签信息
     *
     */
    @ApiOperation(value = "同步CDP标签信息", notes = "功能描述：同步CDP标签信息", httpMethod = "POST")
    @PostMapping(value = "/syncCDPTagTree")
    public void syncCDPTagTree() {
        tagInfoService.syncCDPTagTree();
    }

    /**
     * 功能描述：获取CDP标签及本地配置
     *
     * @param vin 车架号
     * @param mobile 手机号
     * @return CompleteTagInfoVO 全量标签信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "车架号"),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "mobile", value = "手机号"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "memberId", value = "")
    })
    @ApiOperation(value = "获取CDP标签及本地配置", notes = "功能描述：获取CDP标签及本地配置", httpMethod = "GET")
    @GetMapping(value = "/queryCdpTagInfoAndConfigure")
    public List<CompleteTagInfoVo> queryCdpTagInfoAndConfigure(@RequestParam(value = "vin",required = false) String vin,
                                                               @RequestParam(value = "mobile",required = false) String mobile,
                                                               @RequestParam(value = "memberId",required = false) Integer memberId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(vin) && org.apache.commons.lang3.StringUtils.isEmpty(mobile)) {
            throw new ServiceBizException("车架号与手机号不可同时为null!!!");
        }
        List<CompleteTagInfoVo> completeTagInfoVO = tagInfoService.queryCdpTagInfoAndConfigure(vin,mobile,memberId);
        return completeTagInfoVO;
    }

    /**
     * 功能描述：获取字段转译详情
     *
     * @param id 主键id
     * @return FieldTranslationVO 字段转译信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "主键id", required = true)
    })
    @ApiOperation(value = "获取字段转译详情", notes = "功能描述：获取字段转译详情", httpMethod = "GET")
    @GetMapping(value = "/queryFieldTranslationDetail")
    public FieldTranslationVo queryFieldTranslationDetail(@RequestParam(value = "id") Long id) {
        FieldTranslationVo fieldTranslationVO = tagInfoService.queryFieldTranslationDetail(id);
        return fieldTranslationVO;
    }

    /**
     * 功能描述：查询字段下拉框
     *
     * @param tagInfoDTO dto对象
     * @return List<TagInfoVO> 字段下拉框
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "TagInfoDto", name = "tagInfoDTO", value = "dto对象", required = true)
    })
    @ApiOperation(value = "查询字段下拉框", notes = "功能描述：查询字段下拉框", httpMethod = "POST")
    @PostMapping("/queryFieldInfo")
    public List<TagInfoVo> queryFieldInfo(@RequestBody TagInfoDto tagInfoDTO) {
        return tagInfoService.queryFieldInfo(tagInfoDTO);
    }

    /**
     * 功能描述：添加/更新字段转译
     *
     * @param fieldTranslationDto 字段更新修改Dto
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "FieldTranslationDto", name = "fieldTranslationDto", value = "字段更新修改Dto", required = true)
    })
    @ApiOperation(value = "添加/更新字段转译", notes = "功能描述：添加/更新字段转译", httpMethod = "POST")
    @PostMapping("/updateFieldInfo")
    public void updateFieldInfo(@RequestBody FieldTranslationDto fieldTranslationDto) {
         tagInfoService.updateFieldInfo(fieldTranslationDto);
    }

    /**
     * 功能描述：获取字段转译列表信息
     *
     * @param showName 展示名称
     * @param currentPage 当前页
     * @param pageSize 每页条数
     * @return TagInfoVO 字段转译列表信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "showName", value = "展示名称"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "当前页", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "每页条数", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "tagType", value = "", required = true)
    })
    @ApiOperation(value = "获取字段转译标签管理", notes = "功能描述：获取字段转译列表信息", httpMethod = "GET")
    @GetMapping(value = "/queryFieldTranslation")
    public Page<TagInfoVo> queryFieldTranslation(@RequestParam(value = "showName",required = false) String showName,
                                                 @RequestParam(value = "currentPage") Integer currentPage,
                                                 @RequestParam(value = "pageSize") Integer pageSize,
                                                 @RequestParam(value = "tagType") String tagType) {
        return tagInfoService.queryFieldTranslation(showName,currentPage,pageSize, tagType);
    }

    /**
     * 功能描述：删除字段转译
     *
     * @param fieldTranslationDto 字段更新修改Dto
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "FieldTranslationDto", name = "fieldTranslationDto", value = "字段更新修改Dto", required = true)
    })
    @ApiOperation(value = "删除字段转译", notes = "功能描述：删除字段转译", httpMethod = "POST")
    @PostMapping("/deleteFieldInfo")
    public void deleteFieldInfo(@RequestBody FieldTranslationDto fieldTranslationDto) {
        if (StringUtils.isEmpty(fieldTranslationDto.getTagId())){
            throw new ServiceBizException("标签code不能为空！！！");
        }
        tagInfoService.deleteFieldInfo(fieldTranslationDto);
    }
}
