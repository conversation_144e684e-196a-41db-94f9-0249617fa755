package com.volvo.maintain.interfaces.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.CarPickupCarPhotosResDto;
import com.volvo.maintain.application.maintainlead.dto.CarPickupDeliveryBalanceResDto;
import com.volvo.maintain.application.maintainlead.dto.CarPickupDeliveryCheckBalanceResDto;
import com.volvo.maintain.application.maintainlead.dto.order.PriceDto;
import com.volvo.maintain.application.maintainlead.service.CarPickupDeliveryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述：上门取送车 工单相关
 */
@Api(value = "/carPickupDelivery/v1", tags = {"上门取送车 工单相关"})
@RestController
@RequestMapping("/carPickupDelivery/v1")
@Slf4j
public class CarPickupDeliveryController {
	
	@Resource
	private CarPickupDeliveryService carPickupDeliveryService;
	
	/**
	 * 确认下单前余额校验
	 * 
	 * @param priceDto
	 * @return
	 */
	@ApiOperation(value = "确认下单前余额校验", notes = "确认下单前余额校验", httpMethod = "POST")
	@PostMapping("/checkBalance")
	public CarPickupDeliveryCheckBalanceResDto checkBalance(@RequestBody PriceDto priceDto) {
		log.info("CarPickupDeliveryController-checkBalance ownerCode: {}", JSON.toJSONString(priceDto));
		return carPickupDeliveryService.checkBalance(priceDto);
	}
	
	/**
	 * 上门取送车余额查询
	 * @param ownerCode
	 * @return
	 */
	@ApiOperation(value = "上门取送车余额查询", notes = "上门取送车余额查询", httpMethod = "GET")
	@GetMapping("/queryBalance")
	public List<CarPickupDeliveryBalanceResDto> queryBalance(@RequestParam("ownerCode") String ownerCode) {
		log.info("CarPickupDeliveryController-queryBalance ownerCode: {}", ownerCode);
		return carPickupDeliveryService.queryBalance(ownerCode);
	}
	
	/**
	 * 查询验车图片
	 * @param orderId
	 * @return
	 */
	@ApiOperation(value = "查询验车图片", notes = "查询验车图片", httpMethod = "GET")
	@GetMapping("/getCarPhotos")
	public CarPickupCarPhotosResDto getCarPhotos(@RequestParam("orderId") String orderId) {
		log.info("CarPickupDeliveryController-queryBalance orderId: {}", orderId);
		return carPickupDeliveryService.getCarPhotos(orderId);
	}
}
