package com.volvo.maintain.interfaces.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.maintain.application.maintainlead.dto.claimapply.ClaimApplyUseDTO;
import com.volvo.maintain.application.maintainlead.dto.reminder.WarrantyMaintenanceReminderDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.*;
import com.volvo.maintain.application.maintainlead.service.warrantyApproval.WarrantyApprovalService;
import com.volvo.utils.LoginInfoUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 延保审批接口
 */
@Api(value = "/warranty", tags = {"延保审批接口"})
@Slf4j
@RestController
@RequestMapping("/warranty")
public class WarrantyApprovalController {

    @Resource
    private WarrantyApprovalService warrantyApprovalService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClaimApplyPageReqDTO", name = "claimApplyPageReqDTO", value = "", required = true)
    })
    @ApiOperation(value = "延保理赔申请列表查询", notes = "", httpMethod = "POST")
    @PostMapping("/approval/list")
    public Page<ClaimApplyPageRespDTO> approvalList(@RequestBody ClaimApplyPageReqDTO claimApplyPageReqDTO) {
        return warrantyApprovalService.approvalList(claimApplyPageReqDTO);
    }



    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClaimApplyPageReqDTO", name = "reqDTO", value = "", required = true)
    })
    @ApiOperation(value = "延保理赔申请列表查询导出", notes = "", httpMethod = "POST")
    @PostMapping("/approval/export")
    public void exportList(@RequestBody ClaimApplyPageReqDTO reqDTO) {
        warrantyApprovalService.exportList(reqDTO);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ClaimApplyApproveReqDTO", name = "reqDTO", value = "", required = true)
    })
    @ApiOperation(value = "延保理赔申请审批", notes = "", httpMethod = "POST")
    @PostMapping("/approval")
    public void approve(@RequestBody ClaimApplyApproveReqDTO reqDTO) {
        warrantyApprovalService.approve(reqDTO);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "long", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "审批id查询审批记录，易宝来源", notes = "", httpMethod = "GET")
    @GetMapping("/approval/record")
    public List<ClaimApplyApproveRecordRespDTO> approvalRecordList(
            @ApiParam(name = "id", required = true, value = "延保理赔申请id") @RequestParam("id") Long id
    ) {
        return warrantyApprovalService.approvalRecordList(id);
    }


    /**
     * 获取申请信息
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "caseNo", value = "", required = true)
    })
    @ApiOperation(value = "获取申请信息", notes = "获取申请信息", httpMethod = "GET")
    @GetMapping("/approval/detail")
    public ClaimApplyUseDTO getApprovalDetail(@RequestParam("caseNo") String caseNo) {
        return warrantyApprovalService.getApprovalDetail(caseNo);
    }
    /**
     * 受案份额金额修改
     */
    @ApiOperation("授权份额金额修改")
    @PostMapping("/approval/modifyApprovalAmount")
    public void modifyApprovalAmount(@RequestBody ClaimApplyUseDTO dto) {
        log.info("application modifyApprovalAmount");
        warrantyApprovalService.modifyApprovalAmount(dto);
    }
    @ApiOperation(value = "延保回款查询列表 - 店端")
    @PostMapping("/return/storeList")
    public Page<warrantyReturnPageDTO> storeList(@RequestBody warrantyReturnReqDTO dto) {
        // 获取登录用户信息
        CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();
        dto.setOwnerCode(Collections.singletonList(loginInfoDto.getOwnerCode())); // 店端编码
        return warrantyApprovalService.returnList(dto);
    }

    @ApiOperation("延保回款查询明细列表 - 店端")
    @GetMapping("/return/storeDetailList")
    public List<warrantyReturnDetailPageDTO> storeDetailList(@RequestParam(value = "returnId",required = false) Long returnId)  {
        return warrantyApprovalService.detailList(returnId);
    }

    @ApiOperation(value = "延保回款列表导出 - 店端")
    @PostMapping("/return/storeReportList")
    public List<warrantyReturnPageDTO> storeReportList(@RequestBody warrantyReturnReqDTO dto) {
        // 获取登录用户信息
        CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();
        dto.setOwnerCode(Collections.singletonList(loginInfoDto.getOwnerCode())); // 店端编码
        return warrantyApprovalService.reportList(dto);
    }

    @ApiOperation(value = "延保回款列表明细导出 - 店端")
    @GetMapping("/return/storeReportDetailList")
    public List<warrantyReturnDetailPageDTO> storeReportDetailList(@RequestParam(value = "returnIds",required = false) List<Long> returnIds)   {
        return warrantyApprovalService.reportDetailList(returnIds);
    }

    @ApiOperation(value = "延保回款查询列表 - 厂端")
    @PostMapping("/return/factoryList")
    public Page<warrantyReturnPageDTO> factoryList(@RequestBody warrantyReturnReqDTO dto) {
        return warrantyApprovalService.returnList(dto);
    }

    @ApiOperation("延保回款查询明细列表 - 厂端")
    @GetMapping("/return/factoryDetailList")
    public List<warrantyReturnDetailPageDTO> factoryDetailList(@RequestParam(value = "returnId",required = false) Long returnId)  {
        return warrantyApprovalService.detailList(returnId);
    }

    @ApiOperation(value = "延保回款列表导出 - 厂端")
    @PostMapping("/return/factoryReportList")
    public List<warrantyReturnPageDTO> factoryReportList(@RequestBody warrantyReturnReqDTO dto) {
        return warrantyApprovalService.reportList(dto);
    }

    @ApiOperation(value = "延保回款列表明细导出 - 厂端")
    @GetMapping("/return/factoryReportDetailList")
    public List<warrantyReturnDetailPageDTO> factoryReportDetailList(@RequestParam(value = "returnIds",required = false) List<Long> returnIds)   {
        return warrantyApprovalService.reportDetailList(returnIds);
    }
    @ApiOperation("延保消息提醒查询")
    @GetMapping("/warrantyMaintenance/getReminder")
    public WarrantyMaintenanceReminderDTO getReminder(@RequestParam(value = "vin") String vin, @RequestParam(value = "flag") boolean flag, @RequestParam(value = "roNo") String roNo) {
        return warrantyApprovalService.getReminder(vin, flag, roNo);
    }
}
