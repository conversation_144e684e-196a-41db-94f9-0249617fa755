package com.volvo.maintain.interfaces.controller.Em90Delivery;


import cn.hutool.core.exceptions.UtilException;
import com.alibaba.fastjson.JSONObject;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.maintain.application.maintainlead.dto.ReportReasonDto;
import com.volvo.maintain.application.maintainlead.service.Em90Delivery.Em90DeliveryService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.VipCustomService;
import com.volvo.maintain.interfaces.vo.VehicleEntranceVo;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@Api(tags = "em90交车相关")
@RestController
@RequestMapping("/em90Delivery")
@Slf4j
public class Em90DeliveryController {

    @Autowired
    private Em90DeliveryService em90DeliveryService;
    @Autowired
    VipCustomService vipCustomService;

    /**
     * 查询em90工单交车弹框 true弹框  false不弹框
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "查询em90工单交车弹框 true弹框  false不弹框", notes = "查询em90工单交车弹框 true弹框  false不弹框", httpMethod = "GET")
    @GetMapping(value = "/selectEm90DeliveryFrame")
    public boolean selectEm90DeliveryFrame(@RequestParam("ownerCode") String ownerCode,@RequestParam("roNo")String roNo,@RequestParam("vin")String vin){
        if (StringUtils.isEmpty(ownerCode) || StringUtils.isEmpty(roNo) || StringUtils.isEmpty(vin)){
            throw new ServiceBizException("经销商code,工单号,vin不能为空");
        }
        boolean isFrame;
        try {
            isFrame = em90DeliveryService.selectEm90DeliveryFrame(ownerCode, roNo, vin);
        }catch (Exception e){
           log.error("查询em90工单交车弹框异常",e);
           return false;
        }
        return isFrame;
    }

    /**
     * 提交未提交报告原因
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ReportReasonDto", name = "reportReasonDto", value = "", required = true)
    })
    @ApiOperation(value = "提交未提交报告原因", notes = "提交未提交报告原因", httpMethod = "POST")
    @PostMapping(value = "/submitReportReason")
    public void submitReportReason(@RequestBody ReportReasonDto reportReasonDto){
        if (Objects.isNull(reportReasonDto) || StringUtils.isEmpty(reportReasonDto.getReportReason())){
            throw new UtilException("必填字段为空");
        }
        em90DeliveryService.submitReportReason(reportReasonDto);
    }

    /**
     * 查询重点客户弹框
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "license", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = "")
    })
    @ApiOperation(value = "查询重点客户弹框", notes = "查询重点客户弹框", httpMethod = "GET")
    @GetMapping(value = "/queryEmphasisClientRemind")
    public List<String> queryEmphasisClientRemind(@RequestParam("ownerCode") String ownerCode,
                                                  @RequestParam("vin")String vin,
                                                  @RequestParam("license") String license,
                                                  @RequestParam(value = "roNo", required = false) String roNo){
        return em90DeliveryService.queryEmphasisClientRemind(ownerCode, vin,license, roNo);
    }


    /**
     * 环检接车发送邮件
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VehicleEntranceVo", name = "vehicleEntranceVO", value = "", required = true)
    })
    @ApiOperation(value = "环检接车发送邮件", notes = "环检接车发送邮件", httpMethod = "POST")
    @PostMapping("/em90TakeDeliverPrecheckEmail")
    public void em90TakeDeliverPrecheckEmail(@RequestBody VehicleEntranceVo vehicleEntranceVO) {
        if (Objects.isNull(vehicleEntranceVO)){
            log.info("em90TakeDeliverPrecheckEmail vehicleEntranceVO is null");
            return;
        }
        if (org.apache.commons.lang.StringUtils.isEmpty(vehicleEntranceVO.getVin())){
            log.info("em90TakeDeliverPrecheckEmail Vin is isEmpty");
            return;
        }
        // 获取登录用户信息
        CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();
        loginInfoDto.setAppId(null);
        String message = JSONObject.toJSONString(vehicleEntranceVO);
        vipCustomService.saveEntranceReceptionVehicle(message);
    }


}
