package com.volvo.maintain.interfaces.controller.customerProfile;

import com.volvo.exception.ServiceBizException;
import com.volvo.maintain.application.maintainlead.dto.CustomizedLabelDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CustomizedLabelService;
import com.volvo.maintain.interfaces.vo.CompleteTagInfoVo;
import com.volvo.maintain.interfaces.vo.NbTagInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/customizedLabel")
@Api(value = "标签查询")
public class CustomizedLabelController {
    @Autowired
    private CustomizedLabelService customizedLabelService;

    /**
     * NB定制化标签查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "CustomizedLabelDto", name = "customizedLabelDto", value = "", required = true)
    })
    @ApiOperation(value = "NB定制化标签查询", notes = "NB定制化标签查询", httpMethod = "POST")
    @PostMapping("/queryCustomizedLabel")
    public List<NbTagInfoVo> queryCustomizedLabel(@RequestBody CustomizedLabelDto customizedLabelDto) {

        if (CollectionUtils.isEmpty(customizedLabelDto.getTagCodes())){
            throw new ServiceBizException("NB定制化标签查询 标签列表不可为空");
        }

        if (StringUtils.isEmpty(customizedLabelDto.getVin())){
            throw new ServiceBizException("NB定制化标签查询 VIN不能为空");
        }

        return customizedLabelService.queryCustomizedLabel(customizedLabelDto);
    }

    /**
     * 清除预览配置
     */
    @ApiOperation(value = "清除预览配置", notes = "清除预览配置", httpMethod = "DELETE")
    @DeleteMapping("/previewConfiguration")
    public Integer clearPreviewConfiguration() {
       return customizedLabelService.clearPreviewConfiguration();
    }

    /**
     * 清除配置
     * @param  tagId String
     * @return Integer
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "tagId", value = "String", required = true)
    })
    @ApiOperation(value = "清除配置", notes = "清除配置", httpMethod = "PUT")
    @PutMapping("/configuration")
    public Integer clearConfiguration(@RequestParam("tagId") String tagId) {
        if (tagId == null) {
            throw new ServiceBizException("必传参数为null");
        }
        return customizedLabelService.clearConfiguration(tagId);
    }


    /**
     * 预览&预览保存
     */
    @ApiOperation(value = "预览&预览保存", notes = "预览&预览保存", httpMethod = "POST")
    @PostMapping("/previewTempData")
    public List<CompleteTagInfoVo> previewTempData() {
        return customizedLabelService.previewTempData();
    }

    /**
     * 预览&预览保存同步数据
     *
     * @return Integer
     */
    @ApiOperation(value = "预览&预览保存同步数据", notes = "预览&预览保存同步数据", httpMethod = "POST")
    @PostMapping("/previewOrSaveSynchronizeData")
    public Integer previewOrSaveSynchronizeData() {
        return customizedLabelService.previewOrSaveSynchronizeData();
    }

}
