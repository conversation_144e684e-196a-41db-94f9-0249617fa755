package com.volvo.maintain.interfaces.controller;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.CustomerInfoDto;
import com.volvo.maintain.application.maintainlead.dto.TmVehicleDto;
import com.volvo.maintain.application.maintainlead.dto.message.MessageSendDto;
import com.volvo.maintain.application.maintainlead.dto.message.PushMessageSendDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 功能描述：养修线索控制层
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@RestController
@RequestMapping("/commonMethod")
@Api(value = "公共方法adapter层", tags = {"公共方法adapter层"})
@Slf4j
public class CommonMethodController {

    @Autowired
    private CommonMethodService commonMethodService;

    /**
     * 功能描述：查询邀约线索列表
     *
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "查询车辆信息", notes = "功能描述：查询邀约线索列表", httpMethod = "POST")
    @PostMapping("/queryVehicle")
    public TmVehicleDto queryVehicle(@RequestParam("vin") String vin) {
        log.info("queryVehicle  vin:{}", vin);
        TmVehicleDto tmVehicleDto = commonMethodService.queryVehicle(vin);
        return tmVehicleDto;
    }

    /**
     * 功能描述：查询邀约线索列表
     *
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "MessageSendDto", name = "messageSendDto", value = "", required = true)
    })
    @ApiOperation(value = "查询车辆信息", notes = "功能描述：查询邀约线索列表", httpMethod = "POST")
    @PostMapping("/pushSms")
    public void pushSms(@RequestBody MessageSendDto messageSendDto) {
        log.info("queryVehicle  messageSendDto:{}", messageSendDto);
        Boolean aBoolean = commonMethodService.pushSms(messageSendDto);
    }

    /**
     * 功能描述：通用短信发送
     *
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "PushMessageSendDto", name = "pushMessageSendDto", value = "", required = true)
    })
    @ApiOperation(value = "通用短信发送", notes = "功能描述：通用短信发送", httpMethod = "POST")
    @PostMapping("/api/pushMessage")
    public Boolean pushMessage(@RequestBody PushMessageSendDto pushMessageSendDto) {
        log.info("pushMessage  pushMessage:{}", JSON.toJSONString(pushMessageSendDto));
        Boolean b = false;
        if (ObjectUtils.isEmpty(pushMessageSendDto)){
            throw new RuntimeException("短信发送信息不可为空");
        }
        if (ObjectUtils.isEmpty(pushMessageSendDto.getPushMessageRecord()) || ObjectUtils.isEmpty(pushMessageSendDto.getKey())){
            throw new RuntimeException("短信发送信息不可为空");
        }
        if (ObjectUtils.isEmpty(pushMessageSendDto.getMessageSendDto()) && ObjectUtils.isEmpty(pushMessageSendDto.getEmailInfoDto())){
            throw new RuntimeException("短信或邮件发送信息不可为空");
        }
        if (ObjectUtils.isNotEmpty(pushMessageSendDto.getMessageSendDto())){
            b = commonMethodService.pushSms(pushMessageSendDto.getKey(), pushMessageSendDto.getPushMessageRecord(), pushMessageSendDto.getMessageSendDto());
        }

        if (ObjectUtils.isNotEmpty(pushMessageSendDto.getEmailInfoDto())){
            b = commonMethodService.pushEmail(pushMessageSendDto.getKey(), pushMessageSendDto.getPushMessageRecord(), pushMessageSendDto.getEmailInfoDto());
        }
        return b;
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/v1/vehicle")
    public TmVehicleDto queryVehicleByVin(@RequestParam("vin") String vin) {
        log.info("queryVehicleByVin  vin:{}", vin);
        return commonMethodService.queryVehicleByVin(vin);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "", notes = "", httpMethod = "GET")
    @GetMapping("/whitelist/checkWhitelist")
    public Boolean checkWhitelist(@RequestParam("dealer") String dealer,@RequestParam("modType") Integer modType,@RequestParam("rosterType") Integer rosterType,@RequestParam("vin") String vin,@RequestParam("groupCode") String groupCode) {
        log.info("checkWhitelist,dealer:{},modType:{},rosterType:{},vin:{},groupCode:{}", dealer,modType,rosterType,vin,groupCode);
        return commonMethodService.checkWhitelist(dealer,modType,rosterType,vin,groupCode);
    }

    @ApiOperation(value = "RMS查询车主TAB-V2", notes = "RMS查询车主TAB-V2", httpMethod = "GET")
    @GetMapping("/v2/queryCustomInfoListByVinV2")
    public List<CustomerInfoDto> queryCustomInfoListByVinV2(@RequestParam("vin") String vin,
                                                            @RequestParam("ownerCode") String ownerCode) {
        return commonMethodService.queryCustomInfoListByVin(vin, ownerCode);
    }
}
