package com.volvo.maintain.interfaces.controller.workshop;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.OpenOrderTraceService.commonQueryOrderTrace.CommonOrderTraceResponse;
import com.volvo.maintain.application.maintainlead.dto.bookingOrder.BookingOrderDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.BadgeCountSummaryDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.BookingStatusDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.ListPartBuyItemDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.MissingPartsStatusDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.OutboundOrderETAResponseDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.ShortPartItemDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.ShortPartItemWeComDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.ToCRequestParamsDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.ToJDRequestParamsDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.WorkshopCallRecordDto;
import com.volvo.maintain.application.maintainlead.service.workshop.TransparentWorkshopManageService;
import com.volvo.maintain.application.maintainlead.vo.workshop.BookingOrderVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.ShortPartItemVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/workshop")
@Api(value = "透明车间adapter层", tags = {"透明车间adapter层"})
@Slf4j
public class TransparentWorkshopManageController {

    @Autowired
    private TransparentWorkshopManageService transparentWorkshopManageService;

    /**
     *  缺料明细查询
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ShortPartItemVo", name = "shortPartItemVo", value = "", required = true)
    })
    @ApiOperation(value = "缺料明细查询", notes = "缺料明细查询", httpMethod = "POST")
    @PostMapping(value = "/queryShortPart")
    @ResponseBody
    public IPage<ShortPartItemDto> queryShortPart(@RequestBody ShortPartItemVo shortPartItemVo) {
        return transparentWorkshopManageService.queryShortPart(shortPartItemVo);
    }

    /**
     * 查看单据ETA时间接口 （定时任务）
     * 因经销商会在OG中查看商品的基本信息和价格，以及希望能
     * 看到本地仓库库存和全国仓库的可用库存，一个经销商一次
     * 只能查一个商品的数据
     */
    @ApiOperation(value = "查看单据ETA时间接口 （定时任务） 因经销商会在OG中查看商品的基本信息和价格，以及希望能 看到本地仓库库存和全国仓库的可用库存，一个经销商一次 只能查一个商品的数据", notes = "查看单据ETA时间接口 （定时任务） 因经销商会在OG中查看商品的基本信息和价格，以及希望能 看到本地仓库库存和全国仓库的可用库存，一个经销商一次 只能查一个商品的数据", httpMethod = "POST")
    @PostMapping("/api/v1/etaTime/interf")
    public void queryETADocumentDetails() {
        log.info("XXL queryETADocumentDetails begin");
        transparentWorkshopManageService.queryETADocumentDetails();
    }


    /**
     * 查询ETA时间 （C端使用）
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<ToCRequestParamsDto>", name = "toCRequestParamsDto", value = "", required = true)
    })
    @ApiOperation(value = "查询ETA时间 （C端使用）", notes = "查询ETA时间 （C端使用）", httpMethod = "POST")
    @PostMapping("/shortageEta/toc")
    public List<OutboundOrderETAResponseDto> toC(@RequestBody List<ToCRequestParamsDto> toCRequestParamsDto) {
        log.info("query ETADocumentDetails begin");
        return transparentWorkshopManageService.queryETADocumentDetailsToC(toCRequestParamsDto);
    }


    /**
     * 物流节点
     *
     * @param requestParamsDtoList 入参
     * @return 详情
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "List<ToJDRequestParamsDto>", name = "requestParamsDtoList", value = "入参", required = true)
    })
    @ApiOperation(value = "物流节点", notes = "物流节点", httpMethod = "POST")
    @PostMapping("/api/v1/node/interf")
    public List<CommonOrderTraceResponse> queryLogisticsNode(@RequestBody List<ToJDRequestParamsDto> requestParamsDtoList) {
        return transparentWorkshopManageService.queryLogisticsNode(requestParamsDtoList);
    }

    /**
     * 同步零件状态 到 更新采购明细  更新明细中的 是否入库 零件数量
     *
     * @param listPartBuyItemDto 入参
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ListPartBuyItemDto", name = "listPartBuyItemDto", value = "入参", required = true)
    })
    @ApiOperation(value = "同步零件状态 到 更新采购明细  更新明细中的 是否入库 零件数量", notes = "同步零件状态 到 更新采购明细  更新明细中的 是否入库 零件数量", httpMethod = "POST")
    @PostMapping("/api/v1/syncPartStatus")
    public void syncPartStatus(@RequestBody ListPartBuyItemDto listPartBuyItemDto) {
        transparentWorkshopManageService.syncPartStatus(listPartBuyItemDto);
    }


    /**
     * 服务看板缺件查询（企微店端）
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ShortPartItemVo", name = "shortPartItemVo", value = "", required = true)
    })
    @ApiOperation(value = "服务看板缺件查询（企微店端）", notes = "服务看板缺件查询（企微店端）", httpMethod = "POST")
    @PostMapping(value = "/weCom/queryShortPart")
    @ResponseBody
    public IPage<ShortPartItemWeComDto> queryShortPartWeCom(@RequestBody ShortPartItemVo shortPartItemVo) {
        return transparentWorkshopManageService.queryShortPartWeCom(shortPartItemVo);
    }

    /**
     * 查询未到货，已到货，部分到货数量
     *
     * @return 返回对象（包含数量）
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ShortPartItemVo", name = "shortPartItemVo", value = "", required = true)
    })
    @ApiOperation(value = "查询未到货，已到货，部分到货数量", notes = "查询未到货，已到货，部分到货数量", httpMethod = "POST")
    @PostMapping(value = "/weCom/getShortPartStatus")
    @ResponseBody
    public MissingPartsStatusDto getShortPartStatus(@RequestBody ShortPartItemVo shortPartItemVo) {
        return transparentWorkshopManageService.getShortPartStatus(shortPartItemVo);
    }

    /**
     * 根据缺料主键查询 明细
     *
     * @param shortPartItemVo 明细数据
     * @return 明细数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "ShortPartItemVo", name = "shortPartItemVo", value = "明细数据", required = true)
    })
    @ApiOperation(value = "根据缺料主键查询 明细", notes = "根据缺料主键查询 明细", httpMethod = "POST")
    @PostMapping("/weCom/queryShortPartItem")
    public ShortPartItemDto queryShortPartItem(@RequestBody ShortPartItemVo shortPartItemVo) {
        return transparentWorkshopManageService.queryShortPartItem(shortPartItemVo);
    }

    /**
     * 记录通话记录
     * @param workshopCallRecordDto 通话记录详情
     * @return true false
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "WorkshopCallRecordDto", name = "workshopCallRecordDto", value = "通话记录详情", required = true)
    })
    @ApiOperation(value = "记录通话记录", notes = "记录通话记录", httpMethod = "POST")
    @PostMapping("/weCom/addCallLog")
    public Boolean addCallLog(@RequestBody WorkshopCallRecordDto workshopCallRecordDto) {
        if (StringUtils.isEmpty(workshopCallRecordDto.getOwnerCode())
                || StringUtils.isEmpty(workshopCallRecordDto.getRoNo())
                || StringUtils.isEmpty(workshopCallRecordDto.getServiceAdvisor())) {
            throw new ServiceBizException("销商编码,工单号，服务顾问为必要参数，请检查参数!");
        }
        return transparentWorkshopManageService.addCallLog(workshopCallRecordDto);
    }

    /**
     * 查询通话记录
     * @param ownerCode 经销商
     * @param roNo 工单号
     * @param serviceAdvisor 服务顾问
     * @return 通话记录
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "经销商", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = "工单号", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "serviceAdvisor", value = "服务顾问", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageNum", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询通话记录", notes = "查询通话记录", httpMethod = "GET")
    @GetMapping("/weCom/callItem")
    public Page<WorkshopCallRecordDto> queryCallItem(@RequestParam String ownerCode,
                                                     @RequestParam String roNo,
                                                     @RequestParam String serviceAdvisor,
                                                     @RequestParam(value = "pageNum") Integer pageNum,
                                                     @RequestParam(value = "pageSize") Integer pageSize) {
        if (StringUtils.isEmpty(ownerCode)
                || StringUtils.isEmpty(roNo)
                || StringUtils.isEmpty(serviceAdvisor)) {
            throw new ServiceBizException("销商编码,工单号，服务顾问为必要参数，请检查参数!");
        }
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        return transparentWorkshopManageService.queryCallItem(ownerCode, roNo, serviceAdvisor, pageNum, pageSize);
    }

    /**
     * 修改预计交车时间
     * @param roNo 工单号
     * @param endTimeSupposed 预交车时间
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = "工单号", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "endTimeSupposed", value = "预交车时间", required = true)
    })
    @ApiOperation(value = "修改预计交车时间", notes = "修改预计交车时间", httpMethod = "GET")
    @GetMapping("/roStatus/roNo")
    public void updateRepairOrderStatus(@RequestParam("roNo") String roNo, @RequestParam("endTimeSupposed") String endTimeSupposed) {
        if (StringUtils.isEmpty(roNo)) {
            log.error("roNo is null");
            return;
        }
        transparentWorkshopManageService.updateRepairOrderStatus(roNo, endTimeSupposed);
    }

    /**
     * 服务看板预约查询（企微店端）
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "BookingOrderVo", name = "bookingOrderVo", value = "", required = true)
    })
    @ApiOperation(value = "服务看板预约查询（企微店端）", notes = "服务看板预约查询（企微店端）", httpMethod = "POST")
    @PostMapping(value = "/weCom/queryBookingOrder")
    @ResponseBody
    public IPage<BookingOrderDto> queryBookingWeCom(@RequestBody BookingOrderVo bookingOrderVo) {
        return transparentWorkshopManageService.queryBookingWeCom(bookingOrderVo);
    }

    /**
     * 查询预约 数字
     * @param bookingOrderVo 入参参数
     * @return 返回构建的数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "BookingOrderVo", name = "bookingOrderVo", value = "入参参数", required = true)
    })
    @ApiOperation(value = "查询预约 数字", notes = "查询预约 数字", httpMethod = "POST")
    @PostMapping("/weCom/queryBookingStatusCount")
    public BookingStatusDto queryBookingStatus(@RequestBody BookingOrderVo bookingOrderVo) {
        return transparentWorkshopManageService.queryBookingStatus(bookingOrderVo);
    }

    /**
     * 获取所有当日的数据量
     * @param ownerCode 经销商
     * @return 构建数据对象
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = "经销商", required = true)
    })
    @ApiOperation(value = "获取所有当日的数据量", notes = "获取所有当日的数据量", httpMethod = "GET")
    @GetMapping("/weCom/queryTodayStatusCount")
    public BadgeCountSummaryDto buildPartStatusCount(@RequestParam String ownerCode) {
        return transparentWorkshopManageService.buildPartStatusCount(ownerCode);
    }






}
