package com.volvo.maintain.interfaces.controller;

import com.volvo.maintain.application.maintainlead.dto.techclaim.EbaotechIssueDto;
import com.volvo.maintain.application.maintainlead.service.techclaim.EbaotechClaimService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ebaotechClaim")
@Api(value = "易保延保", tags = {"易保延保"})
@Slf4j
public class EbaotechClaimController {

    @Autowired
    private EbaotechClaimService ebaotechClaimService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "EbaotechIssueDto", name = "ebaotechIssueDto", value = "", required = true)
    })
    @ApiOperation(value = "延保审批数据下发", notes = "", httpMethod = "POST")
    @PostMapping("/Issue/approval")
    public void issueApproval(@RequestBody EbaotechIssueDto ebaotechIssueDto) {
        ebaotechClaimService.issueApproval(ebaotechIssueDto);
    }
}
