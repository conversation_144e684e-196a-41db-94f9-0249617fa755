package com.volvo.maintain.interfaces.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.*;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.infoInherit.DtcCluesCategoryInheritDto;
import com.volvo.maintain.application.maintainlead.service.dtcclues.DtcCluesService;
import com.yonyou.cyx.framework.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/dtcClues")
@Api(value = "DTC线索应用层", tags = {"DTC线索应用层"})
@Slf4j
public class DtcCluesController {
    @Autowired
    private DtcCluesService dtcCluesService;
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DtcCluesCategoryListDto", name = "listRequestDto", value = "", required = true)
    })
    @ApiOperation(value = "DTC线索生成类别列表", notes = "", httpMethod = "POST")
    @PostMapping("/generateCategoryList")
    public Page<DtcCluesCategoryInheritDto> generateCategoryList(@RequestBody DtcCluesCategoryListDto listRequestDto) {
        return dtcCluesService.generateCategoryList(listRequestDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DtcCluesCategoryDto", name = "dtcCluesCategoryDto", value = "", required = true)
    })
    @ApiOperation(value = "新增线索种类", notes = "新增线索种类", httpMethod = "POST")
    @RequestMapping(value = "/insertCluesCategory", method = RequestMethod.POST)
    public Integer insertCluesCategory(@RequestBody DtcCluesCategoryDto dtcCluesCategoryDto) {
        return dtcCluesService.insertCluesCategory(dtcCluesCategoryDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "导入线索种类", notes = "导入线索种类", httpMethod = "POST")
    @RequestMapping(value = "/importCluesCategory", method = RequestMethod.POST)
    public List<ImportResultInfoDto> importCluesCategory(@RequestParam(value = "file") MultipartFile importFile) {
        return dtcCluesService.importCluesCategory(importFile);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DtcCluesCategoryDto", name = "dtcCluesCategoryDto", value = "", required = true)
    })
    @ApiOperation(value = "编辑线索种类", notes = "编辑线索种类", httpMethod = "POST")
    @RequestMapping(value = "/updateCluesCategory", method = RequestMethod.POST)
    public Integer updateCluesCategory(@RequestBody DtcCluesCategoryDto dtcCluesCategoryDto) {
        return dtcCluesService.updateCluesCategory(dtcCluesCategoryDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ecu", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dtc", value = "", required = true)
    })
    @ApiOperation(value = "删除线索种类", notes = "删除线索种类", httpMethod = "GET")
    @RequestMapping(value = "/deleteCluesCategory", method = RequestMethod.GET)
    public Integer deleteCluesCategory(@RequestParam("ecu") String ecu, @RequestParam("dtc") String dtc) {
        return dtcCluesService.deleteCluesCategory(ecu, dtc);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DtcCluesCategoryListDto", name = "dtcCluesCategoryListDto", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "category", value = "", required = true)
    })
    @ApiOperation(value = "导出DTC线索种类", notes = "导出DTC线索种类", httpMethod = "POST")
    @RequestMapping(value = "/exportDtcCluesCategory", method = RequestMethod.POST)
    public void exportDtcCluesCategory(@RequestBody DtcCluesCategoryListDto dtcCluesCategoryListDto, @RequestParam("category") Integer category) {
        dtcCluesService.exportDtcCluesCategory(dtcCluesCategoryListDto, category);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DtcCluesCategoryDto", name = "dtcCluesCategoryDto", value = "", required = true)
    })
    @ApiOperation(value = "导出线索种类回调", notes = "导出线索种类回调", httpMethod = "POST")
    @RequestMapping(value = "/v1/exportCluesCategoryCallback", method = RequestMethod.POST)
    public ResponseDTO<List<ExportDtcCluesCategoryDto>> exportCluesCategoryCallback(
            @RequestParam(value = "currentPage") Integer currentPage,
            @RequestParam(value = "pageSize") Integer pageSize,
            @RequestBody ExportDtcCluesCategoryParDto dto) {

        DtcCluesCategoryListDto dtcCluesCategoryListDto = DtcCluesCategoryListDto.builder()
                .faultCategory(dto.getFaultCategory())
                .ecu(dto.getEcu())
                .dtc(dto.getDtc())
                .confirmStatus(dto.getConfirmStatus())
                .indicatorStatus(dto.getIndicatorStatus())
                .currentPage(currentPage)
                .priority(dto.getPriority())
                .pageSize(pageSize)
                .build();
        return new ResponseDTO<>().successData(dtcCluesService.exportCluesCategoryCallback(dtcCluesCategoryListDto, 1));
    }

    @ApiOperation(value = "查询DTC生成类别中已被使用的优先级集合", notes = "查询DTC生成类别中已被使用的优先级集合", httpMethod = "GET")
    @RequestMapping(value = "/queryCategoryPriority", method = RequestMethod.GET)
    public List<Integer> queryCategoryPriority() {
        return dtcCluesService.queryCategoryPriority();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DtcCluesCategoryListDto", name = "dtcCluesCategoryListDto", value = "", required = true)
    })
    @ApiOperation(value = "DTC线索屏蔽类别列表", notes = "DTC线索屏蔽类别列表", httpMethod = "POST")
    @RequestMapping(value = "/shieldCategoryList", method = RequestMethod.POST)
    public Page<DtcCluesCategoryInheritDto> shieldCategoryList(@RequestBody DtcCluesCategoryListDto dtcCluesCategoryListDto) {
        return dtcCluesService.shieldCategoryList(dtcCluesCategoryListDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DtcCluesCategoryDto", name = "dtcCluesCategoryDto", value = "", required = true)
    })
    @ApiOperation(value = "新增屏蔽种类", notes = "新增屏蔽种类", httpMethod = "POST")
    @RequestMapping(value = "/insertShieldCategory", method = RequestMethod.POST)
    public Integer insertShieldCategory(@RequestBody DtcCluesCategoryDto dtcCluesCategoryDto) {
        return dtcCluesService.insertShieldCategory(dtcCluesCategoryDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "导入屏蔽种类", notes = "导入屏蔽种类", httpMethod = "POST")
    @RequestMapping(value = "/importShieldCategory", method = RequestMethod.POST)
    public List<ImportResultInfoDto> importShieldCategory(@RequestParam(value = "file") MultipartFile importFile) {
        return dtcCluesService.importShieldCategory(importFile);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DtcCluesCategoryDto", name = "dtcCluesCategoryDto", value = "", required = true)
    })
    @ApiOperation(value = "编辑屏蔽种类", notes = "编辑屏蔽种类", httpMethod = "POST")
    @RequestMapping(value = "/updateShieldCategory", method = RequestMethod.POST)
    public Integer updateShieldCategory(@RequestBody DtcCluesCategoryDto dtcCluesCategoryDto) {
        return dtcCluesService.updateShieldCategory(dtcCluesCategoryDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ecu", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "dtc", value = "", required = true)
    })
    @ApiOperation(value = "删除屏蔽种类", notes = "删除屏蔽种类", httpMethod = "GET")
    @RequestMapping(value = "/deleteShieldCategory", method = RequestMethod.GET)
    public Integer deleteShieldCategory(@RequestParam("ecu") String ecu, @RequestParam("dtc") String dtc) {
        return dtcCluesService.deleteShieldCategory(ecu, dtc);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "DtcCluesCategoryDto", name = "dtcCluesCategoryDto", value = "", required = true)
    })
    @ApiOperation(value = "导出屏蔽种类回调", notes = "导出屏蔽种类回调", httpMethod = "POST")
    @RequestMapping(value = "/v1/exportShieldCategoryCallback", method = RequestMethod.POST)
    public ResponseDTO<List<ExportDtcCluesCategoryDto>> exportShieldCategoryCallback(
            @RequestParam(value = "currentPage") Integer currentPage,
            @RequestParam(value = "pageSize") Integer pageSize,
            @RequestBody ExportDtcCluesCategoryParDto dto) {

        DtcCluesCategoryListDto dtcCluesCategoryListDto = DtcCluesCategoryListDto.builder()
                .faultCategory(dto.getFaultCategory())
                .ecu(dto.getEcu())
                .dtc(dto.getDtc())
                .confirmStatus(dto.getConfirmStatus())
                .indicatorStatus(dto.getIndicatorStatus())
                .currentPage(currentPage)
                .pageSize(pageSize)
                .build();
        return new ResponseDTO<>().successData(dtcCluesService.exportCluesCategoryCallback(dtcCluesCategoryListDto, 2));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "category", value = "", required = true)
    })
    @ApiOperation(value = "ECU与DTC的联动下拉框", notes = "ECU与DTC的联动下拉框", httpMethod = "GET")
    @RequestMapping(value = "/queryRelationEcuDtc", method = RequestMethod.GET)
    public Map<String, List<EcuDtcRelationDto>> queryRelationEcuDtc(@RequestParam("category") Integer category) {
        return dtcCluesService.queryRelationEcuDtc(category);
    }
}
