package com.volvo.maintain.interfaces.controller.healthcheck;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.volvo.maintain.application.maintainlead.dto.healthcheck.HealthCheckDto;
import com.volvo.maintain.application.maintainlead.service.healthcheck.VehicleHealthCheckService;
import com.volvo.maintain.application.maintainlead.vo.healthcheck.VehicleHealthRecordInfoVo;
import com.volvo.maintain.application.maintainlead.vo.healthcheck.VehicleHealthVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Slf4j
@RequestMapping("/vehicleHealth")
@Api(value = "车辆健康检查", tags = {"车辆健康检查"})
public class VehicleHealthCheckManagerController {

    private final VehicleHealthCheckService vehicleHealthCheckService;

    public VehicleHealthCheckManagerController(VehicleHealthCheckService vehicleHealthCheckService) {
        this.vehicleHealthCheckService = vehicleHealthCheckService;
    }


    /**
     * 根据车架号查询健康检查数据分页
     * @param vin 车架号
     * @param pageNum 分页参数
     * @param pageSize 分页参数
     * @return 分页数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "车架号", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageNum", value = "分页参数"),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "分页参数")
    })
    @ApiOperation(value = "根据车架号查询健康检查数据分页", notes = "根据车架号查询健康检查数据分页", httpMethod = "GET")
    @GetMapping(value = "/list/interf")
    public IPage<VehicleHealthVo> queryHealthByVin(@RequestParam(value = "vin") String vin,
                                                   @RequestParam(value = "pageNum", required = false) Integer pageNum,
                                                   @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        return vehicleHealthCheckService.queryHealthByVin(vin, pageNum, pageSize);
    }


    /**
     * 查询最新一条健康检查报告 （批量）
     * @param dto 入参对象
     * @return 返回的数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "HealthCheckDto", name = "dto", value = "入参对象", required = true)
    })
    @ApiOperation(value = "查询最新一条健康检查报告 （批量）", notes = "查询最新一条健康检查报告 （批量）", httpMethod = "POST")
    @PostMapping("/lastInfo/interf")
    public List<VehicleHealthRecordInfoVo> queryVehicleHealthCheckDetails(@RequestBody HealthCheckDto dto) {
        if (ObjectUtils.isEmpty(dto) || ObjectUtils.isEmpty(dto.getVins()) || StringUtils.isBlank(dto.getStartTime()) || StringUtils.isBlank(dto.getEndTime())) {
            throw new ServiceBizException("Perhaps the time or vin is empty !");
        }
        return vehicleHealthCheckService.queryVehicleHealthCheckDetails(dto);
    }

}
