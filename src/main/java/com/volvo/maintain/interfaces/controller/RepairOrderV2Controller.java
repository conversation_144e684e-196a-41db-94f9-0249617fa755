package com.volvo.maintain.interfaces.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderAndClaimTypeReqDto;
import com.volvo.maintain.application.maintainlead.service.RepairOrderService;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderAndClaimTypeRespVO;
import com.yonyou.cyx.framework.dto.ResponseDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述：工单adapter层
 */
@Api(value = "/repairOrder/v1", tags = {"功能描述：工单adapter层"})
@RestController
@RequestMapping("/repairOrder/v1")
@Slf4j
public class RepairOrderV2Controller {
	
	@Autowired
	private RepairOrderService repairOrderService;
	
	@ApiOperation(value = "二手车查询车辆工单信息及保修类型代码", notes = "二手车查询车辆工单信息及保修类型代码", httpMethod = "POST")
	@PostMapping("/queryRepairOrderAndClaimTypeByVin")
	public ResponseDTO<List<RepairOrderAndClaimTypeRespVO>> queryRepairOrderAndClaimTypeByVin(@RequestBody RepairOrderAndClaimTypeReqDto repairOrderAndClaimTypeReq) {
		log.info("queryRepairOrderAndClaimTypeByVin: {}", JSON.toJSONString(repairOrderAndClaimTypeReq));
		return new ResponseDTO<>().successData(repairOrderService.queryRepairOrderAndClaimTypeByVin(repairOrderAndClaimTypeReq));
	}
}
