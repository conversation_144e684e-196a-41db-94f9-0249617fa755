package com.volvo.maintain.interfaces.controller.customerProfile;


import com.volvo.maintain.application.maintainlead.dto.coupon.QueryCouponDetailInfoDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CouponDetailService;
import com.volvo.maintain.interfaces.vo.coupon.CouponDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 功能描述：卡券查询控制层
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Api(value = "/validCoupon", tags = {"功能描述：卡券查询控制层"})
@RestController
@RequestMapping("/validCoupon")
public class ValidCouponDetailController {
    @Autowired
    private CouponDetailService couponDetailService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "QueryCouponDetailInfoDto", name = "queryCouponDetailInfoDTO", value = "", required = true)
    })
    @ApiOperation(value = "查询卡券明细", notes = "", httpMethod = "GET")
    @GetMapping("/queryCouponInfo")
    public List<CouponDetailVO> queryCouponInfo(@RequestBody QueryCouponDetailInfoDto queryCouponDetailInfoDTO) {
        return couponDetailService.queryCouponInfo(queryCouponDetailInfoDTO);
    }

}
