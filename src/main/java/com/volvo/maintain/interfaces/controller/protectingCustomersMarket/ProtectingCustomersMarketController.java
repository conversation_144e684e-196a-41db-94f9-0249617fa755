package com.volvo.maintain.interfaces.controller.protectingCustomersMarket;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.volvo.maintain.application.maintainlead.dto.OrderTagSnapshotDto;
import com.volvo.maintain.application.maintainlead.service.protectingCustomersMarket.ProtectingCustomersMarketService;
import com.volvo.maintain.interfaces.vo.PurchaseIntentionVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "保客营销")
@RestController
@RequestMapping("/protectingCustomersMarket")
@Validated
public class ProtectingCustomersMarketController {

	@Autowired
	private ProtectingCustomersMarketService protectingCustomersMarketService;

    /**
     *查询是否存在换购意向
     * @param vin 车架号
     * @param delivererMobile 送修人手机号
     * @param roNo 工单号
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "车架号", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "delivererMobile", value = "送修人手机号", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "roNo", value = "工单号")
    })
    @ApiOperation(value = "查询是否存在换购意向", notes = "查询是否存在换购意向", httpMethod = "GET")
    @GetMapping(value = "/queryPurchaseIntention")
    public List<PurchaseIntentionVo> queryPurchaseIntention(@RequestParam("vin") String vin,
                                                           @RequestParam("delivererMobile") String delivererMobile,
                                                           @RequestParam(value = "roNo",required = false) String roNo) {
        if (StringUtils.isEmpty(vin) || StringUtils.isEmpty(delivererMobile)){
            throw new ServiceBizException("车架号或送修人手机号不可为空!!!");
        }
        return protectingCustomersMarketService.queryPurchaseIntention(vin,delivererMobile,roNo);
    }

    /**
     *查询是否存在换购意向
     * @param delivererMobile 送修人手机号
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "delivererMobile", value = "送修人手机号", required = true)
    })
    @ApiOperation(value = "查询是否存在换购意向", notes = "查询是否存在换购意向", httpMethod = "GET")
    @GetMapping(value = "/queryOrderTagSnapshotList")
    public List<OrderTagSnapshotDto> queryOrderTagSnapshotList(@RequestParam(value = "delivererMobile", required = false) String delivererMobile,
                                                               @RequestParam(value = "vin", required = false) String vin) {
        return protectingCustomersMarketService.queryOrderTagSnapshotList(delivererMobile, vin);
    }

}
