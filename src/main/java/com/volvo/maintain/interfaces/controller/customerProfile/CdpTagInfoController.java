package com.volvo.maintain.interfaces.controller.customerProfile;


import com.volvo.maintain.application.maintainlead.dto.CdpEmpowerDto;
import com.volvo.maintain.application.maintainlead.dto.CdpTagListDto;
import com.volvo.maintain.application.maintainlead.dto.CustomerTagsDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CdpTagInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/cdpTagInfo")
@Api(value = "VIP客户")
public class CdpTagInfoController {
    @Autowired
    private CdpTagInfoService cdpTagInfoService;

    /**
     * 批量根据vin查询是否CDP授权客户
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "CdpEmpowerDto", name = "cdpEmpowerDto", value = "", required = true)
    })
    @ApiOperation(value = " 批量查询是否CDP授权客户", notes = "批量根据vin查询是否CDP授权客户", httpMethod = "POST")
    @PostMapping("/queryEmpowerByVins")
    public List<CdpTagListDto> queryEmpowerByVins(@RequestBody CdpEmpowerDto cdpEmpowerDto) {
        List<CdpTagListDto> cdpEmpowerVos = cdpTagInfoService.queryEmpowerByVins(cdpEmpowerDto.getVin());
        return cdpEmpowerVos;
    }

    /**
     * 根据vin查询是否CDP授权客户
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vin", value = "", required = true)
    })
    @ApiOperation(value = "查询是否CDP授权客户", notes = "根据vin查询是否CDP授权客户", httpMethod = "GET")
    @GetMapping("/queryEmpowerByVin")
    public CustomerTagsDto queryEmpowerByVin(@RequestParam("vin") String vin) {
        CustomerTagsDto cdpTagListDTOS = cdpTagInfoService.queryEmpowerByVin(vin);
        return cdpTagListDTOS;
    }


}
