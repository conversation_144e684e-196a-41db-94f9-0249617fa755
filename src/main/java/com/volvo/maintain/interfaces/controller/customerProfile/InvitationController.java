package com.volvo.maintain.interfaces.controller.customerProfile;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.FollowUpPageDto;
import com.volvo.maintain.application.maintainlead.dto.inviteClue.InvitationScripDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.InvitationService;
import com.yonyou.cyx.function.exception.ServiceBizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "邀约")
@RestController
@RequestMapping("/invitation")
public class InvitationController {

    @Autowired
    private InvitationService invitationService;


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "current", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "size", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "typeCodeId", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "isEnable", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "title", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "tagTalkskill", value = "")
    })
    @ApiOperation(value = "邀约话术管理查询", notes = "", httpMethod = "GET")
    @GetMapping("/invitationScriptManageQuery")
    public Page<InvitationScripDto> invitationScriptManageQuery( @RequestParam(value = "current") Integer current,
                                                                @RequestParam(value = "size") Integer size,
                                                                 @RequestParam(value = "typeCodeId",required = false) String typeCodeId,
                                                                 @RequestParam(value = "isEnable",required = false) Integer isEnable,
                                                                 @RequestParam(value = "title",required = false) String title,
                                                                 @RequestParam(value = "tagTalkskill",required = false) String tagTalkskill) {

        if (current == null || size == null) {
            throw new ServiceBizException("参数校验失败");
        }
        return invitationService.invitationScriptManageQuery(current, size, typeCodeId, isEnable, title, tagTalkskill);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "FollowUpPageDto", name = "followUpPageDto", value = "", required = true)
    })
    @ApiOperation(value = "邀约话术跟进页面渲染", notes = "", httpMethod = "POST")
    @PostMapping("/invitationScriptFollowUpPage")
    public List<InvitationScripDto> invitationScriptFollowUpPage(@RequestBody FollowUpPageDto followUpPageDto) {
        if (CollectionUtils.isEmpty(followUpPageDto.getInviteTypes())) {
            throw new ServiceBizException("参数校验失败");
        }
        return invitationService.invitationScriptFollowUpPage(followUpPageDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationScripDto", name = "invitationScripDto", value = "", required = true)
    })
    @ApiOperation(value = "邀约话术保存", notes = "", httpMethod = "POST")
    @PostMapping("/saveInvitationScript")
    public Integer saveInvitationScript(@RequestBody InvitationScripDto invitationScripDto) {

        checkInvitationParams(invitationScripDto);

        return invitationService.saveInvitationScript(invitationScripDto);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "InvitationScripDto", name = "invitationScripDto", value = "", required = true)
    })
    @ApiOperation(value = "邀约话术编辑", notes = "", httpMethod = "POST")
    @PostMapping(value = "/updateInvitationScript")
    public Integer updateInvitationScript(@RequestBody InvitationScripDto invitationScripDto) {

        checkInvitationParams(invitationScripDto);

        return invitationService.updateInvitationScript(invitationScripDto);
    }

    private void checkInvitationParams(@RequestBody InvitationScripDto invitationScripDto) {
        if (CollectionUtils.isEmpty(invitationScripDto.getTypeCodeIds()) && ObjectUtils.isEmpty(invitationScripDto.getTypeCodeId())) {
            throw new ServiceBizException("业务分类不能为空");
        }
        if (ObjectUtils.isEmpty(invitationScripDto.getIsEnable())) {
            throw new ServiceBizException("是否启用不能为空");
        }
        if (StringUtils.isEmpty(invitationScripDto.getTitle())) {
            throw new ServiceBizException("话术标题不能为空");
        }
        if (StringUtils.isEmpty(invitationScripDto.getTagTalkskill())) {
            throw new ServiceBizException("标签话术不能为空");
        }
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "邀约话术删除", notes = "", httpMethod = "GET")
    @GetMapping(value = "/deleteInvitationScript")
    public Integer deleteInvitationScript(@RequestParam(value = "id") Integer id) {
        if (id == null) {
            throw new ServiceBizException("参数校验失败");
        }
        return invitationService.deleteInvitationScript(id);
    }


    /**
     * 邀约话术管理查询详情
     * @param id
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "id", value = "", required = true)
    })
    @ApiOperation(value = "邀约话术管理查询详情", notes = "邀约话术管理查询详情", httpMethod = "GET")
    @GetMapping("/scriptManageDetail/{id}")
    public InvitationScripDto invitationScriptManageDetail(@PathVariable(value = "id") Integer id) {
        if (id == null) {
            throw new ServiceBizException("参数校验失败");
        }
        return invitationService.invitationScriptManageDetail(id);
    }


}
