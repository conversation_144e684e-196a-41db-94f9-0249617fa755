package com.volvo.maintain.interfaces.controller.customermarketing;

import com.volvo.maintain.application.maintainlead.dto.customermarketing.OwnerInfoDTO;
import com.volvo.maintain.application.maintainlead.service.protectingCustomersMarket.ProtectingCustomersMarketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "保客营销-非进店客户")
@RestController
@RequestMapping("/customermarketing")
@Validated
public class CustomermarketingController {
	@Autowired
	private ProtectingCustomersMarketService protectingCustomersMarketService;

	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "string", name = "VIN", value = "vin", required = true)
	})
	@ApiOperation(value = "【保客营销】非进店客户线索生成-查询车主信息", notes = "【保客营销】非进店客户线索生成-查询车主信息", httpMethod = "GET")
	@GetMapping(value = "/v1/queryOwnerInfo")
	public OwnerInfoDTO queryOwnerInfo(@RequestParam(value = "vin") String vin) {
		return protectingCustomersMarketService.queryOwnerInfo(vin);
	}
}
