package com.volvo.maintain.interfaces.controller.whitelistManagement;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.company.DataInputVo;
import com.volvo.maintain.application.maintainlead.dto.white.WhiteListDto;
import com.volvo.maintain.application.maintainlead.service.whitelistManagement.WhitelistManagementService;
import com.volvo.maintain.interfaces.vo.white.VehicleHealthCheckWhiteListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;

@Api(tags = "白名单")
@RestController
@RequestMapping("/whitelistManagement")
@Validated
public class WhitelistManagementController {

    @Autowired
    private WhitelistManagementService whitelistManagementService;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "modType", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "itemCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "isDeleted", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "currentPage", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "pageSize", value = "", required = true)
    })
    @ApiOperation(value = "查询白名单管理列表 厂端", notes = "查询白名单管理列表 厂端", httpMethod = "GET")
    @GetMapping(path = "/selectWhitelist")
    public Page<VehicleHealthCheckWhiteListVo> selectWhitelist(@RequestParam("modType") String modType, @RequestParam("itemCode") String itemCode, @RequestParam("isDeleted") String isDeleted, @RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize) {
        return whitelistManagementService.selectWhitelist(modType,itemCode,isDeleted,currentPage,pageSize);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "itemCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "rosterType", value = "", required = true)
    })
    @ApiOperation(value = "查询白名单明细列表 厂端", notes = "查询白名单管理列表 厂端", httpMethod = "GET")
    @GetMapping(path = "/selectWhitelistDetail")
    public List<VehicleHealthCheckWhiteListVo> selectWhitelistDetail(@RequestParam("itemCode") String itemCode, @RequestParam("rosterType") String rosterType) {
        return whitelistManagementService.selectWhitelistDetail(itemCode,rosterType);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "WhiteListDto", name = "whiteListDto", value = "", required = true)
    })
    @ApiOperation(value = "新增白名单 厂端", notes = "新增白名单 厂端", httpMethod = "POST")
    @PostMapping(path = "/insertWhitelist")
    public int insertWhitelist(@RequestBody WhiteListDto whiteListDto) {
        return whitelistManagementService.insertWhitelist(whiteListDto);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "itemCode", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "isDeleted", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "rosterType", value = "", required = true)
    })
    @ApiOperation(value = "白名单启用/停用 厂端", notes = "白名单启用/停用 厂端", httpMethod = "GET")
    @GetMapping (path = "/toggleWhitelistActivation")
    public int toggleWhitelistActivation(@RequestParam("itemCode") String itemCode, @RequestParam("isDeleted") String isDeleted, @RequestParam("rosterType") String rosterType) {
        return whitelistManagementService.toggleWhitelistActivation(itemCode,isDeleted,rosterType);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "modType", value = "", required = true),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "vehicleOrPhone", value = "", required = true),
            @ApiImplicitParam(paramType = "form", dataType = "file", name = "importFile", value = "", required = true)
    })
    @ApiOperation(value = "白名单VIN/手机号导入 厂端", notes = "白名单VIN/手机号导入 厂端", httpMethod = "POST")
    @PostMapping (path = "/vehicleOrPhoneImport")
    public List<DataInputVo> vehicleOrPhoneImport(@RequestParam("modType") String modType, @RequestParam("vehicleOrPhone") String vehicleOrPhone, @RequestParam(value = "file") MultipartFile importFile) {
        return whitelistManagementService.vehicleOrPhoneImport( modType,vehicleOrPhone,importFile);
    }


    /**
     * 检验白名单是否存在
     * @param modType 白名单类型
     * @return true / false
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "int", name = "modType", value = "白名单类型", required = true)
    })
    @ApiOperation(value = "检验白名单是否存在", notes = "检验白名单是否存在", httpMethod = "GET")
    @GetMapping(value = "/checkWhitelist/enable/interf")
    public Boolean checkIfWhitelistEnabled(@RequestParam(value = "modType") Integer modType){
        return whitelistManagementService.checkIfWhitelistEnabled(modType);
    }


    /**
     * 根据类型查询存在的经销商白名单
     * @param vo 参数白名单类型，白名单 0 roseType
     * @return 返回该名单单类型的经销商
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "VehicleHealthCheckWhiteListVo", name = "vo", value = "参数白名单类型，白名单 0 roseType", required = true)
    })
    @ApiOperation(value = "根据类型查询存在的经销商白名单", notes = "根据类型查询存在的经销商白名单", httpMethod = "POST")
    @PostMapping("/checkWhitelist/exist/interf")
    public List<String> queryWhiteListByParams(@RequestBody VehicleHealthCheckWhiteListVo vo) {
        if (ObjectUtils.isEmpty(vo)) {
            return Collections.emptyList();
        }
        return whitelistManagementService.queryWhiteListByParams(vo);
    }


}
