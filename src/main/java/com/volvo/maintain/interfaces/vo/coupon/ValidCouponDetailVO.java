package com.volvo.maintain.interfaces.vo.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("卡券领取查询VO")
public class ValidCouponDetailVO {

    @ApiModelProperty(value = "客户")
    private List<CouponDetailVO> oneIdCouponDetailVO;

    @ApiModelProperty(value = "卡券失效日期")
    private List<CouponDetailVO> vinCouponDetailVO;

}
