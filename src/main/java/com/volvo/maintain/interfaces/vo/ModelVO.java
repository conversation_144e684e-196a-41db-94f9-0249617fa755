package com.volvo.maintain.interfaces.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("车辆信息")
public class ModelVO{
    private String companyCode;
    private String dataSources;
    private Integer fuelType;
    private Integer id;
    private Integer isValid;
    private String modelCode;
    private String modelName;
    private String modelNameEn;
    private Integer seriesId;
    private Integer modelId;
    private Integer SERIES_ID;
    private Integer MODEL_ID;
    private String MODEL_NAME;
    private String modelYear;
    private String modelCodeName;

}
