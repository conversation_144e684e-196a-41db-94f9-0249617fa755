package com.volvo.maintain.interfaces.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class ContentsPartVo implements Serializable {

    /**
     * 项目类型 1 延保 2 保养活动
     */
    private Integer contentsType;

    /**
     * 业务ID
     */
    private Long bizId;

    /**
     * 零件号
     */
    private String partNo;

    /**
     * 业务类型 延保类型(81501001：普通延保产品 81501003：Evcar延保 83451001：出行无忧 83451002：钥匙险 83451003 : 车辆焕新补贴
     */
    private Integer bizType;
}
