package com.volvo.maintain.interfaces.vo.clues;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 保险线索配置表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InsuranceLeadsConfigVo {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 经销商代码
     */
    private String ownerCode;

    /**
     * 经销商名称
     */
    private String ownerName;

    /**
     * 大区ID
     */
    private Integer afterBigareaId;

    /**
     * 大区名称
     */
    private String afterBigareaName;

    /**
     * 小区ID
     */
    private Integer afterSmallareaId;

    /**
     * 小区名称
     */
    private String afterSmallareaName;

    /**
     * 线索分配类型 96061001 单店保护, 96061002 解除单店保护
     */
    private Integer allocationType;

    /**
     * 提前天数
     */
    private Integer advanceDays;

    /**
     * 规则类型 (续保 字典)
     */
    private Integer insuranceType;

    /**
     * 删除标识（0-未删除，1-已删除）
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建sql人
     */
    private String createSqlby;

    /**
     * 更新sql人
     */
    private String updateSqlby;

    private String flag;

    private Integer currentPage;

    private Integer pageSize;

    // 导出的 条件集合
    private String afterBigareaIds;

    private String afterSmallareaIds;
}