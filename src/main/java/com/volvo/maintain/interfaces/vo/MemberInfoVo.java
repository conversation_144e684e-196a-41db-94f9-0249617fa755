package com.volvo.maintain.interfaces.vo;

import com.volvo.maintain.application.maintainlead.dto.MembervehicleDto;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("会员信息")
public class MemberInfoVo {

    private int custId;

    private String memberName;

    private String memberPhone;

    private String membersex;

    private int membervnum;

    private int memberlockvnum;

    private int membercnum;

    private int memberlevel;

    private String memberstatus;

    private String memberuplevtime;

    private String memberdownlevtime;

    private int status;

    private int id;

    private String uniquenumbercode;

    private int version;

    private int createby;

    private int updateby;

    private String memberidentity;

    private int membertotalnum;

    private String membersource;

    private String memberaddress;

    private String memberdlr;

    private String membertime;

    private String memberqrcode;

    private String createtime;

    private String updatetime;

    private String createtimeend;

    private String membertimestart;

    private String membertimeend;

    private int membervnumstart;

    private int membervnumend;

    private String vehiclename;

    private String vehiclesystem;

    private String openid;

    private int isVehicle;

    private String levelname;

    private String leveldesc;

    private String realName;

    private String memberemail;

    private String memberUrl;

    private String memberbirthday;

    private String memberpreference;

    private String memberhobby;

    private String membercontent;

    private int levelid;

    private List<MembervehicleDto> memberVehicleDTOList;


}
