package com.volvo.maintain.interfaces.vo.rights;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class DealerCarRightsVo {

    private Long id;

    private String rightsNo;

    private String rightsName;

    private String vin;

    private String userRightNo;

    private Integer userRightsStatus;

    /**
     * 生效日期
     */
    private String effectiveDate;

    /**
     * 失效日期
     */
    private String expirationDate;

    private Integer cycleType;

    private Integer cycleCount;

    private Integer usageMode;

    private BigDecimal percentageDiscount;

    private BigDecimal singleDiscountMax;

    private String dataSources;

    private String dealerCode;

    private String dealerName;

    private String companyId;

    private String companyName;

    private String remark;

    private Integer orgType;

    private String tocRightsName;

    private Integer category;

    private Integer firstCategoryCode;

    private String firstCategoryName;

    private Integer secondCategoryCode;

    private String secondCategoryName;

    private Integer thirdCategoryCode;

    private String thirdCategoryName;

    private String excelCreateTime;

    private String excelCreatedBy;

    private String excelCancelTime;

    private String excelCancelBy;

    private String excelUserRightsStatus;

    private String applyProject;

    private String percentageDiscountStr;

    private String singleDiscountMaxStr;
}
