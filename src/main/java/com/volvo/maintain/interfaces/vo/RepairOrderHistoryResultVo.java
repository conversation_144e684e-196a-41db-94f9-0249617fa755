package com.volvo.maintain.interfaces.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


@ApiModel("工单历史接口出参Vo")
@Data
public class RepairOrderHistoryResultVo {

	/**
	 * 工单历史查询接口： 1、客户名称：取值车主名称 2、进厂时间：取值开单时间 3、完工时间：取值竣工时间（即质检时间）
	 * 4、结算时间：关联结算单表，取当前工单最新有效结算单的结算时间
	 * 5、QB号码：关联工单交修项目表，取交修项目表中的QB号码清单，可能存在多个QB号码，中间用逗号分隔进行拼接
	 * 6、出厂时间：待确认，已经让弘毅那边去确认了。
	 */
	@ApiModelProperty(value = "工单号", name = "roNo")
	private String roNo;

	@ApiModelProperty(value = "经销商代码", name = "ownerCode")
	private String ownerCode;

	@ApiModelProperty(value = "客户名称", name = "customerName")
	private String customerName;

	@ApiModelProperty(value = "开单时间", name = "roCreateDate")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date roCreateDate;

	@ApiModelProperty(value = "进厂时间", name = "inDealerDate")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date inDealerDate;

	@ApiModelProperty(value = "维修类型", name = "repairTypeCode")
	private String repairTypeCode;
	
	@ApiModelProperty(value = "维修类型名称", name = "repairTypeName")
    private String repairTypeName;

	@ApiModelProperty(value = "车牌", name = "license")
	private String license;

	@ApiModelProperty(value = "车型", name = "model")
	private String model;

	@ApiModelProperty(value = "年款(tm_vehicle)", name = "modelYear")
	private String modelYear;

	@ApiModelProperty(value = "VIN", name = "vin")
	private String vin;

	@ApiModelProperty(value = "销售日期(tm_vehicle)", name = "salesDate")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date salesDate;

	@ApiModelProperty(value = "车主姓名", name = "ownerName")
	private String ownerName;

	@ApiModelProperty(value = "服务顾问", name = "serviceAdvisor")
	private String serviceAdvisor;

	@ApiModelProperty(value = "完工时间(竣工时间)", name = "completeTime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date completeTime;

	@ApiModelProperty(value = "出厂时间(交车时间)", name = "deliveryDate")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date deliveryDate;

	@ApiModelProperty(value = "结算时间", name = "balanceTime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date balanceTime;

	@ApiModelProperty(value = "结算金额", name = "balanceAmount")
	private Double balanceAmount;

	@ApiModelProperty(value = "进厂里程", name = "inMileage")
	private Double inMileage;

	@ApiModelProperty(value = "故障说明", name = "roTroubleDesc")
	private String roTroubleDesc;

	@ApiModelProperty(value = "工单状态", name = "roStatus")
	private String roStatus;

	@ApiModelProperty(value = "QB号", name = "qbPhone")
	private String qbPhone;

	@ApiModelProperty(value = "交修项目名称（多条用逗号分隔）", name = "handRepairProjectName")
	private String handRepairProjectName;

	@ApiModelProperty(value = "工时名称（多条用逗号分隔）", name = "labourName")
	private String labourName;
	
	@ApiModelProperty(value = "OP_Code（多条用逗号分隔）", name = "labourCode")
    private String labourCode;
	
	@ApiModelProperty(value = "保险公司代码", name = "insurationCode")
    private String insurationCode;
	
	@ApiModelProperty(value = "三包状态", name = "schemeStatus")
    private Integer schemeStatus;
	
	@ApiModelProperty(value = "保险金额", name = "insurationAmount")
    private Double insurationAmount;
	
	@ApiModelProperty(value = "工时保险金额", name = "insurationAmount")
    private Double labourAmount;
	
	@ApiModelProperty(value = "配件保险金额", name = "insurationAmount")
    private Double partSalesAmount;
	
	@ApiModelProperty(value = "附加项目保险金额", name = "insurationAmount")
    private Double receiveAmount;
	
	@ApiModelProperty(value = "客户地址", name = "address")
    private String address;
	
	@ApiModelProperty(value = "客户电话(手机)", name = "mobile")
    private String mobile;
	
	@ApiModelProperty(value = "出厂里程", name = "outMileage")
    private String outMileage;
}
