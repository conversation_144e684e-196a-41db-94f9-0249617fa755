package com.volvo.maintain.interfaces.vo;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class MaintainActivityVo  implements Serializable {

	/**
	 * 主键ID
	 */
	private Long				id;

	/**
	 * 保养活动编号
	 */
	private String				maintainNo;

	/**
	 * 保养活动名称
	 */
	private String				maintainName;

	/**
	 * 零件号
	 */
	private String				partNo;

	/**
	 * 活动类型(新车免费基础保养、服务合同、90系免费保养套餐、售后保养合同)
	 */
	private Integer				activityType;

	/**
	 * 保养类型(基础保养,机油更新)
	 */
	private Integer				maintainType;

	/**
	 * 保养次数
	 */
	private Integer				maintainNum;

	/**
	 * 保养年数
	 */
	private Integer				maintainYear;

	/**
	 * 机油级别(普通机油,高级机油)
	 */
	private Integer				oilLevel;

	/**
	 * 保养活动是否有效(是,否)
	 */
	private Integer				isValidMaintain;

	/**
	 * 车型代码
	 */
	private String				modelCode;

	/**
	 * 发动机号(多个以逗号拼接)
	 */
	private String				engineCode;

	/**
	 * 交修代号
	 */
	private String				itemCode;

	/**
	 * 交修项目
	 */
	private String				itemName;

	/**
	 * 是否限定未做首保车主购买(是,否)
	 */
	private Integer				isRestrict;

	/**
	 * 保修类别
	 */
	private String				claimType;

	/**
	 * 故障原因代码
	 */
	private String				malfunctionCode;

	/**
	 * 原因代码
	 */
	private String				causeCode;

	/**
	 * 维修说明
	 */
	private String				maintenanceInstruction;

	/**
	 * 工时单价(一般工时单价,保修工时单价)
	 */
	private Integer				workTimePrice;

	/**
	 * 零件单价(建议销售价,索赔价)
	 */
	private Integer				partPice;

	/**
	 * 工时赔付比例
	 */
	private BigDecimal			hourlyClaimsRatio;

	/**
	 * 零件赔付比例
	 */
	private BigDecimal			partCompensationRatio;

	/**
	 * 车龄上限
	 */
	private Integer				vehicleAgeCeiling;

	/**
	 * 里程上限
	 */
	private Integer				mileageCeiling;

	/**
	 * 套餐id集合
	 */
	private List<Long>			setIdList;

	/**
	 * 保养活动编号集合
	 */
	private List<String>		maintainNoList;

	/**
	 * 系统id
	 */
	private String				appId;

	/**
	 * 所有者代码
	 */
	private String				ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	private String				ownerParCode;

	/**
	 * 组织id
	 */
	private Integer				orgId;

	/**
	 * 数据来源
	 */
	private Integer				dataSources;

	/**
	 * 是否删除，1：删除，0：未删除
	 */
	private Integer				isDeleted;

	/**
	 * 是否有效
	 */
	private Integer				isValid;

	/**
	 * 创建时间
	 */
	private Date				createdAt;

	/**
	 * 更新时间
	 */
	private Date				updatedAt;

	/**
	 * 是否上架
	 */
	private Integer				isActive;

	/**
	 * 保修类别2
	 */
	private String warrantyTypeTwo;

	/**
	 * 保修的维修说明
	 */
	private String warrantyRepairRemark;
}
