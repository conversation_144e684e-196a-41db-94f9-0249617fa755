package com.volvo.maintain.interfaces.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 异常信息返回
 */
@Data
public class CheckFinalInspectionHandProjecRespVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 交修项目id */
    private String repairProjectId;

    /**
     * 交修项目代码
     */
    private String handRepairProjectCode;

    /**
     * 交修项目名称
     */
    private String handRepairProjectName;
    
    /**
     * 提醒信息
     */
    private String msg;
    
    /**
     * 是否维修部件缺失
     */
    private String isRepairPartsMissing;
    
    /**
     * 是否文件缺失
     */
    private String isFileMissing;
    /**
     * 文件缺失类型List
     */
    private List<String> fileMissingTypeList;
} 