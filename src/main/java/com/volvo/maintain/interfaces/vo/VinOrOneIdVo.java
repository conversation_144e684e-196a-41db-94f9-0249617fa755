package com.volvo.maintain.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("卡券查询Vo")
public class VinOrOneIdVo {
    @ApiModelProperty(value = "车主id",name = "oneId")
    private Integer oneId;

    @ApiModelProperty(value = "vin",name = "vin")
    private String vin;

}
