package com.volvo.maintain.interfaces.vo;

import lombok.Data;

/**
 * <p>
 * 权益信息
 * </p>
 */
@Data
public class EquityInformationVo {
    /**
     * 优先级0延保>1终身保养>2保养套餐>3非车险>4道路救援>5残值基金>6保险基金>8质保（优先级高的权益展示在前，优先级低的展示在后面）
     */
    private Integer  level;

    /**
     * 权益类别
     */
    private String  productType;

    /**
     * 产品名称
     */
    private String  productName;

    /**
     * 车架号
     */
    private String  vin;

    /**
     * 生效时间
     */
    private String  effectiveDate;
    /**
     * 到期时间
     */
    private String  expireDate;

    /**
     * 保养活动可享用次数
     */
    private Integer activityAvailableTimes;

    /**
     * 保养活动剩余享用次数
     */
    private Integer activityLeftTimes;

    /**
     * 保养活动名称
     */
    private String activityName;

    /**
     * 保养活动编号
     */
    private String activityNo;

    /**
     * 活动类型(1—90系免费保养套餐 2—售后保养合同 3—服务合同 4—新车免费基础保养)
     */
    private Integer activityType;

    /**
     * 活动类型(1—90系免费保养套餐 2—售后保养合同 3—服务合同 4—新车免费基础保养)
     */
    private String activityTypeName;

    /**
     * 购买者名称
     */
    private String buyerName;

    /**
     * 该车使用权益的车龄上限
     *
     */
    private Integer carAgeMax;

    /**
     * 汽车销售日期 yyyy-MM-dd HH:mm:ss
     *
     */
    private String carsaleTime;


    /**
     * 结束时间
     */
    private String endTime;

    /**
     * id
     */
    private Integer id;

    /**
     * 该车使用权益的里程上限
     */
    private Integer mileageMax;

    /**
     * 车型名称
     */
    private String modelName;

    /**
     * 购买日期 yyyy-MM-dd HH:mm:ss
     */
    private String purchaseDate;

    /**
     * 销售经销商
     */
    private String saleDealer;

    /**
     * 销售时间
     */
    private String saleTime;

    /**
     * 工单结算日期 yyyy-MM-dd HH:mm:ss
     */
    private String settlementDate;

    /**
     * 权益来源(NEWBIE/DMS)
     */
    private String source;

    /**
     * 销售工单号
     */
    private String workNo;

    /**
     * 零件号
     */
    private String partNo;

    /**
     * 保修类别
     */
    private String claimType;

    /**
     * 来源备注
     */
    private String sourceRemark;

    /**
     * 导入时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 保养类型(83321001基础保养 83321002机油更新)
     */
    private String upkeepType;

    private String saleDealerName;

    private Integer useScope;

    private String useScopeName;

    /**
     * 是否本店购买
     */
    private Integer isOurShopPurchase;

}
