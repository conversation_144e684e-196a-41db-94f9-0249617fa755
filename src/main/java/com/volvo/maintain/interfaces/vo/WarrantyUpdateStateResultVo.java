package com.volvo.maintain.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 延保产品-激活、失效vo
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
@Data
@Builder
@ApiModel("延保产品-激活、失效vo")
public class WarrantyUpdateStateResultVo {

    @ApiModelProperty(name = "giveStatus", value = "状态")
    private Integer giveStatus;

    @ApiModelProperty(name = "orderCode", value = "订单编号")
    private String orderCode;

    @ApiModelProperty(name = "message", value = "信息")
    private String message;
}
