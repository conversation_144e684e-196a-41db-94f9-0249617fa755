package com.volvo.maintain.interfaces.vo.white;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "VehicleHealthCheckWhiteListVo", description = "白名单参数")
@Builder
public class VehicleHealthCheckWhiteListVo {
    /**
    * id
    */
    private Integer id;

    /**
    * 经销商code
    */
    private String ownerCode;

    /**
     * 集团code
     */
    private String groupCode;

    /**
    * 经销商code
    */
    private String ownerName;

    /**
     * vin
     */
    private String vin;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
    * 状态
    */
    private Integer state;

    /**
     * 模块类型
     */
    private Integer modType;

    /**
     * 名单类型
     */
    private Integer rosterType;

    /**
     * 业务描述
     */
    private String description;

    private String businessDescription;

    /**
     * 参数代码
     */
    private String itemCode;


    /**
    * 是否删除
    */
    private Integer isDeleted;

    /**
    * 创建人
    */
    private String createdBy;

    /**
    * 创建时间
    */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
    * 修改人
    */
    private String updatedBy;

    /**
    * 修改时间
    */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;
}