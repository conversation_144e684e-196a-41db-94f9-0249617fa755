package com.volvo.maintain.interfaces.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class InsuranceBillVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投保单号
     */
    private String insuranceNo;


    /**
     * 商业险保险公司名称
     */
    private String viInsuranceName;


    /**
     * 商业险启止日期
     */
    private String insuranceEnd;


    /**
     * 商业险结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date viFinishDate;


    /**
     * 交强单号
     */
    private String clivtaBillNo;


    /**
     * 交强险保险公司名称
     */
    private String clivtaInsuranceName;


    /**
     * 交强险启止日期
     */
    private String clivtaDateEnd;


    /**
     * 交强险结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date clivtaFinishDate;


}
