package com.volvo.maintain.interfaces.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("标签信息")
public class CdpTagInfoVo {

    /**
     * 主键ID
     */
    private Long id;



    /**
     * 标签ID
     */
    private String tagId;


    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签名称
     */
    private String tagValue;

    /**
     * 标签类型
     */
    private String tagType;


}
