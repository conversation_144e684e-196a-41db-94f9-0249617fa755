package com.volvo.maintain.interfaces.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ExtendedWarrantyProductVo  implements Serializable {


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 产品件号
     */
    private String productNo;

    /**
     * 产品件号
     */
    private List<String> productNos;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 延保提供方 PICC/易保
     */
    private Integer provider;

    /**
     * 延保类型
     */
    private Integer type;

    /**
     * 延保年数
     */
    private Integer numberOfYears;

    /**
     * 适用车型
     */
    private String modelName;

    /**
     * 适用车型代码
     */
    private String modelCode;

    /**
     * 保修部件
     */
    private Integer warrantyComponents;

    /**
     * 新旧类型
     */
    private Integer newOldType;

    /**
     * 车龄下限
     */
    private Integer ageLowerLimit;

    /**
     * 车龄上限
     */
    private Integer ageUpperLimit;

    /**
     * 条件关系 And/Or
     */
    private Integer relation;

    /**
     * 里程下限
     */
    private Integer mileageLowerLimit;

    /**
     * 里程上限
     */
    private Integer mileageUpperLimit;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     * 业务类型: 83441001:车险   83441002:非车险
     */
    private Integer bizType;

    /**
     * 延保产品关联的发动机号列表
     */
    private List<String> engineCodeList;

    /**延保产品关联的发动机号字符串：发动机号通过，拼接*/
    private String engineCodesStr;

    /**
     * 是否限制车龄里程范围: 10041001:是   10041002:否
     */
    private Integer isLimit;

    /**
     * 仅购车门店可售,1004,default 10041002
     */
    private Integer isPurchaseStore;

    /**
     * 是否包含轮胎规格参数
     */
    private Integer isTyre;
}
