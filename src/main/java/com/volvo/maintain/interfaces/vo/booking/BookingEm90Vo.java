package com.volvo.maintain.interfaces.vo.booking;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 预约单信息Vo
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiModel("预约单信息Vo")
@Data
public class BookingEm90Vo {

    @ApiModelProperty(value = "车牌号",name = "LICENSE")
    private String license;

    @ApiModelProperty(value = "车架号",name = "vin")
    private String vin;

    @ApiModelProperty(value = "经销商代码",name = "ownerCode")
    private String ownerCode;

    @ApiModelProperty(value = "预约单号",name = "bookingOrderNo")
    private String bookingOrderNo;

    @ApiModelProperty(value = "中台预约单号",name = "appointmentId")
    private String appointmentId;

    @ApiModelProperty(value = "预约登记时间",name = "acceptBookingDate")
    private String acceptBookingDate;

    @ApiModelProperty(value = "车型",name = "model")
    private String model;

    @ApiModelProperty(value = "预约进厂时间",name = "bookingComeTime")
    private String bookingComeTime;

    @ApiModelProperty(value = "预约类别",name = "bookingTypeCode")
    private String bookingTypeCode;

    @ApiModelProperty(value = "预约单创建时间",name = "createdAt")
    private String createdAt;

    @ApiModelProperty(value = "预约状态",name = "bookingOrderStatus")
    private String bookingOrderStatus;

    @ApiModelProperty(value = "下单人UserId",name = "em90UserId")
    private String em90UserId;

    @ApiModelProperty(value = "下单人Mobile",name = "em90UserMobile")
    private String em90UserMobile;
}
