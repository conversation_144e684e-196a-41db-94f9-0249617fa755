package com.volvo.maintain.interfaces.vo;

import cn.hutool.json.JSONArray;
import com.volvo.maintain.application.maintainlead.dto.carebuy.SaveCareBuyedDto;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceBillListDto;
import com.volvo.maintain.interfaces.vo.carebuy.CareBuyedVo;
import com.volvo.maintain.interfaces.vo.coupon.CouponDetailVO;
import com.volvo.maintain.interfaces.vo.rights.DealerCarRightsVo;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 功能描述：NB标签信息
 *
 * <AUTHOR>
 * @date 2024/01/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("NB标签信息")
public class NbTagInfoVo {

    /**
     * 主键ID
     */
    private Long id;


    /**
     * 标签ID
     */
    private String tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签值
     */
    private String  tagValue;

    /**
     * 详情集合
     */
    private List<NbTagInfoVo> detailTagInfoVOS;

    /**
     * NB定制化详情集合
     */
    private JSONArray nbDetailTagInfoVOS;


    /**
     * NB定制化详情集合
     */
    private List<List<CdpTagInfoVo>> cdpTagInfoVOS;

    /**
     * 保养套餐详情集合
     */
    private List<CareBuyedVo> careBuyedDtoList;

    /**
     * 延保详情集合
     */
    private List<ExtendedWarrantyVo> warrantyInfoData;

    /**
     * 保险详情集合
     */
    private List<InsuranceBillListDto> insuranceBillList;

    /**
     * 卡券详情集合
     */
    private List<CouponDetailVO> couponDetailVO;

    /**
     * 经销商权益集合
     */
    private List<DealerCarRightsVo> dealerCarRightsVos;
    /**
     * 非车险集合
     */
    private List<NonCarInsuranceVo> nonCarInsuranceVoList;
    /**
     * 原厂权益集合8n1
     */
    private List<EquityInformationVo> equityInformationList;
}
