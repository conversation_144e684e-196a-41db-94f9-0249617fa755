package com.volvo.maintain.interfaces.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class ParsingDataVO implements Serializable , IExcelModel, IExcelDataModel {

    /**
     * VIN 码
     */
    @Excel(name = "VIN", orderNum = "1")
    private String vin;
    
    /**
     * remark(失败原因)
     */
    private String remark;
    
    /**
     * remark(失败原因)
     */
    private String errorMsg;
    
    /**
     * VIN 码
     */
    private Integer rowNum;
}
