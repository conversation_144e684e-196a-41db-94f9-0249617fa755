package com.volvo.maintain.interfaces.vo.booking;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 预约单返回参数Vo
 * <AUTHOR>
 * @since 2020-04-15
 */
@ApiModel("预约单返回信息Vo")
@Data
public class BookingOrderResponseVo {

    @ApiModelProperty(value = "未确认列表",name = "bookingOrderConfirm")
    private List<BookingEm90Vo> bookingOrderConfirm;

    @ApiModelProperty(value = "未进厂列表",name = "bookingOrderEnter")
    private List<BookingEm90Vo> bookingOrderEnter;

}
