package com.volvo.maintain.interfaces.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("弹窗提示信息")
public class MessagePopupVo {


    /**
     * 标签ID   保群组车型id
     */
    private String tagId;


    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签名称  默认为true
     */
    private String tagValue;

    /**
     * 值显示类型
     */
    private Integer showType;
    /**
     * 业务类型
     */
    private String businessType;

}
