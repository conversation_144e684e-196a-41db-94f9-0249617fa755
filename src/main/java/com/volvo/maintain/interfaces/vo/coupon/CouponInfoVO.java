package com.volvo.maintain.interfaces.vo.coupon;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.volvo.maintain.application.maintainlead.dto.coupon.SaveCouponInfoDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 卡券表
 *
 * <AUTHOR>
 * @since 2020-05-21
 */
@Data
@ApiModel(value = "CouponInfoVO", description = "CouponInfoVO")
public class CouponInfoVO extends SaveCouponInfoDto {
    @ApiModelProperty(value = "id", required = true)
    private Long id;

    @ApiModelProperty(value = "创建时间 yyyy-MM-dd HH:mm:ss", dataType = "java.lang.String")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;

}