package com.volvo.maintain.interfaces.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
@ApiModel("车辆、客户及关联客户信息显示列表查询的结果Vo")
public class VehicleConnectingOwnersInfoVo {
    /**
     * 中间表主键
     */
    @ApiModelProperty(value = "voId",name = "voId")
    private Integer voId;

    /**
     * 关联客户信息标识
     */
    @ApiModelProperty(value = "关联客户信息",name = "sign")   //必须存储车主ownerNo
    private String sign;

    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    @ApiModelProperty(value = "VIN",name = "vin")
    private String vin;

    @ApiModelProperty(value = "VINForShow",name = "vinForShow")
    private String vinForShow;

    @ApiModelProperty(value = "oneId",name = "oneId")
    private Long oneId;

    @ApiModelProperty(value = "客户编号",name = "ownerNo")
    private String ownerNo;

    @ApiModelProperty(value = "客户姓名",name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "客户姓名(脱敏)",name = "ownerNameForShow")

    private String ownerNameForShow;
    @ApiModelProperty(value = "数据类型",name = "dataType")
    private String dataType;

    @ApiModelProperty(value = "性别",name = "gender")
    private Integer gender;

    @ApiModelProperty(value = "联系人",name = "contactorName")
    private String contactorName;

    @ApiModelProperty(value = "是否沃世界绑定(是否)",name = "isWorldBingding")
    private Integer isWorldBingding;

    @ApiModelProperty(value = "微信号",name = "wechatNumber")
    private String wechatNumber;

    @ApiModelProperty(value = "联系人手机    手机2",name = "contactorMobile")
    private String contactorMobile;

    @ApiModelProperty(value = "客户电话",name = "phone")
    private String phone;

    @ApiModelProperty(value = "客户手机   手机1",name = "mobile")
    private String mobile;
    @ApiModelProperty(value = "客户手机   手机1(脱敏)",name = "mobileForShow")
    private String mobileForShow;

    @ApiModelProperty(value = "客户性质",name = "ownerProperty")
    private Integer ownerProperty;

    @ApiModelProperty(value = "客户类型",name = "customerType")
    private Integer customerType;

    @ApiModelProperty(value = "与客户关系",name = "customerRelation")
    private Integer customerRelation;

    @ApiModelProperty(value = "最近进场时间",name = "roCreateDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date roCreateDate;

    @ApiModelProperty(value = "省份",name = "province")
    private Integer province;

    @ApiModelProperty(value = "省份",name = "city")
    private Integer city;

    @ApiModelProperty(value = "区县",name = "district")
    private Integer district;

    @ApiModelProperty(value = "邮箱",name = "eMail")
    private String eMail;

    @ApiModelProperty(value = "传真",name = "fax")
    private String fax;

    @ApiModelProperty(value = "地址",name = "address")
    private String address;

    @ApiModelProperty(value = "邮编",name = "zipCode")
    private String zipCode;

    @ApiModelProperty(value = "喜欢联系方式",name = "bestConcatType")
    private Integer bestConcatType;

    @ApiModelProperty(value = "职业",name = "position")
    private Integer position;

    @ApiModelProperty(value = "职称",name = "contactorPosition")
    private Integer contactorPosition;

    @ApiModelProperty(value = "备注",name = "remark")
    private String remark;

    @ApiModelProperty(value = "车型",name = "modelYear")
    private String model;

    @ApiModelProperty(value = "年款",name = "yearModel")
    private String yearModel;
    /**
     * 发动机号
     */
    @ApiModelProperty(value = "发动机号",name = "engineNo")
    private String engineNo;

    /**
     * 电机号1
     */
    @ApiModelProperty(value = "电机号1",name = "motorNo1")
    private String motorNo1;

    /**
     * 电机号2
     */
    @ApiModelProperty(value = "电机号2",name = "motorNo2")
    private String motorNo2;

    /**
     * 动力形式(燃油车、新能源)
     */
    @ApiModelProperty(value = "动力形式",name = "dynamicCode")
    private Integer dynamicCode;

    /**
     * 品牌  厂牌
     */
    @ApiModelProperty(value = "品牌", name = "brand")
    private String brand;

    /**
     * 车系
     */
    @ApiModelProperty(value = "车系" , name = "series")
    private String series;


    /**
     * 车型年款
     */
    @ApiModelProperty(value = "车款" ,name = "modelYear")
    private String modelYear;

    /**
     * 变速箱箱号
     */
    @ApiModelProperty(value = "变速箱" ,name = "gearBox")
    private String gearBox;


    /**
     * 建档日期
     */
    @ApiModelProperty(value = "年款" ,name = "foundDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date foundDate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改日期" ,name = "systemUpdateDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date systemUpdateDate;

    /**
     * 经销商
     */
    @ApiModelProperty(value = "销售经销商" ,name = "salesAgentName")
    private String salesAgentName;

    /**
     * 服务专员
     */
    @ApiModelProperty(value = "上次SA" ,name = "roServiceAdvisor")   //tt_reapir_order表 serviceAdvisor
    private String roServiceAdvisor;


    /**
     * 水货车(是 否)1004
     */
    @ApiModelProperty(value = "水货车" ,name = "smuggledGoodsVehicle")
    private Integer smuggledGoodsVehicle;

    /**
     * 选装(销售主数据)
     */
    @ApiModelProperty(value = "选装" ,name = "optionPackag")       //重新添加字段
    private String optionPackag;


    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人" ,name = "updatedBy")
    private String updatedBy;

    /**
     * 配置
     */
    @ApiModelProperty(value = "配置",name = "apackage" )
    private String apackage;

    /**
     * 配置
     */
    @ApiModelProperty(value = "配置",name = "apackageCode" )
    private String apackageCode;

    /**
     * 配置
     */
    @ApiModelProperty(value = "配置",name = "config" )
    private String config;

    /**
     * 车型代码
     */
    @ApiModelProperty(value = "车型代码",name = "modelCode" )
    private String modelCode;

    /**
     * MODEL_CODE的前3位字符
     */
    @ApiModelProperty(value = "MODEL_CODE的前3位字符",name = "seriesCode" )
    private String seriesCode;

    /**
     * 品牌代码
     */
    @ApiModelProperty(value = "品牌代码",name = "brandCode" )
    private String brandCode;

    /**
     * 销售日期
     */
    @ApiModelProperty(value = "销售日期",name = "salesDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date salesDate;

    /**
     * 车辆性质
     */
    @ApiModelProperty(value = "车辆性质",name = "businessKind")
    private Integer businessKind;



    /**
     * 车辆用途
     */
    @ApiModelProperty(value = "车辆用途",name = "vehiclePurpose")
    private Integer vehiclePurpose;

    /**
     * 外饰色
     */
    @ApiModelProperty(value = "外饰色",name = "color")
    private String color;

    /**
     * 内饰色
     */
    @ApiModelProperty(value = "内饰色",name = "innerColor")
    private String innerColor;


    /**
     * 燃油类别
     */
    @ApiModelProperty(value = "燃料类别",name = "fuelType")
    private Integer fuelType;

    /**
     * 排量
     */
    @ApiModelProperty(value = "排量",name = "engineDesc")
    private String engineDesc;

    /**
     * 排放标准
     */
    @ApiModelProperty(value = "排放标准",name = "dischargeStandard")
    private Integer dischargeStandard;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期",name = "productDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date productDate;

    /**
     * 钥匙号
     */
    @ApiModelProperty(value = "钥匙号",name = "keyNumber")
    private String keyNumber;

    /**
     * 产地
     */
    @ApiModelProperty(value = "产地",name = "productingArea")
    private String productingArea;

    /**
     * 厂牌型号
     */
    @ApiModelProperty(value = "厂牌型号",name = "brandModel")
    private String brandModel;



    /**
     * 行驶证号
     */
    @ApiModelProperty(value = "行驶证号",name = "drivingLicense")
    private String drivingLicense;


    /**
     * 销售顾问
     */
    @ApiModelProperty(value = "销售顾问",name = "consultant")
    private String consultant;

    /**
     * 是否本公司购车
     */
    @ApiModelProperty(value = "本经销商购车",name = "isSelfCompany")
    private Integer isSelfCompany;




    /**
     * 上牌日期
     */
    @ApiModelProperty(value = "上牌日期",name = "licenseDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date licenseDate;

    /**
     * 指定技师
     */
    @ApiModelProperty(value = "指定技师",name = "chiefTechnician")
    private String chiefTechnician;

    /**
     * 服务专员
     */
    @ApiModelProperty(value = "服务顾问",name = "serviceAdvisor")
    private String serviceAdvisor;

    /**
     * 续保专员
     */
    @ApiModelProperty(value = "续保专员",name = "insuranceAdvisor")
    private String insuranceAdvisor;

    /**
     * 定保专员
     */
    @ApiModelProperty(value = "定保专员",name = "maintainAdvisor")
    private String maintainAdvisor;

    /**
     * 客服专员
     */
    @ApiModelProperty(value = "客服专员",name = "dcrcAdvisor")
    private String dcrcAdvisor;


    /**
     * 优惠截止日期
     */
    @ApiModelProperty(value = "优惠截止日期",name = "discountExpireDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date discountExpireDate;

    /**
     * 优惠模式代码
     */
    @ApiModelProperty(value = "优惠模式",name = "discountModeCode")
    private String discountModeCode;


    /**
     * 购买方式
     */
    @ApiModelProperty(value = "购买方式",name = "waysToBuy")
    private Integer waysToBuy;

    /**
     * 置换意向车型
     */
    @ApiModelProperty(value = "置换意向车型",name = "replaceIntentModel")
    private String replaceIntentModel;

    /**
     * 置换日期
     */
    @ApiModelProperty(value = "置换日期",name = "replaceDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date replaceDate;


    /**
     * 重购日期
     */
    @ApiModelProperty(value = "置换日期",name = "rebuyDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date rebuyDate;

    /**
     * 提车日期
     */
    @ApiModelProperty(value = "提车日期",name = "vehicleDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date vehicleDate;

    /**
     * 营运信息
     */
    @ApiModelProperty(value = "营运信息",name = "operateMessage")
    private String operateMessage;

    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期",name = "invoiceDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date invoiceDate;


    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否激活",name = "isValid")
    private Integer isValid;

    @ApiModelProperty(value = "上次进厂里程",name = "lastInFactorMileage")
    private String lastInFactorMileage;

    @ApiModelProperty(value = "行驶里程",name = "mileage")
    private Double mileage;
}
