package com.volvo.maintain.interfaces.vo;

import com.volvo.maintain.application.maintainlead.dto.OrderTagSnapshotDto;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("换购意向信息")
public class PurchaseIntentionVo {


    /**
     * 标签ID   保客营销群组车型id
     */
    private String tagId;


    /**
     * 标签名称  保客营销默认为增换购
     */
    private String tagName;

    /**
     * 标签名称  保客营销默认为true
     */
    private String tagValue;

    /**
     * 值显示类型
     */
    private Integer showType;

    /**
     * 推荐车型
     */
    private List<String> recommendedCarModels;


    /**
     *  推荐理由
     */
    private List<String> reasonsForRecommendation;

    /**
     * 推荐话术
     */
    private List<String> recommendedScript;

    /**
     * 跟进历史
     */
    private List<OrderTagSnapshotDto> followHistory;

    private String detailTagPid;
}
