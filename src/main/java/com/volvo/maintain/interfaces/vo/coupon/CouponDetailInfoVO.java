package com.volvo.maintain.interfaces.vo.coupon;

import com.volvo.maintain.application.maintainlead.dto.CollectRangeDto;
import com.volvo.maintain.application.maintainlead.dto.coupon.UseRuleDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 卡券明细表 卡券领取
 *
 * <AUTHOR>
 * @since 2020-05-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "CouponDetailInfoVO", description = "CouponDetailInfoVO")
public class CouponDetailInfoVO extends CouponInfoVO {

    @ApiModelProperty(value = "卡券代码")
    private String couponCode;
    @ApiModelProperty(value = "卡券名称")
    private String couponName;
    @ApiModelProperty(value = "主档编号(使用场景为<权益激活>类的卡券 值必有)")
    private String rightsNo;
    @ApiModelProperty(value = "主档名称(使用场景为<权益激活>类的卡券 值必有)")
    private String rightsName;
    @ApiModelProperty(value = "适用权益 (官方品牌充电站有值:useScenes值为:83171011)")
    private Integer channelCardType;
    @ApiModelProperty(value = "适用充电站93061001 沃尔沃汽车上海虹桥机场尊享充电站93061002 沃尔沃汽车深圳宝安机场尊享充电站93061003 沃尔沃汽车深圳卓悦中心尊享充电站(详见couponInfoExtDO 类)(使用场景为品牌充电站的券必有)")
    private Integer chargingStation;
    @ApiModelProperty(value = "使用渠道(0:全部 null也是全部 1:APP 2:小程序 )")
    private Integer useChannel;
    @ApiModelProperty(value = "适用机型(1 :7KW/220V 交流充电桩 2 :11KW/220V 交流充电桩) 充电桩的券，这个参数必有")
    private Integer applyType;
    @ApiModelProperty(value = "oneId")
    private Integer oneId;
    @ApiModelProperty(value = "memberId")
    private Integer memberId;
    @ApiModelProperty(value = "状态名称(去使用、已过期 、已失效等)")
    private String ticketStateName;
    @ApiModelProperty(value = "卡券类型名称(充电服务券、门店礼品券、门店优惠券、商城优惠券、商城礼品券等等)")
    private String couponTypeName;
    @ApiModelProperty(value = "卡券类型(31081001 抵用折扣券 31081002 消费券 31081003 折扣券 31081004 储值卡 31081005 优惠券 31081006 兑换券)")
    private Integer couponType;
    @ApiModelProperty(value = "优惠券类型(83311001 抵用 83311002 打折)")
    private Integer offerType;
    @ApiModelProperty(value = "使用场景(83171001:线上商城 83171002:线下门店 83171003:通用 83171004:E代驾 83171005: 权益激活 83171006:线下零售 83171007:卡密 83171008:品牌充电站 83171009:一键加电 83171010:充电桩 83171011:官方品牌充电站)")
    private Integer useScenes;
    @ApiModelProperty(value = "卡券面额")
    private Double couponValue;
    @ApiModelProperty(value = "卡券原始面额")
    private Double couponYuanValue;
    @ApiModelProperty(value = "卡券折扣")
    private Integer couponDiscount;
    @ApiModelProperty(value = "卡券满减(满)")
    private Integer couponFull;
    @ApiModelProperty(value = "卡券描述")
    private String denomination;
    @ApiModelProperty(value = "期限类型（字典）有效期类型【82851001 期限 82851002 时间】")
    private Integer termType;
    @ApiModelProperty(value = "使用期限（单位月）自领取日起")
    private Integer term;
    @ApiModelProperty(value = "开始时间（yyyy-MM-dd HH:mm:ss）")
    private String startDate;
    @ApiModelProperty(value = "结束时间（yyyy-MM-dd HH:mm:ss）")
    private String endDate;
    @ApiModelProperty(value = "是否是商品")
    private Integer asGoods;
    @ApiModelProperty(value = "是否上架(未上架:31091001 已上架:31091002)")
    private Integer asList;
    @ApiModelProperty(value = "可领取总数")
    private Integer totalGet;
    @ApiModelProperty(value = "已领取数")
    private Integer tokenCount;
    @ApiModelProperty(value = "是否可无限领取(否:31101001 是:31101002)")
    private Integer existLimit;
    @ApiModelProperty(value = "每人最大可领取数")
    private Integer maxLimit;
    @ApiModelProperty(value = "用途描述")
    private String couponExplain;
    @ApiModelProperty(value = "卡券图片")
    private String couponImg;
    @ApiModelProperty(value = "卡券创建日期 yyyy-MM-dd HH:mm:ss")
    private String couponCreateDate;
    @ApiModelProperty(value = "卡券业务类型(MKT:31111001 售后券、:31111002 销售券:31111003)")
    private Integer serviceType;
    @ApiModelProperty(value = "是否需要激活（否:31121001 是:31121002）")
    private Integer activationRequired;
    @ApiModelProperty(value = "是否被占用(0:否 1:是)")
    private Integer isOccupied;
    @ApiModelProperty(value = "是否亲善(0:否 1:是)")
    private Integer kindness;
    @ApiModelProperty(value = "占用编号")
    private Integer occupyNumber;
    @ApiModelProperty(value = "占用名称")
    private String occupyName;
    @ApiModelProperty(value = "占用来源(33041001:活动管理 33041002:内容管理 33041003:抽奖小游戏 33041004:首页 33041005:会员任务 33041006:会员权益 33041007:会员成就)")
    private Integer occupySource;
    @ApiModelProperty(value = "卡券状态（82841001 未提交 ,82841002 提交待确认 ,82841003 已确认 ,82841004 已过期 ,82841005 已停用,）")
    private Integer publishState;
    @ApiModelProperty(value = "创建人姓名")
    private String creator;
    @ApiModelProperty(value = "是否被作废(0:否 1:是)")
    private Integer isVolid;
    @ApiModelProperty(value = "激活条件（字典）")
    private Integer activationConditions;
    @ApiModelProperty(value = "创建时间(开始)")
    private String createTimeBegin;
    @ApiModelProperty(value = "创建时间(结束)")
    private String createTimeEnd;
    @ApiModelProperty(value = "卡券编号/卡券名/创建人")
    private String couponCodeNameCreator;
    @ApiModelProperty(value = "被其他渠道绑定的卡券（1：是 0：否）")
    private Integer isBind;
    @ApiModelProperty(value = "创建人")
    private Long createBy;
    @ApiModelProperty(value = "领用对象描述")
    private String receivingObjectDesc;
    @ApiModelProperty(value = "领取范围")
    private CollectRangeDto collectRangeDto;

    @ApiModelProperty(value = "useRule", required = true)
    private UseRuleDto useRuleDto;
    /**
     * 卡券领用
     */
    @ApiModelProperty(value = "id", required = true)
    private Long id;
    @ApiModelProperty(value = "tt_coupon_info表ID")
    private Long couponId;
    @ApiModelProperty(value = "tt_customer_info表ID")
    private Integer customerId;
    @ApiModelProperty(value = "余额")
    private Integer leftValue;
    @ApiModelProperty(value = "vin")
    private String vin;
    @ApiModelProperty(value = "卡券状态(31061000:未领取 ,31061001:已领取, 31061002:已锁定, 31061003:已使用, 31061004:已过期, 31061005:已作废)")
    private Integer ticketState;
    @ApiModelProperty(value = "获得时间")
    private String getDate;
    @ApiModelProperty(value = "激活时间")
    private String activateDate;
    @ApiModelProperty(value = "锁定单据号")
    private String lockOrderNo;
    @ApiModelProperty(value = "单据类型")
    private Integer orderType;
    @ApiModelProperty(value = "最后使用时间")
    private String lastUseDate;
    @ApiModelProperty(value = "兑换码 核销码")
    private String exchangeCode;
    @ApiModelProperty(value = "活动编号")
    private String activityCode;
    @ApiModelProperty(value = "激活状态（已激活:31071001 未激活:31071002）")
    private Integer activeState;
    @ApiModelProperty(value = "二维码")
    private String qrCodeUrl;
    @ApiModelProperty(value = "卡券失效日期")
    private String expirationDate;
    @ApiModelProperty(value = "卡券来源(83241001 VCDC发券 83241002 沃世界领券 83241003 商城购买 83241004 MA发券 83241005 售后经销商发券 83241006:新车发券 83241007:会员权益领取 83241008:充电业务发券)")
    private Integer couponSource;
    @ApiModelProperty(value = "来源编号", required = true)
    private Integer sourceNumber;
    @ApiModelProperty(value = "商品类型 (1:主商品 2:副商品) ")
    private Integer productType;

}