package com.volvo.maintain.interfaces.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("车主信息查询结果")
public class OwnerInfoResultsVo {

    @ApiModelProperty(value = "车主名称", name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "性别",name = "gender")
    private Integer gender;
    @ApiModelProperty(value = "生日",name = "birthday")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date birthday;
    @ApiModelProperty(value = "客户属性",name = "ownerProperty")
    private Integer ownerProperty;
    @ApiModelProperty(value = "车主手机1",name = "mobileFirst")
    private String mobileFirst;
    @ApiModelProperty(value = "车主手机2",name = "mobileSecond")
    private String mobileSecond;
    @ApiModelProperty(value = "车主电话",name = "phone")
    private String phone;
    @ApiModelProperty(value = "车主微信号",name = "wechatNumber")
    private String wechatNumber;
    @ApiModelProperty(value = "车主职业",name = "position")
    private Integer position;
    @ApiModelProperty(value = "车主地址",name = "address")
    private String address;

}
