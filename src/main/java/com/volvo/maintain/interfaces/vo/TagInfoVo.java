package com.volvo.maintain.interfaces.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 功能描述：标签信息
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("标签信息")
public class TagInfoVo {

    /**
     * 主键ID
     */
    private Long id;



    /**
     * 标签ID
     */
    private String tagId;

    /**
     * 内部标签ID
     */
    private String innerTagId;


    /**
     * 标签名称
     */
    private String tagName;


    /**
     * 显示名称
     */
    private String showName;
    /**
     * 标签类型
     */
    private String tagType;

    /**
     * 标签来源
     */
    private Integer tagSource;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 是否支持查看详情
     */
    private Integer isGetDetail;

    /**
     * 详情获取类型
     */
    private Integer detailGetType;

    /**
     * 是否是详情标签
     */
    private Integer isDetailTag;

    /**
     * 详情标签父tag_id
     */
    private String detailTagPid;

    /**
     * 展示优先级
     */
    private Integer showLevel;

    /**
     * 是否命中规则
     */
    private Integer ruleFlag = 10041001;

    /**
     * 板块类型：1/2/3
     */
    private Integer blockType;

    /**
     * 展示一级板块
     */
    private Integer  showFirstBlock;

    /**
     * 展示二级板块
     */
    private Integer  showSecondBlock;


    /**
     * 展示三级板块
     */
    private Integer  showThirdBlock;

    /**
     * 展示四级板块
     */
    private Integer  showFourthBlock;

    /**
     * 展示五级板块
     */
    private Integer  showFifthBlock;

    /**
     * 展示六级板块
     */
    private Integer  showSixthBlock;

    /**
     * 值规则
     */
    private String  valueRule;

    /**
     * 排序
     */
    private Integer showSort;

    /**
     * 标签值
     */
    private String  tagValue;

    /**
     * 是否标签
     */
    private Integer isTag;

    /**
     * 值获取类型
     */
    private Integer getType;

    /**
     * 值显示类型
     */
    private Integer showType;
    /**
     * 创建人
     */
    private String  createdBy;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 更新人
     */
    private String  updatedBy;

    /**
     * 更新时间
     */
    private String  updatedAt;

    /**
     * 是否删除
     */
    private Integer  isDeleted;

    /**
     * 详情集合
     */
    private List<TagInfoVo> detailTagInfoVOS;



    /**
     * 转译集合
     */
    private List<TagInfoVo> translationData;

    /**
     * 运算规则
     */
    private List<String> operator;

    /**
     * 对比值
     */
    private List<String> oddsRatio;

}
