package com.volvo.maintain.interfaces.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 校验信息入参
 */
@Data
public class CheckFinalInspectionReqVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 经销商 */
    private String ownerCode;

    /** 工单号 */
    private String roNo;
    
    /** 交修项目id */
    private String repairProjectId;
    
    /** 交修项目编号 */
    private String repairProjectCode;

    /** 交修项目编号 */
    private String repairProjectName;
    
	/**
	 * 保修部件一级
	 */
    private String repairPartsLevel1;
    /**
     * 保修部件二级
     */
    private String repairPartsLevel2;
    /**
     * 维修类型
     */
    private String repairTypeCode;
    /**
     * 维修现象
     */
    private String faultPhenomenon;
    /**
     * 是否不修
     */
    private String isNotRepaired;
    /**
     * 文件类型
     */
    private List<String> attTypeList;
} 