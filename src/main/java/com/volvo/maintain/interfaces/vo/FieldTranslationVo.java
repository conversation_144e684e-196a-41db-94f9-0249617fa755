package com.volvo.maintain.interfaces.vo;

import com.volvo.maintain.application.maintainlead.dto.TagValueRuleDto;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 功能描述：字段转译详情信息
 *
 * <AUTHOR>
 * @date 2024/01/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("字段转译详情信息")
public class FieldTranslationVo {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 标签ID
     */
    private String tagId;


    /**
     * 标签名称
     */
    private String tagName;


    /**
     * 显示名称
     */
    private String showName;
    /**
     * 转译规则
     */
    private TagValueRuleDto.ConvertDto convert;

    /**
     * 字段集合
     */
    private List<TagInfoVo> fieldInfoList;
}
