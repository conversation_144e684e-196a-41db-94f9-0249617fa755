package com.volvo.maintain.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;

public class EmpVo {
    @ApiModelProperty("邮箱")
    private String eMail;
    @ApiModelProperty("人员id")
    private String empId;
    @ApiModelProperty("openId")
    private String openId;
    @ApiModelProperty("账号id")
    private String userId;
    @ApiModelProperty("微信手机号")
    private String wechatPhone;

    public EmpVo() {
    }

    public String getEMail() {
        return this.eMail;
    }

    public String getEmpId() {
        return this.empId;
    }

    public String getOpenId() {
        return this.openId;
    }

    public String getUserId() {
        return this.userId;
    }

    public String getWechatPhone() {
        return this.wechatPhone;
    }

    public void setEMail(final String eMail) {
        this.eMail = eMail;
    }

    public void setEmpId(final String empId) {
        this.empId = empId;
    }

    public void setOpenId(final String openId) {
        this.openId = openId;
    }

    public void setUserId(final String userId) {
        this.userId = userId;
    }

    public void setWechatPhone(final String wechatPhone) {
        this.wechatPhone = wechatPhone;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof EmpVo)) {
            return false;
        } else {
            EmpVo other = (EmpVo)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label71: {
                    Object this$eMail = this.getEMail();
                    Object other$eMail = other.getEMail();
                    if (this$eMail == null) {
                        if (other$eMail == null) {
                            break label71;
                        }
                    } else if (this$eMail.equals(other$eMail)) {
                        break label71;
                    }

                    return false;
                }

                Object this$empId = this.getEmpId();
                Object other$empId = other.getEmpId();
                if (this$empId == null) {
                    if (other$empId != null) {
                        return false;
                    }
                } else if (!this$empId.equals(other$empId)) {
                    return false;
                }

                label57: {
                    Object this$openId = this.getOpenId();
                    Object other$openId = other.getOpenId();
                    if (this$openId == null) {
                        if (other$openId == null) {
                            break label57;
                        }
                    } else if (this$openId.equals(other$openId)) {
                        break label57;
                    }

                    return false;
                }

                Object this$userId = this.getUserId();
                Object other$userId = other.getUserId();
                if (this$userId == null) {
                    if (other$userId != null) {
                        return false;
                    }
                } else if (!this$userId.equals(other$userId)) {
                    return false;
                }

                Object this$wechatPhone = this.getWechatPhone();
                Object other$wechatPhone = other.getWechatPhone();
                if (this$wechatPhone == null) {
                    if (other$wechatPhone == null) {
                        return true;
                    }
                } else if (this$wechatPhone.equals(other$wechatPhone)) {
                    return true;
                }

                return false;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof EmpVo;
    }

    public int hashCode() {
        int result = 1;
        Object $eMail = this.getEMail();
        result = result * 59 + ($eMail == null ? 43 : $eMail.hashCode());
        Object $empId = this.getEmpId();
        result = result * 59 + ($empId == null ? 43 : $empId.hashCode());
        Object $openId = this.getOpenId();
        result = result * 59 + ($openId == null ? 43 : $openId.hashCode());
        Object $userId = this.getUserId();
        result = result * 59 + ($userId == null ? 43 : $userId.hashCode());
        Object $wechatPhone = this.getWechatPhone();
        result = result * 59 + ($wechatPhone == null ? 43 : $wechatPhone.hashCode());
        return result;
    }

    public String toString() {
        return "EmpVo(eMail=" + this.getEMail() + ", empId=" + this.getEmpId() + ", openId=" + this.getOpenId() + ", userId=" + this.getUserId() + ", wechatPhone=" + this.getWechatPhone() + ")";
    }
}
