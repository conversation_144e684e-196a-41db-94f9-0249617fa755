package com.volvo.maintain.interfaces.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 异常信息返回
 */
@Data
public class CheckFinalInspectionRespVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 经销商 */
    private String ownerCode;

    /** 工单号 */
    private String roNo;

    /** 交修项目明细 */
    private List<CheckFinalInspectionHandProjecRespVO> finalInspectionHandProjecList;
} 