package com.volvo.maintain.interfaces.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("客户查询Vo")
public class OwnerQueryParamsVo {

    @ApiModelProperty(hidden = true)
    private String ownerCode;

    /**
     * 车主信息
     */
    @ApiModelProperty(value = "车主信息",name = "ownerNo")
    private String ownerInfo;

    @ApiModelProperty(value = "车辆",name = "vin")
    private String vin;

    @ApiModelProperty(value = "排序字段",name = "sort")
    private String sort;

    @ApiModelProperty(value = "排序类型",name = "sortType")
    private String sortType;

    @ApiModelProperty(value = "判断是否为pad",name = "isPad")
    private String isPad;
}
