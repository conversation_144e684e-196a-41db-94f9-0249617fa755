package com.volvo.maintain.interfaces.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class InvoiceDetailDTO {


    /**
     * 车架号(VIN)
     **/
    private String cjh;

    /**
     * 发票代码
     **/
    private String fpdm;

    /**
     * 发票号码
     **/
    private String fphm;

    /**
     * 发票日期
     **/
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String kprq;

    /**
     * 发票金额
     **/
    private String fpje;


    /**
     * 发票状态
     **/
    private String invoiceStatus;


    /**
     * 正票发票号码
     **/
    private String zpfphm;


    /**
     * 正票类别代码
     **/
    private String zplbdm;

    /**
     * 购方名称
     */
    private String gfmc;
    /**
     * 身份证号码
     */
    private String sfzhm;
}
