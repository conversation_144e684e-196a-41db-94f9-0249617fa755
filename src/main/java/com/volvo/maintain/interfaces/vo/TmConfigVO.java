package com.volvo.maintain.interfaces.vo;


import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class TmConfigVO {


    /**
     * id
     **/
    private Long id;

    /**
     * 建议零售价
     **/
    private Double msrp;

    /**
     * 配置年款
     */
    private String configYear;

    /**
     * 配置code
     */
    private String configCode;

    /**
     * 是否直售（1：是  0：否）
     */
    private Integer isDirect;
}
