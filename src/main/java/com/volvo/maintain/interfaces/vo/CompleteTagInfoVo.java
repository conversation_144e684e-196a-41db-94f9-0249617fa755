package com.volvo.maintain.interfaces.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("全量标签信息")
public class CompleteTagInfoVo {



    /**
     * 板块id
     */
    private Integer plateId;

    /**
     * 板块name
     */
    private String plateName;

    /**
     * 展现形式
     */
    private Integer presentationForm;
    /**
     * 本地标签
     */
    private List<TagInfoVo> tagInfoVOS;
    /**
     * 字段标签
     */
    private List<TagInfoVo> infoVOList;

    /**
     * 子版块集合
     */
    private List<CompleteTagInfoVo> completeTagInfoVOS;
}

