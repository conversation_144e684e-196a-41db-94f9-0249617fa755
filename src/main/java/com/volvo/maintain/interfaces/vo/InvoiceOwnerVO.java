package com.volvo.maintain.interfaces.vo;

import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class InvoiceOwnerVO {

    /**
     * 车主名称
     **/
    private String ownerName;

    /**
     * 证件号码
     **/
    private String certificateNo;

    /**
     * 开票时间
     **/
    private String invoiceDate;

    /**
     * 发票列表
     **/
    private List<InvoiceDetailDTO> jdcfphzDetailDTOS;

    /**
     * 价税合计 发票金额
     */
    private String taxAmountTotal;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 数电发票号码
     */
    private String allElectronicInvoiceNumber;

    /**
     * 发票状态
     */
    private String invoiceStatus;

    /**
     * 厂牌型号
     */
    private String brandModel;

}
