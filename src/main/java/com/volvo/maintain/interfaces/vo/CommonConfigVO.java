package com.volvo.maintain.interfaces.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CommonConfigVO {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 配置代码
     */
    @ApiModelProperty("配置代码")
    private String configCode;

    /**
     * 分组类型
     */
    @ApiModelProperty("分组类型")
    private String groupType;

    /**
     * 配置key
     */
    @ApiModelProperty("配置key")
    private String configKey;

    /**
     * 配置值
     */
    @ApiModelProperty("配置值")
    private String configValue;

    /**
     * 配置描述
     */
    @ApiModelProperty("配置描述")
    private String configDesc;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    private String configExt1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    private String configExt2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    private String configExt3;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 是否删除:1，删除；0，未删除
     */
    @ApiModelProperty("是否删除:1，删除；0，未删除")
    private Integer isDeleted;

    /**
     * 数据创建时间
     */
    @ApiModelProperty("数据创建时间")
    private Date createdAt;

    /**
     * 数据创建人
     */
    @ApiModelProperty("数据创建人")
    private String createdBy;

    /**
     * 数据修改时间
     */
    @ApiModelProperty("数据修改时间")
    private Date updatedAt;

    /**
     * 数据修改人
     */
    @ApiModelProperty("数据修改人")
    private String updatedBy;

    /**
     * 创建sql人
     */
    @ApiModelProperty("创建sql人")
    private String createSqlby;

    /**
     * 更新人sql人
     */
    @ApiModelProperty("更新人sql人")
    private String updateSqlby;
}
