package com.volvo.maintain.application.maintainlead.dto.part;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel("零件工单信息")
public class PartOrderDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 零件号
     */
    private String partNo;
    /**
     * 未入库总数量
     */
    private BigDecimal total;
    /**
     * 操作人
     */
    private String updateBy;
    /**
     * 零件采购价格
     */
    private BigDecimal claimPrice;

}
