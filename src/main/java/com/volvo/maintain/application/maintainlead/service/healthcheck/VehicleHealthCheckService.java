package com.volvo.maintain.application.maintainlead.service.healthcheck;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.volvo.maintain.application.maintainlead.dto.healthcheck.HealthCheckDto;
import com.volvo.maintain.application.maintainlead.vo.healthcheck.VehicleHealthRecordInfoVo;
import com.volvo.maintain.application.maintainlead.vo.healthcheck.VehicleHealthVo;

import java.util.List;

public interface VehicleHealthCheckService {


    /**
     * 查询最新一条健康检查报告 （批量）
     * @param dto 入参对象
     * @return 返回的数据
     */
    List<VehicleHealthRecordInfoVo> queryVehicleHealthCheckDetails(HealthCheckDto dto);

    /**
     * 根据车架号查询健康检查数据分页
     * @param vin 车架号
     * @param pageNum 分页参数
     * @param pageSize 分页参数
     * @return 分页数据
     */
    IPage<VehicleHealthVo> queryHealthByVin(String vin, Integer pageNum, Integer pageSize);
}
