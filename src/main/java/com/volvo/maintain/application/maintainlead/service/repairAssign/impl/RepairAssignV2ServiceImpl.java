package com.volvo.maintain.application.maintainlead.service.repairAssign.impl;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.PageDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderReqDTO;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderReqV2DTO;
import com.volvo.maintain.application.maintainlead.dto.repairAssign.RepairInfoDTO;
import com.volvo.maintain.application.maintainlead.dto.repairAssign.TtRoAssignDTO;
import com.volvo.maintain.application.maintainlead.service.repairAssign.RepairAssignV2Service;
import com.volvo.maintain.application.maintainlead.vo.FinalInspectionContentItemVO;
import com.volvo.maintain.application.maintainlead.vo.FinalInspectionContentVO;
import com.volvo.maintain.application.maintainlead.vo.FinalInspectionTreeVO;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderV2VO;
import com.volvo.maintain.application.maintainlead.vo.RoHandRepairProjectVO;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.util.RepairOrderSorterUtils;
import com.volvo.maintain.interfaces.vo.CheckFinalInspectionReqVO;
import com.volvo.maintain.interfaces.vo.CheckFinalInspectionRespVO;
import com.volvo.maintain.interfaces.vo.FinalInspectionAttVO;
import com.yonyou.cyx.function.exception.ServiceBizException;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class RepairAssignV2ServiceImpl implements RepairAssignV2Service {
	
	@Resource
	private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
	
	@Resource
	private DmscloudServiceFeign dmscloudServiceFeign;
	
	@Override
	public List<RepairOrderV2VO> queryRepairOrders(RepairOrderReqV2DTO repairOrderReq) {
		log.info("RepairAssignV2ServiceImpl queryRepairOrders ：{}", JSON.toJSONString(repairOrderReq));
		RepairOrderReqDTO repairOrderReqDTO = new RepairOrderReqDTO();
		repairOrderReqDTO.setOwnerCode(repairOrderReq.getOwnerCode());
		repairOrderReqDTO.setRoNo(repairOrderReq.getRoNo());
		
		DmsResponse<List<RepairOrderV2VO>> queryRepairOrders = domainMaintainOrdersFeign.queryRepairOrders(repairOrderReqDTO);
		log.info("queryRepairOrders: {}", JSON.toJSONString(queryRepairOrders));
		if(Objects.isNull(queryRepairOrders)) {
			throw new ServiceBizException("系统异常！");
		}
		if(queryRepairOrders.isFail()) {
			log.info("下游异常：{} ", JSON.toJSONString(queryRepairOrders));
			throw new ServiceBizException(queryRepairOrders.getErrMsg());
		}
		List<RepairOrderV2VO> data2 = queryRepairOrders.getData();
		try {
			String roNo = repairOrderReq.getRoNo();
			Set<String> collect = handleFinalInspectionTag(roNo);
			log.info("转换的set数据：{}", JSON.toJSONString(collect));
			data2 = queryRepairOrders.getData();
			data2.forEach(obj->{
				String key = String.join("-", obj.getOwnerCode(), obj.getRoNo());
				obj.setFinalInspectionTag("10041002");
				if(collect.contains(key)) {
					obj.setFinalInspectionTag("10041001");
				}
			});
			log.info("补偿完成");
		} catch (Exception e) {
			log.info("补偿状态异常：", e);
		}
		RepairOrderSorterUtils.sortRepairOrders(data2);
		return data2;
	}

	private Set<String> handleFinalInspectionTag(String roNo) {
		Set<String> collect = new HashSet<>();
		try {			
			Map<String, String> map = new HashMap<>();
			map.put("RO_NO", roNo.substring(0, 10));
			map.put("COMPLETE_TAG", "10041002");
			
			log.info("请求入参： {}", JSON.toJSONString(map));
			DmsResponse<PageDto<Map>> queryRepairAssignzj = dmscloudServiceFeign.queryRepairAssignzj(map);
			log.info("请求返回：{}", JSON.toJSONString(queryRepairAssignzj));
			PageDto<Map> data = queryRepairAssignzj.getData();
			List<Map> rows = data.getRows();
			collect = rows.stream().filter(Objects::nonNull).map(obj->String.join("-", String.valueOf(obj.get("OWNER_CODE")), String.valueOf(obj.get("RO_NO")))).collect(Collectors.toSet());
		} catch (Exception e) {
			log.info("请求异常：", e);
		}
		return collect;
	}
	
	@Override
	public List<RoHandRepairProjectVO> qualityInspection(List<RepairOrderReqDTO> repairOrderListReq) {
		log.info("RepairAssignV2ServiceImpl qualityInspection ：{}", JSON.toJSONString(repairOrderListReq));
		DmsResponse<List<RoHandRepairProjectVO>> qualityInspection = domainMaintainOrdersFeign.qualityInspection(repairOrderListReq);
		log.info("qualityInspection: {}", JSON.toJSONString(qualityInspection));
		if(Objects.isNull(qualityInspection)) {
			throw new ServiceBizException("系统异常！");
		}
		
		if(qualityInspection.isFail()) {
			log.info("下游异常：{} ", JSON.toJSONString(qualityInspection));
			throw new ServiceBizException(qualityInspection.getErrMsg());
		}
		return qualityInspection.getData();
	}

	@Override
	public FinalInspectionTreeVO queryRepairAnalysisByType(String repairTypeCode) {
		log.info("RepairAssignV2ServiceImpl queryRepairAnalysisByType ：{}", repairTypeCode);
		DmsResponse<FinalInspectionTreeVO> res = domainMaintainOrdersFeign.queryRepairAnalysisByType(repairTypeCode);
		log.info("queryRepairAnalysisByType: {}", JSON.toJSONString(res));
		if(Objects.isNull(res)) {
			throw new ServiceBizException("系统异常！");
		}
		
		if(res.isFail()) {
			log.info("下游异常：{} ", JSON.toJSONString(res));
			throw new ServiceBizException(res.getErrMsg());
		}
		return res.getData();
	}

	@Override
	public List<FinalInspectionContentVO> queryFinalInspectionContentByType(List<String> repairTypeCodeList) {
		log.info("RepairAssignV2ServiceImpl queryFinalInspectionContentByType ：{}", JSON.toJSONString(repairTypeCodeList));
		DmsResponse<List<FinalInspectionContentVO>> res = domainMaintainOrdersFeign.queryFinalInspectionContentByType(repairTypeCodeList);
		log.info("queryFinalInspectionContentByType: {}", JSON.toJSONString(res));
		if(Objects.isNull(res)) {
			throw new ServiceBizException("系统异常！");
		}
		
		if(res.isFail()) {
			log.info("下游异常：{} ", JSON.toJSONString(res));
			throw new ServiceBizException(res.getErrMsg());
		}
		return res.getData();
	}

	@Override
	public void maintainRepairAssignCompleteV2(List<TtRoAssignDTO> dtoList) {
		log.info("RepairAssignV2ServiceImpl maintainRepairAssignCompleteV2 ：{}", JSON.toJSONString(dtoList));
		// 查询内容清单数据
		for (TtRoAssignDTO ttRoAssignDTO : dtoList) {
			log.info("工单组件： {}", JSON.toJSONString(ttRoAssignDTO));
			List<RepairInfoDTO> repairProjectList = ttRoAssignDTO.getRepairProjectList();
			List<String> repairTypeCodeList = repairProjectList.stream().filter(Objects::nonNull).map(RepairInfoDTO::getRepairTypeCode).collect(Collectors.toList());
			List<FinalInspectionContentVO> queryFinalInspectionContentByType = queryFinalInspectionContentByType(repairTypeCodeList);
			Set<String> set = new HashSet<>();
			for (FinalInspectionContentVO finalInspectionContent : queryFinalInspectionContentByType) {
				List<FinalInspectionContentItemVO> items = finalInspectionContent.getItems();
				set.add(finalInspectionContent.getOnlyCode());
				List<String> collect = items.stream().filter(Objects::nonNull).map(FinalInspectionContentItemVO::getOnlyCode).filter(Objects::nonNull).collect(Collectors.toList());
				set.addAll(collect);
			}
			log.info("查询到的内容key 集合： {}", JSON.toJSONString(set));
			// 拆分内容清单数据
			Map<String, String> checkItems = ttRoAssignDTO.getCheckItems();
			Map<String, String> checkItemsNew = new HashMap<>();
			for (Map.Entry<String, String> entry : checkItems.entrySet()) {
				String key = entry.getKey();
				String val = entry.getValue();
				if(set.contains(key)) {
					checkItemsNew.put(key, val);
				}
			}
			log.info("老的key集合： {}", JSON.toJSONString(checkItems));
			log.info("新的key集合： {}", JSON.toJSONString(checkItemsNew));
			ttRoAssignDTO.setCheckItems(checkItemsNew);
		}
		
		// 调用dmscloud-service逻辑
		DmsResponse<Void> res = dmscloudServiceFeign.maintainRepairAssignComplete(dtoList);
		if(Objects.isNull(res)) {
			throw new ServiceBizException("系统异常！");
		}
		
		if(res.isFail()) {
			log.info("下游异常：{} ", JSON.toJSONString(res));
			throw new ServiceBizException(res.getErrMsg());
		}
		
	}

	@Override
	public List<CheckFinalInspectionRespVO> checkFinalInspection(
			List<CheckFinalInspectionReqVO> checkFinalInspectionReqList) {
		log.info("RepairAssignV2ServiceImpl checkFinalInspection ：{}", JSON.toJSONString(checkFinalInspectionReqList));
		DmsResponse<List<CheckFinalInspectionRespVO>> res = domainMaintainOrdersFeign.checkFinalInspection(checkFinalInspectionReqList);
		log.info("checkFinalInspection: {}", JSON.toJSONString(res));
		if(Objects.isNull(res)) {
			throw new ServiceBizException("系统异常！");
		}
		if(res.isFail()) {
			log.info("下游异常：{} ", JSON.toJSONString(res));
			throw new ServiceBizException(res.getErrMsg());
		}
		return res.getData();
	}

	@Override
	public List<FinalInspectionAttVO> queryFinalInspectionAttByRoNo(String ownerCode, String roNo) {
		log.info("RepairAssignV2ServiceImpl queryFinalInspectionAttByRoNo ownerCode ：{}, roNo: {}", ownerCode, roNo);
		DmsResponse<List<FinalInspectionAttVO>> res = domainMaintainOrdersFeign.queryFinalInspectionAttByRoNo(ownerCode, roNo);
		log.info("queryFinalInspectionAttByRoNo: {}", JSON.toJSONString(res));
		if(Objects.isNull(res)) {
			throw new ServiceBizException("系统异常！");
		}
		
		if(res.isFail()) {
			log.info("下游异常：{} ", JSON.toJSONString(res));
			throw new ServiceBizException(res.getErrMsg());
		}
		return res.getData();
	}
}

