package com.volvo.maintain.application.maintainlead.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 车辆健康检查详情入参DTO
 * @Date 2024/9/25 16:17
 */
@Data
@ApiModel("车辆详情查询入参")
public class VehicleHealthCheckDetailParamDto {

    @ApiModelProperty("工单号")
    private String roNo;

    @ApiModelProperty("车辆健康检查编号")
    private String vhcNo;

    @ApiModelProperty("经销商")
    private String ownerCode;

    @ApiModelProperty("维修项目代码")
    private String labourCode;

    @ApiModelProperty("不正常的大类配置id")
    private List<Integer> notNormalConfigClassId;

    @ApiModelProperty("类目排序map")
    private Map<String,Integer> vhcClassSortMap;

}
