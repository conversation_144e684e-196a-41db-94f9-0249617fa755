package com.volvo.maintain.application.maintainlead.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.part.PartDto;
import com.volvo.maintain.application.maintainlead.dto.part.ShortPartExportResultDTO;
import com.volvo.maintain.application.maintainlead.dto.part.ShortPartReplenishmentDTO;
import com.volvo.maintain.application.maintainlead.dto.part.ShortPartResultDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 补料
 */
public interface ReplenishmentService {
    /**
     * 检查是否缺料
     */
    List<ShortPartResultDTO> checkReplenishment(List<PartDto> parts, boolean flag);
    /**
     * 缺料记录生成及补货
     */
    Long shortageReplenishment(ShortPartReplenishmentDTO shortPartDto);
    
    /**
     * 最小包装量
     * @param partsNo
     * @return
     */
	Map<String, BigDecimal> getMinOrderQty(String partsNo);
	
	/**
	 * 维修工单查询
	 * @param queryParams
	 * @return
	 */
	Page<Map<String, String>> findAll(Map<String, String> queryParams);
	/**
	 * 缺料明细导出
	 * @param map
	 */
	void queryShortPartExport(Map map);
	/**
	 * 缺料明细导出--下载中心调用
	 * @param map
	 */
	List<Map> queryShortPart(Map map);
	/**
	 * 刷新按钮检查是否缺料
	 */
	List<ShortPartResultDTO> checkReplenishmentNew(String ownerCode, String roNo, Integer orderFlag);
	/**
	 * 刷新按钮缺料记录生成及补货
	 */
	Long shortageReplenishmentNew(ShortPartReplenishmentDTO shortPartDto);
}
