package com.volvo.maintain.application.maintainlead.emums;

import lombok.Getter;

@Getter
public enum PurchaseConditionsTypeEnum {

    MILEAGE_LESS_THAN_LAST_WORK_ORDER("里程数小于最近一次非作废的工单填写的里程数", "35021001", "里程数小于最近一次非作废的工单填写的里程数"),
    INVOICE_INFO_NOT_FOUND("查询不到开票关键信息", "35021002", "查询不到开票关键信息"),
    VEHICLE_TYPE_NOT_IN_RANGE("车辆的新旧车类型不符合产品购买范围", "35021003", "车辆的新旧车类型不符合产品购买范围"),
    EXISTENCE_EFFECTIVE("车辆已有正在生效的延保不具备任何延保产品的购买资格", "35021004", "车辆已有正在生效的延保不具备任何延保产品的购买资格"),
    MODEL_CODE_NOT_IN_RANGE("车辆的车型代码不符合产品购买范围", "35021005", "车辆的车型代码不符合产品购买范围"),
    ENGINE_CODE_NOT_IN_RANGE("车辆的发动机代码不符合产品的购买范围", "35021006", "车辆的发动机代码不符合产品的购买范围"),
    AGE_OR_MAINTENANCE_INTERVAL_TOO_LONG("总车龄过长不符、每两次保养间隔过久不符", "35021007", "总车龄过长不符、每两次保养间隔过久不符"),
    MILEAGE_OR_MAINTENANCE_INTERVAL_TOO_HIGH("总里程过多不符、每两次保养间隔过多不符", "35021008", "总里程过多不符、每两次保养间隔过多不符"),
    NOT_EXIST_CONTENTS_PART("不存在产品零件配置", "35021009", "不存在产品零件配置"),
    NOT_IN_SALE_AND_EFFECTIVE_RANGE("不符合上架和生效范围", "35021010", "不符合上架和生效范围"),
    NO_INITIAL_MAINTENANCE("限定未做首保不符合购买范围", "35021011", "限定未做首保不符合购买范围"),
    MAX_PURCHASE_QUANTITY_LIMIT("限制单次最大购买数量", "35021012", "限制单次最大购买数量"),
    PURCHASE_QUANTITY_LIMIT("限制购买数量", "35021013", "限制购买数量"),
    CXWY_REPURCHASE_CHECK("出险无优复购验证", "35021014", "您的权益当前有效，此商品将在权益到期前XX天开放复购"),
    CXWY_GIVESTATUS_CHECK("出险无优存在异常购买记录", "35021015", "当前权益未生效，暂不支持重复购买。此商品将在权益到期前XX天开放复购");

    private final String description;
    private final String errorCode;
    private final String message;

    PurchaseConditionsTypeEnum(String description, String errorCode, String message) {
        this.description = description;
        this.errorCode = errorCode;
        this.message = message;
    }

    public static String getMessageByErrorCode(String errorCode) {
        for (PurchaseConditionsTypeEnum condition : values()) {
            if (condition.getErrorCode().equals(errorCode)) {
                return condition.getMessage();
            }
        }
        return null;
    }

}
