package com.volvo.maintain.application.maintainlead.vo.order;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "店端查询取送车订单DTO列表", description = "店端查询取送车订单DTO列表")
public class ShopVehicleDeliverVo implements Serializable {
    private static final long serialVersionUID = -3642514849053878977L;

    /**
     * 经销商代码
     */
    @ApiModelProperty(value = "经销商代码")
    private String dealerCode;
    /**
     * 取送车中台编号中台生成
     */
    @ApiModelProperty(value = "取送车中台编号中台生成")
    private String orderCode;
    /**
     * 申请单号   -> 中台取送车单号
     */
    @ApiModelProperty(value = "申请单号")
    private Long id;
    /**
     * 订单编号   -> E代驾单号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderId;
    /**
     * 取送车订单类型 82711001 取车, 82711002 送车
     */
    @ApiModelProperty(value = "取送车订单类型 82711001 取车, 82711002 送车")
    private Integer type;
    /**
     * 订单状态：82721001 待确认, 82721002 已下单, 82721003 资金已冻结, 82721004 订单取消, 82721005 等待司机接单, 82721006 司机已接单, 82721007 司机已开启订单, 82721008 司机已就位, 82721009 司机开车中, 82721010 司机到达目的地, 82721011 已收车, 82721012 订单已完成, 82721013 已评价
     */
    @ApiModelProperty(value = "订单状态：82721001 待确认, 82721002 已下单, 82721003 资金已冻结, 82721004 订单取消, 82721005 等待司机接单, 82721006 司机已接单, 82721007 司机已开启订单, 82721008 司机已就位, 82721009 司机开车中, 82721010 司机到达目的地, 82721011 已收车, 82721012 订单已完成, 82721013 已评价, 82721016 已关闭")
    private Integer orderStatus;
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String carNo;
    /**
     * VIN
     */
    @ApiModelProperty(value = "VIN")
    private String vin;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrderNo;
    /**
     * 预约单号
     */
    @ApiModelProperty(value = "预约单号")
    private String reservationNo;
    /**
     * 是否添加取送车费用 10041001是 10041002否
     */
    @ApiModelProperty(value = "是否添加取送车费用 10041001是 10041002否")
    private Integer addPickUpAndDropOffFee;
    /**
     * 进厂时间   -> 工单开单时间
     */
    @ApiModelProperty(value = "进厂时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date workOrderIssuingTime;
    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date placeOrderTime;
    /**
     * 是否绑定沃世界 10041001是、10041002否
     */
    @ApiModelProperty(value = "是否绑定沃世界 10041001是、10041002否")
    private Integer isBindingWoWorld;
    /**
     * 预约时间
     */
    @ApiModelProperty(value = "预约时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bookingTime;
    /**
     * 出发时间（接车后出发时间）     -> 司机已就位，取送车操作记录对应的状态的日期
     */
    @ApiModelProperty(value = "出发时间（接车后出发时间）")
    private String deliverStartTime;
    /**
     * 到达时间（车辆到达时间）       -> 司机已到达目的地，取送车操作记录对应的时间
     */
    @ApiModelProperty(value = "到达时间（车辆到达时间）")
    private String deliverEndTime;
    /**
     * 出发地  -> 取车地
     */
    @ApiModelProperty(value = "出发地")
    private String pickupAddress;
    /**
     * 目的地   ->还车地址
     */
    @ApiModelProperty(value = "目的地")
    private String returnAddress;
    /**
     * 总里程（公里）    -> 详情 E代驾详情 - 费用明细 - 预估距离 中取到
     */
    @ApiModelProperty(value = "总里程（公里）")
    private String distance;
    /**
     * 消费金额        ->  详情 E代驾详情 - 费用明细 - 预估价格 中取到
     */
    @ApiModelProperty(value = "消费金额")
    private String totalFee;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 司机工号      -> 详情 E代驾详情 - 费用明细 - 司机工号 中取到
     */
    @ApiModelProperty(value = "司机工号")
    private String driverNo;
    /**
     * 订单来源
     */
    private Integer orderSource;

    /**
     * 进厂与下单是否为同一个月
     */
    @ApiModelProperty(value = "进厂与下单是否为同一个月 true 是,false 否")
    private Boolean sameMonth = Boolean.FALSE;

    /**
     * 进厂时间是否小于下单时间
     */
    @ApiModelProperty(value = "进厂时间是否小于下单时间 true 是,false 否")
    private Boolean ltPlaceOrderTime = Boolean.FALSE;

    /**
     * 首次响应时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "首次响应时间 yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstResponseTime;

    /**
     * 订单来源是否是C端创建:10041001是,10041002否
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "订单来源是否是C端创建:10041001是,10041002否")
    private Integer minProgram;

    /**
     * 跟进时间(单位:分钟)
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "跟进时间(单位:分钟)")
    private Integer followUpOnTime;

    /**
     * 司机姓名
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 司机联系方式
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "司机联系方式")
    private String driverContactInfo;

    /**
     * 取车地址联系人姓名  最多20个字符
     */
    @ApiModelProperty(value = "取车地址联系人姓名")
    private String pickupContactName;

    /**
     * 取车地址联系人手机号 手机号
     */
    @ApiModelProperty(value = "取车地址联系人手机号")
    private String pickupContactPhone;

    @ApiModelProperty(value = "司机接单时间")
    private Date takeOrderTime;

    @ApiModelProperty(value = "等待司机接单时间")
    private Date waitDriverTakeOrderTime;

    @ApiModelProperty(value = "司机接单耗时(单位:min)")
    private Integer takeOrderTimeConsuming;

    @ApiModelProperty(value = "按时到达时差(单位:min)")
    private Integer driverArriveTimeDif;

    @ApiModelProperty(value = "绑定时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bindingTime;

    @ApiModelProperty(value = "取消原因")
    private String cancelReason;

    @ApiModelProperty(value = "取消对象")
    private Integer cancelSource;

    @ApiModelProperty(value = "异议反馈")
    private String reviewReason;

    @ApiModelProperty(value = "车型名称")
    private String modelName;

    @ApiModelProperty(value = "是否首任车主（0-非首任，1-首任")
    private Integer firstOwner;

    @ApiModelProperty(value = "车型code")
    private String modelCode;

    @ApiModelProperty(value = "置顶标识(0:否;1:em90待确认置顶)")
    private Integer isTop;
    
    @ApiModelProperty(value = "服务商类型 0:e代驾，1:滴滴")
    private Integer supplierType;

}
