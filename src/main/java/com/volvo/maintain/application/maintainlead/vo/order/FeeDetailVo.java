package com.volvo.maintain.application.maintainlead.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 商户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "FeeDetailVO 对象", description = "FeeDetailVO")
public class FeeDetailVo {

    @ApiModelProperty(value = "费用明细 ")
    private String feeDetail;
    @ApiModelProperty(value = "延误费用 单位元")
    private String delayTotalFee;
    @ApiModelProperty(value = "延误时间 单位分")
    private String delayTime;
    @ApiModelProperty(value = "预估距离 单位公里")
    private String distance;
    @ApiModelProperty(value = "预估价格 单位元")
    private String totalFee;
    @ApiModelProperty(value = "起步费用 单位元")
    private String startFee;
    @ApiModelProperty(value = "超出费用 单位元")
    private String overFee;

}