package com.volvo.maintain.application.maintainlead.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Description 根据零件查工时入参
 * @Date 2024/11/2 18:33
 */
@Data
@ApiModel("根据零件查工时入参")
public class VhcQueryLabourDto  {

    @ApiModelProperty("经销商代码")
    private String ownerCode;

    @ApiModelProperty("零件编号")
    private String partNo;

    @ApiModelProperty(value = "车架号")
    @NotEmpty(message = "vin不能为空！")
    private String vin;

    @ApiModelProperty(value = "当前页")
    private Integer pageNum;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

}
