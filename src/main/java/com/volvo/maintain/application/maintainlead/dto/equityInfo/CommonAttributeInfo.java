package com.volvo.maintain.application.maintainlead.dto.equityInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: CommonAttributeInfo
 **/
@Data
@ApiModel("CommonAttributeInfo")
public class CommonAttributeInfo {

    /**
     * 属性名称
     */
    @ApiModelProperty("属性名称")
    private String attributeName;

    /**
     * 属性编码
     */
    @ApiModelProperty("属性编码")
    private String attributeCode;

    /**
     * 属性值编码
     */
    @ApiModelProperty("属性值编码")
    private String attributeValueCode;

    /**
     * 属性值
     */
    @ApiModelProperty("属性值")
    private String attributeValue;
}