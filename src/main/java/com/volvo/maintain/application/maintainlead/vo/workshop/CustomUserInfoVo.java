package com.volvo.maintain.application.maintainlead.vo.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 自定义人员信息
 * @Date 2024/11/14 16:49
 */
@ApiModel("自定义人员信息")
@Data
public class CustomUserInfoVo {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("登录账号")
    private String userCode;

    @ApiModelProperty("员工姓名")
    private String employeeName;

    @ApiModelProperty("员工编号")
    private String employeeNo;

    @ApiModelProperty("所属部门")
    private String orgName;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("手机号码")
    private String mobilePhone;

    @ApiModelProperty("在职状态")
    private String employeeStatus;

}
