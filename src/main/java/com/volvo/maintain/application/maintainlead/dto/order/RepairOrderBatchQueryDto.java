package com.volvo.maintain.application.maintainlead.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量查询养修工单")
public class RepairOrderBatchQueryDto {
    @ApiModelProperty("进厂经销商")
    private String intoDealerCode;

    @ApiModelProperty("进厂工单号")
    private String intoRoNo;

    @ApiModelProperty("预约单号")
    private String bookingOrderNo;

    @ApiModelProperty("经销商代码")
    private String dealerCode;

    private String roNo;

    private String ownerCode;


    public RepairOrderBatchQueryDto(String ownerCode, String roNo) {
        this.ownerCode = ownerCode;
        this.roNo = roNo;
    }
}
