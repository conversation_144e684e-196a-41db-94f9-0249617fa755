package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2023/12/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("标签信息")
public class TagListDto {

    // 标签名称
    private String tagName;
    // 显示名称
    private String showName;
    // 标签id
    private String tagId;
    // 标签来源
    private Integer tagSource;

    // 标签类型
    private String tagType;

    // 标签类型
    private String tagDescription;

    // 是否删除（0:未删除 1:已删除）
    private String removed;

    // 是否支持查看详情
    private Integer isGetDetail;

    //展示优先级
    private Integer showLevel;

    // 是否详情标签
    private Integer isDetailTag;

    //值规则
    private String valueRule;
}
