package com.volvo.maintain.application.maintainlead.dto.dtcclues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("线索种类导入")
public class ExportDtcCluesCategoryParDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "故障类别")
    private String faultCategory;

    @ApiModelProperty(value = "电子控制单元(ECU)")
    private String ecu;

    @ApiModelProperty(value = "故障诊断码(DTC)")
    private String dtc;

    @ApiModelProperty(value = "Confirm状态")
    private Integer confirmStatus;

    @ApiModelProperty(value = "Indicator状态")
    private Integer indicatorStatus;

    @ApiModelProperty(value = "优先级")
    private Integer priority;
}
