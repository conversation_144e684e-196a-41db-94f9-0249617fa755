package com.volvo.maintain.application.maintainlead.dto.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 查询自定义信息入参
 * @Date 2024/11/14 16:46
 */

@Data
@ApiModel("查询自定义信息入参")
public class QueryCustomInfoDto {

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "经销商代码")
    private String ownerCode;

    @ApiModelProperty(value = "当前页")
    private Integer pageNum;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize;
}
