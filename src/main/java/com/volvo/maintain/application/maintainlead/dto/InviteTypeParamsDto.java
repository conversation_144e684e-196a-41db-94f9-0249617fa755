package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/12/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("邀约类型入参")
public class InviteTypeParamsDto {
    @ApiModelProperty("车架号")
    private List<String> vin;

    @ApiModelProperty("经销商code")
    private String ownerCode;

    @ApiModelProperty("开始时间")
    private String beginTime;

    @ApiModelProperty("结束时间")
    private String endTime;
}
