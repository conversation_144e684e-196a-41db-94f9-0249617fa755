package com.volvo.maintain.application.maintainlead.service.strategy;

import com.volvo.design.strategy.AbstractStrategyChoose;
import com.volvo.event.ApplicationInitializingEvent;
import com.volvo.exception.ServiceException;
import com.volvo.utils.ApplicationContextUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class CluesStrategyChoose extends AbstractStrategyChoose {

    @Override
    public void onApplicationEvent(ApplicationInitializingEvent event) {
        Map<String, ClueExecuteStrategy> actual = ApplicationContextUtils.getBeansOfType(ClueExecuteStrategy.class);
        actual.forEach((beanName, bean) -> {
            AbstractStrategyChoose beanExist = (AbstractStrategyChoose)this.abstractExecuteStrategyMap.get(bean.mark());
            if (beanExist != null) {
                throw new ServiceException(String.format("[%s] Duplicate execution policy", bean.mark()));
            } else {
                this.abstractExecuteStrategyMap.put(bean.mark(), bean);
            }
        });
    }
}
