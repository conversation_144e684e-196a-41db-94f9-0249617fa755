package com.volvo.maintain.application.maintainlead.dto.rights;

import lombok.Data;

import java.util.List;

@Data
public class GiveRecordRequestDto  {

    /**
     * vin
     */
    private String vin;

    /**
     * 订单号
     */
    private String orderCode;

    /**
     * 购买状态
     */
    private List<String> giveStatus;

    /**
     * 产品件号
     */
    private String productNo;

    /**
     * 开始时间 （间隔时间最大不能超过10天） 格式：yyyy-MM-dd HH:mm:ss 查询支付时间：pay_time >= startDate
     */
    private String startDate;

    /**
     * 结束时间（间隔时间最大不能超过10天） 格式：yyyy-MM-dd HH:mm:ss 查询支付时间：pay_time >= startDate
     */
    private String endDate;

    /**
     * 当前页
     */
    private Integer pageNum;

    /**
     *
     */
    private Integer pageSize;


}
