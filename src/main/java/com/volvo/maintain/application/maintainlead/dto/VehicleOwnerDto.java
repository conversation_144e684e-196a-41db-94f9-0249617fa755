package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 车主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="车主信息查询对象", description="车主信息ID集合查询")
public class VehicleOwnerDto {

    @ApiModelProperty(value = "oneId")
    private Long id;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "客户姓名")
    private String name;

    @ApiModelProperty(value = "OPEN_ID")
    private String openId;

    @ApiModelProperty(value = "返回状态")
    private Integer  record;

    @ApiModelProperty(value = "状态说明")
    private String  message;

}
