package com.volvo.maintain.application.maintainlead.dto.workshop;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MissingPartsStatusDto implements Serializable {

    // 未到货数量
    private Integer undeliveredCount;

    // 已到货数量
    private Integer deliveredCount;

    // 部分到货数量
    private Integer partiallyDeliveredCount;

    /**
     * 已到店
     */
    private Integer bookingArrived;

    /**
     * 未到店
     */
    private Integer bookingNotArrived;

    /**
     * 全部
     */
    private Integer bookingAll;

    private String deliveredCountStatus;

    /**
     * 数量
     */
    private Integer deliveredCountStatusCount;
}
