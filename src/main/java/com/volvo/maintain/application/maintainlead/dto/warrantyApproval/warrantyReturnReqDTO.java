package com.volvo.maintain.application.maintainlead.dto.warrantyApproval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "延保回款查询列表查询DTO", description = "延保回款查询列表查询DTO")
public class warrantyReturnReqDTO {


    @ApiModelProperty("年")
    private String yearNo;

    @ApiModelProperty("月")
    private String monthNo;

    @ApiModelProperty("经销商(店端根据当前经销商获取)")
    private List<String> ownerCode;

    @ApiModelProperty("10461001 店端类型, 10461003  厂端类型")
    private String dataType;

    @ApiModelProperty("当前页")
    private Long currentPage;

    @ApiModelProperty("每页显示条数（每次最大不能超过5000条）")
    private Long pageSize;

    @ApiModelProperty("id")
    private Long returnId;
}
