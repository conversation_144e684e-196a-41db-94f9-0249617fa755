package com.volvo.maintain.application.maintainlead.dto.rights;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WarrantyCXWYCheckRspDTO {
	/**
	 * vin
	 */
	@ApiModelProperty(value = "车架号")
	private String vin;

	/**
	 * 产品件号
	 */
	@ApiModelProperty(value = "产品件号")
	private String productNo;

	/**
	 * productNo
	 */
	@ApiModelProperty(value = "错误码")
	private String errorCode;

	/**
	 * productNo
	 */
	@ApiModelProperty(value = "错误描述")
	private String errorMsg;
}