package com.volvo.maintain.application.maintainlead.dto.equityInfo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.util.List;

/**
 * <p>用户清单列表入参</p>
 *
 * <AUTHOR>
 * @date 2023/8/14
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecordDetailedListDTO  {
    /**
     *车架号
     */
    private List<String> vinList;
    /**
     * 车型
     */
    private String modelCode;

    /**
     * 年款
     */
    private String configYear;

    /**
     * 配置code
     */
    private String configCode;

    /**
     * 清单状态(1-启用；0-禁用)
     */
    private String rewardStatus;

    /**
     *创建时间开始
     */
    private String createTimeBegin;
    /**
     *创建时间结束
     */
    private String createTimeEnd;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RecordDetailedListDTO that = (RecordDetailedListDTO) o;
        return new EqualsBuilder().appendSuper(super.equals(o)).append(vinList, that.vinList).append(modelCode, that.modelCode).append(configYear, that.configYear).append(configCode, that.configCode).append(rewardStatus, that.rewardStatus).append(createTimeBegin, that.createTimeBegin).append(createTimeEnd, that.createTimeEnd).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).appendSuper(super.hashCode()).append(vinList).append(modelCode).append(configYear).append(configCode).append(rewardStatus).append(createTimeBegin).append(createTimeEnd).toHashCode();
    }
}
