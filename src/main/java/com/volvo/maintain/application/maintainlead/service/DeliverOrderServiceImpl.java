package com.volvo.maintain.application.maintainlead.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.map.MapUtil;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderDto;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.vo.CarOwnerRelationVo;
import com.volvo.maintain.application.maintainlead.vo.DeliverVo;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ImResponse;
import com.volvo.maintain.infrastructure.util.StringUtil;
import com.volvo.maintain.interfaces.vo.MemberInfoVo;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 送修人订单处理
 */
@Service
@Slf4j
public class DeliverOrderServiceImpl implements DeliverOrderService{

    @Autowired
    private VehicleOwnershipServiceFeign vehicleOwnershipServiceFeign;

    @Autowired
    private VehicleCarOwnerFeign vehicleCarOwnerFeign;

    @Autowired
    private MIdEndMemberCenterFeign mIdEndMemberCenterFeign;

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;

    /**
     * 记录异常送修人工单
     *
     * @param roNo      工单号
     * @param traceTime 回访时间类型
     * @return map 返回结果
     */
    @Override
    public Map<String, Object> deliveryOrderExceptionRecord(String roNo, String traceTime) {
        DmsResponse<Map<String, Object>> result = dmscloudServiceFeign.saveReceiveMoney(roNo, traceTime);
        log.info("deliveryOrderExceptionRecord->result:{}", result);
        if (result.isFail() || MapUtil.isEmpty(result.getData())){
            throw new ServiceBizException(result.getErrMsg());
        }
        Map<String, Object> repairOrderInfo = result.getData();
       try {
           //对象转map 忽略null值
           RepairOrderDto repairOrderDto = BeanUtil.mapToBean(repairOrderInfo, RepairOrderDto.class, CopyOptions.create().setIgnoreNullValue(true));
           StringUtil.requireNonNull(repairOrderDto,"交车工单对象参数异常！");
           // 提前定义好的逻辑终止条件，避免重复代码
           boolean shouldDeliver = !isRepairTypeCodeValid(repairOrderDto) && !processCarOwnerRelations(repairOrderDto);
           if (shouldDeliver) {
               structureDeliver(roNo, repairOrderDto, repairOrderInfo, true);
           }
       }catch (Exception e) {
            log.info("deliveryOrderExceptionRecord-exception",e);
       }
       //确认交车后 -> 清除车辆健康检查小类
        log.info("清除不修原因：{}", roNo);
        DmsResponse<Void> response = domainMaintainOrdersFeign.closeVhcItemInfoByRoNo(roNo);
        log.info("deliveryOrderExceptionRecord :{}",response);
        if (Objects.isNull(response) || response.isFail()){
            log.info("deliveryOrderExceptionRecord 清除不修原因异常");
        }
        return repairOrderInfo;
    }

    /**
     * 匹配手机号结果
     * @param memberInfoList 会员中心的会员手机号集合
     * @param delivererMobile 工单表中的送修人手机号
     * @return boolean 结果
     */
    public boolean isContainsDelivererMobile(List<MemberInfoVo> memberInfoList, String delivererMobile) {
        if (CollectionUtils.isEmpty(memberInfoList) && org.apache.commons.lang3.StringUtils.isBlank(delivererMobile)) {
            // 只打印集合大小和是否为空的信息，减少内存使用
            log.info("isContainsDelivererMobile->requestParams: memberInfoListSize={},delivererMobile={}",memberInfoList,delivererMobile);
            return false;
        }
        return memberInfoList.stream()
                .anyMatch(memberInfo -> Objects.equals(memberInfo.getMemberPhone(), delivererMobile));
    }

    @Override
    public String queryDeliverMobileException(String mobile, String ownerCode,String vin) {
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(ownerCode) || StringUtils.isEmpty(vin)){
            throw new ServiceBizException("必填字段为空");
        }
        log.info("queryDeliverMobileException mobile:{},ownerCode:{},vin:{}",mobile,ownerCode,vin);
        //判断白名单 (如果不在白名单内才继续)
        DmsResponse<Boolean> booleanDmsResponse = dmscloudServiceFeign.checkWhitelist(ownerCode, CommonConstant.DELIVER_SHIELD, 0, mobile);
        log.info("isContainsDelivererMobile booleanDmsResponse:{}",booleanDmsResponse);
        if (Objects.isNull(booleanDmsResponse) || booleanDmsResponse.isFail()){
            log.info("isContainsDelivererMobile 查询白名单异常");
        }
        if (booleanDmsResponse.getData()){
            log.info("isContainsDelivererMobile 存在白名单");
            return CommonConstant.DELIVER_SHIELD_NORMAL;
        }
        //1查询人车关系 2查询会员关系 3判断手机号
        if (containsMobileBinding(vin,mobile)){
            log.info("queryDeliverMobileException 匹配正常");
            return CommonConstant.DELIVER_SHIELD_NORMAL;
        }
        //查询系统配置（次数和天数）
        String number=getDeliverNumber();
        log.info("queryDeliverMobileException number:{}",number);
        String day=getDeliverDay();
        log.info("queryDeliverMobileException day:{}",day);
        //组装时间范围 yyyy-MM-dd HH:mm:ss
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String timeBegin = LocalDateTime.now().minus(Integer.parseInt(day), ChronoUnit.DAYS).format(formatter);
        String timeEnd = LocalDateTime.now().format(formatter);
        log.info("queryDeliverMobileException 组装开始时间:{} 结束时间:{}",timeBegin,timeEnd);
        Integer deliverNumber = queryDeliverMobileNumber(mobile, ownerCode, vin, timeBegin, timeEnd);
        //判断次数是否满足配置里面的次数
        if (deliverNumber>=Integer.parseInt(number)){
            return CommonConstant.DELIVER_SHIELD_ABNORMAL;
        }else {
            return CommonConstant.DELIVER_SHIELD_NORMAL;
        }
    }



    /**
     * 记录异常送修人工单 (取消交车)
     * @param roNo      工单号
     * @return map 返回结果
     */
    @Override
    public Map<String, Object> cancelDeliverExceptionRecord(String roNo) {
        StringUtil.requireNonNull(roNo,"取消交车参数异常！");
        DmsResponse<Map<String, Object>> result = dmscloudServiceFeign.getVehicle(roNo);
        log.info("cancelDeliverExceptionRecord->result:{}", result);
        if (result.isFail() || MapUtil.isEmpty(result.getData())){
            throw new ServiceBizException(result.getErrMsg());
        }
        Map<String, Object> repairOrderInfo = result.getData();
        //对象转map 忽略null值
        RepairOrderDto repairOrderDto = BeanUtil.mapToBean(repairOrderInfo, RepairOrderDto.class, CopyOptions.create().setIgnoreNullValue(true));
        StringUtil.requireNonNull(repairOrderDto,"取消交车数据异常！");
        structureDeliver(repairOrderDto.getRoNo(), repairOrderDto, repairOrderInfo,false);
        return repairOrderInfo;
    }



    /**
     * 构建送修人异常记录对象
     * ps:私有方法，如表中vin为空，故工单表中的vin为空。vin硬性参数必须存在
     * @param roNo 入参：工单号（客户端入参）
     * @param repairOrderDto 工单信息
     * @param repairOrderInfo 工单信息map
     */
    public void structureDeliver(String roNo, RepairOrderDto repairOrderDto, Map<String, Object> repairOrderInfo, boolean flag) {
        StringUtil.requireNonNull(repairOrderDto,"交车工单对象参数异常！");
        StringUtil.requireNonNull(repairOrderDto.getVin(),"交车工单对象参数VIN异常！");
        StringUtil.requireNonNull(repairOrderDto.getDelivererMobile(),"交车工单对象参数Mobile异常！");
        DeliverVo deliverVo = new DeliverVo();
        deliverVo.setRoNo(roNo);
        deliverVo.setVin(repairOrderDto.getVin());
        deliverVo.setDelivererPhone(repairOrderDto.getDelivererMobile());
        deliverVo.setOwnerCode(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode());
        if (flag) {
            repairOrderInfo.put("row", dmscloudServiceFeign.saveBoundVehicleOrder(deliverVo));
        }else {
            repairOrderInfo.put("row", dmscloudServiceFeign.deleteBoundVehicleOrder(deliverVo));
        }
        repairOrderInfo.put("flag", flag);
    }


    /**
     * 查询会员信息根据 集合Id
     * @param memberIdList 会员idList
     * @return 会员信息List
     */
    public List<MemberInfoVo> queryMemberInfoByList(List<String> memberIdList) {
        StringUtil.requireNonNull(memberIdList,"对应会员ID为空！");
        ResponseDto<List<MemberInfoVo>> memberInfoByIdList = mIdEndMemberCenterFeign.getMemberInfoByIdList(memberIdList);
        if (memberInfoByIdList.isFail() || CollectionUtils.isEmpty(memberInfoByIdList.getData())) {
            throw new ServiceBizException("获取会员信息异常");
        }
        return memberInfoByIdList.getData();
    }

    /**
     * 查询人车关系
     * @param vin 车架号
     * @return 人车关系集合
     */
    public List<CarOwnerRelationVo> queryRelationList(String vin) {
        StringUtil.requireNonNull(vin,"人车关系参数异常！");
        ImResponse<List<CarOwnerRelationVo>> listImResponse = vehicleCarOwnerFeign.getRelationList(vin);
        log.info("queryRelationListAndMidResponse->listImResponse:{}", listImResponse);
        if (null == listImResponse) {
            throw new ServiceBizException("获取绑车关系异常");
        }
        return listImResponse.getData();
    }


    /**
     * 判断手机号绑定
     *1查询人车关系
     *2查询会员关系
     *3判断手机号
     * 返回匹配结果 匹配不中为true
     */
    private boolean containsMobileBinding(String vin,String mobile){
        log.info("containsMobileBinding vin:{},mobile:{}",vin,mobile);
        if (StringUtils.isEmpty(vin)){
            log.info("containsMobileBinding vin isEmpty");
            return false;
        }
        if (StringUtils.isEmpty(mobile)){
            log.info("containsMobileBinding mobile isEmpty");
            return false;
        }
        //查询绑车关系根据vin，得到会员id。
        List<CarOwnerRelationVo> carOwnerRelationVos = queryRelationList(vin);
        if(CollectionUtils.isEmpty(carOwnerRelationVos)){
            log.info("carOwnerRelationVos isEmpty");
            return false;
        }
        List<String> memberIdList = carOwnerRelationVos.stream().filter(Objects::nonNull).map(CarOwnerRelationVo::getMemberId).collect(Collectors.toList());
        //查询会员信息根据会员id List，得到会员信息集合
        List<MemberInfoVo> memberInfoList = queryMemberInfoByList(memberIdList);
        if(CollectionUtils.isEmpty(memberInfoList)){
            log.info("memberInfoList isEmpty");
            return false;
        }
        //送修人手机号  多余了
        String delivererMobile = mobile;
        //返回匹配结果 匹配不中为false
        boolean containsDelivererMobile = isContainsDelivererMobile(memberInfoList, delivererMobile);
        log.info("containsMobileBinding :{}",carOwnerRelationVos);
        return containsDelivererMobile;
    }


    //获取送修人屏蔽次数
    private String getDeliverNumber(){
        DmsResponse<CommonConfigDto> deliverNumber = dmscloudServiceFeign.getConfigByKey(CommonConstant.DELIVER_NUMBER, CommonConstant.DELIVER_PARAM);
        log.info("deliverNumber :{}", deliverNumber);
        if (Objects.isNull(deliverNumber) || deliverNumber.isFail() || Objects.isNull(deliverNumber.getData())) {
            throw new ServiceBizException("getDeliverNumber 获取送修人屏蔽次数 异常");
        }
        if (StringUtils.isEmpty(deliverNumber.getData().getConfigValue())){
            throw new ServiceBizException("getDeliverNumber 获取送修人屏蔽次数为空");
        }
        return deliverNumber.getData().getConfigValue();
    }

    //获取送修人屏蔽天数
    private String getDeliverDay(){
        DmsResponse<CommonConfigDto> deliverNumber = dmscloudServiceFeign.getConfigByKey(CommonConstant.DELIVER_DAY, CommonConstant.DELIVER_PARAM);
        log.info("deliverNumber :{}", deliverNumber);
        if (Objects.isNull(deliverNumber) || deliverNumber.isFail() || Objects.isNull(deliverNumber.getData())) {
            throw new ServiceBizException("getDeliverNumber 获取送修人屏蔽天数 异常");
        }
        if (StringUtils.isEmpty(deliverNumber.getData().getConfigValue())){
            throw new ServiceBizException("getDeliverNumber 获取送修人屏蔽天数为空");
        }
        return deliverNumber.getData().getConfigValue();
    }


    /**
     * 根据时间查询送修人手机号次数
     */
    private Integer queryDeliverMobileNumber(String mobile, String ownerCode, String vin, String timeBegin, String timeEnd) {
        //根据上面组装的时间等信息(手机号 经销商,时间) 去查询次数
        DeliverVo deliverVo = new DeliverVo();
        deliverVo.setOwnerCode(ownerCode);
        deliverVo.setDelivererPhone(mobile);
        deliverVo.setVin(vin);
        deliverVo.setTimeBegin(timeBegin);
        deliverVo.setTimeEnd(timeEnd);
        log.info("queryDeliverMobileException DeliverVo:{}", deliverVo);
        DmsResponse<Integer> deliverMobileNumberDmsResponse = dmscloudServiceFeign.queryDeliverMobileNumber(deliverVo);
        log.info("deliverMobileNumberDmsResponse :{}", deliverMobileNumberDmsResponse);
        if (Objects.isNull(deliverMobileNumberDmsResponse) || deliverMobileNumberDmsResponse.isFail() || Objects.isNull(deliverMobileNumberDmsResponse.getData())) {
            throw new ServiceBizException("queryDeliverMobileNumber 异常");
        }
        return deliverMobileNumberDmsResponse.getData();
    }

    /**
     * 处理车主关系
     * @param repairOrderDto 工单对象
     * @return 返回结果
     */
    private boolean processCarOwnerRelations(RepairOrderDto repairOrderDto) {
        List<CarOwnerRelationVo> carOwnerRelationVos = queryRelationList(repairOrderDto.getVin());
        if (CollectionUtils.isEmpty(carOwnerRelationVos)) {
            log.info("processCarOwnerRelations carOwnerRelationVos isEmpty");
            return false; // 直接返回false，需要记录数据
        }
        List<String> memberIdList = carOwnerRelationVos.stream()
                .filter(Objects::nonNull)
                .map(CarOwnerRelationVo::getMemberId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(memberIdList)) {
            log.info("processCarOwnerRelations memberIdList isEmpty");
            return false; // 无会员信息，需要记录数据
        }
        List<MemberInfoVo> memberInfoList = queryMemberInfoByList(memberIdList);
        if (CollectionUtils.isEmpty(memberInfoList)) {
            log.info("processCarOwnerRelations memberInfoList isEmpty");
            return false; // 无会员信息，需要记录数据
        }
        // 检查是否包含送修人手机号
        return isContainsDelivererMobile(memberInfoList, repairOrderDto.getDelivererMobile());
    }

    /**
     * 判断是否是维修类型
     * @param repairOrderDto 工单对象
     * @return 是否是维修类型
     */
    private boolean isRepairTypeCodeValid(RepairOrderDto repairOrderDto) {
        return org.apache.commons.lang3.StringUtils.isNotBlank(repairOrderDto.getRepairTypeCode())
                && CommonConstant.REPAIRTYPECODE.equals(repairOrderDto.getRepairTypeCode());
    }

}
