package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.volvo.exception.ServiceBizException;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CdpTagInfoService;
import com.volvo.maintain.infrastructure.constants.CdpConstant;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.CdpFeign;
import com.volvo.maintain.infrastructure.gateway.DmscusCustomerFeign;
import com.volvo.maintain.infrastructure.gateway.response.CdpResponse;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.CdpTagInfoVo;
import com.yonyou.cyx.framework.util.bean.ApplicationContextHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.RetryContext;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class CdpTagInfoServiceImpl implements CdpTagInfoService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${CDP.appId}")
    private String appId;

    @Value("${CDP.appSecret}")
    private String AppSecret;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private CdpFeign cdpFegin;

    @Autowired
    private DmscusCustomerFeign dmscusCustomerFeign;

    @Override
    public List<CdpTagListDto> queryEmpowerByVins(List<String> vin) {
        return queryEmpowerByVins_(vin);
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public List<CdpTagListDto> queryEmpowerByVins_(List<String> vin) {
        JSONObject param = getJsonObject(null);
        param.put("vins", vin);
        logger.info("vin批量查询接口-请求参数：{}", param);
        CdpResponse<List<CustomerTagsDto>> cdpResult = null;
        try {
            cdpResult = cdpFegin.getVehicleLabels(param);
            logger.info("vin批量查询接口-响应结果：{}", cdpResult);
        } catch (Exception e) {
            throw new ServiceBizException("queryEmpowerByVins_ e:{}", e);
        }
        if (cdpResult.isFail()) {
            throw new ServiceBizException("getVehicleLabels is error:" + cdpResult.getMsg());
        }
        List<CdpTagListDto> cdpTagListDTOS = this.analysisCdpTagInfo(cdpResult.getData());
        return cdpTagListDTOS;
    }

    @Override
    public CustomerTagsDto queryEmpowerByVin(String vin) {
        return queryEmpowerByVin_(vin);
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public CustomerTagsDto queryEmpowerByVin_(String vin) {
        JSONObject param = getJsonObject(null);
        param.put("vins", vin);
        logger.info("获取用户/车辆属性及标签-请求参数：{}", param);
        CdpResponse<JSONObject> cdpResult = null;
        try {
            cdpResult = cdpFegin.queryTagListByVin(param);
            logger.info("获取用户/车辆属性及标签-响应结果：{}", cdpResult);
        } catch (Exception e) {
            throw new ServiceBizException("queryEmpowerByVin_ e:{}", e);
        }
        if (cdpResult.isFail()) {
            throw new ServiceBizException("getCustomerTags is error:" + cdpResult.getMsg());
        }
        JSONObject data = cdpResult.getData();
        if (null == data) {
            return new CustomerTagsDto();
        }
        return JSONObject.toJavaObject(data, CustomerTagsDto.class);
    }

    /**
     * 解析cdp返回标签信息
     */
    private List<CdpTagListDto> analysisCdpTagInfo(List<CustomerTagsDto> data) {
        List<CdpTagListDto> cdpTagListDTOS = new ArrayList<>();
        data.forEach(i -> {
            i.getTagList().forEach(t -> {
                if (t.getTagID().contains(CommonConstant.CDP_TAG_WHETHER_EMPOWER)) {
                    CdpTagListDto cdpTagListDTO = new CdpTagListDto();
                    if (StringUtils.isNotEmpty(i.getVin())) {
                        cdpTagListDTO.setVin(i.getVin());
                    }
                    if (StringUtils.isNotEmpty(i.getIdValue())) {
                        cdpTagListDTO.setVin(i.getIdValue());
                    }
                    cdpTagListDTO.setTagValue(t.getTagValue());
                    cdpTagListDTOS.add(cdpTagListDTO);
                }
            });
        });

        return cdpTagListDTOS;
    }

    /**
     * 根据手机号查询是否属于群组
     */
    @Override
    public CdpCheckInSegmentsBaseByAuthDto checkInSegmentsBaseByAuth(String delivererMobile, String configValue) {
        return checkInSegmentsBaseByAuth_(delivererMobile, configValue);
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public CdpCheckInSegmentsBaseByAuthDto checkInSegmentsBaseByAuth_(String delivererMobile, String configValue) {
        JSONObject param = getJsonObject(null);
        param.put("profileType",CommonConstant.TAG_CUSTOMER_PROFILEBASE);
        param.put("id_value",delivererMobile);
        param.put("id_name",CommonConstant.CDP_MOBILE_ID_NAME);
        param.put("segmentList",configValue.split(","));
        logger.info("根据手机号查询是否属于人群-请求参数：{}", param);
        CdpResponse<JSONObject> cdpResult = null;
        try {
            cdpResult = cdpFegin.checkInSegmentsBaseByAuth(param);
            logger.info("checkInSegmentsBaseByAuth cdpResult:{}", cdpResult);
        } catch (Exception e) {
            throw new ServiceBizException("checkInSegmentsBaseByAuth e:{}", e);
        }
        if (null == cdpResult.getData()) {
            return null;
        }
        CdpCheckInSegmentsBaseByAuthDto dto = JSONObject.toJavaObject(cdpResult.getData(), CdpCheckInSegmentsBaseByAuthDto.class);
        logger.info("根据手机号查询是否属于人群-响应结果：{}", dto);
        return dto;
    }

    /**
     * 根据viin查询是否属于群组
     */
    @Override
    public CdpCheckInSegmentsBaseByAuthDto checkInSegmentsBaseByVin(String vin, String configValue) {
        return checkInSegmentsBaseByVin_(vin, configValue);
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public CdpCheckInSegmentsBaseByAuthDto checkInSegmentsBaseByVin_(String vin, String configValue) {
        JSONObject param = getJsonObject(null);
        param.put("profileType", CommonConstant.TAG_VEHICLE_PROFILEBASE);
        param.put("id_value", vin);
        param.put("id_name", CommonConstant.CDP_VIN_ID_NAME);
        Optional.ofNullable(configValue).ifPresent(v -> {
            param.put("segmentList", configValue.split(","));
        });
        logger.info("根据vin查询是否属于人群-请求参数：{}", param);
        CdpResponse<JSONObject> cdpResult = null;
        try {
            cdpResult = cdpFegin.checkInSegmentsBaseByVin(param);
            logger.info("checkInSegmentsBaseByVin_ cdpResult:{}", cdpResult);
        } catch (Exception e) {
            throw new ServiceBizException("checkInSegmentsBaseByVin_ error:{}", e);
        }
        if (null == cdpResult.getData()) {
            return null;
        }
        CdpCheckInSegmentsBaseByAuthDto dto = JSONObject.toJavaObject(cdpResult.getData(), CdpCheckInSegmentsBaseByAuthDto.class);
        logger.info("根据vin查询是否属于人群-响应结果：{}", dto);
        return dto;
    }

    private JSONObject getJsonObject(String vin) {
        CdpTokenPortraitDto token = this.getCdpToken(vin);
        JSONObject param = new JSONObject();
        param.put("appid", appId);
        param.put("token", token.getToken());
        param.put("access_timestamp", System.currentTimeMillis());
        return param;
    }

    @Override
    public CdpTokenPortraitDto getCdpToken(String target) {
        DmsResponse<CdpTokenPortraitDto> cdpToken = dmscusCustomerFeign.getCdpToken();
        logger.info("queryCdpToken,queryCdpToken:{}" , cdpToken);
        if (ObjectUtils.isEmpty(cdpToken)) {
            throw new ServiceBizException("queryCdpToken,error");
        }

        if (cdpToken.isFail()){
            throw new ServiceBizException("queryCdpToken e:{}", cdpToken.getErrMsg());
        }

        if (ObjectUtils.isEmpty(cdpToken.getData())) {
            throw new ServiceBizException("queryCdpToken is null");
        }

        return cdpToken.getData();
    }
    
    @Override
    public List<TagListDto> queryTagList() {
        return queryTagList_();
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public List<TagListDto> queryTagList_() {
        JSONObject param = getJsonObject(null);
        logger.info("获取CDP全量标签信息入参:{}", JSONObject.toJSONString(param));
        CdpResponse<List<SyncTagInfoDTo>> syncTagInfoDTOS = null;
        try {
            syncTagInfoDTOS = cdpFegin.queryTagList(param);
            logger.info("获取CDP全量标签信息响应结果:{}", JSONObject.toJSONString(syncTagInfoDTOS));
        } catch (Exception e) {
            throw new ServiceBizException("queryTagList_ e:{}", e);
        }
        if (syncTagInfoDTOS.isFail()) {
            throw new ServiceBizException("queryTagList is error:" + syncTagInfoDTOS.getMsg());
        }
        List<TagListDto> cdpTagListDtoList = this.resolutionCdpTagInfo(syncTagInfoDTOS.getData());
        return cdpTagListDtoList;
    }

    private List<TagListDto> resolutionCdpTagInfo(List<SyncTagInfoDTo> data) {
        logger.info("CDP标签处理前:{}",JSONObject.toJSONString(data));
        List<TagListDto> list = new ArrayList<>();
        data.forEach(i->{
            TagListDto tagListDto = new TagListDto();
            tagListDto.setTagSource(CommonConstant.CDP_TAG_SOURCE);
            //拆分CDP标签id
            AnalysisTagIdDto analysisTagIdDto =  this.resolutionCdpTagId(i.getTagId());
            tagListDto.setTagId(analysisTagIdDto.getTagId());
            tagListDto.setTagType(analysisTagIdDto.getTagType());
            tagListDto.setTagName(i.getDisplayName());
            tagListDto.setShowName(i.getDisplayName());
            tagListDto.setTagDescription(i.getDescription());
            tagListDto.setIsDetailTag(CommonConstant.DICT_IS_NO);
            tagListDto.setIsGetDetail(CommonConstant.DICT_IS_NO);
            tagListDto.setShowLevel(CommonConstant.SHOW_LEVEL_DONT_SHOW);
            tagListDto.setRemoved(i.getRemoved());
            tagListDto.setValueRule(CommonConstant.TAG_VALUE_RULE);
            list.add(tagListDto);
        });
        logger.info("CDP标签处理后:{}",JSONObject.toJSONString(list));
        return list;
    }
    private AnalysisTagIdDto resolutionCdpTagId(String tagId) {
        AnalysisTagIdDto analysisTagIdDto = new AnalysisTagIdDto();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(tagId)) {
            //切割cdp返回标签id
            int firstUnderscoreIndex = tagId.indexOf('_');
            // 找到第二个下划线的位置
            int secondUnderscoreIndex = tagId.indexOf('_', firstUnderscoreIndex + 1);
            // 如果找到了第二个下划线
            if (secondUnderscoreIndex != -1) {
                // 切割字符串为两部分
                analysisTagIdDto.setTagId(tagId.substring(secondUnderscoreIndex + 1));
                analysisTagIdDto.setTagType(tagId.substring(0, secondUnderscoreIndex));
            }
        }

        return analysisTagIdDto;
    }

    @Override
    public CustomerTagsDto queryTagListByVinAndMobile(String vin, String mobile) {
        return queryTagListByVinAndMobile_(vin, mobile);
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public CustomerTagsDto queryTagListByVinAndMobile_(String vin, String mobile) {
        logger.info("queryTagListByVinAndMobile start...");
        String target = null;
        JSONObject param = getJsonObject(vin);
        if (StringUtils.isNotEmpty(vin)){
            param.put("profile_type",CommonConstant.TAG_VEHICLE_PROFILEBASE);
            param.put("id_value",vin);
            param.put("id_name",CommonConstant.CDP_VIN_ID_NAME);
            target = vin;
        }else if (StringUtils.isNotEmpty(mobile)){
            param.put("profile_type",CommonConstant.TAG_CUSTOMER_PROFILEBASE);
            param.put("id_value",mobile);
            param.put("id_name",CommonConstant.CDP_MOBILE_ID_NAME);
            target = mobile;
        }

        // 尝试从缓存中获取
        CustomerTagsDto cacheCustomerTags = getCacheCdpTag(target);
        if (null == cacheCustomerTags) {
            logger.info("获取用户/车辆属性及标签接口-请求参数：{}", param);
            CdpResponse<JSONObject> cdpResult = null;
            try {
                cdpResult = cdpFegin.queryTagListByVin(param);
                logger.info("获取用户/车辆属性及标签接口-响应结果：{}", cdpResult);
            } catch (Exception e) {
                throw new ServiceBizException("queryTagListByVinAndMobile_ e:{}", e);
            }
            if (null == cdpResult || cdpResult.isFail()) {
                throw new ServiceBizException("getCustomerTags isFail");
            }
            JSONObject data = cdpResult.getData();
            if (null == data) {
                logger.info("queryTagListByVinAndMobile cacheCustomerTags isnull");
                return new CustomerTagsDto();
            }
            // 尝试缓存
            cacheCustomerTags = JSONObject.toJavaObject(data, CustomerTagsDto.class);
            setCacheCdpTag(Arrays.asList(cacheCustomerTags));
        }
        //解析cdp标签集合
        List<CdpTagInfoVo> cdpTagInfoVOS = resolutionCdpTagList(cacheCustomerTags.getTagList());
        List<CdpTagInfoVo> attListInfoVOS = resolutionCdpAttList(cacheCustomerTags.getAttLisit());
        cdpTagInfoVOS.addAll(attListInfoVOS);
        cacheCustomerTags.setTagInfoVOS(cdpTagInfoVOS);
        logger.info("queryTagListByVinAndMobile end...");
        return cacheCustomerTags;
    }

    /**
     * 缓存cdp标签信息
     */
    public void setCacheCdpTag(List<CustomerTagsDto> tags) {
        logger.info("setCacheCdpTag start");
        if (CollectionUtils.isEmpty(tags)) {
            logger.info("setCacheCdpTag tags isEmpty");
            return;
        }
        RBucket<String> bucket = null;
        for (CustomerTagsDto tag : tags) {
            if (null == tag || (null == tag.getIdValue() && null == tag.getVin())) {
                logger.info("setCacheCdpTag tag isnull");
                continue;
            }
            String idValue = tag.getIdValue();
            String vin = tag.getVin();
            logger.info("setCacheCdpTag idValue:{},vin:{}", idValue, vin);
            String key = CommonConstant.CDP_CUSTOMER_OR_VEHICLE_PREFIX.concat(StringUtils.isBlank(idValue) ? vin : idValue);
            logger.info("setCacheCdpTag key:{}", key);
            try {
                bucket = redissonClient.getBucket(key, new StringCodec(CommonConstant.CDP_VALUE_UTF8));
                bucket.set(JSONObject.toJSONString(tag), CommonConstant.CDP_CUSTOMER_CACHE_TIME, TimeUnit.SECONDS);
                logger.info("setCacheCdpTag set success");
            } catch (Exception e) {
                logger.info("setCacheCdpTag e:{}", e);
            }
        }
        logger.info("setCacheCdpTag end...");
    }

    /**
     * 获取cdp标签缓存信息
     */
    public CustomerTagsDto getCacheCdpTag(String target) {
        logger.info("getCacheCdpTag target:{}", target);
        if (StringUtils.isBlank(target)) {
            return null;
        }
        String key = CommonConstant.CDP_CUSTOMER_OR_VEHICLE_PREFIX.concat(target);
        logger.info("getCacheCdpTag key:{}", key);
        // 尝试从缓存中获取，如果获取不到则去cdp进行查询
        try {
            RBucket<String> bucket = redissonClient.getBucket(key, new StringCodec(CommonConstant.CDP_VALUE_UTF8));
            Objects.requireNonNull(bucket, "getCacheCdpTag bucket isnull");
            String s = bucket.get();
            if (StringUtils.isBlank(s)) {
                logger.info("getCacheCdpTag s isBlank");
                return null;
            }
            CustomerTagsDto customerTagsDto = JSONObject.toJavaObject(JSON.parseObject(s), CustomerTagsDto.class);
            if (null == customerTagsDto) {
                logger.info("getCacheCdpTag customerTagsDto isnull");
                return null;
            }
            logger.info("getCacheCdpTag get success");
            return customerTagsDto;
        } catch (Exception e) {
            logger.info("getCacheCdpTag e:{}", e);
        }
        return null;
    }

    @Override
    public List<CdpTagInfoVo> queryTagListByOneId(String oneId) {
        return queryTagListByOneId_(oneId);
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public List<CdpTagInfoVo> queryTagListByOneId_(String oneId) {
        JSONObject param = getJsonObject(null);
       if (StringUtils.isNotEmpty(oneId)){
            param.put("profile_type",CommonConstant.TAG_VEHICLE_PROFILEBASE);
            param.put("id_value",oneId);
            param.put("id_name",CommonConstant.CDP_CAR_ONE_ID);
        }
        logger.info("获取用户/车辆属性及标签接口-请求参数：{}", param);
        CdpResponse<JSONObject> cdpResult = null;
       try {
           cdpResult = cdpFegin.queryTagListByVin(param);
           logger.info("获取用户/车辆属性及标签接口-响应结果：{}", cdpResult);
       } catch (Exception e) {
           throw new ServiceBizException("queryTagListByOneId_ e:{}", e);
       }
        if (cdpResult.isFail()) {
            throw new ServiceBizException("getCustomerTags is error:" + cdpResult.getMsg());
        }
        JSONObject data = cdpResult.getData();
        if (null == data) {
            return new ArrayList<>();
        }
        CustomerTagsDto customerTagsDto = JSONObject.toJavaObject(data, CustomerTagsDto.class);

        //解析cdp标签集合
        List<CdpTagInfoVo> cdpTagInfoVOS = resolutionCdpTagList(customerTagsDto.getTagList());
        List<CdpTagInfoVo> attListInfoVOS = resolutionCdpAttList(customerTagsDto.getAttLisit());
        cdpTagInfoVOS.addAll(attListInfoVOS);
        return cdpTagInfoVOS;
    }

    private List<CdpTagInfoVo> resolutionCdpTagList(List<CdpTagListDto> tagList) {
        logger.info("CDP标签解析前:{}", tagList);
        List<CdpTagInfoVo> cdpTagInfoVOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(tagList)) {
            return cdpTagInfoVOS;
        }
        tagList.forEach(i->{
            CdpTagInfoVo cdpTagInfoVO = new CdpTagInfoVo();
            cdpTagInfoVO.setTagName(i.getTagName());
            cdpTagInfoVO.setTagValue(i.getTagValue());
            //拆分CDP标签id
            AnalysisTagIdDto analysisTagIdDto =  this.resolutionCdpTagId(i.getTagID());
            cdpTagInfoVO.setTagId(analysisTagIdDto.getTagId());
            cdpTagInfoVO.setTagType(analysisTagIdDto.getTagType());
            cdpTagInfoVOS.add(cdpTagInfoVO);
        });
        logger.info("CDP标签解析后:{}", cdpTagInfoVOS);
        return cdpTagInfoVOS;
    }

    private List<CdpTagInfoVo> resolutionCdpAttList(List<CdpAttributeListDto> tagList) {
        logger.info("CDP属性解析前:{}", tagList);
        List<CdpTagInfoVo> cdpTagInfoVOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(tagList)) {
            return cdpTagInfoVOS;
        }
        tagList.forEach(i->{
            CdpTagInfoVo cdpTagInfoVO = new CdpTagInfoVo();
            cdpTagInfoVO.setTagName(i.getAttName());
            cdpTagInfoVO.setTagValue(i.getAttValue());
            //拆分CDP标签id
            cdpTagInfoVO.setTagId(i.getAttID());
            cdpTagInfoVOS.add(cdpTagInfoVO);
        });
        logger.info("CDP属性解析后:{}", cdpTagInfoVOS);
        return cdpTagInfoVOS;
    }

    @Override
    public List<List<CdpTagInfoVo>> customCdpTagList(String mobile) {
        logger.info("customCdpTagList mobile:{}",mobile);
        // 代理调用，否则 Retryable 不生效
        CdpTagInfoServiceImpl proxy = ApplicationContextHelper.getBeanByType(CdpTagInfoServiceImpl.class);
        return proxy.customCdpTagList_(mobile);
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public List<List<CdpTagInfoVo>> customCdpTagList_(String mobile) {
        RetryContext context = RetrySynchronizationManager.getContext();
        int retryCount = context.getRetryCount();

        logger.info("查询客户-*姓名标签 customCdpTagList:{},retryCount:{}",mobile, retryCount);
        CdpTokenPortraitDto token = this.getCdpToken(null);

        //调用cdp接口查询数据
        CdpCustomerParamsDto cdpCustomerParamsDto =new CdpCustomerParamsDto();
        cdpCustomerParamsDto.setAppid(appId);
        cdpCustomerParamsDto.setToken(token.getToken());
        cdpCustomerParamsDto.setAccess_timestamp(System.currentTimeMillis());
        cdpCustomerParamsDto.setSubject_id(mobile);
        cdpCustomerParamsDto.setSubject_id_name(CdpConstant.CDP_TAG_MOBILE);
        cdpCustomerParamsDto.setSubject_profile_type(CdpConstant.CUSTOMER_PROFILEBASE);
        cdpCustomerParamsDto.setObject_profile_type(CdpConstant.VEHICLE_PROFILEBASE);
        cdpCustomerParamsDto.setObject_profile_name(CdpConstant.ONE_ID);
        cdpCustomerParamsDto.setRelation_type(Collections.singletonList(CdpConstant.CDP_CAR_OWNER));
        logger.info("queryCdpCustomer cdpCustomerParamsDto:{}",cdpCustomerParamsDto);
        //客户-姓名
        CdpResponse<JSONObject> relatedProfiles = null;
        try {
            relatedProfiles = cdpFegin.getRelatedProfiles(cdpCustomerParamsDto);
            logger.info("customCdpTagList relatedProfiles:{}",relatedProfiles);
        } catch (Exception e) {
            throw new ServiceBizException("customCdpTagList relatedProfiles error");
        }
        if (relatedProfiles.isFail()) {
            throw new ServiceBizException("customCdpTagList relatedProfiles isFail");
        }

        List<String> customerIds = getCustomerId(relatedProfiles);
        logger.info("customCdpTagList customerIds:{}",customerIds);

        if(CollectionUtils.isEmpty(customerIds)){
            return null;
        }
        List<List<CdpTagInfoVo>> cdpTagInfoVOS = new ArrayList<>();
        for (String customerId : customerIds) {
            cdpTagInfoVOS.add(this.queryTagListByOneId(customerId));
        }
        return cdpTagInfoVOS;
    }

    /**
     * 获取用户id
     */
    public List<String> getCustomerId(CdpResponse<JSONObject> relatedProfiles){
        if (!relatedProfiles.isSuccess()){
            return null;
        }
        JSONObject data = relatedProfiles.getData();
        if (null == data) {
            return null;
        }
        CdpCustomerResponseDto responseDto = JSONObject.toJavaObject(data, CdpCustomerResponseDto.class);
        if (ObjectUtils.isEmpty(responseDto.getRelated_profiles()) || ObjectUtils.isEmpty(responseDto.getRelated_profiles().get(0))){
            return null;
        }
        return Arrays.asList(responseDto.getRelated_profiles().get(0).getObject_oneid());
    }


}
