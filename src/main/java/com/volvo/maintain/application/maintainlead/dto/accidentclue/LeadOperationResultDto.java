package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueDto;
import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueResDto;
import com.volvo.maintain.infrastructure.gateway.response.CompanyDetailDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;



@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class LeadOperationResultDto {

    /**
     * liteCrm 线索id
     */
    private Long id;

    /**
     * 湖仓线索id
     */
    private String sourceClueId;

    /**
     * 车辆VIN码
     */
    private String vehicleVin;

    /**
     * LiteCrm收集时间
     */
    private String leadsReceiveTime;

    /**
     * 线索类型
     */
    private String leadsType;
    /**
     * 数据状态 0:非异常 1:异常
     */
    private Integer dataStatus;

    private ClueDataDto data;

    private CompanyDetailDto companyDetail;

    private CompanyDetailDTO companyDetailDto;

    private DistributeClueResDto distributeClueResDto;

    private DistributeClueDto distributeClueDto;
}
