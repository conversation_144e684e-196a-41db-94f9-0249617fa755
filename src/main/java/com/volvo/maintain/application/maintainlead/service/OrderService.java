package com.volvo.maintain.application.maintainlead.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.RequestDto;
import com.volvo.maintain.application.maintainlead.dto.SettlementDocConfirmCompensateDto;
import com.volvo.maintain.application.maintainlead.vo.order.*;
import com.volvo.maintain.application.maintainlead.dto.order.*;
import org.springframework.validation.BindingResult;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface OrderService {

    Page<ShopVehicleDeliverVo> shopSearchVehiicleDeliverPage(RequestDto<ShopVehicleDeliverDto> shopVehicleDeliverDto);

    boolean cancelVehicleOrder(RequestDto<CancelOrderDto> cancelOrderDto);

    PlaceOrderVo placeOrder(RequestDto<PlaceOrderDto> placeOrderDto);

    List<ShopVehicleDeliverVo> shopSearchVehiicleDeliverList(RequestDto<ShopVehicleDeliverDto> shopVehicleDeliverDto);

    List<VehicleDeliverDetailVo> allDetail(RequestDto<QueryVehicleDeliverDto> queryVehicleDeliverDto);

    BigDecimal balanceCustomerId(String customerId);

    List<DriverVo> getFixList(RequestDto<DriverDto> driverDto);

    PriceVo price(RequestDto<PriceDto> priceDto);

    DetailVo detailOrderId(String orderId);

    TraceVo traceOrderId(String orderId, Integer type);

    DriverInfoVo driverInfoOrderId(String orderId, Integer type);

    Map<String, Object> getCarPhotosOrderId(String orderId, Integer daijiaType);

    SaveVehicleDeliverVo addVehicleDeliverOldDms(SaveVehicleDeliverDto saveVehicleDeliverDto, BindingResult bindingResult);
    /**
     * 结算单确认补偿
     */
    void settlementDocConfirmCompensate(SettlementDocConfirmCompensateDto parseObject);
}
