package com.volvo.maintain.application.maintainlead.dto.part;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 缺料补货DTO
 * </p>
 */
@ApiModel(description = "缺料补货DTO")
@Data
public class ShortPartReplenishmentDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 零件明细集合
     */
    @ApiModelProperty(value = "零件明细集合", hidden = true)
    private List<PartDto> parts;
    /**
     * 缺料明细集合
     */
    @ApiModelProperty(value = "缺料明细集合", hidden = true)
    private List<ShortPartResultDTO> shortages;

    /**
     * 预约时间
     */
    @ApiModelProperty(value = "预约时间", hidden = true)
    private String reservationTime;
    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型", hidden = true)
    private Integer orderType;
    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", hidden = true)
    private Integer purchasePaymentType;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", hidden = true)
    private String ownerCode;
    /**
     * 经销商
     */
    @ApiModelProperty(value = "经销商", hidden = true)
    private String roNo;
    /**
     * 调用来源 C客户接待
     */
    @ApiModelProperty(value = "调用来源 C客户接待", hidden = true)
    private String sourceData;
}
