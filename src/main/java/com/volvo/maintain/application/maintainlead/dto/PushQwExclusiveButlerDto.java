package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName PushQwExclusiveButlerDto
 * <AUTHOR>
 * @Date 2024/02/29
 */
@Data
@ApiModel("推送企微EM90专属客服 ")
public class PushQwExclusiveButlerDto {

    @ApiModelProperty(value = "车主唯一标识号(车架号)",name = "vin")
    private String vin;

    @ApiModelProperty(value = "推送内容" , name = "content")
    private String content;
}
