package com.volvo.maintain.application.maintainlead.service.strategy.rights;

import com.volvo.maintain.application.maintainlead.emums.RightsStrategyEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Evcar延保策略
 */
@Slf4j
@Component
public class EvcarWarrantyStrategy extends AbsExtWarrantyExecuteStrategy {

    @Override
    public String mark() {
        return RightsStrategyEnum.EVCAR_EXTENDED_WARRANTY.getCode().toString();
    }
}
