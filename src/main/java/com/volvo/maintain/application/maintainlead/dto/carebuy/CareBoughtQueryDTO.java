
package com.volvo.maintain.application.maintainlead.dto.carebuy;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CareBoughtQueryDTO implements Serializable {

	/** serialVersionUID */
	private static final long serialVersionUID = 1L;

	/**
	 * 活动类型
	 */
	private Integer activityType;

	/**
	 * 保养活动编号
	 */
	private String activityNo;

	/**
	 * 保养活动名称
	 */
	private String activityName;

	/**
	 * 车架号
	 */
	private String vin;

	/**
	 * 购买工单号
	 */
	private String workNo;

	/**
	 * 购买日期（开始）
	 * 
	 */
	private String purchaseDateBegin;

	/**
	 * 购买日期（结束）
	 * 
	 */
	private String purchaseDateEnd;
	/**
	 * 销售经销商
	 */
	private String saleDealer;

	/**
	 * 销售经销商
	 */
	private String[] saleDealers;

	/**
	 * 工单结算日期(开始)(yyyy-MM-dd HH:mm:ss)
	 *
	 */
	private String settlementDateBegin;

	/**
	 * 工单结算日期(结束)(yyyy-MM-dd HH:mm:ss)
	 *
	 */
	private String settlementDateEnd;

	/**
	 * 使用范围
	 */
	private String useScope;

	private Integer currentPage;

	private Integer pageSize;

	/**
	 * 客户端传入的区域id
	 */
	private String areaManage;

	private  String giveStatus;

}
