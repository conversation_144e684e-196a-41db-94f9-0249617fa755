package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("中台经销商信息")
public class IsExistByCodeDto {

    private String companyCode;


    private Boolean isAllExist;

    private List<String> notExistCompanyCodeList;

}
