package com.volvo.maintain.application.maintainlead.dto.vhc;

import lombok.Data;

import java.io.Serializable;

/**
 * vhc报价-检查项二级类目DTO
 */
@Data
public class VhcRemindDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Integer id;
    /**
     * 经销商
     */
    private String ownerCode;
    /**
     * 车辆健康检查编号
     */
    private String vhcNo;
    /**
     * VHC开始提醒：89710001
     * 报价反选后的消息提醒：89710002
     * 转工单后的消息提醒：89710003
     * 报价打印：89710004
     * 流转仓库：89710005
     * 流转服务顾问：89710006
     */
    private String remindType;

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 车牌号
     */
    private String license;


    /**
     * 创建人id
     */
    private String createdBy;

    /**
     * 创建人
     */
    private String createdByName;

    /**
     * 送修人
     */
    private String deliverer;

}
