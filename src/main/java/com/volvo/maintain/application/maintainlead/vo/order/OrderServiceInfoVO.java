package com.volvo.maintain.application.maintainlead.vo.order;


import lombok.Data;

import java.io.Serializable;

/**
 * 服务过程信息
 */
@Data
public class OrderServiceInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 经销商代码
     */
    private String ownerCode;

    /**
     * 工单号
     */
    private String orderNo;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 服务信息url
     */
    private String serviceInformationUrl;

    /**
     * 信息类型：0图片 1视频
     */
    private Integer infoType;

    /**
     * 备注
     */
    private String remark;



}
