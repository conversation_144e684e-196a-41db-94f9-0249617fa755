package com.volvo.maintain.application.maintainlead.vo.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 提醒规则详情信息
 * @Date 2024/11/14 13:41
 */
@ApiModel("提醒规则详情信息")
@Data
public class RemindRuleDetailVo {

    @ApiModelProperty("提醒类型")
    private String businessType;

    @ApiModelProperty("是否启用")
    private String isEnabled;

    @ApiModelProperty("提醒规则")
    private String remindRule;

    @ApiModelProperty("运算规则")
    private String operationSymbol;

    @ApiModelProperty("值")
    private String remindValue;

    @ApiModelProperty("提醒时间")
    private String remindTime;

    @ApiModelProperty("提醒角色")
    private Map<Integer, List<RemindRoleVo>> remindRoleInfo;
}
