package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ETADocumentDetails implements Serializable {

    /**
     * 销售订单主档信息
     */
    private SalesMainDetailsDto salesMainDetails;

    /**
     * 销售订单明细列表
     */
    private List<itemDetailsDto> itemDetailsList;

    /**
     * 出库单列表
     */
    private List<OutboundOrderDto> odoList;

    /**
     * BO单列表
     */
    private List<SalesItemOutStockDetailsDto> boList;

}
