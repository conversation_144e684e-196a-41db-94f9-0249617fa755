package com.volvo.maintain.application.maintainlead.service.strategy.rights;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.rights.*;
import com.volvo.maintain.application.maintainlead.emums.RightsStrategyEnum;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.MaintainActivityVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 服务合同策略
 */
@Slf4j
@Component
public class ServiceContractStrategy extends AbsRightsExecuteStrategy {

    @Autowired
    private DmscusRepairFeign dmscusRepairFeign;

    @Autowired
    private DmscusIfserviceFeign dmscusIfserviceFeign;

    @Autowired
    private DmscusReportFeign dmscusReportFeign;

    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;

    @Autowired
    private MidEndOrgCenterFeign midEndOrgCenterFeign;

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public String mark() {
        return RightsStrategyEnum.SERVICE_CONTRACT.getCode().toString();
    }


    /**
     * 查询可上架商品列表
     *
     * @param dto
     */
    @Override
    public IPage<RightsProductDto> productList(RightsProductDto dto) {
        //查询服务合同商品列表
        Page<RightsProductDto> page = new Page<>();
        DmsResponse<Page<MaintainActivityVo>> pageDmsResponse = dmscusRepairFeign.maintainActivityGetByPage(dto.getProductName(),dto.getProductNo(),dto.getProductType(),10041001,1,10041001,dto.getPageNum(),dto.getPageSize());
        log.info("查询服务合同商品列表接口返回结果：{}",pageDmsResponse);
        Page<MaintainActivityVo> data = pageDmsResponse.getData();
        List<MaintainActivityVo> records = data.getRecords();
        if (pageDmsResponse.isSuccess() && ObjectUtils.isNotEmpty(data) && !CollectionUtils.isEmpty(records)) {
            BeanUtil.copyProperties(data,page);
            page.setRecords(records.stream().map(e -> {
                RightsProductDto productDto = new RightsProductDto();
                productDto.setProductName(e.getMaintainName());
                productDto.setProductNo(e.getPartNo());
                productDto.setProductType(e.getActivityType());
                return productDto;
            }).collect(Collectors.toList()));
        }
        return page;
    }

    @Override
    public List<GiveStatusSyncDto> giveSync(List<GiveStatusSyncDto> statusSyncDtos) {
        //服务合同 激活/取消流程
        DmsResponse<List<GiveStatusSyncDto>> dmsResponse = dmscusIfserviceFeign.syncGiveStatus(statusSyncDtos);
        if(dmsResponse.isFail()){
            throw new ServiceBizException(dmsResponse.getErrMsg());
        }
        return dmsResponse.getData();
    }

    @Override
    public List<PurchaseEligibilityCheckResponseDto> buyValid(PurchaseEligibilityCheckRequestDto purchaseEligibilityCheckRequestDto) {
        DmsResponse<List<PurchaseEligibilityCheckResponseDto>> response = dmscusRepairFeign.serveContractBuyValid(purchaseEligibilityCheckRequestDto);
        log.info("服务合同购买校验结果:{}",response);
        if(response.isFail()){
            throw new ServiceBizException(response.getErrMsg());
        }
        DmsResponse<CommonConfigDto> configDmsResponse= dmscloudServiceFeign.getConfigByKey(RightsStrategyEnum.SERVICE_CONTRACT.getCode().toString(), "mall_sales_product_quantity");
        CommonConfigDto data = configDmsResponse.getData();
        if (configDmsResponse.isFail() || Objects.isNull(data)) {
            throw new ServiceBizException("获取购买数量异常");
        }
        if (org.springframework.util.StringUtils.isEmpty(data.getConfigValue())){
            throw new ServiceBizException("获取购买数量为空");
        }
        List<PurchaseEligibilityCheckResponseDto> dtos = response.getData();
        dtos.forEach(obj -> {
            if (org.springframework.util.StringUtils.isEmpty(obj.getCode())){
                obj.setCount(Integer.parseInt(data.getConfigValue()));
            }
        });
        setErrorMsg(dtos);
        return dtos;
    }


    @Override
    public void give(ContractPurchaseGiveDto giveDto){
        if(Objects.isNull(giveDto.getMileage())){
            giveDto.setMileage(1);
        }
        //此处调服务合同购买接口
        DmsResponse<Void> dmsResponse=dmscusIfserviceFeign.give(giveDto);
        log.info("dmscusIfservice give dmsResponse:{}",dmsResponse);
        if(dmsResponse.isFail()){
            throw new ServiceBizException(dmsResponse.getErrMsg());
        }
    }

    @Override
    public List<OrderActivationDetailsResponseDto> giveActList(OrderActivationDetailsRequestDto orderActivationDetailsRequestDto){
        log.info("服务合同激活记录查询参数:{}",orderActivationDetailsRequestDto);
        DmsResponse<List<OrderActivationDetailsResponseDto>> dmsResponse=dmscusIfserviceFeign.serviceContractGiveRecordList(orderActivationDetailsRequestDto);
        log.info("服务合同激活记录查询结果:{}",dmsResponse);
        if(dmsResponse.isFail()){
            throw new ServiceBizException(dmsResponse.getErrMsg());
        }
        return  buildOrderActivationDetailsResponseDto(dmsResponse.getData());
    }

    private  List<OrderActivationDetailsResponseDto> buildOrderActivationDetailsResponseDto(List<OrderActivationDetailsResponseDto> strategyResponses) {
        log.info("buildOrderActivationDetailsResponseDto give strategyResponses result:{}", strategyResponses);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(strategyResponses)) {
            return Lists.newArrayList();
        }
        List<String>giveIds= strategyResponses.stream().map(OrderActivationDetailsResponseDto::getGiveId).collect(Collectors.toList());
        DmsResponse<List<OrderActivationDetailsResponseDto>> dmsResponse=  dmscloudServiceFeign.getGiveStatusByGiveId(giveIds);
        log.info("buildOrderActivationDetailsResponseDto give dmsResponse result:{}", dmsResponse);
        if(dmsResponse.isFail()){
            throw new com.volvo.exception.ServiceBizException(dmsResponse.getErrMsg());
        }
        List<OrderActivationDetailsResponseDto> giveStatusList= dmsResponse.getData();
        giveStatusList.stream()
                .map(rel -> strategyResponses.stream()
                        .filter(ord -> rel.getGiveId().equals(ord.getGiveId()))
                        .findFirst()
                        .map(ord -> {
                            rel.setVin(ord.getVin());
                            rel.setOrderCode(ord.getOrderCode());
                            rel.setProductNo(ord.getProductNo());
                            return rel;
                        }).orElse(null)
                ).collect(Collectors.toList());
        return  giveStatusList;
    }


    @Override
    public List<UsageDetailsResponseDto> useList(UsageDetailsRequestDto usageDetailsDto){
        RequestDto requestDto = new RequestDto();
        requestDto.setData(usageDetailsDto);
        requestDto.setPage(1L);
        requestDto.setPageSize(50l);
        DmsResponse<List<UsageDetailsResponseDto>> dmsResponse=midEndVehicleCenterFeign.careRecordList(requestDto);
        log.info("服务合同使用记录查询结果:{}",dmsResponse);
        if(dmsResponse.isFail()){
            throw new ServiceBizException(dmsResponse.getErrMsg());
        }
        List<UsageDetailsResponseDto> responseDtos = dmsResponse.getData();
        if (ObjectUtils.isEmpty(responseDtos)) {
            return  Collections.emptyList();
        }
        String collect1 = responseDtos.stream().map(UsageDetailsResponseDto::getDealerNo).collect(Collectors.joining(","));
        IsExistByCodeDto build2 = IsExistByCodeDto.builder().companyCode(collect1).build();
        log.info("调用mid-end-org-center查询经销商名称:{}", build2);
        ResponseDto<List<CompanyDetailByCodeDto>> listResponseDto = midEndOrgCenterFeign.selectByCompanyCode(build2);
        if (Objects.isNull(listResponseDto) || listResponseDto.isFail()) {
            log.error("调用mid-end-org-center查询经销商名称失败:{}", listResponseDto);
            throw new ServiceBizException("查询经销商失败");
        }
        if (!CollectionUtils.isEmpty(listResponseDto.getData())) {
            responseDtos.stream().forEach(item -> {
                item.setUseDate(item.getBillingDate());
                item.setOwnerCode(item.getDealerNo());
                if(StringUtils.isNotBlank(item.getOwnerCode())){
                    for (CompanyDetailByCodeDto itme2:listResponseDto.getData()) {
                        if(item.getOwnerCode().equals(itme2.getCompanyCode())){
                            item.setOwnerName(itme2.getCompanyNameCn());
                        }
                    }
                }
            });
        }
        return  responseDtos;
    }

    @Override
    public IPage<GiveRecordResponseDto> givelist(GiveRecordRequestDto giveRecordRequestDto)  {
        DmsResponse<Page<GiveRecordResponseDto>> dmsResponse= dmscusReportFeign.queryGiveList(giveRecordRequestDto);
        log.info("giveList dmsResponse:{}",dmsResponse);
        if (dmsResponse.isFail()) {
            throw new ServiceBizException(dmsResponse.getErrMsg());
        }
        return  dmsResponse.getData();
    }
}
