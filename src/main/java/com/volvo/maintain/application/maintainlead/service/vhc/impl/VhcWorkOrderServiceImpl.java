package com.volvo.maintain.application.maintainlead.service.vhc.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.vhc.*;
import com.volvo.maintain.application.maintainlead.dto.workshop.SceneMessageRemindDto;
import com.volvo.maintain.application.maintainlead.mq.producer.WorkshopMessageReminderProducer;
import com.volvo.maintain.application.maintainlead.service.vhc.VhcWorkOrderService;
import com.volvo.maintain.application.maintainlead.vo.UserInfoVo;
import com.volvo.maintain.infrastructure.constants.AccidentCluesConstant;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.constants.VhcConstants;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VhcWorkOrderServiceImpl implements VhcWorkOrderService {
    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
    @Autowired
    private MidEndAuthCenterFeign midEndAuthCenterFeign;
    @Autowired
    private MaintenanceServiceFeign maintenanceServiceFeign;
    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;

    @Autowired
    private WorkshopMessageReminderProducer workshopMessageReminderProducer;


    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Override
    public VhcQuotedDTO queryMaintenanceItems(String vhcNo) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if(Objects.isNull(currentLoginInfo.getOwnerCode())){
            throw new ServiceBizException("获取登录人信息失败");
        }
        DmsResponse<VhcQuotedDTO> queryMaintenanceItems = domainMaintainOrdersFeign.queryMaintenanceItems(vhcNo, currentLoginInfo.getOwnerCode());
        log.info("queryMaintenanceItems:{}", JSON.toJSONString(queryMaintenanceItems));
        if (null == queryMaintenanceItems || queryMaintenanceItems.isFail()) {
            log.info("queryMaintenanceItems:{}", JSON.toJSONString(queryMaintenanceItems));
            throw new ServiceBizException("查询维修项失败："+queryMaintenanceItems.getErrMsg());
        }
        VhcQuotedDTO data = queryMaintenanceItems.getData();
        // 封装车辆图片信息
        DmsResponse<TmVehicleDto> vehicle = midEndVehicleCenterFeign.queryVehicleByVIN(data.getVin());
        log.info("NB定制化标签查询 车辆信息 selectInviteClueTag->vehicleResponse:{}",vehicle);
        if(vehicle == null || !CommonConstant.SUCCESS_CODE.equals(vehicle.getReturnCode()) || Objects.isNull(vehicle.getData())){
            return data;
        } else {
            TmVehicleDto vehicleDto = vehicle.getData();
            data.setColorPcImage(vehicleDto.getColorPcImage());
            data.setColorDsPcImage(vehicleDto.getColorDsPcImage());
            data.setColorBigImage(vehicleDto.getColorBigImage());
            data.setColorSmallImage(vehicleDto.getColorSmallImage());
            data.setModelName(vehicleDto.getModelName());
            data.setModelCode(vehicleDto.getModelCode());
        }
        return data;
    }

    @Override
    public Integer checkedPartShort(String partNo, BigDecimal practicalQuantity, String storageCode) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if(Objects.isNull(currentLoginInfo.getOwnerCode())){
            throw new ServiceBizException("获取登录人信息失败");
        }
        DmsResponse<Integer> checkedPartShort = domainMaintainOrdersFeign.checkedPartShort(partNo, practicalQuantity, currentLoginInfo.getOwnerCode(), storageCode);
        if (null == checkedPartShort || checkedPartShort.isFail() || null == checkedPartShort.getData()) {
            log.info("checkedWorkOrderHavePart:{}", JSON.toJSONString(checkedPartShort));
            throw new ServiceBizException("校验是否缺料失败："+checkedPartShort.getErrMsg());
        }
        return checkedPartShort.getData();
    }

    @Override
    public Integer checkedWorkOrderHavePart(String partNo, String labourCode, String roNo) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if(Objects.isNull(currentLoginInfo.getOwnerCode())){
            throw new ServiceBizException("获取登录人信息失败");
        }
        DmsResponse<CommonConfigDto> configByKey = dmscloudServiceFeign.getConfigByKey(VhcConstants.VHC_SET_CODE,VhcConstants.VHC_CODE);
        log.info("selectVhcList 套餐 configByKey :{}",configByKey);
        if (ObjectUtils.isEmpty(configByKey) && Objects.isNull(configByKey.getData())) {
            log.info("selectVhcList 查询VHC套餐code为空");
            return null;
        }
        DmsResponse<CommonConfigDto> configCode = dmscloudServiceFeign.getConfigByKey(VhcConstants.VHC_LABOUR_CODE,VhcConstants.VHC_CODE);
        log.info("selectVhcList 工时code configByKey :{}",configCode);
        if (ObjectUtils.isEmpty(configCode) && Objects.isNull(configCode.getData())) {
            log.info("selectVhcList 查询VHC工时code为空");
            return null;
        }

        DmsResponse<Integer> checkedWorkOrderHavePart = domainMaintainOrdersFeign.checkedWorkOrderHavePart(partNo,labourCode, roNo, currentLoginInfo.getOwnerCode(), configByKey.getData().getConfigValue(), configCode.getData().getConfigValue());
        if (null == checkedWorkOrderHavePart || checkedWorkOrderHavePart.isFail() || null == checkedWorkOrderHavePart.getData()) {
            log.info("checkedWorkOrderHavePart:{}", JSON.toJSONString(checkedWorkOrderHavePart));
            throw new ServiceBizException("校验是否在工单失败："+checkedWorkOrderHavePart.getErrMsg());
        }
        return checkedWorkOrderHavePart.getData();
    }

    @Override
    public void saveMaintenanceItems(VhcMaintanceReqDTO dto) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if(Objects.isNull(currentLoginInfo.getOwnerCode())){
            throw new ServiceBizException("获取登录人信息失败");
        }
        dto.setOwnerCode(currentLoginInfo.getOwnerCode());
        //调用领域层进行数据保存或修改
        DmsResponse<Void> voidDmsResponse = domainMaintainOrdersFeign.saveMaintenanceItems(dto);
        if(voidDmsResponse.isFail()){
            log.info("saveMaintenanceItems isfail:{}", JSON.toJSONString(voidDmsResponse));
            throw new ServiceBizException("操作失败"+voidDmsResponse.getErrMsg());
        }

        if(dto.isFlag()){
            sendTouchpointMessage(currentLoginInfo.getOwnerCode(),dto.getRoNo(),CommonConstant.WORK_SHOP_VHC_QUOTATION);
        }

    }


    private void sendTouchpointMessage(String ownerCode, String roNo,String sceneType) {
        //发送VHC报价
        SceneMessageRemindDto sceneMessageRemindDto = new SceneMessageRemindDto();
        sceneMessageRemindDto.setOwnerCode(ownerCode);
        sceneMessageRemindDto.setBusinessId(roNo);
        sceneMessageRemindDto.setSceneType(sceneType);
        sceneMessageRemindDto.setBusinessParameter(new JSONObject());
        // 发送消息
        workshopMessageReminderProducer.sendOrderMsg(sceneMessageRemindDto, roNo);
    }

    /**
     * 消息推送app
     * @param
     */
    private void pushAppMsg(String vhcNo,String roNo,String ownerCode) {
        log.info("pushAppMsg 开始:{},{},{}",vhcNo,roNo,ownerCode);
        DmsResponse<VhcInfoPoDTO> vhcInfoPoDTODmsResponse = domainMaintainOrdersFeign.selectVhcInfo(ownerCode, roNo, vhcNo);
        log.info("vhcInfoPoDTODmsResponse :{}",vhcInfoPoDTODmsResponse);
        if (!vhcInfoPoDTODmsResponse.isFail() && Objects.nonNull(vhcInfoPoDTODmsResponse.getData())){
            VhcMessageRequestDto vhcMessageRequestDto = new VhcMessageRequestDto();
            vhcMessageRequestDto.setDealerCode(ownerCode);
            vhcMessageRequestDto.setHealthNo(vhcNo);
            vhcMessageRequestDto.setRoNo(roNo);
            vhcMessageRequestDto.setVin(vhcInfoPoDTODmsResponse.getData().getVin());
            vhcMessageRequestDto.setExamineTime(DateUtil.formatLocalDateTime(vhcInfoPoDTODmsResponse.getData().getCreatedAt()));
            log.info("vhcMessageRequestDto :{}",vhcMessageRequestDto);
            DmsResponse dmsResponse = maintenanceServiceFeign.examiningReportPush(vhcMessageRequestDto);
            log.info("dmsResponse :{}",dmsResponse);
        }
    }

    @Override
    public void pushCustomer(String vhcNo, String roNo, Integer flag, String itemIds) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if(Objects.isNull(currentLoginInfo.getOwnerCode())){
            throw new ServiceBizException("获取登录人信息失败");
        }
        //流转仓储，需要查询仓管员角色
        String kgyIds = "";
        if(flag == 3){
            ResponseDto<EmpByRoleCodeDto> reqDos = new ResponseDto<>();
            EmpByRoleCodeDto empDto = new EmpByRoleCodeDto();
            // 经销商
            empDto.setCompanyCode(currentLoginInfo.getOwnerCode());
            // 在职状态
            empDto.setIsOnjob(CommonConstant.IS_ON_JOB_IN);
            // 角色code
            empDto.setRoleCode(Collections.singletonList(AccidentCluesConstant.KGY));
            reqDos.setData(empDto);
            DmsResponse<List<EmpByRoleCodeDto>> response = midEndAuthCenterFeign.queryDealerUser(reqDos);
            if (null == response || response.isFail() || CollectionUtils.isEmpty(response.getData())) {
                throw new ServiceBizException("获取仓管员信息失败");
            }
            List<EmpByRoleCodeDto> data = response.getData();
            kgyIds = data.stream().map(item -> String.valueOf(item.getUserId())).collect(Collectors.joining(","));
        }
        if (flag==1){
            //消息推送app
            pushAppMsg(vhcNo,roNo,currentLoginInfo.getOwnerCode());
        }

        DmsResponse<Void> voidDmsResponse = domainMaintainOrdersFeign.pushCustomer(vhcNo, roNo, currentLoginInfo.getOwnerCode(), flag, kgyIds, itemIds);
        if(voidDmsResponse.isFail()){
            log.info("pushCustomer isfail:{}", JSON.toJSONString(voidDmsResponse));
            throw new ServiceBizException("操作失败"+voidDmsResponse.getErrMsg());
        }


    }

    @Override
    public List<VhcRemindDTO> queryVhcRemind(Long userId) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if(Objects.isNull(currentLoginInfo.getOwnerCode())){
            throw new ServiceBizException("获取登录人信息失败");
        }
        DmsResponse<List<VhcRemindDTO>> queryVhcRemind = domainMaintainOrdersFeign.queryVhcRemind(userId, currentLoginInfo.getOwnerCode());
        if (null == queryVhcRemind || queryVhcRemind.isFail()) {
            log.info("queryVhcRemind:{}", JSON.toJSONString(queryVhcRemind));
            throw new ServiceBizException("查询消息提醒失败");
        }
        List<VhcRemindDTO> vhcRemindDTOS = queryVhcRemind.getData();
        if(CollectionUtils.isNotEmpty(vhcRemindDTOS)) {
            //更新创建人名称字段
            List<Long> userIds = vhcRemindDTOS.stream().filter(e -> StringUtils.isNotBlank(e.getCreatedBy())).map(e -> Long.parseLong(e.getCreatedBy())).distinct().collect(Collectors.toList());
            UserInfoByUserIdsDto userInfoByUserIdsDto = UserInfoByUserIdsDto.builder().userIds(userIds).build();
            RequestDto<UserInfoByUserIdsDto> build = new RequestDto<>();
            build.setData(userInfoByUserIdsDto);
            log.info("queryUserInfoByIds params:{}", JSON.toJSONString(build));
            ResponseDto<List<UserInfoVo>> listResponseDto = midEndAuthCenterFeign.queryUserInfoByIds(build);
            if(listResponseDto.isSuccess() && CollectionUtils.isNotEmpty(listResponseDto.getData())){
                log.info("queryUserInfoByIds is fail:{}", JSON.toJSONString(listResponseDto));
                List<UserInfoVo> userInfoVos = listResponseDto.getData();
                Map<Integer, String> userInfoMaps = userInfoVos.stream().collect(Collectors.toMap(UserInfoVo::getUserId, UserInfoVo::getEmployeeName));
                vhcRemindDTOS.stream().forEach(e -> e.setCreatedByName(userInfoMaps.get(Integer.parseInt(e.getCreatedBy()))));
            }
        }
        return vhcRemindDTOS;
    }

    @Override
    public void removeInfoById(Integer id) {
        domainMaintainOrdersFeign.removeInfoById(id);
    }

    @Override
    public void confirmRepair(VhcConfirmRepairDTO dto) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if(Objects.isNull(currentLoginInfo.getOwnerCode())){
            throw new ServiceBizException("获取登录人信息失败");
        }
        dto.setOwnerCode(currentLoginInfo.getOwnerCode());
        DmsResponse<Void> voidDmsResponse = domainMaintainOrdersFeign.confirmRepair(dto);
        if (null == voidDmsResponse || voidDmsResponse.isFail()) {
            log.info("confirmRepair:{}", JSON.toJSONString(voidDmsResponse));
            throw new ServiceBizException("确认是否维修失败"+voidDmsResponse.getErrMsg());
        }

//        DmsResponse<VhcInfoPoDTO> vhcInfoPoDTODmsResponse = domainMaintainOrdersFeign.selectVhcInfo(currentLoginInfo.getOwnerCode(), null, dto.getVhcNo());
//        log.info("vhcInfoPoDTODmsResponse :{}",vhcInfoPoDTODmsResponse);
//        VhcInfoPoDTO vhcInfoPoDTO = vhcInfoPoDTODmsResponse.getData();
//        if (!vhcInfoPoDTODmsResponse.isFail() && Objects.nonNull(vhcInfoPoDTO)){
//            sendTouchpointMessage(currentLoginInfo.getOwnerCode(),vhcInfoPoDTO.getRoNo(),CommonConstant.WORK_SHOP_VHC_CONFIRM);
//        }
    }

    @Override
    public void verification(String roNo, String ownerCode, String isPad) {
        DmsResponse<Boolean> booleanDmsResponse = domainMaintainOrdersFeign.checkPushVhcPricesheet(roNo, ownerCode);
        if (Objects.nonNull(booleanDmsResponse) && booleanDmsResponse.isSuccess() && !booleanDmsResponse.getData()) {
            log.info("checkPushVhcPricesheet:{}", JSON.toJSONString(booleanDmsResponse));
            throw new ServiceBizException("请先推送车辆健康检查报价给用户");
        }
        dmscloudServiceFeign.verification(roNo,isPad);
        //质检确认维修状态
        DmsResponse<CommonConfigDto> vhcLabourCode = dmscloudServiceFeign.getConfigByKey(VhcConstants.VHC_LABOUR_CODE,VhcConstants.VHC_CODE);
        log.info("selectVhcPricesheetList configByKey :{}",vhcLabourCode);
        if (ObjectUtils.isEmpty(vhcLabourCode) && Objects.isNull(vhcLabourCode.getData())) {
            log.info("selectVhcPricesheetList 查询VHC工时code为空");
            return;
        }
        DmsResponse<CommonConfigDto> vhcSetCode = dmscloudServiceFeign.getConfigByKey(VhcConstants.VHC_SET_CODE,VhcConstants.VHC_CODE);
        log.info("selectVhcList 套餐 configByKey :{}",vhcSetCode);
        if (ObjectUtils.isEmpty(vhcSetCode) && Objects.isNull(vhcSetCode.getData())) {
            log.info("selectVhcList 查询VHC套餐code为空");
            return;
        }
        domainMaintainOrdersFeign.confirmClassResult(ownerCode,roNo,vhcLabourCode.getData().getConfigValue(),vhcSetCode.getData().getConfigValue());
    }

    @Override
    public void saveNotRepair(VhcNotRepairReqDTO dto) {
        domainMaintainOrdersFeign.saveNotRepair(dto);
    }

    private void checkParams(VhcMaintanceReqDTO dto){
        List<VhcItemDTO> vhcItemList = dto.getVhcItemList();
        if(CollectionUtils.isEmpty(vhcItemList)){
            throw new ServiceBizException("维修项二级类目数据不能为空");
        }
        for (VhcItemDTO item:vhcItemList) {
            if(StringUtils.isBlank(item.getWorkDate())){
                throw new ServiceBizException(item.getItemName()+"内容不完整请补充");
            }
            if(CollectionUtils.isEmpty(item.getVhcPricesheetItemList())){
                throw new ServiceBizException(item.getItemName()+"内容不完整请补充");
            }
        }
    }
}
