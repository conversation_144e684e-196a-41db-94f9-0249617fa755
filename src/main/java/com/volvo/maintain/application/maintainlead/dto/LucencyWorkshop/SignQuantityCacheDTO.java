package com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 签到数量缓存数据传输对象
 */
@Slf4j
@Data
public class SignQuantityCacheDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 经销商代码
     */
    private String ownerCode;

    /**
     * 开始日期
     */
    private String beginDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 签到数量数据
     */
    private List<SignQuantityDTO> signQuantityList;

    /**
     * 缓存创建时间
     */
    private LocalDateTime cacheTime;

    /**
     * 数据版本号（用于并发控制）
     */
    private Long version;

    public SignQuantityCacheDTO() {
        this.cacheTime = LocalDateTime.now();
        this.version = System.currentTimeMillis();
    }

    public SignQuantityCacheDTO(String ownerCode, String beginDate, String endDate, List<SignQuantityDTO> signQuantityList) {
        this.ownerCode = ownerCode;
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.signQuantityList = signQuantityList;
        this.cacheTime = LocalDateTime.now();
        this.version = System.currentTimeMillis();
    }

    /**
     * 检查缓存是否需要刷新（超过5分钟）
     */
    public boolean needsRefresh() {
        LocalDateTime date = LocalDateTime.now();
        boolean flag = cacheTime.isBefore(date.minusMinutes(8));
        log.info("SignQuantityCacheDTO,cacheTime:{}, date:{}, flag:{}", cacheTime, date, flag);
        return flag;
    }
}
