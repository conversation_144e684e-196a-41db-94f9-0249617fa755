package com.volvo.maintain.application.maintainlead.service.clues;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsConfigDto;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsConfigLogDto;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsTransparencyDto;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsTransparencyQueryDto;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceAssigPersonImportDTO;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceRecordImportDTO;
import com.volvo.maintain.infrastructure.gateway.response.ImportTempResult;
import com.volvo.maintain.interfaces.vo.clues.InsuranceLeadsConfigVo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

public interface RenewalOfInsuranceService {

    /**
     * 初始化经销商配置（单店保护，）
     * @param ownerCode 经销商
     * @return 返回字符串 成功失败 文字提示
     */
    String initConfig(String ownerCode, String allocationType);

    /**
     *  根据参数查询配置信息
     * @param afterBigAreaId 大区Id
     * @param afterSmallAreaId 小区Id
     * @param ownerCode 经销商
     * @return 根据参数查询配置信息
     */
    Page<InsuranceLeadsConfigDto> queryCompanyConfig(String afterBigAreaId, String afterSmallAreaId, String ownerCode, Integer pageNum, Integer pageSize);

    /**
     * 根据对应参数进行修改数据
     * @param InsuranceLeadsConfigVo 经销商信息
     * @return 返回修改的数据集合
     */
    List<InsuranceLeadsConfigDto> modifyOwnerCodeConfig(InsuranceLeadsConfigVo InsuranceLeadsConfigVo);

    /**
     * 导入经销商配置文件
     * @param importFile excel文件
     * @return 解析数据
     */
    ImportTempResult<InsuranceLeadsConfigDto> importDealerConfigInfo(MultipartFile importFile, Boolean tab);

    /**
     * 导出经销商规则配置数据
     * @param dto 导出参数查询
     */
    void dataResultExport(InsuranceLeadsConfigDto dto);
    /**
     * 店端修改提前出单日期
     * @param advanceDays 提前出单天数
     * @return 返回修改的数据集合
     */
    List<InsuranceLeadsConfigDto> modifyOwnerCodeConfigDate(Integer advanceDays);
    /**
     *  查询经销商规则提前出单日期修改记录
     * @param ownerCode 经销商
     * @return 根据参数查询配置信息
     */
    Page<InsuranceLeadsConfigLogDto> queryInsuranceLeadsModifyLog(String ownerCode, Integer flag, Integer pageNum, Integer pageSize);

    List<InsuranceLeadsConfigDto> queryInsuranceLeadsExport(InsuranceLeadsConfigVo vo);
    /**
     *  店端-保险跟进-导入线索
     * @param importFile excel文件
     * @return 解析数据
     */
    void importRenewalOfInsurance(MultipartFile importFile);
    /**
     *  店端-保险跟进-查询导入到临时表的数据
     * @return 根据参数查询配置信息
     */
    Page<InsuranceRecordImportDTO> importQuery(Integer flag, Integer pageNum, Integer pageSize);
    /**
     * 初始化经销商配置（单店保护，）
     * @return 返回字符串 成功失败 文字提示
     */
    String initConfigJob();

    /**
     * 续保线索超时关闭
     */
    void closeRenewalLead(Date adviseInDate);
    /**
     *  店端-保险跟进-执行线索导入
     * @return 解析数据
     */
    void executeImportRenewalOfInsurance();

    /**
     * 续保线索自动分撇
     */
    void assignClue();
    /**
     *  店端-续保任务分配-导入分配人员
     * @param importFile excel文件
     * @return 解析数据
     */
    void importAssigPerson(MultipartFile importFile);
    /**
     *  店端-续保分配-查询导入分配人到临时表的数据
     * @return 根据参数查询配置信息
     */
    Page<InsuranceAssigPersonImportDTO> queryImportAssigPerson(Integer flag, Integer pageNum, Integer pageSize);
    /**
     *  店端-续保分配-分配人导入执行
     * @return 解析数据
     */
    void executeImportAssigPerson();

    /**
     * 续保线索跟进透明导出接口
     */
    void exportTransparency(InsuranceLeadsTransparencyQueryDto queryDto);

    Page<InsuranceLeadsTransparencyDto> queryTransparency(@RequestBody InsuranceLeadsTransparencyQueryDto queryDto);

    /**
     * 下载中心回调
     * @param queryDto
     * @return
     */
    List<InsuranceLeadsTransparencyDto> exportCallback(InsuranceLeadsTransparencyQueryDto queryDto);
}
