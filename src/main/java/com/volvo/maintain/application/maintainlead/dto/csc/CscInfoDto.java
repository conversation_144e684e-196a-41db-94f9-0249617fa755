package com.volvo.maintain.application.maintainlead.dto.csc;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * 张善龙
 * 2024.1.11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("CSC信息")
public class CscInfoDto {

    private String cscCode;

    private String cscClass1;

    private String cscClass2;

    private String cscClass3;

    private String cscClass4;

    private String cscContent;

    private String cscRemark;

    private String cscRemark1;

}
