package com.volvo.maintain.application.maintainlead.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 终检内容清单VO
 */
@Data
public class FinalInspectionContentVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 配置文案 */
    private String describe;

    /** 文案key */
    private String onlyCode;

    /** 子项 */
    private List<FinalInspectionContentItemVO> items;
} 