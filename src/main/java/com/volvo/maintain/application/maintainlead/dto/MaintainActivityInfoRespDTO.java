package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "根据活动编号批量查询保养类型返回", description = "根据活动编号批量查询保养类型返回")
@Data
public class MaintainActivityInfoRespDTO {
	
	@ApiModelProperty(value = "服务合同编号", name = "serveContractCode")
	private String maintainNos;

	@ApiModelProperty(value = "服务合同编号", name = "serveContractCode")
	private String claimType;
}
