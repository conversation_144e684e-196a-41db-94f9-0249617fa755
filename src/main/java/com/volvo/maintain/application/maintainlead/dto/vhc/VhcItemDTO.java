package com.volvo.maintain.application.maintainlead.dto.vhc;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * vhc报价-检查项二级类目DTO
 */
@Data
public class VhcItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Integer id;
    /**
     * 检查项名称
     */
    private String itemName;
    /**
     * 预计服务时长
     */
    private String workDate;
    /**
     * 检查项备注
     */
    private String itemContent;
    /**
     * 检查项照片
     */
    private String itemPhotograph;
    /**
     * 确认状态
     */
    private String confirmState;
    /**
     * 不修原因反馈
     */
    private String noRepairCause;
    /**
     * 是否转工单
     */
    private String transferWorkOrder;
    /**
     * 维修结果
     */
    private String classResult;
    /**
     * 维修报价明细
     */
    private List<VhcPricesheetItemDTO> vhcPricesheetItemList;
}
