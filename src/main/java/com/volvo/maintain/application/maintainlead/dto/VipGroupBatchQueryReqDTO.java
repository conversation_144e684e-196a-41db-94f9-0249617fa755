package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量vin或者车牌查询vip群组DTO")
public class VipGroupBatchQueryReqDTO {

    @ApiModelProperty("vin")
    private List<String> vinList;

    @ApiModelProperty("车牌")
    private List<String> licensePlateList;

}
