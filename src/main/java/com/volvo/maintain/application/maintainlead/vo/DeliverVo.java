package com.volvo.maintain.application.maintainlead.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeliverVo {

    @ApiModelProperty(value = "经销商code ", name = "ownerCode")
    private String ownerCode;
    @ApiModelProperty(value = "工单号 ", name = "roNo")
    private String roNo;
    @ApiModelProperty(value = "vin ", name = "vin")
    private String vin;
    @ApiModelProperty(value = "送修人手机号 ", name = "delivererPhone")
    private String delivererPhone;
    @ApiModelProperty(value = "开始时间 ", name = "timeBegin")
    private String timeBegin;
    @ApiModelProperty(value = "结束时间 ", name = "timeEnd")
    private String timeEnd;
}
