package com.volvo.maintain.application.maintainlead.dto.claimapply;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 延保理赔申请和使用记录
 */
@Data
@ApiModel("延保理赔申请和使用记录")
public class ClaimApplyUseDTO implements Serializable{

	@ApiModelProperty("延保理赔申请id")
	private Long id;

	@ApiModelProperty("系统ID")
	private String appId;

	@ApiModelProperty("所有者代码")
	private String ownerCode;

	@ApiModelProperty("所有者的父组织代码")
	private String ownerParCode;

	@ApiModelProperty("组织ID")
	private Integer orgId;

	@ApiModelProperty("private Long id;")
	private Long logId;

	@ApiModelProperty("产品件号")
	private String productNo;

	@ApiModelProperty("产品名称")
	private String productName;

	@ApiModelProperty("车架号")
	private String vin;

	@ApiModelProperty("车牌号")
	private String licensePlateNum;

	@ApiModelProperty("发动机号")
	private String engineNo;

	@ApiModelProperty("销售经销商名称")
	private String dealerName;

	@ApiModelProperty("销售经销商")
	private String dealerCode;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty("进厂日期")
	private Date inDate;

	@ApiModelProperty("修理厂地址")
	private String repairAddress;

	@ApiModelProperty("联系人")
	private String linkman;

	@ApiModelProperty("联系人电话")
	private String linkmanTel;

	@ApiModelProperty("联系人手机")
	private String linkmanPhone;

	@ApiModelProperty("报案人")
	private String reportCaseMan;

	@ApiModelProperty("报案人电话")
	private String reportCaseManTel;

	@ApiModelProperty("报案人手机")
	private String reportCaseManPhone;

	@ApiModelProperty("与被保险人关系")
	private Integer insuredRelation;

	@ApiModelProperty("车辆使用人姓名")
	private String vehicleUser;

	@ApiModelProperty("车辆使用人联系方式")
	private String vehicleUserTel;

	@ApiModelProperty("出险时行驶里程")
	private Integer mileage;

	@ApiModelProperty("索赔金额")
	private BigDecimal claimAmount;

	@ApiModelProperty("故障发生地点")
	private String faultAddress;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty("故障发生时间")
	private Date faultTime;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty("延保购买日期")
	private Date purchaseDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty("延保到期日期")
	private Date expireDate;

	@ApiModelProperty("客户描述故障现象")
	private String faultDescribe;

	@ApiModelProperty("修理厂检修过程")
	private String checkRepairProcess;

	@ApiModelProperty("修理厂维修方案")
	private String repairProgramme;

	@ApiModelProperty("工单号")
	private String orderNo;

	@ApiModelProperty("报案号")
	private String caseNo;

	@ApiModelProperty("理赔申请状态")
	private Integer applyStatus;

	@ApiModelProperty("理赔申请审核意见")
	private String audiOpinion;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty("结案日期")
	private Date closeCaseDate;

	@ApiModelProperty("数据来源")
	private Integer dataSources;

	@ApiModelProperty("是否有效")
	private Integer isValid;

	@ApiModelProperty("工单状态（80491001：在修   80491002：已提交结案   80491003：已结算   80491004：预工单）'")
	private Integer orderStatus;

	@ApiModelProperty("产品大类(业务类型): 83441001:车险   83441002:非车险")
	private Integer bizType;

	@ApiModelProperty("延保提供方 （81541001：PICC  81541002：易保）")
	private Integer provider;

	@ApiModelProperty("金融产品主键id")
	private Long productId;

	@ApiModelProperty("是否易保线上对接，1004")
	private Integer ebIntegration;

	@ApiModelProperty("保司报案号")
	private String insuranceRegistNo;

	@ApiModelProperty("延保申请零件")
	private List<ClaimApplyPartDTO> applyParts;

	@ApiModelProperty("延保申请工时")
	private List<ClaimApplyLabourDTO> labourDTOS;

	@ApiModelProperty("延保申请影像件数据")
	private List<ClaimApplyImageDataDTO> imageDataDTOS;

	@ApiModelProperty("延保申请故障描述")
	private List<ClaimApplyHandDTO> handDTOS;

	@ApiModelProperty("保单号")
	private String insuranceOrderNo;

	@ApiModelProperty("授权总金额")
	private BigDecimal totalApprovalAmount;

	/**
	 * 承保渠道(1-保司 2-自营) 自营沃尔沃35341001自营人保35341002
	 */
	private  Integer insurerChannel;
}
