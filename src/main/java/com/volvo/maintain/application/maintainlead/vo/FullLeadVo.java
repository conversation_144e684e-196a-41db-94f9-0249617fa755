package com.volvo.maintain.application.maintainlead.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 功能描述：全量线索查询VO对象
 *
 * <AUTHOR>
 * @since 2023/12/18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "FullLeadVo", description = "全量线索返回对象")
public class FullLeadVo {

    @ApiModelProperty(value = "线索id")
    private Long id;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNum;

    @ApiModelProperty(value = "vin")
    private String vin;

    @ApiModelProperty(value = "邀约类型")
    private Integer inviteType;

    @ApiModelProperty(value = "邀约类型描述")
    private String inviteTypeDesc;

    @ApiModelProperty(value = "建议进厂日期/续保到期日期")
    private String adviseInDate;

    @ApiModelProperty(value = "返厂概率")
    private String returnIntentionLevel;

    @ApiModelProperty(value = "续保客户类型")
    private Integer insuranceType;

    @ApiModelProperty(value = "续保客户类型描述")
    private String insuranceTypeDesc;

    @ApiModelProperty(value = "跟进状态")
    private Integer followStatus;

    @ApiModelProperty(value = "跟进状态描述")
    private String followStatusDesc;

    @ApiModelProperty(value = "AI得分")
    private Integer totalScore;

    @ApiModelProperty(value = "通话记录id")
    private Long callDetailId;

    @ApiModelProperty(value = "通话时间")
    private String callTime;

    @ApiModelProperty(value = "通话时长")
    private Integer callLength;

    @ApiModelProperty(value = "下次跟进日期")
    private String planFollowDate;

    @ApiModelProperty(value = "实际跟进日期")
    private String actualFollowDate;

    @ApiModelProperty(value = "二次跟进日期")
    private String twiceFollowDate;

    @ApiModelProperty(value = "跟进人员id")
    private String saId;

    @ApiModelProperty(value = "跟进人员")
    private String saName;

    @ApiModelProperty(value = "线索下发日期")
    private String createdAt;

    @ApiModelProperty(value = "跟进内容")
    private String content;

    @ApiModelProperty(value = "失败原因")
    private Integer loseReason;

    @ApiModelProperty(value = "失败原因描述")
    private String loseReasonDesc;

    @ApiModelProperty(value = "未用AI原因")
    private Integer itemCode;

    @ApiModelProperty(value = "未用AI原因描述")
    private String itemCodeDesc;

    @ApiModelProperty(value = "线索异常状态")
    private Integer itemType;

    @ApiModelProperty(value = "线索异常状态描述")
    private String itemTypeDesc;

    @ApiModelProperty(value = "是否二次跟进")
    private String isAi;

    @ApiModelProperty(value = "流失线索类型")
    private Integer lossType;

    @ApiModelProperty(value = "流失线索类型描述")
    private String lossTypeDesc;

    @ApiModelProperty(value = "备注")
    private String itemName;

    @ApiModelProperty(value = "卡券code")
    private String couponCode;

    @ApiModelProperty(value = "卡券名称")
    private String couponName;

    @ApiModelProperty(value = "故障灯类型")
    private String warningName;

    @ApiModelProperty(value = "跟进次数")
    private Integer recordNum;

    @ApiModelProperty(value = "预计进店时间")
    private String forecastTime;

    @ApiModelProperty(value = "线索状态")
    private Integer clueStatus;

    @ApiModelProperty(value = "邀约时间")
    private String inviteTime;

    @ApiModelProperty(value = "工单号")
    private String roNo;

    @ApiModelProperty(value = "经销商")
    private String dealerCode;
    @ApiModelProperty(value = "禁用:true不禁用false")
    private boolean adviseInDateDisable = false;
    @ApiModelProperty(value = "厂端保险到期日期")
    private String factoryInsuranceExpiryDate;

}
