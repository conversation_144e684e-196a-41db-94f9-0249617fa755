package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("工单进场时间")
public class RepairOrderEntryTimeDto {

    @ApiModelProperty("进场时间")
    private String entryTime;
    @ApiModelProperty("经销商")
    private String ownerCode;
    @ApiModelProperty("工单号")
    private String roNo;
    @ApiModelProperty("车架号")
    private String vin;



}
