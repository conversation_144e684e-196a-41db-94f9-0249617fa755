package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;


/**
 * 事故线索DTO
 * <AUTHOR>
 *
 * 2024年4月29日
 */
@Data
@Builder
@ApiModel("事故线索SA呼叫登记")
@AllArgsConstructor
@NoArgsConstructor
public class AccidentCluesSaNumberDto implements Serializable{
	private static final long serialVersionUID = 1L;
	
	 /**
     * 主键ID
     */
    private Long acId;
    /**
     * 虚拟手机号
     */
    private String virtualPhone;
    /**
     * 登录者手机号
     */
    private String loginPhone;
    /**
     * crm响应信息
     */
    private String msg;

   
}
