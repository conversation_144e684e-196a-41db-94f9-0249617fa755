package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * 功能描述：取送车下单
 *
 * <AUTHOR>
 * @since 2024/03/08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("取送车下单")
public class TaskDeliverCarRequestDto {
    @ApiModelProperty(value = "主键id")
    private Integer id;
    @ApiModelProperty(value = "取送车中台编号")
    private String orderCode;

    @ApiModelProperty(value = "e代驾 订单id")
    private Integer orderId;

    @ApiModelProperty(value = "vin")
    private String vin;

    @ApiModelProperty(value = "经销商code")
    private String dealerCode;

    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "经销商联系人")
    private String dealerContacts;

    @ApiModelProperty(value = "经销商手机")
    private String dealerCell;

    @ApiModelProperty(value = "下单人oneid")
    private Integer customerOneId;

    @ApiModelProperty(value = "车主oneid")
    private Integer ownerOneId;

    @ApiModelProperty(value = "养修预约单号")
    private String reservationNo;

    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    @ApiModelProperty(value = "渠道号")
    private Integer channel;

    @ApiModelProperty(value = "商户id 最多20个字符")
    private String customerId;

    @ApiModelProperty(value = "取送车订单类型 82711001 取车, 82711002 送车")
    private Integer type;

    @ApiModelProperty(value = "订单成单模式 0-客服派单 1-司机抢单 3-实时派单")
    private Integer mode;

    @ApiModelProperty(value = "下单人手机号")
    private String createMobile;

    @ApiModelProperty(value = "车主手机号")
    private String mobile;

    @ApiModelProperty(value = "车主姓名")
    private String username;

    @ApiModelProperty(value = "取车地址联系人姓名")
    private String pickupContactName;

    @ApiModelProperty(value = "取车地址联系人手机号")
    private String pickupContactPhone;
    @ApiModelProperty(value = "取车地址")
    private String pickupAddress;
    @ApiModelProperty(value = "取车地址经度")
    private double pickupAddressLng;
    @ApiModelProperty(value = "取车地址纬度")
    private double pickupAddressLat;
    @ApiModelProperty(value = "还车地址联系人姓名")
    private String returnContactName;
    @ApiModelProperty(value = "还车地址联系人手机号")
    private String returnContactPhone;
    @ApiModelProperty(value = "还车地址")
    private String returnAddress;
    @ApiModelProperty(value = "还车地址经度")
    private double returnAddressLng;
    @ApiModelProperty(value = "还车地址纬度")
    private double returnAddressLat;
    @ApiModelProperty(value = "预约时间")
    private String bookingTime;
    @ApiModelProperty(value = "车牌号")
    private String carNo;
    @ApiModelProperty(value = "车辆品牌名称")
    private String carBrandName;
    @ApiModelProperty(value = "车辆车系名称")
    private String carSeriesName;
    @ApiModelProperty(value = "订单支付方式，默认为0 0-vip扣款 1-用户付款")
    private Integer payMode;
    @ApiModelProperty(value = "优惠券码 payMode=1的情况下，可以使用优惠券")
    private String couponCode;
    @ApiModelProperty(value = "中转地址")
    private String midAddress;
    @ApiModelProperty(value = "中转地址经度")
    private double midAddressLng;
    @ApiModelProperty(value = "中转地址纬度")
    private double midAddressLat;
    @ApiModelProperty(value = "驻店司机工号")
    private String fixedDriverId;
    @ApiModelProperty(value = "是否发送短信 0-发送短信 1-不发短信 默认是 0")
    private Integer pushSms;
    @ApiModelProperty(value = "操作时间")
    private String operateTime;
    @ApiModelProperty(value = "工单号")
    private String workOrderNo;
    @ApiModelProperty(value = "工单开单时间")
    private String workOrderIssuingTime;
    @ApiModelProperty(value = "是否添加取送车费用 10041001是 10041002否")
    private Integer addPickUpAndDropOffFee;
    @ApiModelProperty(value = "订单来源（51281001 E代驾 51281002 老DMS）")
    private Integer orderSource;
    @ApiModelProperty(value = "下单时间")
    private String placeOrderTime;
    @ApiModelProperty(value = "订单来源是否是C端创建:10041001是,10041002否 默认为：否")
    private Integer minProgram;
    @ApiModelProperty(value = "司机姓名")
    private String driverName;
    @ApiModelProperty(value = "司机联系方式")
    private String driverContactInfo;
    @ApiModelProperty(value = "取送车订单的类型区分（1：实时单 2：预约单）")
    private Integer orderKind;
    @ApiModelProperty(value = "顾问id")
    private Integer adviserId;
    @ApiModelProperty(value = "备注信息")
    private String remarks;

    @ApiModelProperty(value = "取车省code")
    private String pickupProvinceCode;

    @ApiModelProperty(value = "取车市code")
    private String pickupCityCode;

    @ApiModelProperty(value = "取车区code")
    private String pickupAreaCode;

    @ApiModelProperty(value = "送车省code")
    private String returnProvinceCode;

    @ApiModelProperty(value = "送车市code")
    private String returnCityCode;

    @ApiModelProperty(value = "送车区code")
    private String returnAreaCode;



}
