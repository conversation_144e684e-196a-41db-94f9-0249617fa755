package com.volvo.maintain.application.maintainlead.dto.carebuy;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.volvo.maintain.application.maintainlead.dto.csc.CscInfoDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

/**
 * 服务合同中台入参VO
 * 张善龙
 * 2024.1.11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("服务合同中台入参出参VO")
public class SaveCareBuyedDto {

    @ApiModelProperty(value = "服务ID",name = "id")
    private Integer id;

    @ApiModelProperty(value = "保养活动可享用次数",name = "activityAvailableTimes")
    private Integer activityAvailableTimes;

    @ApiModelProperty(value = "保养活动剩余享用次数",name = " activityLeftTimes")
    private Integer  activityLeftTimes;

    @ApiModelProperty(value = "保养活动名称",name = "activityName")
    private String  activityName;

    @ApiModelProperty(value = "保养活动编号",name = " activityNo")
    private String  activityNo;

    @ApiModelProperty(value = "活动类型(1—90系免费保养套餐 2—售后保养合同 3—服务合同 4—新车免费基础保养)",name = "activityType")
    private Integer activityType;

    @ApiModelProperty(value = "购买者名称",name = " buyerName")
    private String  buyerName;

    @ApiModelProperty(value = "该车使用权益的车龄上限",name = "carAgeMax")
    private Integer carAgeMax;

    @ApiModelProperty(value = "汽车销售日期",name = "carsaleTime")
    private String carsaleTime;

    @ApiModelProperty(value = "服务生效日期",name = "effectiveDate")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String effectiveDate;

    @ApiModelProperty(value = "服务失效日期",name = "expirationDate")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String expirationDate;

    @ApiModelProperty(value = "该车使用权益的里程上限",name = "mileageMax")
    private Integer mileageMax;

    @ApiModelProperty(value = "车型名称",name = "modelName")
    private String modelName;

    @ApiModelProperty(value = "销售经销商",name = "saleDealer")
    private String saleDealer;


    @ApiModelProperty(value = "工单结算日期",name = "settlementDate")
    private String settlementDate;

    @ApiModelProperty(value = "权益来源(NEWBIE/DMS)",name = "source")
    private String source;

    @ApiModelProperty(value = "来源备注",name = "sourceRemark")
    private String sourceRemark;

    @ApiModelProperty(value = "车身编号(车架号)",name = "vin")
    private String vin;

    @ApiModelProperty(value = "销售工单号",name = "workNo")
    private String workNo;

    @ApiModelProperty(value = "创建日期",name = "carsaleTime")
    private String createTime;

    @ApiModelProperty(value = "修改日期",name = "carsaleTime")
    private String updateTime;

    @ApiModelProperty(value = "查询范围(1:购买成功[默认] 2:全部记录)",name = "queryRange")
    private Integer queryRange;

    @ApiModelProperty(value = "保养类型",name = "upkeepType")
    private Integer upkeepType;

    /**
     * 权益包含套餐
     */
    private List<CareMealDto> careMeals;

    /**
     * Csc查询 用于90系免费保养合同专属出参
     */
    private List<CscInfoDto> cscInfoVoList;

    /**
     * 自定义出参用于90系免费保养合同大小桶油效验字段
     */
    @ApiModelProperty(value = "大小桶油效验字段",name = "isBigLittleOil")
    private Integer isBigLittleOil;

    /**
     * 前台出参购买ID
     */
    @ApiModelProperty(value = "购买ID",name = "careBuyedId")
    private Integer careBuyedId;

}
