package com.volvo.maintain.application.maintainlead.dto.rights;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PurchaseEligibilityCheckRequestDto {

    /**
     * 里程
     */
    private Integer mileage;

    /**
     * 产品件号(c)
     */
    private List<String> productNoList;

    /**
     * Vin (c)
     */
    private String vin;

    // 补充数据 有车辆中心提供

    /**
     * 发动机代码
     */
    private String engineNo;

    /**
     * 车龄
     */
    private Integer age;

    /**
     * 开票日期
     */
    private Date invoiceDate;

    /**
     * 车辆销售价格
     */
    private Double invoicePrice;

    /**
     * 车型
     */
    private String modelCode;

    /**
     * 购买数量
     */
    private  Integer count;
}
