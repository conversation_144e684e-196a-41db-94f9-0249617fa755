package com.volvo.maintain.application.maintainlead.service.vhc;

import com.volvo.maintain.application.maintainlead.dto.vhc.VehicleHealthCheckDetailDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VehicleHealthCheckDetailParamDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VehicleHealthCheckInfoDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcItemConfigInfoDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 车辆健康检查
 * @Date 2024/9/25 15:55
 */
public interface VehicleHealthCheckDeService {

    /**
     * 查询车辆健康检查详情
     * @param vehicleHealthCheckDetailParamDto
     * @return
     */
    VehicleHealthCheckDetailDto getVehicleHealthCheckDetail(VehicleHealthCheckDetailParamDto vehicleHealthCheckDetailParamDto);

    /**
     * 根据配置的大类id查询对应的小类配置信息
     * @param configClassId
     * @return
     */
    List<VhcItemConfigInfoDto> getVhcItemInfoByClassId(Integer classId, String configClassId);

    /**
     * 保存车辆健康信息
     * @param vehicleHealthCheckInfoDto
     */
    void saveVehicleHealthCheckInfo(VehicleHealthCheckInfoDto vehicleHealthCheckInfoDto);
}
