package com.volvo.maintain.application.maintainlead.dto.customermarketing;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

@ApiModel("车主信息")
@Data
public class OwnerInfoDTO implements Serializable {
	@ApiModelProperty(value = "车架号", required = true, hidden = true)
	private String vin;
	@ApiModelProperty(value = "送修人手机号", required = true, hidden = true)
	private String delivererMobile;
	@ApiModelProperty(value = "工单号", required = true, hidden = true)
	private String roNo;
	@ApiModelProperty(value = "经销商code", required = true, hidden = true)
	private String ownerCode;
	@ApiModelProperty(value = "开单时间", required = true, hidden = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String roCreatedDate;
	@ApiModelProperty(value = "车牌号", required = true, hidden = true)
	private String license;
	@ApiModelProperty(value = "进厂里程", required = true, hidden = true)
	private Double inMileage;
	@ApiModelProperty(value = "车主", required = true, hidden = true)
	private String deliverer;
	@ApiModelProperty(value = "性别", required = true, hidden = true)
	private Integer delivererGender;
}