package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import com.volvo.maintain.application.maintainlead.dto.RecentRepairOrderDto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("账单详情")
public class InsuranceDealerDto {

    /**
     * 续保经销信息
     */
    private List<InsuranceBillDto> insuranceBill;

    /**
     * 最近进厂经销商信息
     */
    private RecentRepairOrderDto repairOrder;

}
