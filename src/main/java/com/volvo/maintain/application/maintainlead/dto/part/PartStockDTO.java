package com.volvo.maintain.application.maintainlead.dto.part;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PartStockDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 系统ID
     */
    private String appId;
    /**
     * 所有者代码
     */
    private String ownerCode;
    /**
     * 备件代码
     */
    private String partNo;
    /**
     * 仓库代码
     */
    private String storageCode;
    /**
     * 备件名称
     */
    private String partName;
    /**
     * 库存数量
     */
    private BigDecimal stockQuantity;
    /**
     * 在途库存数量
     */
    private BigDecimal deliveryQuantity;
    /**
     * 最大库存
     */
    private BigDecimal maxStock;
    /**
     * 最小库存
     */
    private BigDecimal minStock;
    /**
     * 借进数量
     */
    private BigDecimal borrowQuantity;
    /**
     * 借出数量
     */
    private BigDecimal lendQuantity;
    /**
     * 锁定数量
     */
    private BigDecimal lockedQuantity;


}

