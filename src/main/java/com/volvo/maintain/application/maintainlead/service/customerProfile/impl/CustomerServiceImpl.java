package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.exception.ServiceBizException;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.clues.InviteInsuranceVehicleRecordExtDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CdpTagInfoService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CustomerService;
import com.volvo.maintain.application.maintainlead.vo.InviteVehicleRecordVo;
import com.volvo.maintain.infrastructure.constants.CdpConstant;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.CdpResponse;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.MidVehicleVo;
import com.volvo.maintain.interfaces.vo.OwnerInfoResultsVo;
import com.volvo.maintain.interfaces.vo.RepairOrderHistoryParamsVo;
import com.volvo.maintain.interfaces.vo.RepairOrderHistoryResultVo;
import com.volvo.maintain.interfaces.vo.carebuy.CareBuyedVo;
import com.yonyou.cyx.framework.util.bean.ApplicationContextHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.RetryContext;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 客户画像身份id
 */
@Service
public class CustomerServiceImpl implements CustomerService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${CDP.appId}")
    private String appId;
    @Autowired
    private CdpFeign cdpFeign;
    @Autowired
    private CdpTagInfoService cdpTagInfoService;
    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;
    @Autowired
    private DmscusCustomerFeign dmscusCustomerFeign;

    @Qualifier("thread360PoolNew")
    @Autowired
    private ThreadPoolTaskExecutor thread360Pool;

    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;

    @Autowired
    private DomainInsuranceLeadsFeign domainInsuranceLeadsFeign;

    @Autowired
    private DomainMaintainAuthFeign domainMaintainAuthFeign;

    @Autowired
    private DomainMaintainLeadFeign domainMaintainLeadFeign;

    @Override
    public List<CustomerInfoDto> queryCustomInfoList(String vin, String mobile, String ownerCode) {
        logger.info("queryCustomInfoListAndRequestParams->vin:{},mobile:{},ownerCode:{}", vin, mobile, ownerCode);
        //查询cdp车主（member,mobile,oneId）
        CompletableFuture<CustomerInfoDto> cdpCustomerFuture = CompletableFuture.supplyAsync(() -> {
            logger.info("queryCustomInfoListAndRequestParams->queryCdpCustomer");
            return queryCdpCustomer(vin, "v1");
        }, thread360Pool);
        //查询中台车主
        CompletableFuture<CustomerInfoDto> midCustomerFuture = CompletableFuture.supplyAsync(() -> {
            logger.info("queryCustomInfoListAndRequestParams->queryMidCustomer");
            return queryMidCustomer(vin,ownerCode);
        }, thread360Pool);
        //查询自店车主
        CompletableFuture<CustomerInfoDto> ownerCustomerFuture = CompletableFuture.supplyAsync(() -> {
            logger.info("queryCustomInfoListAndRequestParams->queryOwnerCustomer");
            return queryOwnerCustomer(vin, ownerCode);
        }, thread360Pool);
        //查询故障灯客户
        CompletableFuture<CustomerInfoDto> faultLightFuture = CompletableFuture.supplyAsync(() -> {
            logger.info("queryCustomInfoListAndRequestParams->queryFaultLight");
            return queryFaultLight(vin, ownerCode);
        }, thread360Pool);
        //查询dms默认参数
        CompletableFuture<CustomerInfoDto> defaultParam = CompletableFuture.supplyAsync(() -> {
            logger.info("queryCustomInfoListAndRequestParams->queryDmsDefaultParam");
            return queryDmsDefaultParam(CdpConstant.CDPTAGIDKEY);
        }, thread360Pool);
        //查询零附件客户
        CompletableFuture<CustomerInfoDto> inviteVehicleFuture = CompletableFuture.supplyAsync(() -> queryInviteVehicleRecord(ownerCode, vin), thread360Pool);
        //查询最近3个送修人
        CompletableFuture<List<CustomerInfoDto>> recentlyDelivererFuture = CompletableFuture.supplyAsync(() -> queryRepairOwner(vin, ownerCode), thread360Pool);
        //查询投保人
        CompletableFuture<CustomerInfoDto> policyholderFuture = CompletableFuture.supplyAsync(() -> queryPolicyholder(vin, ownerCode, true), thread360Pool);
        //查询自店投保人
        CompletableFuture<CustomerInfoDto> ownerPolicyholderFuture = CompletableFuture.supplyAsync(() -> queryPolicyholder(vin, ownerCode, false), thread360Pool);
        //将所有的future添加到列表中
        List<CompletableFuture<CustomerInfoDto>> futuresList = Arrays.asList(cdpCustomerFuture, midCustomerFuture, ownerCustomerFuture, faultLightFuture, defaultParam, inviteVehicleFuture, policyholderFuture, ownerPolicyholderFuture);
        // 等待所有的future完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                cdpCustomerFuture,
                midCustomerFuture,
                ownerCustomerFuture,
                faultLightFuture,
                defaultParam,
                recentlyDelivererFuture,
                policyholderFuture,
                ownerPolicyholderFuture
        );
        // 从Future中提取结果
        List<CustomerInfoDto> customerInfoList = new ArrayList<>();
        try {
            allFutures.get(60, TimeUnit.SECONDS); // 等待所有查询完成
            // 将CompletableFutures的结果添加到最终列表
            customerInfoList.addAll(futuresList.stream()
                    .map(CompletableFuture::join)// 获取每个 future 的结果
                    .collect(Collectors.toList())); // 收集结果
            // 添加recentlyDeliverer查询结果到最终列表
            if(CollectionUtils.isNotEmpty(recentlyDelivererFuture.get())){
                customerInfoList.addAll(recentlyDelivererFuture.get());
            }
        } catch (InterruptedException | ExecutionException  | TimeoutException e) {
            logger.info("queryCustomInfoList->CompletableFutureError", e);
            e.printStackTrace();
        }

        DmsResponse<Boolean> booleanDmsResponse = domainMaintainLeadFeign.queryIsGuaranteeSlip(vin, ownerCode);
        logger.info("queryIsGuaranteeSlip:{}", JSON.toJSONString(booleanDmsResponse));
        logger.info("customerInfoList:{}", JSON.toJSONString(customerInfoList));
        if(booleanDmsResponse.isSuccess() && !booleanDmsResponse.getData()){
            List<CustomerInfoDto> collect = customerInfoList.stream()
                    .filter(Objects::nonNull)
                    .filter(item -> Objects.isNull(item.getCustomerType()) || !item.getCustomerType().equals(CdpConstant.CUSTOMER_TYPE_POLICYHOLDER))
                    .collect(Collectors.toList());
            logger.info("queryCustomInfoList->customerInfoListThreadResult>collect:{}", JSONObject.toJSONString(collect));
            return collect;
        }
        //List<CustomerInfoDto> customerInfoDto = customerInfoList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        logger.info("queryCustomInfoList->customerInfoListThreadResult:{}", JSONObject.toJSONString(customerInfoList));
        return customerInfoList;
    }

    @Override
    public List<CustomerInfoDto> queryCustomInfoListByVin(String vin, String ownerCode) {
        logger.info("queryCustomInfoListV2 vin:{},ownerCode:{}", vin, ownerCode);
        // 查询cdp车主（member,mobile,oneId）
        CompletableFuture<CustomerInfoDto> cdpCustomerFuture = CompletableFuture.supplyAsync(() -> {
            logger.info("queryCustomInfoListV2 queryCdpCustomer");
            return queryCdpCustomer(vin, "v2");
        }, thread360Pool);
        // 查询自店车主
        CompletableFuture<CustomerInfoDto> ownerCustomerFuture = CompletableFuture.supplyAsync(() -> {
            logger.info("queryCustomInfoListAndRequestParams queryOwnerCustomer");
            return queryOwnerCustomer(vin, ownerCode);
        }, thread360Pool);
        // 查询最近3个送修人
        CompletableFuture<List<CustomerInfoDto>> recentlyDelivererFuture = CompletableFuture.supplyAsync(() -> {
            logger.info("queryCustomInfoListAndRequestParams queryRepairOwner");
            return queryRepairOwner(vin, ownerCode);
        }, thread360Pool);
        // 查询自店投保人
        CompletableFuture<CustomerInfoDto> ownerPolicyholderFuture = CompletableFuture.supplyAsync(() -> {
            logger.info("queryCustomInfoListAndRequestParams queryPolicyholder");
            return queryPolicyholder(vin, ownerCode, false);
        }, thread360Pool);

        // 将所有的future添加到列表中
        List<CompletableFuture<CustomerInfoDto>> futuresList = Arrays.asList(cdpCustomerFuture, ownerCustomerFuture, ownerPolicyholderFuture);
        // 等待所有的future完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                cdpCustomerFuture,
                ownerCustomerFuture,
                recentlyDelivererFuture,
                ownerPolicyholderFuture
        );
        // 从Future中提取结果
        List<CustomerInfoDto> customerInfoList = new ArrayList<>();
        try {
            allFutures.get(60, TimeUnit.SECONDS); // 等待所有查询完成
            // 将CompletableFutures的结果添加到最终列表
            customerInfoList.addAll(futuresList.stream()
                    .map(CompletableFuture::join)// 获取每个 future 的结果
                    .collect(Collectors.toList())); // 收集结果
            // 添加recentlyDeliverer查询结果到最终列表
            if (CollectionUtils.isNotEmpty(recentlyDelivererFuture.get())) {
                customerInfoList.addAll(recentlyDelivererFuture.get());
            }
        } catch (Exception e) {
            logger.error("queryCustomInfoListAndRequestParams e:{}", e);
        }
        if (CollectionUtils.isEmpty(customerInfoList)) {
            return null;
        }
        return customerInfoList;
    }

    /**
     * 查询配置参数
     * @param cdpTagIdKey
     * @return
     */
    @Override
    public CustomerInfoDto queryDmsDefaultParam(String cdpTagIdKey) {
        DmsResponse<CustomerInfoDto> dmsDefaultParam = dmscloudServiceFeign.queryDmsDefaultParam(cdpTagIdKey);
        logger.info("queryDmsDefaultParam->Response:{}",dmsDefaultParam);
        if(Objects.isNull(dmsDefaultParam.getData())){
            logger.info("queryDmsDefaultParam->Response data null");
            return null;
        }
        logger.info("queryDmsDefaultParam->result:{}",dmsDefaultParam);
        return dmsDefaultParam.getData();
    }
    @Override
    public List<CustomerInfoDto> queryCustomInfoListByMobile(String vin, String mobile, String ownerCode) {
        List<CustomerInfoDto> customerInfoList = queryCustomInfoList(vin, mobile, ownerCode);
        if(CollectionUtils.isEmpty(customerInfoList)){
            return Collections.emptyList();
        }
        //设置默认车主(根据前端的手机号)
        defaultOwnerResult(mobile, customerInfoList);
        //查询dms默认参数
        List<String> dmsDefaultList = queryDmsDefaultParams(customerInfoList);
        logger.info("queryCustomInfoListByMobile->dmsDefaultList:{}",dmsDefaultList);
        //去重手机号查询 CDP 客户属性
        List<String> mobileList = distinctMobile(customerInfoList);
        if(CollectionUtils.isEmpty(mobileList)){
            logger.info("queryCustomInfoListByMobile->mobileList:{}",mobileList);
            return Collections.emptyList();
        }
        logger.info("queryCustomInfoListByMobile->mobileList:{}",mobileList);
        //去重之后查询 CDP 客户属性
        CdpResponse<List<CustomerTagsDto>> cdpCustomResult = queryCdpCustomAndTag(vin, mobileList);
        // 设置返厂概率
        queryReturnFactoryProbability(customerInfoList, dmsDefaultList, cdpCustomResult);
        //如果中台车主有memberId,且有车主手机号与中台车主手机号相同，则把memberId赋值给相同手机号的车主
        adjustOwnerMemberIdByMidMobile(customerInfoList);
        return customerInfoList;
    }

    /**
     * 如果中台车主有memberId,且有车主手机号与中台车主手机号相同，则把memberId赋值给相同手机号的车主
     * @param customerInfoList
     */
    public void adjustOwnerMemberIdByMidMobile(List<CustomerInfoDto> customerInfoList) {
        //要判断List对象的属性是否与其他的对象属性相等，需嵌套循环 复杂度过高会有性能问题，所以采用哈希表
        Map<String, String> mobileToMemberIdMap = new HashMap<>();
        customerInfoList.stream().filter(Objects::nonNull).forEach(s -> {
            if (s.getMemberId() != null) {
                mobileToMemberIdMap.putIfAbsent(s.getMobile(), String.valueOf(s.getMemberId()));
            }
        });
        customerInfoList.stream().filter(Objects::nonNull).forEach(s -> {
           if (mobileToMemberIdMap.containsKey(s.getMobile())) {
                    s.setMemberId(Integer.valueOf(mobileToMemberIdMap.get(s.getMobile())));
                }
        });
    }

    private  void queryReturnFactoryProbability(List<CustomerInfoDto> customerInfoList, List<String> dmsDefaultList, CdpResponse<List<CustomerTagsDto>> cdpCustomResult) {
        if(CollectionUtils.isNotEmpty(cdpCustomResult.getData())){
            Map<String, String> levelMap = cdpCustomResult.getData().stream()
                    .collect(Collectors.toMap(CustomerTagsDto::getVin, v -> Optional.ofNullable(v.getTagList())
                            .map(list -> list.stream()
                                    .filter(att -> dmsDefaultList.get(0).equals(att.getTagID()))
                                    .findFirst()
                                    .map(CdpTagListDto::getTagValue)
                                    .orElse(CdpConstant.DEFAULTGRADE))
                            .orElse(CdpConstant.DEFAULTGRADE)));
            //集合存在 null对象。
            customerInfoList.stream().filter(Objects::nonNull).forEach(s -> {
                s.setReturnFactoryProbability(levelMap.getOrDefault(s.getMobile(),CdpConstant.DEFAULTGRADE));
            });
            CustomerInfoDto customerInfoDto = queryDmsDefaultParam(CdpConstant.FULLNAMEAFTERSALE);
            Map<String, String> levelMapName = cdpCustomResult.getData().stream()
                    .collect(Collectors.toMap(CustomerTagsDto::getVin, v -> Optional.ofNullable(v.getAttList())
                            .map(list -> list.stream()
                                    .filter(att -> customerInfoDto.getDmsDefault().equals(att.getAttID()))
                                    .findFirst()
                                    .map(CdpAttributeListDto::getAttValue)
                                    .orElse(CdpConstant.DEFAULTGRADE))
                            .orElse(CdpConstant.DEFAULTGRADE)));
            if(Objects.nonNull(customerInfoList.get(0))){
                customerInfoList.get(0).setCustomerName(levelMapName.getOrDefault(customerInfoList.get(0).getMobile(),null));
            }
        }
    }

    /**
     * 去重手机号
     * @param customerInfoList
     * @return
     */
    private static List<String> distinctMobile(List<CustomerInfoDto> customerInfoList) {
        return customerInfoList.stream()
                .filter(Objects::nonNull)
                .map(CustomerInfoDto::getMobile)
                .filter(StringUtils::isNotBlank) // 提取 mobile 字段
                .distinct() // 去重
                .collect(Collectors.toList());
    }

    private  List<String> queryDmsDefaultParams(List<CustomerInfoDto> customerInfoList) {
        logger.info("queryDmsDefaultParams->customerInfoList:{}", JSONObject.toJSONString(customerInfoList));
        List<String> dmsDefaultList = customerInfoList.stream()// 将列表转换成stream
                .filter(Objects::nonNull) // 过滤掉所有为null的values
                .map(CustomerInfoDto::getDmsDefault)// 过滤掉所有为null的values
                .filter(StringUtils::isNotBlank) // 获取每个CustomerInfoDto的dmsDefault值
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(dmsDefaultList)){
            throw new ServiceBizException("获取dms默认参数失败");
        }
        return dmsDefaultList;
    }

    /**
     * 设置默认车主(根据前端的手机号)
     */
    private static void defaultOwnerResult(String mobile, List<CustomerInfoDto> customerInfoList) {
        List<CustomerInfoDto> customerInfoDto = customerInfoList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if(StringUtils.isNotBlank(mobile)){
            for (CustomerInfoDto dto : customerInfoDto) {
               if(Objects.equals(dto.getMobile(), mobile)){
                   dto.setDefaultOwner(true);
                   break;
               }
            }
        }
    }

    /**
     * 批量手机号查询cdp客户属性和标签属性
     * @param mobileList
     * @return
     */
    @Override
    public CdpResponse<List<CustomerTagsDto>> queryCdpCustomAndTag(String vin, List<String> mobileList) {
        // 代理调用，否则 Retryable 不生效
        CustomerServiceImpl proxy = ApplicationContextHelper.getBeanByType(CustomerServiceImpl.class);
        return proxy.queryCdpCustomAndTag_(vin, mobileList);
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public CdpResponse<List<CustomerTagsDto>> queryCdpCustomAndTag_(String vin, List<String> mobileList) {
        RetryContext context = RetrySynchronizationManager.getContext();
        int retryCount = context.getRetryCount();
        logger.info("queryCdpCustomAndTag retryCount:{}", retryCount);
        CdpTokenPortraitDto cdpToken = cdpTagInfoService.getCdpToken(vin);
        CdpCustomerParamsDto cdpCustomerParamsDto = new CdpCustomerParamsDto();
        cdpCustomerParamsDto.setAppid(appId);
        cdpCustomerParamsDto.setToken(cdpToken.getToken());
        cdpCustomerParamsDto.setAccess_timestamp(System.currentTimeMillis());
        cdpCustomerParamsDto.setMobile(mobileList);
        logger.info("queryCustomInfoListByMobile->requestParams:{}",cdpCustomerParamsDto);
        CdpResponse<List<CustomerTagsDto>> cdpCustomResult = null;
        try {
            cdpCustomResult = cdpFeign.queryBatchTagList(cdpCustomerParamsDto);
            logger.info("queryCustomInfoListByMobile->responseResult:{}",cdpCustomResult);
        } catch (Exception e) {
            throw new ServiceBizException("queryCustomInfoListByMobile error");
        }
        if (cdpCustomResult.isFail()) {
            logger.info("queryFaultLight Response 异常：{}",cdpCustomResult.getMsg());
            throw new ServiceBizException("获取cdp客户属性信息失败");
        }
        return cdpCustomResult;
    }

    @Override
    public Page<RepairOrderHistoryResultVo> queryCustomerJourney(int current, int size, RepairOrderHistoryParamsVo paramsVo) {
        DmsResponse<Page<RepairOrderHistoryResultVo>> dmsResponse = dmscloudServiceFeign.queryCustomJourney(current, size, paramsVo);
        logger.info("queryCustomerJourney->dmsResponse:{}",dmsResponse);
        if (dmsResponse.isFail()) {
            throw new ServiceBizException("获取客户旅程失败");
        }
        return dmsResponse.getData();
    }

    /**
     * 查询cdp会员信息
     */
    public CustomerInfoDto queryCdpCustomer(String vin, String version) {
        logger.info("queryCdpCustomer vin:{}",vin);
        // 代理调用，否则 Retryable 不生效
        CustomerServiceImpl proxy = ApplicationContextHelper.getBeanByType(CustomerServiceImpl.class);
        return proxy.queryCdpCustomer_(vin, version);
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public CustomerInfoDto queryCdpCustomer_(String vin, String version) {
        RetryContext context = RetrySynchronizationManager.getContext();
        int retryCount = context.getRetryCount();
        logger.info("queryCdpCustomer vin:{},retryCount:{}", vin, retryCount);
        CdpTokenPortraitDto cdpToken = cdpTagInfoService.getCdpToken(vin);
        logger.info("queryCdpCustomer cdpToken:{}", cdpToken);
        return Optional.ofNullable(cdpToken)
                .map(token -> {
                    CdpCustomerParamsDto cdpCustomerParamsDto = buildCdpCustomerParamsDto(token.getToken(), vin);
                    logger.info("queryCdpCustomer cdpCustomerParamsDto:{}", cdpCustomerParamsDto);
                    CdpResponse<JSONObject> relatedProfiles = null;
                    try {
                        if (Objects.equals(version, "v1")) {
                            logger.info("queryCdpCustomer getRelatedProfiles version eq v1");
                            relatedProfiles = cdpFeign.getRelatedProfiles(cdpCustomerParamsDto);
                        } else {
                            logger.info("queryCdpCustomer getRelatedProfiles version eq v2");
                            relatedProfiles = cdpFeign.getRelatedProfilesV2(cdpCustomerParamsDto);
                        }
                        logger.info("customCdpTagList relatedProfiles:{}",relatedProfiles);
                    } catch (Exception e) {
                        throw new ServiceBizException("customCdpTagList relatedProfiles error");
                    }
                    logger.info("queryCdpCustomer->cdpFeignAndGetRelatedProfilesResponse:{}", relatedProfiles);
                    if (relatedProfiles.isFail()) {
                        logger.info("queryCdpCustomer->cdpFeignAndGetRelatedProfilesResponseException：{}", relatedProfiles.getMsg());
                        throw new ServiceBizException("getRelatedProfiles fail");
                    }
                    if (Objects.equals(version, "v1")) {
                        logger.info("queryCdpCustomer fromResponse version eq v1");
                        return createCustomerInfoDtoFromResponse(vin, relatedProfiles);
                    } else {
                        logger.info("queryCdpCustomer fromResponse version eq v2");
                        return createCustomerInfoDtoFromResponseV2(vin, relatedProfiles);
                    }
                })
                .orElse(null);
    }

    private CdpCustomerParamsDto buildCdpCustomerParamsDto(String token, String vin) {
        CdpCustomerParamsDto cdpCustomerParamsDto = new CdpCustomerParamsDto();
        cdpCustomerParamsDto.setAppid(appId);
        cdpCustomerParamsDto.setToken(token);
        cdpCustomerParamsDto.setAccess_timestamp(System.currentTimeMillis());
        cdpCustomerParamsDto.setSubject_id(vin);
        cdpCustomerParamsDto.setSubject_id_name(CdpConstant.CDP_TAG_VIN);
        cdpCustomerParamsDto.setObject_profile_type(CdpConstant.CUSTOMER_PROFILEBASE);
        cdpCustomerParamsDto.setObject_profile_name(CdpConstant.ID_MOBILE);
        cdpCustomerParamsDto.setRelation_type(Collections.singletonList(CdpConstant.CDP_CAR_OWNER));
        cdpCustomerParamsDto.setSubject_profile_type(CdpConstant.VEHICLE_PROFILEBASE);
        return cdpCustomerParamsDto;
    }

    private CustomerInfoDto createCustomerInfoDtoFromResponse(String vin, CdpResponse<JSONObject> response) {
        CustomerInfoDto result = new CustomerInfoDto();
        result.setCustomerType(CdpConstant.CUSTOMER_TYPE_CDP);
        //String customerId = getCustomerId(response);
        result.setMobile(getCustomerId(response));
        result.setVin(vin);
        logger.info("createCustomerInfoDtoFromResponse->result :{}", result);
        return StringUtils.isBlank(result.getMobile()) ? null : result;
    }

    private CustomerInfoDto createCustomerInfoDtoFromResponseV2(String vin, CdpResponse<JSONObject> response) {
        logger.info("createCustomerInfoDtoFromResponseV2 start...");
        if (response.isFail()) {
            return null;
        }
        JSONObject data = response.getData();
        if (null == data) {
            logger.info("createCustomerInfoDtoFromResponseV2 data isNull");
            return null;
        }
        CdpCustomerResponseV2Dto dto = JSONObject.toJavaObject(data, CdpCustomerResponseV2Dto.class);
        List<RelatedProfilesV2Dto> relatedProfiles = dto.getRelated_profiles();
        if (CollectionUtils.isEmpty(relatedProfiles)) {
            logger.info("createCustomerInfoDtoFromResponseV2 relatedProfiles isEmpty");
            return null;
        }
        RelatedProfilesV2Dto relatedProfilesV2Dto = relatedProfiles.get(0);
        if (null == relatedProfilesV2Dto) {
            logger.info("createCustomerInfoDtoFromResponseV2 relatedProfilesV2Dto isNull");
            return null;
        }
        List<ObjectOneIdV2Dto> objectOneid = relatedProfilesV2Dto.getObject_oneid();
        if (CollectionUtils.isEmpty(objectOneid)) {
            logger.info("createCustomerInfoDtoFromResponseV2 objectOneid isEmpty");
            return null;
        }
        ObjectOneIdV2Dto objectOneIdV2Dto = objectOneid.get(0);
        if (null == objectOneIdV2Dto) {
            logger.info("createCustomerInfoDtoFromResponseV2 objectOneIdV2Dto isNull");
            return null;
        }
        CustomerInfoDto result = new CustomerInfoDto();
        result.setCustomerType(CdpConstant.CUSTOMER_TYPE_CDP);
        result.setVin(vin);
        result.setMobile(objectOneIdV2Dto.getId());
        result.setCustomerName(objectOneIdV2Dto.getName());
        logger.info("createCustomerInfoDtoFromResponse result:{}", result);
        return result;
    }

    /**
     * 查询中台车主
     *
     * @param vin
     * @param ownerCode
     */
    private CustomerInfoDto queryMidCustomer(String vin, String ownerCode){

        MidVehicleVo midVehicleVo = getMidVehicleVo(vin);
        if (midVehicleVo == null) {
            if (ObjectUtils.isEmpty(ownerCode)){
                logger.info("queryMidCustomer,ownerInfoResultsVo->ownerCode 经销商信息为空");
                return null;
            }
            midVehicleVo = new MidVehicleVo();
            DmsResponse<OwnerInfoResultsVo> ownerInfo = dmscloudServiceFeign.getOwnerInfo(vin, ownerCode);
            logger.info("queryMidCustomer,ownerInfo->getOwnerInfo:{}",ownerInfo);
            if (ownerInfo.isFail()){
                logger.info("queryMidCustomer,ownerInfo->Response 异常");
                return null;
            }
            OwnerInfoResultsVo ownerInfoResultsVo = ownerInfo.getData();
            if (Objects.isNull(ownerInfoResultsVo)){
                logger.info("queryMidCustomer,ownerInfoResultsVo->Response 车主信息为空");
                return null;
            }
            midVehicleVo.setName(ownerInfoResultsVo.getOwnerName());
            midVehicleVo.setMobile(ownerInfoResultsVo.getMobileFirst());
            midVehicleVo.setContactorMobile(ownerInfoResultsVo.getMobileSecond());
            midVehicleVo.setContactorPhone(ownerInfoResultsVo.getPhone());
        };
        return createdCustomerInfo(vin, midVehicleVo);
    }

    private MidVehicleVo getMidVehicleVo(String vin) {
        RequestDto<CareBuyedVo> objectRequestDto = new RequestDto<>();
        CareBuyedVo careBuyedVo = new CareBuyedVo();
        careBuyedVo.setVin(vin);
        objectRequestDto.setData(careBuyedVo);
        objectRequestDto.setPage(1L);
        objectRequestDto.setPageSize(1L);
        DmsResponse<Page<MidVehicleVo>> listDmsResponse = midEndVehicleCenterFeign.listOwnerVehiclePage(objectRequestDto);

        logger.info("queryMidCustomer->listOwnerVehiclePage:{}",listDmsResponse);
        if (listDmsResponse.isFail()){
            logger.info("queryMidCustomer,listDmsResponse->Response 异常");
            return null;
        }
        Page<MidVehicleVo> data = listDmsResponse.getData();
        if (Objects.isNull(data)){
            logger.info("queryMidCustomer,data->Response 车主信息为空");
            return null;
        }
        List<MidVehicleVo> records = data.getRecords();
        if (CollectionUtils.isEmpty(records)){
            logger.info("queryMidCustomer,records->Response 车主信息为空");
            return null;
        }
        MidVehicleVo midVehicleVo = records.get(0);
        if (ObjectUtils.isEmpty(midVehicleVo)){
            logger.info("queryMidCustomer,midVehicleVo->Response 车主信息为空");
            return null;
        }

        if(ObjectUtils.isEmpty(midVehicleVo.getOneId())){
            logger.info("queryMidCustomer,midVehicleVo,OneId->Response 车主信息为空");
            return null;
        }
        return midVehicleVo;
    }

    private CustomerInfoDto createdCustomerInfo(String vin, MidVehicleVo midVehicleVo) {
        String mobile = null;
        if (ObjectUtils.isNotEmpty(midVehicleVo.getMobile())){
            mobile = midVehicleVo.getMobile();
        } else if (ObjectUtils.isNotEmpty(midVehicleVo.getContactorMobile())) {
            mobile = midVehicleVo.getContactorMobile();
        } else if (ObjectUtils.isNotEmpty(midVehicleVo.getContactorPhone())) {
            mobile = midVehicleVo.getContactorPhone();
        }

        CustomerInfoDto result=new CustomerInfoDto();
        result.setCustomerType(CdpConstant.CUSTOMER_TYPE_MID);
        result.setCustomerName(midVehicleVo.getName());
        result.setMobile(mobile);
        result.setMemberUrl(null);
        result.setVin(vin);
        logger.info("createdCustomerInfo->result :{}",result);
        return result;
    }

    /**
     * 查询自店车主
     */
    @Override
    public CustomerInfoDto queryOwnerCustomer(String vin, String ownerCode) {
        CustomerInfoDto result = new CustomerInfoDto();
        // 如果不存在经销商则无需查询自店车主
        if (StringUtils.isBlank(ownerCode)) {
            return result;
        }
        // 查询自店车主
        DmsResponse<CustomerInfoDto> response = dmscloudServiceFeign.queryCustomerInfo(vin, ownerCode);
        logger.info("queryOwnerCustomer->customerInfoDtoDmsResponse{}", response);
        if (null == response || response.isFail()) {
            logger.info("queryOwnerCustomer->Response 异常");
            return result;
        }
        // 查询data
        CustomerInfoDto data = response.getData();
        if (null == data) {
            return result;
        }
        // 回写数据
        result.setVin(vin);
        result.setMobile(StringUtils.isBlank(data.getMobile()) ? null : data.getMobile());
        result.setCustomerName(data.getCustomerName());
        result.setCustomerType(CdpConstant.CUSTOMER_TYPE_OWNER);
        logger.info("queryOwnerCustomer->result :{}", result);
        return result;
    }

    /**
     * 查询最近3个送修人
     */
    @Override
    public List<CustomerInfoDto> queryRepairOwner(String vin, String ownerCode){
        Map<String, String> queryParam = new HashMap<>();
        queryParam.put("vin", vin);
        queryParam.put("ownerCode", ownerCode);
        logger.info("queryRepairOwner->queryRepairOrderInfoByVinAndOwnerCode:{}", queryParam);
        DmsResponse<List<RepairOrderDto>> repairOrderResponse = domainMaintainOrdersFeign.queryRepairOrderInfoByVinAndOwnerCode(queryParam);
        logger.info("repairOrderResponse->Response:{}", repairOrderResponse);
        return Optional.ofNullable(repairOrderResponse)
                .filter(response -> !response.isFail())
                .map(DmsResponse::getData)
                .filter(CollectionUtils::isNotEmpty)
                .map(this::convertToCustomerInfoDtoList)
                .orElseGet(() -> {
                    logger.info("No valid repair order data or response indicates failure.");
                    return null;
                });
    }

    private List<CustomerInfoDto> convertToCustomerInfoDtoList(List<RepairOrderDto> repairOrderDTOList) {
        Integer[] customerTypes = { CdpConstant.CUSTOMER_TYPE_SEND_REPAIR_A, CdpConstant.CUSTOMER_TYPE_SEND_REPAIR_B, CdpConstant.CUSTOMER_TYPE_SEND_REPAIR_C };
        AtomicInteger index = new AtomicInteger();
        return repairOrderDTOList.stream()
                .limit(3)
                .map(repairOrderDTO -> {
                    CustomerInfoDto dto = convertToCustomerInfoDto(repairOrderDTO);
                    // 获取当前的索引并对数组长度取余
                    int typeIndex = index.getAndIncrement() % customerTypes.length;
                    dto.setCustomerType(customerTypes[typeIndex]);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    private CustomerInfoDto convertToCustomerInfoDto(RepairOrderDto repairOrderDTO) {
        CustomerInfoDto customerInfoDto = new CustomerInfoDto();
        customerInfoDto.setCustomerName(StringUtils.isBlank(repairOrderDTO.getDeliverer())
                ? repairOrderDTO.getOwnerName()
                : repairOrderDTO.getDeliverer());
        customerInfoDto.setMobile(StringUtils.isBlank(repairOrderDTO.getDelivererMobile())
                ? repairOrderDTO.getDelivererPhone()
                : repairOrderDTO.getDelivererMobile());
        customerInfoDto.setVin(repairOrderDTO.getVin());
        logger.info("convertToCustomerInfoDto->customerInfoDto:{}", customerInfoDto);
        return customerInfoDto;
    }

    /**
     * 故障灯车主
     */
    public CustomerInfoDto queryFaultLight(String vin, String ownerCode){
        DmsResponse<CustomerInfoDto> customerInfoDtoDmsResponse = dmscusCustomerFeign.queryFaultLight(vin, ownerCode);
        logger.info("queryFaultLight->customerInfoDtoDmsResponse{}",customerInfoDtoDmsResponse);
        if (customerInfoDtoDmsResponse.isFail()){
            logger.info("queryFaultLight->Response 异常");
        }
        if(Objects.isNull(customerInfoDtoDmsResponse.getData())){
            return null;
        }
        CustomerInfoDto faultLightInfo = customerInfoDtoDmsResponse.getData();
        return createdFaultLifgtInfo(vin, faultLightInfo);
    }

    private CustomerInfoDto createdFaultLifgtInfo(String vin, CustomerInfoDto faultLightInfo) {
        CustomerInfoDto result = new CustomerInfoDto();
        result.setVin(vin);
        result.setMobile(faultLightInfo.getMobile());
        result.setCustomerName(faultLightInfo.getCustomerName());
        result.setCustomerType(CdpConstant.CUSTOMER_TYPE_FAULT_LIGHT);
        logger.info("queryFaultLight->createdFaultLiftInfo :{}",result);
        return result;
    }

    /**
     *获取用户id
     */
    public String getCustomerId(CdpResponse<JSONObject> relatedProfiles){
        if (!relatedProfiles.isSuccess()){
            return null;
        }
        JSONObject data = relatedProfiles.getData();
        if (null == data) {
            return null;
        }
        CdpCustomerResponseDto dto = JSONObject.toJavaObject(data, CdpCustomerResponseDto.class);
        if (CollectionUtils.isNotEmpty(dto.getRelated_profiles())&& Objects.nonNull(dto.getRelated_profiles().get(0).getObject_oneid()[0])){
            return dto.getRelated_profiles().get(0).getObject_oneid()[0];
        }
        return null;
    }

    /**
     * 查询零附件
     * @param ownerCode 经销商
     * @param vin vin
     * @return 返回零附件客户
     */
    private CustomerInfoDto queryInviteVehicleRecord(String ownerCode, String vin){
        DmsResponse<InviteVehicleRecordVo> customerInfoDtoDmsResponse = dmscusCustomerFeign.queryInviteVehicleByVin(ownerCode, vin, CommonConstant.PARTSVOUCHER);
        logger.info("queryInviteVehicleRecord->customerInfoDtoDmsResponse{}",JSONObject.toJSONString(customerInfoDtoDmsResponse));
        if (customerInfoDtoDmsResponse.isFail()){
            logger.info("queryInviteVehicleRecord->Response 异常");
        }
        if(BeanUtil.isEmpty(customerInfoDtoDmsResponse.getData())){
            return null;
        }
        InviteVehicleRecordVo inviteRecord = customerInfoDtoDmsResponse.getData();
        return createdInviteRecordInfo(vin, inviteRecord);
    }

    /**
     *
     * @param vin vin
     * @param inviteRecord 拿到的数据
     * @return 返回构建对象
     */
    private CustomerInfoDto createdInviteRecordInfo(String vin, InviteVehicleRecordVo inviteRecord) {
        CustomerInfoDto result = new CustomerInfoDto();
        result.setVin(vin);
        result.setMobile(inviteRecord.getTel());
        result.setCustomerName(inviteRecord.getName());
        result.setCustomerType(CdpConstant.CUSTOMER_TYPE_INVITE);
        logger.info("queryFaultLight->createdInviteRecordInfo :{}",JSONObject.toJSONString(result));
        return result;
    }



    public CustomerInfoDto queryPolicyholder(String vin, String ownerCode, Boolean tab){
        logger.info("queryPolicyholder requestParams vin:{},ownerCode:{}",vin,ownerCode);
        boolean existWhite = Optional.ofNullable(ownerCode)
                .map(this::isWhite)
                .orElse(false);
        if (!existWhite) {
            return null;
        }
        CustomerInfoDto result = new CustomerInfoDto();
        InviteInsuranceVehicleRecordExtDto policyholderDto = queryPolicyholderByVinAndOwnerCode(vin, ownerCode, tab);
        if (ObjectUtils.isEmpty(policyholderDto)) {
            return null;
        }
        result.setCustomerType(tab ? CdpConstant.CUSTOMER_TYPE_POLICYHOLDER : CdpConstant.CUSTOMER_TYPE_OWNER_POLICYHOLDER);
        result.setVin(vin);
        result.setMobile(tab ? policyholderDto.getInsuredPhone() : policyholderDto.getPolicyHolderMobile());
        result.setCustomerName(tab ? policyholderDto.getInsuredName() : policyholderDto.getPolicyHolderName());
        return result;
    }


    public InviteInsuranceVehicleRecordExtDto queryPolicyholderByVinAndOwnerCode(String vin, String ownerCode, Boolean tab){
        DmsResponse<InviteInsuranceVehicleRecordExtDto> domainLeadResponse = domainInsuranceLeadsFeign.queryPolicyholder(vin, ownerCode, tab);
        logger.info("queryPolicyholderByVinAndOwnerCode responseParams:{}",JSONObject.toJSONString(domainLeadResponse));
        if (domainLeadResponse.isFail()) {
            throw new com.yonyou.cyx.function.exception.ServiceBizException("获取线索领域失败");
        }
        if (domainLeadResponse.getData() == null) {
            return null;
        }
        return domainLeadResponse.getData();
    }

    private boolean isWhite(String dealer) {
        logger.info("Renewal of insurance Policyholder  isWhite dealer:{}", dealer);
        DmsResponse<Object> response = domainMaintainAuthFeign.checkWhitelist(dealer, CommonConstant.RENEWAL_INSURANCE_WHITE, CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE, "");
        logger.info("Renewal of insurance Policyholder isWhite response:{}",response);
        if (Objects.isNull(response) || response.isFail()){
            logger.info("Renewal of insurance Policyholder isWhite error");
            return false;
        }
        Object data = response.getData();
        if (null == data) {
            logger.info("Renewal of insurance Policyholder isWhite data isnull");
            return false;
        }
        try {
            return Boolean.parseBoolean(data.toString());
        } catch (Exception e) {
            logger.info("Renewal of insurance Policyholder isWhite e:{}", e);
            return false;
        }
    }



}
