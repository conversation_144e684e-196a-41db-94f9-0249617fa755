package com.volvo.maintain.application.maintainlead.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 返回邀约线索列表信息
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MaintainLeadPageDto implements Serializable {
    private List<MaintainLeadDto> maintainLeadDtoList;

    private Long total;

    private Long size;

    private Long current;
}
