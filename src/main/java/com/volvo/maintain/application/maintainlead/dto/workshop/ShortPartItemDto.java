package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShortPartItemDto {

    /**
     * 是否BO订单
     */
    private Integer isBo;

    /**
     * 当前库存
     */
    private BigDecimal stockQuantity;

    private Integer chose;

    /**
     * 仓库
     */
    private String storageName;

    /**
     * 缺料记录ID
     */
    private Integer shortId;

    private String ownerCode;

    /**
     * 仓库代码
     */
    private String storageCode;

    /**
     * 零件号
     */
    private String partNo;

    private String partName;

    /**
     * 库位代码
     */
    private String storagePositionCode;

    /**
     * 出入库类型
     */
    private Integer inOutType;

    /**
     * 缺料类型
     */
    private Integer shortType;

    /**
     * 单据号码
     */
    private String sheetNo;

    /**
     * 是否已结案
     */
    private Integer closeStatus;

    /**
     * 是否急件
     */
    private Integer isUrgent;

    /**
     * 车牌号
     */
    private String license;

    /**
     * 缺件数量
     */
    private BigDecimal shortQuantity;

    /**
     * 经手人
     */
    private String handler;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 发料时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String sendTime;

    /**
     * 缺料日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createdAt;

    /**
     * 关联 采购明细id
     */
    private Long purchaseOrderDetailId;

    /**
     * 是否背靠背
     */
    private Long isLinked;

    /**
     * 缺件状态
     */
    private Long missingPartsStatus;

    /**
     * 预计到货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String expectedDeliveryTime;

    /**
     * 零件状态,替换件;block
     */
    private Long partsStatus;

    /**
     * 替换件号
     */
    private String replaceParts;


    /**
     * 采购单号，生成规则YV+YY+MM+四位自增长数字；批量采购订单号，生成规则YI+YY+MM+四位自增长数字
     */
    private String purchaseNo;

    /**
     * "派工状态(80591001：未派工 80591002：部分派工 80591003：已派工)"
     */
    private String assignStatus;

    /**
     * 服务顾问
     */
    private String serviceAdvisor;

    private String vin;

    private String modelCode;

    private String modelName;

    /**
     * 预计交车时间
     */
    private String endTimeSupposed;

    private String roStatus;

    private List<PartsStockDetailDto> purchaseOrderDetailsDtoList;

    /**
     * 详情页面 已到货 未到货标签名称
     */
    private String missingPartsStatusTag;

    /**
     * 技师
     */
    @ApiModelProperty(value = "技师", hidden = true)
    private String technician;



}
