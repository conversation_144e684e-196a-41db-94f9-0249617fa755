package com.volvo.maintain.application.maintainlead.dto.claimapply;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/09/13
 */
@Data
public class ClaimApplyHandDTO implements Serializable {

    /**
     * 系统id
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织id
     */
    private Integer orgId;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 理赔申请id 关联tt_claim_apply_use的id
     */
    private Long applyId;

    /**
     * 故障编码
     */
    private String handRepairProjectCode;

    /**
     * 故障编码描述
     */
    private String handRepairProjectName;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

}
