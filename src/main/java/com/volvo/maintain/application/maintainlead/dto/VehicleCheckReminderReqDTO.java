package com.volvo.maintain.application.maintainlead.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("交车校验提醒")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VehicleCheckReminderReqDTO {

    @ApiModelProperty("车架号")
    private String vin;

    @ApiModelProperty("经销商CODE")
    private String ownerCode;

    @ApiModelProperty("工单")
    private String roNo;

    @ApiModelProperty("提醒类型 1: em90车辆检测报告提醒 2: 重点维修组弹窗 3: 重点关照组弹窗")
    private List<String> remindType;

    @ApiModelProperty("未提交报告原因")
    private String reportReason;

}
