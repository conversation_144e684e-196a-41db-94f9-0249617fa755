package com.volvo.maintain.application.maintainlead.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WarrantyMaintenanceReminderListDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 经销商代码
     */
    private String ownerCode;

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 结算单号
     */
    private String balanceNo;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 进厂行驶里程
     */
    private BigDecimal inMileage;

    /**
     * 交车日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryDate;

    /**
     * 提醒内容
     */
    private String reminderMsg;

    /**
     * 是否提醒 : 10041001 是 10041002 否
     */
    private Integer isReminder;
}
