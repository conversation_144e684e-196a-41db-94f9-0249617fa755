package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 功能描述：全量线索查询DTO对象
 *
 * <AUTHOR>
 * @since 2023/12/18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "FullLeadQueryDto", description = "全量线索查询对象")
public class FullLeadQueryDto {

    @ApiModelProperty(value = "经销商代码")
    private String dealerCode;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNum;

    @ApiModelProperty(value = "vin")
    private String vin;

    @ApiModelProperty(value = "邀约类型")
    private List<Integer> inviteTypeList;

    @ApiModelProperty(value = "跟进状态")
    private List<Integer> followStatusList;

    @ApiModelProperty(value = "故障灯跟进状态")
    private List<Integer> faultLightFollowStatus;

    @ApiModelProperty(value = "跟进人员id")
    private List<Long> saIdList;

    @ApiModelProperty(value = "跟进人")
    private String saName;

    @ApiModelProperty(value = "下次跟进日期(开始)")
    private String planFollowDateBegin;

    @ApiModelProperty(value = "下次跟进日期(结束)")
    private String planFollowDateEnd;

    @ApiModelProperty(value = "建议进厂日期/续保到期日期(开始)")
    private String adviseInDateBegin;

    @ApiModelProperty(value = "建议进厂日期/续保到期日期(结束)")
    private String adviseInDateEnd;

    @ApiModelProperty(value = "二次跟进日期")
    private String twiceMonth;

    @ApiModelProperty(value = "当前页", name = "currentPage")
    private Long currentPage;

    @ApiModelProperty(value = "多少页", name = "pageSize")
    private Long pageSize;

    @ApiModelProperty(value = "是否包含故障灯线索")
    private Integer isContainsFaultLight;

    @ApiModelProperty(value = "是否包含续保线索")
    private Integer isContainsInsurance;

    @ApiModelProperty(value = "售后大区")
    private String largeAreaId;

    @ApiModelProperty(value = "区域经理")
    private String areaId;

    @ApiModelProperty(value = "经销商代码列表")
    private List<String> dealerCodeList;
}
