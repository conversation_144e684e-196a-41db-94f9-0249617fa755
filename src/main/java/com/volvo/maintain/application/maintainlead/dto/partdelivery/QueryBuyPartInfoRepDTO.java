package com.volvo.maintain.application.maintainlead.dto.partdelivery;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 在途数据请求
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
public class QueryBuyPartInfoRepDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 零件号
     */
	@ApiModelProperty(value = "零件号", name = "partNo")
    private String partNo;

    /**
     * 仓库
     */
	@ApiModelProperty(value = "仓库", name = "storageCode")
    private String storageCode;
}
