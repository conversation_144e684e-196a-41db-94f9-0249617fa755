package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class OutboundOrderToCResponseDto {

    /**
     * 采购单明细表  id 关联条件
     */
    private Long purchaseOrderDetailId;

    /**
     * 单据号码
     */
    private String sheetNo;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 配件代码
     */
    private String partNo;

    /**
     * 配件名称
     */
    private String partName;


    /**
     * 采购单号，生成规则YV+YY+MM+四位自增长数字；批量采购订单号，生成规则YI+YY+MM+四位自增长数字
     */
    private List<String> purchaseNoList;

    /**
     * 缺料记录ID
     */
    private Integer shortId;
    /**
     * 仓库代码
     */
    private String storageCode;
    /**
     * 库位代码
     */
    private String storagePositionCode;
    /**
     * 出入库类型
     */
    private Integer inOutType;

    /**
     * 缺料类型
     */
    private Integer shortType;

    /**
     * 是否已结案
     */
    private Integer closeStatus;

    /**
     * 是否急件
     */
    private Integer isUrgent;

    /**
     * 车牌号
     */
    private String license;

    /**
     * 缺件数量
     */
    private BigDecimal shortQuantity;

    /**
     * 经手人
     */
    private String handler;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 电话
     */
    private String phone;


    /**
     * 发料时间
     */
    private Date sendTime;

    /**
     * 是否BO订单
     */
    private Integer isBo;
    /**
     * 缺料日期
     */
    private Date createdAt;
    /**
     * 是否背靠背
     */
    private Long isLinked;



}
