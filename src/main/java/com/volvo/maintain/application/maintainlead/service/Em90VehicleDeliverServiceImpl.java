package com.volvo.maintain.application.maintainlead.service;

import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.message.MessageSendDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import com.volvo.maintain.application.maintainlead.vo.VehicleDeliverVO;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;
import com.volvo.utils.JSONUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class Em90VehicleDeliverServiceImpl implements Em90VehicleDeliverService {
    @Autowired
    protected RedissonClient redissonClient;

    @Autowired
    MidEndOrderCenterFeign midEndOrderCenterFeign;

    @Autowired
    DominAutoFeign dominAutoFeign;

    @Autowired
    MidEndVehicleCenterFeign midEndVehicleCenterFeign;
    @Autowired
    MidEndAuthCenterFeign midEndAuthCenterFeign;
    @Autowired
    DmscloudServiceFeign dmscloudServiceFeign;
    @Autowired
    CommonMethodService commonMethodService;

    @Autowired
    MidEndDictCenterFeign midEndDictCenterFeign;

    @Autowired
    private DomainMaintainAuthFeign domainMaintainAuthFeign;

/*    @Autowired
    private RocketMQTemplate rocketMQTemplate;*/

    @Override
    public void em90Deliver(String message) {
        //解析消息
        VehicleDeliveryStatusChangeDTO vehicleDeliveryStatusChangeDTO = JSONUtil.jsonToObj(message, VehicleDeliveryStatusChangeDTO.class);
        log.info("vehicleDeliveryStatusChangeDTO :{}", vehicleDeliveryStatusChangeDTO);
        if (vehicleDeliveryStatusChangeDTO == null) {
            throw new ServiceBizException("消息体转换失败");
        }
        if (vehicleDeliveryStatusChangeDTO.getOrderStatus() == null) {
            throw new ServiceBizException("状态不能为空");
        }
        if (vehicleDeliveryStatusChangeDTO.getAppointId() == null) {
            throw new ServiceBizException("id不为空");
        }
        if (!Objects.equals(vehicleDeliveryStatusChangeDTO.getOrderStatus().toString(),CommonConstant.EM90_82721001) && !Objects.equals(vehicleDeliveryStatusChangeDTO.getOrderStatus().toString(),CommonConstant.EM90_82721002)){
            log.info("状态只处理EM90_82721001 EM90_82721002");
            return;
        }
        //通过订单号查询订单详情 入参
        RequestDto dataDto = new RequestDto();
        DataDto data = new DataDto();
        data.setId(vehicleDeliveryStatusChangeDTO.getAppointId());
        dataDto.setData(data);
        log.info("DataDto data通过订单号查询订单详情:{}", data);
        MidResponse<List<VehicleDeliverDTO>> listDmsResponse = dominAutoFeign.allDetail(dataDto);
        log.info("listDmsResponse:{}", listDmsResponse);
        if (listDmsResponse.isFail()) {
            log.info("listDmsResponse 通过订单号查询订单详情异常");
            return;
        }
        if (CollectionUtils.isEmpty(listDmsResponse.getData())) {
            log.info("listDmsResponse.getData isEmpty");
            return;
        }
        VehicleDeliverDTO vehicleDeliverDTO = listDmsResponse.getData().get(0);
        //vin
        String vin = vehicleDeliverDTO.getVehicleDeliverVO().getVin();
        //经销商code
        String dealerCode = vehicleDeliverDTO.getVehicleDeliverVO().getDealerCode();
        //养修预约单号
        String reservationNo = vehicleDeliverDTO.getVehicleDeliverVO().getReservationNo();
        //根据vin查询车辆信息
        if (StringUtils.isNotEmpty(vin)) {
            DmsResponse<TmVehicleDto> vehicleByVIN = midEndVehicleCenterFeign.getVehicleByVIN(vin);
            log.info("midEndVehicleCenterFeign getVehicleByVIN DmsResponse:{}", vehicleByVIN);
            if (Objects.isNull(vehicleByVIN)) {
                log.info("根据vin查询车辆信息为null");
                return;
            }
            if (vehicleByVIN.isFail()) {
                log.info("根据vin查询车辆信息异常");
                return;
            }
            if (Objects.isNull(vehicleByVIN.getData())) {
                log.info("根据vin查询车辆信息为空");
                return;
            }
            if (StringUtils.isEmpty(vehicleByVIN.getData().getModelCode())) {
                log.info("根据vin查询车辆信息车型为空");
                return;
            }


            //判断车型是否为em90
            DmsResponse<CommonConfigDto> emKey = dmscloudServiceFeign.getConfigByKey(CommonConstant.MODEL_CODE_EM90, CommonConstant.GROUP_TYPE_EM90);
            log.info("emKey :{}", emKey);
            if (Objects.isNull(emKey)) {
                log.info("emKey为null");
                return;
            }
            if (emKey.isFail()) {
                log.info("emKey异常");
                return;
            }
            if (Objects.isNull(emKey.getData())) {
                log.info("emKey异常");
                return;
            }
            if (!vehicleByVIN.getData().getModelCode().equals(emKey.getData().getConfigValue())) {
                log.info("该车型不属于em90");
                return;
            }
            //根据订单状态 发送对应消息
            switch (vehicleDeliveryStatusChangeDTO.getOrderStatus().toString()) {
                case "82721001":
                    // 待确认
                    // TODO 0321 release 注释代码，如需提测烦请自行打开再进行uat发布
                    sendFWJL(vehicleDeliveryStatusChangeDTO, vehicleDeliverDTO, dealerCode, reservationNo);

                    // 如果是待下单则set到redis
                    setRedisEM9082721001(vehicleDeliveryStatusChangeDTO.getAppointId());
                    break;
                case "82721002":
                    //已下单
                    // TODO 0321 release 注释代码，如需提测烦请自行打开再进行uat发布
                    sendPrefectureEqual(vehicleDeliverDTO);
                    break;
            }
        }
    }

    private void sendFWJL(VehicleDeliveryStatusChangeDTO vehicleDeliveryStatusChangeDTO, VehicleDeliverDTO vehicleDeliverDTO, String dealerCode, String reservationNo) {
        try {
            //查询经销商角色
            ResponseDto<EmpByRoleCodeDto> responseDto = new ResponseDto<>();
            EmpByRoleCodeDto empByRoleCodeDto = new EmpByRoleCodeDto();
            empByRoleCodeDto.setCompanyCode(dealerCode);
            empByRoleCodeDto.setIsOnjob(CommonConstant.IS_ON_JOB_IN);
            empByRoleCodeDto.setRoleCode(Arrays.asList(CommonConstant.ROLE_CODE_QSCZY, CommonConstant.ROLE_CODE_FWJL));
            responseDto.setData(empByRoleCodeDto);
            log.info("responseDto :{}", responseDto);
            DmsResponse<List<EmpByRoleCodeDto>> listDmsResponse1 = midEndAuthCenterFeign.queryDealerUser(responseDto);
            log.info("listDmsResponse1:{}", listDmsResponse1);
            if (Objects.isNull(listDmsResponse1) || listDmsResponse1.isFail()) {
                log.info("responseDto 查询经销商角色异常");
                return ;
            }
            if (!CollectionUtils.isEmpty(listDmsResponse1.getData())) {
                listDmsResponse1.getData().forEach(e -> {
                    //取送车专员
                    // 由于订单中心老逻辑还在继续发送给取送车专员，暂不开启sendQSCZY相关代码
                    //sendQSCZY(reservationNo, e, vehicleDeliverDTO, vehicleDeliveryStatusChangeDTO.getOrderStatus());
                });
            }

            responseDto.getData().setRoleCode(Arrays.asList(CommonConstant.ROLE_CODE_FWJL, CommonConstant.ROLE_CODE_DZ));
            DmsResponse<List<EmpByRoleCodeDto>> listDmsResponse2 = midEndAuthCenterFeign.queryDealerUser(responseDto);
            log.info("listDmsResponse2:{}", listDmsResponse2);
            if (Objects.isNull(listDmsResponse2) || listDmsResponse2.isFail()) {
                log.info("responseDto 查询经销商角色异常");
                return ;
            }
            if (!CollectionUtils.isEmpty(listDmsResponse2.getData())) {
                listDmsResponse2.getData().forEach(e -> {
                    //服务经理 店总
                    sendFWJL(e, vehicleDeliverDTO, vehicleDeliveryStatusChangeDTO.getOrderStatus());
                });
            }
        } catch (Exception e) {
            log.info("sendFWJL:{}", e);
        }
    }

    /**
     * EM90下单存入redis
     */
    private void setRedisEM9082721001(Integer orderId) {
        log.info("setRedisEM9082721001 orderId:{}", orderId);
        // 获取系统配置
        DmsResponse<CommonConfigDto> configResult = dmscloudServiceFeign.getConfigByKey(CommonConstant.EM90_DELAY_TIME, CommonConstant.EM90_GROUP_TYPE);
        log.info("configResult response:{}", JSONObject.toJSONString(configResult));
        if (null == configResult || configResult.isFail()) {
            log.info("setRedisEM9082721001 configResult isNull or isFail");
            return;
        }
        CommonConfigDto data = configResult.getData();
        if (null == data) {
            log.info("setRedisEM9082721001 data isNull");
            return;
        }
        Long value = Long.parseLong(data.getConfigValue());
        String key = CommonConstant.EM90_TO_BE_CONFIRMED_PREFIX.concat(String.valueOf(orderId));
        log.info("setRedisEM9082721001 value:{},key:{}", value, key);

        // set redis
        RBucket<String> bucket = redissonClient.getBucket(key, new StringCodec("utf-8"));
        bucket.set(CommonConstant.EM90_82721001 + "", value, TimeUnit.MINUTES);
    }

    @Override
    public TaskDeliverCarResponseDto vehicleDeliver(TaskDeliverCarRequestDto taskDeliverCarRequestDto) {
        Objects.requireNonNull(taskDeliverCarRequestDto, "vehicleDeliver taskDeliverCarRequestDto isNull");

        log.info("vehicleDeliver begin request:{}", JSONObject.toJSONString(taskDeliverCarRequestDto));
        RequestDto<TaskDeliverCarRequestDto> requestDtoRequestDto = getTaskDeliverCarRequestDtoRequestDto(taskDeliverCarRequestDto);

        // 调用白名单
        boolean data = isWhite(taskDeliverCarRequestDto.getDealerCode());
        MidResponse<TaskDeliverCarResponseDto> taskDeliverCarResponseDtoMidResponse = null;
        if (data){
            taskDeliverCarResponseDtoMidResponse = dominAutoFeign.vehicleDeliver(requestDtoRequestDto);
            log.info("vehicleDeliver end response:{}", JSONObject.toJSONString(taskDeliverCarResponseDtoMidResponse));
            if (taskDeliverCarResponseDtoMidResponse.isFail()) {
                throw new ServiceBizException("订单中心异常:"+ taskDeliverCarResponseDtoMidResponse.getReturnMessage());
            }
        }else {
            taskDeliverCarResponseDtoMidResponse = midEndOrderCenterFeign.vehicleDeliver(requestDtoRequestDto);
            log.info("vehicleDeliver end response:{}", JSONObject.toJSONString(taskDeliverCarResponseDtoMidResponse));
            if (taskDeliverCarResponseDtoMidResponse.isFail()) {
                throw new ServiceBizException("订单中心异常:"+ taskDeliverCarResponseDtoMidResponse.getReturnMessage());
            }
        }

        return taskDeliverCarResponseDtoMidResponse.getData();
    }


    private RequestDto<TaskDeliverCarRequestDto> getTaskDeliverCarRequestDtoRequestDto(TaskDeliverCarRequestDto taskDeliverCarRequestDto) {
        RequestDto<TaskDeliverCarRequestDto> requestDtoRequestDto = new RequestDto<>();
        if (StringUtils.isNotEmpty(taskDeliverCarRequestDto.getPickupAreaCode()) && StringUtils.isNotEmpty(taskDeliverCarRequestDto.getReturnAreaCode())) {
            MidResponse<List<RegionAllDto>> regionAllDtos = midEndDictCenterFeign.queryRegionAll();
            if (Objects.isNull(regionAllDtos) || regionAllDtos.isFail() || CollectionUtils.isEmpty(regionAllDtos.getData())) {
                log.info("getTaskDeliverCarRequestDtoRequestDto regionAllDtos isNull");
                return requestDtoRequestDto;
            }
            try {
                log.info("regionAllDtos size:{}", regionAllDtos.getData().size());
                Map<String, RegionAllDto> flattenedMap = new HashMap<>();
                this.flattenMultiLevelList(flattenedMap, regionAllDtos.getData());
                //判断当前类型为取车还是送车
                this.setRegionCode(flattenedMap, taskDeliverCarRequestDto);
            } catch (Exception e) {
                log.error("EM90取送车代下单号:{},获取省市区信息异常:{}", taskDeliverCarRequestDto.getCarNo(), e);
            }
        }
        requestDtoRequestDto.setData(taskDeliverCarRequestDto);
        log.info("taskDeliverCarResponseDtoMidResponse request:{}", JSONObject.toJSONString(requestDtoRequestDto));
        return requestDtoRequestDto;
    }

    private boolean isWhite(String dealer) {
        log.info("vehicleDeliver isWhite dealer:{}", dealer);
        DmsResponse<Object> response = domainMaintainAuthFeign.checkWhitelist(dealer, 91111032, CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE, "");
        log.info("vehicleDeliver isWhite response:{}",response);
        if (Objects.isNull(response) || response.isFail()){
            log.info("vehicleDeliver isWhite error");
            return false;
        }
        Object data = response.getData();
        if (null == data) {
            log.info("vehicleDeliver isWhite data isnull");
            return false;
        }
        try {
            return Boolean.parseBoolean(data.toString());
        } catch (Exception e) {
            log.info("vehicleDeliver isWhite e:{}", e);
            return false;
        }
    }

    private void setRegionCode(Map<String, RegionAllDto> flattenedMap, TaskDeliverCarRequestDto taskDeliverCarRequestDto) {
        String provinceCode = null;
        String cityCode=null;
        String areaCode=null;
        RegionAllDto cityRegionAllDto;
        RegionAllDto provinceRegionAllDto;
        String regionCode = CommonConstant.EM90_TASK_CAR.equals(taskDeliverCarRequestDto.getType()) ?
                taskDeliverCarRequestDto.getPickupAreaCode() :
                taskDeliverCarRequestDto.getReturnAreaCode();
        RegionAllDto areaRegionAllDto = flattenedMap.get(regionCode);
        if (Objects.nonNull(areaRegionAllDto)){
            cityRegionAllDto = flattenedMap.get(areaRegionAllDto.getParentRegionId());
            if (Objects.nonNull(cityRegionAllDto)){
                provinceRegionAllDto= flattenedMap.get(cityRegionAllDto.getParentRegionId());
                if (Objects.nonNull(provinceRegionAllDto)){
                    provinceCode = provinceRegionAllDto.getRegionCode();
                    cityCode = cityRegionAllDto.getRegionCode();
                    areaCode =areaRegionAllDto.getRegionCode();
                } else if (CommonConstant.REGION_CROWN_CODE.equals(cityRegionAllDto.getParentRegionId())) {
                    provinceCode = cityRegionAllDto.getRegionCode();
                    cityCode = areaRegionAllDto.getRegionCode();
                }
            }else if(CommonConstant.REGION_CROWN_CODE.equals(areaRegionAllDto.getParentRegionId())){
                provinceCode = areaRegionAllDto.getRegionCode();
            }
        }
        if (CommonConstant.EM90_TASK_CAR.equals(taskDeliverCarRequestDto.getType())){
            taskDeliverCarRequestDto.setPickupProvinceCode(provinceCode);
            taskDeliverCarRequestDto.setPickupCityCode(cityCode);
            taskDeliverCarRequestDto.setPickupAreaCode(areaCode);
        }else {
            taskDeliverCarRequestDto.setReturnProvinceCode(provinceCode);
            taskDeliverCarRequestDto.setReturnCityCode(cityCode);
            taskDeliverCarRequestDto.setReturnAreaCode(areaCode);
        }
    }

    @Override
    public void em90TakeDeliverCarUnconfirmed(String message) {
        //解析消息
        VehicleDeliveryStatusChangeDTO vehicleDeliveryStatusChangeDTO = JSONUtil.jsonToObj(message, VehicleDeliveryStatusChangeDTO.class);
        log.info("vehicleDeliveryStatusChangeDTO :{}", vehicleDeliveryStatusChangeDTO);
        if (vehicleDeliveryStatusChangeDTO == null) {
            throw new ServiceBizException("消息体转换失败");
        }
        if (vehicleDeliveryStatusChangeDTO.getOrderStatus() == null) {
            throw new ServiceBizException("状态不能为空");
        }
        if (vehicleDeliveryStatusChangeDTO.getAppointId() == null) {
            throw new ServiceBizException("id不为空");
        }
        // 只用监听下单状态
        if (Objects.equals(vehicleDeliveryStatusChangeDTO.getOrderStatus(), "")) {

        }
        //通过订单号查询订单详情 入参
        RequestDto dataDto = new RequestDto();
        DataDto data = new DataDto();
        data.setId(vehicleDeliveryStatusChangeDTO.getAppointId());
        dataDto.setData(data);
        log.info("DataDto data通过订单号查询订单详情:{}", data);
        MidResponse<List<VehicleDeliverDTO>> listDmsResponse = dominAutoFeign.allDetail(dataDto);
        log.info("listDmsResponse:{}", listDmsResponse);
        if (listDmsResponse.isFail()) {
            log.info("listDmsResponse 通过订单号查询订单详情异常");
            return;
        }
        if (CollectionUtils.isEmpty(listDmsResponse.getData())) {
            log.info("listDmsResponse.getData isEmpty");
            return;
        }
        VehicleDeliverDTO vehicleDeliverDTO = listDmsResponse.getData().get(0);
        //获取系统配置
        DmsResponse<CommonConfigDto> configResult = dmscloudServiceFeign.getConfigByKey(CommonConstant.EM90_DELAY_TIME, CommonConstant.EM90_GROUP_TYPE);
        log.info("configResult response:{}", JSONObject.toJSONString(configResult));
        if (configResult.isFail()) {
            throw new ServiceBizException("获取系统配置失败:{}", JSONObject.toJSONString(configResult));
        }
        Map<String, String> map = new HashMap<>();
        map.put("vin", vehicleDeliverDTO.getVehicleDeliverVO().getVin());
        map.put("orderCode", vehicleDeliverDTO.getVehicleDeliverVO().getOrderCode());
        map.put("dealerCode", vehicleDeliverDTO.getVehicleDeliverVO().getDealerCode());
        map.put("carNo", vehicleDeliverDTO.getVehicleDeliverVO().getCarNo());
        log.info("em90TakeDeliverCarUnconfirmed push:{}", JSONObject.toJSONString(map));


    }

    @Override
    public void em90TakeDeliverCarUnconfirmedPush(String orderId) {
        log.info("em90TakeDeliverCarUnconfirmedPush begin");
        if (org.apache.commons.lang.StringUtils.isBlank(orderId)) {
            log.info("em90TakeDeliverCarUnconfirmedPush orderId isBlank");
            return;
        }

        // 通过订单号查询订单详情 入参
        RequestDto dataDto = new RequestDto();
        DataDto data = new DataDto();
        data.setId(Integer.parseInt(orderId));
        dataDto.setData(data);
        log.info("em90TakeDeliverCarUnconfirmedPush allDetail:{}", data);
        MidResponse<List<VehicleDeliverDTO>> listDmsResponse = dominAutoFeign.allDetail(dataDto);
        log.info("em90TakeDeliverCarUnconfirmedPush listDmsResponse:{}", listDmsResponse);
        if (null == listDmsResponse || listDmsResponse.isFail()) {
            log.info("em90TakeDeliverCarUnconfirmedPush listDmsResponse isNull or listDmsResponse isFail");
            return;
        }
        if (CollectionUtils.isEmpty(listDmsResponse.getData())) {
            log.info("em90TakeDeliverCarUnconfirmedPush listDmsResponse.getData isEmpty");
            return;
        }
        List<VehicleDeliverDTO> dtos = listDmsResponse.getData();
        VehicleDeliverDTO dto = dtos.get(0);
        Objects.requireNonNull(dto, "em90TakeDeliverCarUnconfirmedPush dto isNull");
        VehicleDeliverVO vehicleDeliverVO = dto.getVehicleDeliverVO();
        Objects.requireNonNull(vehicleDeliverVO, "em90TakeDeliverCarUnconfirmedPush vehicleDeliverVO isNull");
        String orderStatus = vehicleDeliverVO.getOrderStatus();
        if (!Objects.equals(orderStatus, CommonConstant.EM90_82721001)) {
            log.info("em90TakeDeliverCarUnconfirmedPush orderStatus ne 82721001");
            return;
        }
        String dealerCode = vehicleDeliverVO.getDealerCode();
        String carNo = vehicleDeliverVO.getCarNo();
        String orderCode = vehicleDeliverVO.getId();
        log.info("em90TakeDeliverCarUnconfirmedPush dealerCode:{},carNo:{},orderCode:{}", dealerCode, carNo, orderCode);
        if (org.apache.commons.lang.StringUtils.isBlank(dealerCode) ||
                org.apache.commons.lang.StringUtils.isBlank(carNo) ||
                org.apache.commons.lang.StringUtils.isBlank(orderCode)
        ) {
            log.info("em90TakeDeliverCarUnconfirmedPush dealerCode or carNo or orderCode isBlank");
            return;
        }

        //获取系统配置
        DmsResponse<CommonConfigDto> configResult = dmscloudServiceFeign.getConfigByKey(CommonConstant.EM90_TAKE_DELIVER_CAT_CONFIG_KEY, CommonConstant.EM90_GROUP_TYPE);
        log.info("em90TakeDeliverCarUnconfirmedPush response:{}", configResult);
        if (null == configResult || configResult.isFail() || null == configResult.getData()) {
            throw new ServiceBizException("获取系统配置失败:{}", JSONObject.toJSONString(configResult));
        }
        String value = configResult.getData().getConfigValue();
        log.info("em90TakeDeliverCarUnconfirmedPush value:{}", value);
        if (org.apache.commons.lang.StringUtils.isBlank(value)) {
            log.info("em90TakeDeliverCarUnconfirmedPush value isBlank");
            return ;
        }

        // 发送消息
        MessageSendDto messageSendDto = new MessageSendDto();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("licenseNo", carNo);
        paramMap.put("orderCode", orderCode);
        paramMap.put("dealerCode", dealerCode);
        messageSendDto.setParamMap(paramMap);
        messageSendDto.setMobiles(value);
        messageSendDto.setTemplateId(CommonConstant.EM90_NOTE_TEMPLATE_ID);
        log.info("em90TakeDeliverCarUnconfirmedPush messageSendDto:{}", messageSendDto);
        PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
        pushMessageRecordVo.setSinceType(CommonConstant.PUSH_MESSAGE_SINCE_TYPE);
        pushMessageRecordVo.setBizNo(CommonConstant.EM90_VEHICLE_DELIVER_UNCONFIRMED_REMINDER);
        pushMessageRecordVo.setSubBizNo(dealerCode + "/" + orderCode + "/unconfirmed");
        commonMethodService.pushSms(CommonConstant.EM90_VEHICLE_DELIVER_UNCONFIRMED_REMINDER, pushMessageRecordVo, messageSendDto);

        log.info("em90TakeDeliverCarUnconfirmedPush end");
    }

    @Override
    public Integer updateVehicleDeliver(TaskDeliverCarRequestDto taskDeliverCarRequestDto) {
        log.info("updateVehicleDeliver begin:{}",JSONObject.toJSONString(taskDeliverCarRequestDto));
        RequestDto<TaskDeliverCarRequestDto> requestDtoRequestDto = getTaskDeliverCarRequestDtoRequestDto(taskDeliverCarRequestDto);

        DmsResponse<Boolean> booleanDmsResponse = dmscloudServiceFeign.customerInterAspect(taskDeliverCarRequestDto.getDealerCode(), 91111032, 0);
        if (booleanDmsResponse.isFail()) {
            throw new ServiceBizException("获取白名单异常:"+ booleanDmsResponse.getReturnMessage());
        }
        Boolean data = booleanDmsResponse.getData();
        if (ObjectUtils.isEmpty(data)){
            throw new ServiceBizException("获取白名单异常");
        }
        MidResponse<Integer> taskDeliverCarResponseDtoMidResponse = null;
        if (data){
            taskDeliverCarResponseDtoMidResponse = dominAutoFeign.updateVehicleDeliver(requestDtoRequestDto);
            log.info("updateVehicleDeliver end response:{}", JSONObject.toJSONString(taskDeliverCarResponseDtoMidResponse));
            if (taskDeliverCarResponseDtoMidResponse.isFail()) {
                throw new ServiceBizException("订单中心异常:" + taskDeliverCarResponseDtoMidResponse.getReturnMessage());
            }
        }else {
            taskDeliverCarResponseDtoMidResponse = midEndOrderCenterFeign.updateVehicleDeliver(requestDtoRequestDto);
            log.info("updateVehicleDeliver end response:{}", JSONObject.toJSONString(taskDeliverCarResponseDtoMidResponse));
            if (taskDeliverCarResponseDtoMidResponse.isFail()) {
                throw new ServiceBizException("订单中心异常:" + taskDeliverCarResponseDtoMidResponse.getReturnMessage());
            }
        }
        return taskDeliverCarResponseDtoMidResponse.getData();
    }

    public void flattenMultiLevelList(Map<String, RegionAllDto> flattenedMap, List<RegionAllDto> multiLevelList) {

        for (RegionAllDto region : multiLevelList) {
            if (Objects.nonNull(region)&&StringUtils.isNotEmpty(region.getRegionCode())) {
                // 处理当前 Region 对象
                flattenedMap.put(region.getRegionCode(), region);
                if (!CollectionUtils.isEmpty(region.getTcRegionList())) {
                    // 递归处理子地区列表
                    flattenMultiLevelList(flattenedMap, region.getTcRegionList());
                }
            }
        }

    }

    /**
     * 取送车专员 短信通知
     */
    public void  sendQSCZY(String reservationNo,EmpByRoleCodeDto empByRoleCodeDto,VehicleDeliverDTO vehicleDeliverDTO,Integer orderStatus){
        try {
            log.info("sendQSCZY reservationNo:{}, EmpByRoleCodeDto:{},VehicleDeliverDTO:{}", reservationNo, empByRoleCodeDto, vehicleDeliverDTO);
            VehicleDeliverVO vehicleDeliverVO = vehicleDeliverDTO.getVehicleDeliverVO();
            Calendar calendar = Calendar.getInstance();
            MessageSendDto messageSendDto = new MessageSendDto();
            messageSendDto.setMobiles(empByRoleCodeDto.getPhone());
            if (!StringUtils.isEmpty(reservationNo)) {
                messageSendDto.setTemplateId(queryTemplateId(CommonConstant.EM90_TEMPLATEID_QSCZY_1));
                messageSendDto.setParamMap(new HashMap<String, String>() {{
                    //车主
                    put("ownerName", StringUtils.isEmpty(vehicleDeliverVO.getUsername()) ? " " : vehicleDeliverVO.getUsername());
                    //车牌
                    put("licenseNo", StringUtils.isEmpty(vehicleDeliverVO.getCarNo()) ? " " : vehicleDeliverVO.getCarNo());
                    if (vehicleDeliverVO.getBookingTime() == null) {
                        //预约月份
                        put("month", "0");
                        //预约日
                        put("day", "0");
                        //预约时
                        put("hour", "0");
                    } else {
                        //预约月份
                        put("month", calendar.get(Calendar.MONTH) + 1 + "");
                        //预约日
                        put("day", calendar.get(Calendar.DATE) + "");
                        //预约时
                        String minutePart = calendar.get(Calendar.MINUTE) == 0 ? "" : calendar.get(Calendar.MINUTE) < 10 ? ":0" + calendar.get(Calendar.MINUTE) : ":" + calendar.get(Calendar.MINUTE);
                        put("hour", calendar.get(Calendar.HOUR_OF_DAY) + minutePart);
                    }
                    //车主手机号
                    put("ownerPhone", StringUtils.isEmpty(vehicleDeliverVO.getMobile()) ? " " : vehicleDeliverVO.getMobile());
                    //上门取车联系人
                    put("contactName", StringUtils.isEmpty(vehicleDeliverVO.getPickupContactName()) ? " " : vehicleDeliverVO.getPickupContactName());
                    //上门取车联系电话
                    put("contactPhone", StringUtils.isEmpty(vehicleDeliverVO.getPickupContactPhone()) ? " " : vehicleDeliverVO.getPickupContactPhone());
                    //订单
                    put("orderCode", StringUtils.isEmpty(vehicleDeliverVO.getId()) ? " " : vehicleDeliverVO.getId());
                    //经销商COde
                    put("dealerCode", StringUtils.isEmpty(vehicleDeliverVO.getDealerCode()) ? " " : vehicleDeliverVO.getDealerCode());
                }});
            } else {
                messageSendDto.setTemplateId(queryTemplateId(CommonConstant.EM90_TEMPLATEID_QSCZY_2));
                messageSendDto.setParamMap(new HashMap<String, String>() {{
                    //车主
                    put("ownerName", StringUtils.isEmpty(vehicleDeliverVO.getUsername()) ? " " : vehicleDeliverVO.getUsername());
                    //车型
                    put("modelName", StringUtils.isEmpty(vehicleDeliverVO.getCarSeriesName()) ? " " : vehicleDeliverVO.getCarSeriesName());
                    if (vehicleDeliverVO.getBookingTime() == null) {
                        put("bookingTime", " ");
                    } else {
                        //booktime
                        put("bookingTime", vehicleDeliverVO.getBookingTime());
                    }
                    //车主手机号
                    put("ownerPhone", StringUtils.isEmpty(vehicleDeliverVO.getMobile()) ? " " : vehicleDeliverVO.getMobile());
                    //上门取车联系人
                    put("contactName", StringUtils.isEmpty(vehicleDeliverVO.getPickupContactName()) ? " " : vehicleDeliverVO.getPickupContactName());
                    //上门取车联系电话
                    put("contactPhone", StringUtils.isEmpty(vehicleDeliverVO.getPickupContactPhone()) ? " " : vehicleDeliverVO.getPickupContactPhone());
                    //订单
                    put("orderCode", StringUtils.isEmpty(vehicleDeliverVO.getId()) ? " " : vehicleDeliverVO.getId());
                    //经销商COde
                    put("dealerCode", StringUtils.isEmpty(vehicleDeliverVO.getDealerCode()) ? " " : vehicleDeliverVO.getDealerCode());
                    //车牌
                    put("licenseNo", StringUtils.isEmpty(vehicleDeliverVO.getCarNo()) ? " " : vehicleDeliverVO.getCarNo());
                }});
            }
            PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
            pushMessageRecordVo.setSinceType(2);
            pushMessageRecordVo.setBizNo(CommonConstant.EM90_VEHICLE_DELIVER_CREATE_REMINDER);
            pushMessageRecordVo.setSubBizNo(vehicleDeliverDTO.getVehicleDeliverVO().getDealerCode().concat("-").concat(vehicleDeliverDTO.getVehicleDeliverVO().getId()).concat("-").concat(empByRoleCodeDto.getPhone()).concat(orderStatus.toString()));
            commonMethodService.pushSms(CommonConstant.EM90_VEHICLE_DELIVER_CREATE_REMINDER, pushMessageRecordVo, messageSendDto);
            log.info("sendQSCZY end");
        } catch (Exception e) {
            log.error("sendQSCZY 异常", e);
        }
    }

    /**
     * 服务经理 店总 短信通知
     */
    public void sendFWJL(EmpByRoleCodeDto empByRoleCodeDto,VehicleDeliverDTO vehicleDeliverDTO,Integer orderStatus){
        //Tem_0_YALBPZYT
        try {
            log.info("sendFWJL EmpByRoleCodeDto:{},VehicleDeliverDTO:{}", empByRoleCodeDto, vehicleDeliverDTO);
            VehicleDeliverVO vehicleDeliverVO = vehicleDeliverDTO.getVehicleDeliverVO();
            MessageSendDto messageSendDto = new MessageSendDto();
            messageSendDto.setMobiles(empByRoleCodeDto.getPhone());
            messageSendDto.setTemplateId(queryTemplateId(CommonConstant.EM90_TEMPLATEID_FWJL));
            HashMap<String, String> paramMap = new HashMap<>();
            paramMap.put("licenseNo", vehicleDeliverVO.getCarNo());
            paramMap.put("orderCode", vehicleDeliverVO.getId());
            paramMap.put("dealerCode", vehicleDeliverVO.getDealerCode());
            messageSendDto.setParamMap(paramMap);
            //消息通知
            PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
            pushMessageRecordVo.setSinceType(2);
            pushMessageRecordVo.setBizNo(CommonConstant.EM90_VEHICLE_DELIVER_CREATE_REMINDER);
            pushMessageRecordVo.setSubBizNo(vehicleDeliverDTO.getVehicleDeliverVO().getDealerCode() + vehicleDeliverDTO.getVehicleDeliverVO().getId().concat(empByRoleCodeDto.getPhone()).concat(orderStatus.toString()));
            commonMethodService.pushSms(CommonConstant.EM90_VEHICLE_DELIVER_CREATE_REMINDER, pushMessageRecordVo, messageSendDto);
            log.info("sendFWJL end");
        } catch (Exception e) {
            log.error("sendFWJL 异常", e);
        }
    }


    /**
     * 已下单 出发地址和到达地址不在同一地级市消息通知
     */
    public void sendPrefectureEqual(VehicleDeliverDTO vehicleDeliverDTO) {
        log.info("sendPrefectureEqual 已下单 出发地址和到达地址不在同一地级市消息通知:{}", vehicleDeliverDTO);
        if (Objects.isNull(vehicleDeliverDTO) || Objects.isNull(vehicleDeliverDTO.getVehicleDeliverVO())){
            log.info("VehicleDeliverDTO isNull");
            return;
        }
        if (StringUtils.isEmpty(vehicleDeliverDTO.getVehicleDeliverVO().getPickupCityCode()) || StringUtils.isEmpty(vehicleDeliverDTO.getVehicleDeliverVO().getReturnCityCode())) {
            log.info("sendPrefectureEqual 取送车地址 市为空");
            return;
        }
        if (vehicleDeliverDTO.getVehicleDeliverVO().getPickupCityCode().equals(vehicleDeliverDTO.getVehicleDeliverVO().getReturnCityCode())) {
            log.info("sendPrefectureEqual 取送车地址市 相同");
            return;
        }
        //获取配置电话号码
        DmsResponse<CommonConfigDto> emKey = dmscloudServiceFeign.getConfigByKey(CommonConstant.GROUP_TYPE_EM90_MODELCODE, CommonConstant.GROUP_TYPE_EM90);
        log.info("emKey 获取配置电话号码:{}", emKey);
        if (Objects.isNull(emKey) || Objects.isNull(emKey.getData()) || StringUtils.isEmpty(emKey.getData().getConfigValue())) {
            log.info("获取配置电话号码为null");
            return;
        }
        PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
        pushMessageRecordVo.setSinceType(2);
        pushMessageRecordVo.setBizNo(CommonConstant.EM90_VEHICLE_DELIVER_CONFIRM_REMINDER);
        pushMessageRecordVo.setSubBizNo(vehicleDeliverDTO.getVehicleDeliverVO().getDealerCode().concat("-")+vehicleDeliverDTO.getVehicleDeliverVO().getId().concat("-").concat(emKey.getData().getConfigValue()));
        MessageSendDto messageSendDto = new MessageSendDto();
        messageSendDto.setMobiles(emKey.getData().getConfigValue());
        messageSendDto.setTemplateId(queryTemplateId(CommonConstant.EM90_TEMPLATEID_DZBYZ));
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("licenseNo", vehicleDeliverDTO.getVehicleDeliverVO().getCarNo());
        paramMap.put("orderCode", vehicleDeliverDTO.getVehicleDeliverVO().getId());
        paramMap.put("dealerCode", vehicleDeliverDTO.getVehicleDeliverVO().getDealerCode());
        messageSendDto.setParamMap(paramMap);
        commonMethodService.pushSms(CommonConstant.EM90_VEHICLE_DELIVER_CONFIRM_REMINDER, pushMessageRecordVo, messageSendDto);

        log.info("sendPrefectureEqual end");
    }

    /**
     * 获取短信模板id
     */
    public String queryTemplateId(String key) {
        log.info("queryTemplateId key:{}", key);
        DmsResponse<CommonConfigDto> emKey = dmscloudServiceFeign.getConfigByKey(key, CommonConstant.GROUP_TYPE_EM90_TEMPLATEID);
        log.info("queryTemplateId end:{}", emKey);
        return emKey.getData().getConfigValue();
    }

    /**
     * em90 push 处理
     */
    public void ekEm90push(String key) {
        Objects.requireNonNull(key, "ekEm90push isNull");
        if (!key.contains(CommonConstant.EM90_TO_BE_CONFIRMED_PREFIX)) {
            log.info("ekEm90push EM90_TO_BE_CONFIRMED_PREFIX ne key");
            return;
        }
        int len = key.length();
        int i = key.indexOf("&");
        log.info("ekEm90push len:{},i:{}", len, i);
        if (len < 1 || i < 1) {
            log.info("ekEm90push len lt 1 or i lt 1");
            return;
        }

        try {
            String orderId = key.substring(i + 1, len);
            log.info("ekEm90push orderId:{}", orderId);
            em90TakeDeliverCarUnconfirmedPush(orderId);
        } catch (Exception e) {
            log.error("ekEm90push error:{}", e);
        }
    }

}
