package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SalesMainDetailsDto {

    /**
     * 采购单外部编号
     */
    private String sellerSalesNo;

    /**
     * 经销商外部编码
     */
    private String outCustomerNo;

    private String salesNo;

    private String outSalesNo;



    /**
     * 采购订单明细主键ID;
     */
    private Long id;

    private String purchaseOrder;

    private String ownerCode;


}
