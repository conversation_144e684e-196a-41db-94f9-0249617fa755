package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据会放到缓存,新增属性的时候注意考虑redis的性能
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AllModeDto implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 车型ID
	 */
	private String id;
	/**
	 * 车型通用名称
	 */
	private String modelName;
}
