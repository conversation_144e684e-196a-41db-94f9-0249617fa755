package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CareBuyedExtDto;
import com.volvo.maintain.application.maintainlead.dto.carebuy.SaveCareBuyedDto;
import com.volvo.maintain.application.maintainlead.dto.coupon.LoyalCouponDto;
import com.volvo.maintain.application.maintainlead.dto.coupon.QueryCouponDetailInfoDto;
import com.volvo.maintain.application.maintainlead.dto.coupon.QueryCouponDto;
import com.volvo.maintain.application.maintainlead.dto.equityInfo.*;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceBillListDto;
import com.volvo.maintain.application.maintainlead.dto.inviteClue.InviteClueParamDto;
import com.volvo.maintain.application.maintainlead.dto.inviteClue.InviteClueResultDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.*;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.constants.LevelEnum;
import com.volvo.maintain.infrastructure.constants.NbTagEnum;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import com.volvo.maintain.infrastructure.util.StringUtil;
import com.volvo.maintain.interfaces.vo.*;
import com.volvo.maintain.interfaces.vo.carebuy.CareBuyedVo;
import com.volvo.maintain.interfaces.vo.coupon.CouponDetailVO;
import com.volvo.maintain.interfaces.vo.rights.DealerCarRightsVo;
import com.volvo.utils.BeanMapperUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.*;


@Service
public class CustomizedLabelServiceImpl implements CustomizedLabelService {
    public static final String TAG_TRUE = "是";
    public static final String TAG_FALSE = "否";
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final int TWO_THOUSAND_AND_FIFTEEN = 2015;

    @Autowired
    private CouponDetailService couponDetailService;

    @Autowired
    private CdpTagInfoService cdpTagInfoService;

    @Autowired
    private TagInfoService tagInfoService;

    @Autowired
    private CommonMethodService commonMethodService;

    @Autowired
    private DomainMaintainLeadFeign maintainLeadsFegin;

    /**
     * fegin
     */
    @Autowired
    private DmscloudServiceFeign dmscloudServiceFegin;

    /**
     * fegin
     */
    @Autowired
    private DmscusCustomerFeign dmscusCustomerFeign;

    /**
     * fegin
     */
    @Autowired
    private DmscusRepairFeign dmscusRepairFeign;

    /**
     * fegin
     */
    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;

    /**
     * fegin
     */
    @Autowired
    private MIdEndMemberCenterFeign mIdEndMemberCenterFeign;

    @Qualifier("thread360Pool")
    @Autowired
    private ThreadPoolTaskExecutor thread360Pool;

    @Autowired
    MidEndDictCenterFeign midEndDictCenterFeign;

    @Autowired
    private DomainMaintainAuthFeign domainMaintainAuthFeign;

    @Autowired
    private DomainInsuranceLeadsFeign domainInsuranceLeadsFeign;

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Autowired
    private DomainMaintainLeadFeign domainMaintainLeadFeign;

    @Autowired
    private DealerCarRights dealerCarRights;
    @Autowired
    private CarRightsServiceFeign carRightsServiceFeign;
    @Autowired
    private BusinessRulesServiceFeign businessRulesServiceFeign;

    /**
     * NB定制化标签查询
     */
    @Override
    public List<NbTagInfoVo> queryCustomizedLabel(CustomizedLabelDto customizedLabelDto) {
        logger.info("NB定制化标签查询开始 入参 CustomizedLabelVO:{}", customizedLabelDto);

        String vin = customizedLabelDto.getVin();
        String ownerCode = commonMethodService.getOwnerCode();
        String mobile = customizedLabelDto.getPhone();
        String selectType = customizedLabelDto.getSelectType();
        String licensePlate = customizedLabelDto.getLicensePlate();
        List<String> tagCodes = customizedLabelDto.getTagCodes();

        CompletableFuture<NbTagInfoVo> vipTagFuture = null;
        CompletableFuture<NbTagInfoVo> cdpTagInfoFuture  = null;
        CompletableFuture<NbTagInfoVo> fleetCodeTagFuture = null;
        CompletableFuture<NbTagInfoVo> oneIdCouponData = null;
        CompletableFuture<NbTagInfoVo> selectCareBuy = null;
        CompletableFuture<NbTagInfoVo> queryExtendedWarrantyInfo = null;
        CompletableFuture<NbTagInfoVo> selectInsureHistory = null;
        // 最近一次商业险保险起止日期
        CompletableFuture<NbTagInfoVo> queryCommercialInsuranceByDate = null;
        // 最近一次交强险保险起止日期
        CompletableFuture<NbTagInfoVo> queryCompulsoryInsuranceByLastDate = null;

        CompletableFuture<List<NbTagInfoVo>>  inviteClueTagInfoVO = null;
        CompletableFuture<List<NbTagInfoVo>>  vinValidCoupon = null;
        CompletableFuture<List<NbTagInfoVo>>  delValidCoupon = null;
        CompletableFuture<List<NbTagInfoVo>>  dealerRights = null;
        CompletableFuture<NbTagInfoVo> queryNonCarInsuranceInfo = null;
        // 质保”/“道路救援”/“二手车”/“保险、终身保养 权益信息
        CompletableFuture<NbTagInfoVo> query5n1Info = null;
        TmVehicleDto tmVehicleDTO = new TmVehicleDto();
        if (tagCodes.contains(NbTagEnum.TAG001.getCode()) || tagCodes.contains(NbTagEnum.TAG007.getCode())  || tagCodes.contains(NbTagEnum.TAG013.getCode()) || tagCodes.contains(NbTagEnum.TAG014.getCode()) ) {
            tmVehicleDTO = queryVehicle(vin);
            logger.info("NB定制化标签查询开始 获取车辆信息 TmVehicleDto:{}", tmVehicleDTO);
        }

        TmVehicleDto finalTmVehicleDTO = tmVehicleDTO;
        //是否VIP
        if (tagCodes.contains(NbTagEnum.TAG001.getCode())) {
            logger.info("NB定制化标签 是否VIP 查询开始 ");
            vipTagFuture = CompletableFuture.supplyAsync(() -> checkVip(licensePlate, finalTmVehicleDTO), thread360Pool);
            logger.info("NB定制化标签 是否VIP 查询结束 vipTagFuture:{}", vipTagFuture);
        }

        if (tagCodes.contains(NbTagEnum.TAG002.getCode()) && StringUtils.isNotEmpty(mobile) && Objects.equals(selectType, "0")) {
            //客户-姓名
            logger.info("NB定制化标签 客户-姓名 查询开始");
            cdpTagInfoFuture = CompletableFuture.supplyAsync(() -> getCdpTagInfoVOS(mobile), thread360Pool);
            logger.info("NB定制化标签  客户-姓名 查询结束 cdpTagInfoFuture:{}", cdpTagInfoFuture);
        }

        if (tagCodes.contains(NbTagEnum.TAG015.getCode())) {
            //是否大客户
            fleetCodeTagFuture = CompletableFuture.supplyAsync(() -> checkFleetCode(vin), thread360Pool);
        }

        if (tagCodes.contains(NbTagEnum.TAG013.getCode()) || tagCodes.contains(NbTagEnum.TAG014.getCode()) ) {
            //线索
            inviteClueTagInfoVO = CompletableFuture.supplyAsync(() -> selectInviteClueTag(vin, tagCodes, finalTmVehicleDTO), thread360Pool);
        }
        if (tagCodes.contains(NbTagEnum.TAG004.getCode())) {
            oneIdCouponData = CompletableFuture.supplyAsync(() -> oneIdCouponData(customizedLabelDto, mobile), thread360Pool);
        }
        if (tagCodes.contains(NbTagEnum.TAG008.getCode()) ) {
            vinValidCoupon = CompletableFuture.supplyAsync(() -> vinValidCoupon(vin , ownerCode, tagCodes), thread360Pool);
        }else if (tagCodes.contains(NbTagEnum.TAG009.getCode()) || tagCodes.contains(NbTagEnum.TAG010.getCode()) || tagCodes.contains(NbTagEnum.TAG011.getCode()) || tagCodes.contains(NbTagEnum.TAG012.getCode())) {
            vinValidCoupon = CompletableFuture.supplyAsync(() -> validCoupon(vin, tagCodes), thread360Pool);
        }

        if (tagCodes.contains(NbTagEnum.TAG017.getCode())) {
            //保养套餐
            selectCareBuy = CompletableFuture.supplyAsync(() -> selectCareBuy(vin), thread360Pool);
        }

        if (tagCodes.contains(NbTagEnum.TAG018.getCode())) {
            //查询有效延保购买记录
            queryExtendedWarrantyInfo =  CompletableFuture.supplyAsync(() -> queryExtendedWarrantyInfo(vin), thread360Pool);
        }
        if (tagCodes.contains(NbTagEnum.TAG027.getCode())) {
            //查询非车险
            queryNonCarInsuranceInfo =  CompletableFuture.supplyAsync(() -> queryNonCarInsuranceInfo(vin, ownerCode), thread360Pool);
        }
        if (tagCodes.contains(NbTagEnum.TAG019.getCode())) {
            //保险起止时间
            selectInsureHistory = CompletableFuture.supplyAsync(() -> selectInsureHistory(vin, customizedLabelDto.getSelectType()), thread360Pool);
        }

        if (tagCodes.contains(NbTagEnum.TAG020.getCode()) || tagCodes.contains(NbTagEnum.TAG021.getCode())) {
            // 最近一次商业险保险起止日期
            queryCommercialInsuranceByDate = CompletableFuture.supplyAsync(() -> queryCommercialInsuranceByDateHistory(vin, customizedLabelDto.getSelectType()), thread360Pool);
        }
        if (tagCodes.contains(NbTagEnum.TAG021.getCode())) {
            // 最近一次交强险保险起止日期
            queryCompulsoryInsuranceByLastDate = CompletableFuture.supplyAsync(() -> compulsoryInsuranceDetailHistory(vin, customizedLabelDto.getSelectType()), thread360Pool);
        }
        if (tagCodes.contains(NbTagEnum.TAG022.getCode()) || tagCodes.contains(NbTagEnum.TAG023.getCode()) || tagCodes.contains(NbTagEnum.TAG024.getCode()) || tagCodes.contains(NbTagEnum.TAG025.getCode())) {
            // 优惠减免卡券数量  优惠打折卡券数量
            delValidCoupon = CompletableFuture.supplyAsync(() -> delValidCoupon(vin, ownerCode, tagCodes), thread360Pool);
        }
        if (tagCodes.contains(NbTagEnum.TAG026.getCode())) {
            //经销商权益
            dealerRights = CompletableFuture.supplyAsync(() -> dealerRights(vin, ownerCode), thread360Pool);
        }
        if (tagCodes.contains(NbTagEnum.TAG028.getCode())) {
            // 查询质保、保险、道路救援、二手车权益信息、终身保养权益
            query5n1Info =  CompletableFuture.supplyAsync(() -> query5n1Info(vin), thread360Pool);
        }


        List<CompletableFuture<NbTagInfoVo>> objects = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(vipTagFuture)){
            objects.add(vipTagFuture);
        }
        if(ObjectUtils.isNotEmpty(cdpTagInfoFuture)){
            objects.add(cdpTagInfoFuture);
        }
        if(ObjectUtils.isNotEmpty(fleetCodeTagFuture)){
            objects.add(fleetCodeTagFuture);
        }
        if(ObjectUtils.isNotEmpty(oneIdCouponData)){
            objects.add(oneIdCouponData);
        }
        if(ObjectUtils.isNotEmpty(selectCareBuy)){
            objects.add(selectCareBuy);
        }
        if(ObjectUtils.isNotEmpty(queryExtendedWarrantyInfo)){
            objects.add(queryExtendedWarrantyInfo);
        }
        if(ObjectUtils.isNotEmpty(selectInsureHistory)){
            objects.add(selectInsureHistory);
        }
        if(ObjectUtils.isNotEmpty(queryCommercialInsuranceByDate)){
            objects.add(queryCommercialInsuranceByDate);
        }
        if(ObjectUtils.isNotEmpty(queryCompulsoryInsuranceByLastDate)){
            objects.add(queryCompulsoryInsuranceByLastDate);
        }
        if(ObjectUtils.isNotEmpty(queryNonCarInsuranceInfo)){
            objects.add(queryNonCarInsuranceInfo);
        }
        if(ObjectUtils.isNotEmpty(query5n1Info)){
            objects.add(query5n1Info);
        }
        // 从Future中提取结果
        List<NbTagInfoVo> tagInfoVOS = new ArrayList<>();

        if (tagCodes.contains(NbTagEnum.TAG006.getCode())) {
            NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
            nbTagInfoVO.setTagId(NbTagEnum.TAG006.getCode());
            nbTagInfoVO.setTagValue(vin);
            tagInfoVOS.add(nbTagInfoVO);
        }

        if (tagCodes.contains(NbTagEnum.TAG007.getCode())) {
            tagInfoVOS.add(queryLicensePalate(tmVehicleDTO));
        }

        try {
            if (!CollectionUtils.isEmpty(objects)){
                CompletableFuture[] completableFutures = new CompletableFuture[objects.size()];

                for (int i = 0; i < objects.size(); i++) {
                    completableFutures[i] = objects.get(i);
                }
                // 等待所有的future完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                        completableFutures
                );

                allFutures.get(60, TimeUnit.SECONDS);
                // 将CompletableFutures的结果添加到最终列表
                tagInfoVOS.addAll(objects.stream()
                        .map(CompletableFuture::join) // 获取每个 future 的结果
                        .collect(Collectors.toList())); // 收集结果
            }

            // 添加inviteClueTagInfoVO查询结果到最终列表
            if(ObjectUtils.isNotEmpty(inviteClueTagInfoVO)) {
                tagInfoVOS.addAll(inviteClueTagInfoVO.get());
            }

            if(ObjectUtils.isNotEmpty(vinValidCoupon)) {
                List<NbTagInfoVo> nbTagInfoVos = vinValidCoupon.get();
                couponSort(nbTagInfoVos);
                tagInfoVOS.addAll(nbTagInfoVos);
            }
            if(ObjectUtils.isNotEmpty(delValidCoupon)) {
                List<NbTagInfoVo> nbTagInfoVos = delValidCoupon.get();
                couponSort(nbTagInfoVos);
                tagInfoVOS.addAll(nbTagInfoVos);
            }

            if(ObjectUtils.isNotEmpty(dealerRights)) {
                tagInfoVOS.addAll(dealerRights.get());
            }
        } catch (InterruptedException | ExecutionException  | TimeoutException e) {
            logger.info("queryCustomizedLabel->CompletableFutureError", e);
            e.printStackTrace();
        }
        logger.info("NB定制化标签查询结束 tagInfoVOS：{}", tagInfoVOS);

        this.query8n1Info(tagInfoVOS);
        return tagInfoVOS;
    }

    public List<NbTagInfoVo> dealerRights(String vin, String ownerCode) {
        logger.info("delValidCoupon start...");
        List<NbTagInfoVo> tagInfoVOList = new ArrayList<>();

        List<DealerCarRightsVo> dealerCarRightsVos = dealerCarRights.dealerRightsList(vin, ownerCode);
        if(!CollectionUtils.isEmpty(dealerCarRightsVos)){
            //按照 expirationDate排序
            Collections.sort(dealerCarRightsVos, Comparator.comparing(DealerCarRightsVo::getExpirationDate,
                        Comparator.nullsFirst(Comparator.naturalOrder())));
            NbTagInfoVo tagInfoVo = new NbTagInfoVo();
            tagInfoVo.setTagId(NbTagEnum.TAG026.getCode());
            tagInfoVo.setTagValue(dealerCarRightsVos.size()+"");
            tagInfoVo.setDealerCarRightsVos(dealerCarRightsVos);
            tagInfoVOList.add(tagInfoVo);
            processUserResponse(tagInfoVo);
        }
        return tagInfoVOList;
    }

    public void processUserResponse(NbTagInfoVo nbTagInfoVO){
        //原厂券 / 经销商券
        /*if(Objects.nonNull(nbTagInfoVO) && org.apache.commons.collections.CollectionUtils.isNotEmpty(nbTagInfoVO.getCouponDetailVO())){
            processCoupon(nbTagInfoVO.getCouponDetailVO());
        }*/

        //经销商权益
        if(Objects.nonNull(nbTagInfoVO) && org.apache.commons.collections.CollectionUtils.isNotEmpty(nbTagInfoVO.getDealerCarRightsVos())){
            nbTagInfoVO.getDealerCarRightsVos().forEach(e -> {
                //折扣
                if(Objects.nonNull(e.getPercentageDiscount())){
                    e.setPercentageDiscountStr(e.getPercentageDiscount().divide(new BigDecimal(10),2,BigDecimal.ROUND_FLOOR).toPlainString()+"折");
                }else{
                    e.setPercentageDiscountStr("无金额");
                }
                //抵扣上线
                if(Objects.nonNull(e.getSingleDiscountMax())){
                    e.setSingleDiscountMaxStr(e.getSingleDiscountMax().setScale(2, RoundingMode.HALF_UP).toPlainString()+"元");
                }else{
                    e.setSingleDiscountMaxStr("无上限");
                }
            });
        }
    }

    public static void processCoupon(List<CouponDetailVO> couponDetailVOs) {
        //判断卡券类型
        couponDetailVOs.forEach(e -> {
            //卡券类型(31081004 储值卡 31081005 优惠券 31081006 兑换券
            switch (e.getCouponType()){
                case "31081004":
                    if(Objects.nonNull(e.getLeftValue())){
                        e.setCouponValueStr(BigDecimal.valueOf(e.getLeftValue()).divide(new BigDecimal("100"),2, RoundingMode.HALF_UP).toPlainString()+"元");
                    }
                    break;
                case "31081006":
                    if (Objects.isNull(e.getCouponValue())){
                        e.setCouponValue(CommonConstant.ZERO);
                    }
                    e.setCouponValueStr(BigDecimal.valueOf(e.getCouponValue()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP).toPlainString()+"元");
                    break;
                case "31081005":
                    //打折
                    if(Objects.equals(83311002,e.getOfferType()) && Objects.nonNull(e.getCouponDiscount())){
                        e.setCouponValueStr(BigDecimal.valueOf(e.getCouponDiscount()).divide(new BigDecimal(10),2,BigDecimal.ROUND_FLOOR).toPlainString()+"折");
                    }
                    //抵用
                    if(Objects.equals(83311001,e.getOfferType()) && Objects.nonNull(e.getCouponValue())){
                        e.setCouponValueStr(BigDecimal.valueOf(e.getCouponValue()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP).toPlainString()+"元");
                    }
                    break;
            }
        });
    }

    private List<NbTagInfoVo> delValidCoupon(String vin, String ownerCode, List<String> tagCodes) {
        logger.info("delValidCoupon start...");
        List<NbTagInfoVo> tagInfoVOList = new ArrayList<>();

        //卡券数量
        List<CouponDetailVO> couponDetailInfoVOS = delCoupon(vin,ownerCode);

        NbTagEnum.queryNbTagEnum22_23_24_25().forEach(e->{
            if (tagCodes.contains(e.getCode())){
                getDelCoupon(tagInfoVOList, couponDetailInfoVOS,e);
            }
        });

        return tagInfoVOList;
    }

    private List<CouponDetailVO> delCoupon(String vin, String ownerCode) {
        logger.info("delCoupon start...");

        QueryCouponDto queryCouponDto = QueryCouponDto.builder()
                .vinList(Collections.singletonList(vin))
                .useScenesList(Collections.singletonList(83171012))
                .ticketStateList(Collections.singletonList(31061001)).build();

        if (StringUtils.isNotEmpty(ownerCode)){
            queryCouponDto.setLimitDealer(Collections.singletonList(ownerCode));
        }
        List<CouponDetailVO> couponDetailVOS = couponDetailService.allCouponAfter(queryCouponDto);
        //卡券数量
        return couponDetailVOS;
    }

    private NbTagInfoVo queryCommercialInsuranceByDateHistory(String vin, String selectType) {
        logger.info("queryCommercialInsuranceByDateHistory start...selectType={}", selectType);
        NbTagInfoVo vo = new NbTagInfoVo();
        vo.setTagId(NbTagEnum.TAG020.getCode());
        //1-后端由其他接口调用 0-前端直接调用
        if ("1".equals(selectType)) {
            logger.info("newbie customization tag commercial insurance last time detail->vin:{}",vin);
            DmsResponse<InsuranceBillVo> insuranceBillVODmsResponse = dmscusRepairFeign.queryInsuranceDateByVin(vin);
            logger.info("newbie customization tag commercial insurance last time detail response data:{}",JSONObject.toJSONString(insuranceBillVODmsResponse));
            if (insuranceBillVODmsResponse.isFail() || ObjectUtils.isEmpty(insuranceBillVODmsResponse.getData())) {
                return vo;
            }
            vo.setTagValue(insuranceBillVODmsResponse.getData().getInsuranceEnd());
        } else {
            logger.info("newbie customization tag commercial insurance history->vin:{}",vin);
            DmsResponse<List<InsuranceBillListDto>> insuranceBillListResponse = domainInsuranceLeadsFeign.selectInsureHistory(vin);
            logger.info("newbie customization tag commercial insurance history->insuranceBillListResponse:{}", JSONObject.toJSONString(insuranceBillListResponse));
            if (insuranceBillListResponse.isFail() || CollectionUtils.isEmpty(insuranceBillListResponse.getData())) {
                return vo;
            }
            // 最近一次商业险保险起止日期 历史记录
            vo.setInsuranceBillList(insuranceBillListResponse.getData());
        }
        logger.info("newbie customization tag commercial insurance history  end...");
        return vo;
    }

    private NbTagInfoVo compulsoryInsuranceDetailHistory(String vin, String selectType) {
        logger.info("compulsoryInsuranceDetailHistory start...selectType={}", selectType);
        NbTagInfoVo vo = new NbTagInfoVo();
        vo.setTagId(NbTagEnum.TAG021.getCode());
        //1-后端由其他接口调用 0-前端直接调用
        if ("1".equals(selectType)) {
            logger.info("newbie customization tag compulsory insurance last time detail->vin:{}",vin);
            DmsResponse<InsuranceBillVo> compulsoryInsuranceDetailResponse = domainInsuranceLeadsFeign.queryClivtaInsuranceByVin(vin);
            logger.info("newbie customization tag compulsory insurance last time detail response data:{}",JSONObject.toJSONString(compulsoryInsuranceDetailResponse));
            if (compulsoryInsuranceDetailResponse.isFail() || ObjectUtils.isEmpty(compulsoryInsuranceDetailResponse.getData())) {
                return vo;
            }
            vo.setTagValue(compulsoryInsuranceDetailResponse.getData().getClivtaDateEnd());
        } else {
            logger.info("newbie customization tag compulsory insurance detail history->vin:{}",vin);
            DmsResponse<List<InsuranceBillListDto>> compulsoryInsuranceListResponse = domainInsuranceLeadsFeign.selectInsureHistory(vin);
            logger.info("newbie customization tag compulsory insurance detail history->insuranceBillListResponse:{}", JSONObject.toJSONString(compulsoryInsuranceListResponse));
            if (compulsoryInsuranceListResponse.isFail() || CollectionUtils.isEmpty(compulsoryInsuranceListResponse.getData())) {
                return vo;
            }
            // 最近一次商业险保险起止日期 历史记录
            vo.setInsuranceBillList(compulsoryInsuranceListResponse.getData());
        }
        logger.info("newbie customization tag  queryCommercialInsuranceByDateHistory  end...");
        return vo;
    }

    private NbTagInfoVo queryLicensePalate(TmVehicleDto tmVehicleDTO) {
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG007.getCode());

        if (ObjectUtils.isEmpty(tmVehicleDTO)){
            return nbTagInfoVO;
        }

        nbTagInfoVO.setTagValue(tmVehicleDTO.getPlateNumber());
        return nbTagInfoVO;
    }

    private List<NbTagInfoVo> validCoupon(String vin, List<String> tagCodes) {
        logger.info("validCoupon start...");
        List<NbTagInfoVo> tagInfoVOList = new ArrayList<>();

        QueryCouponDetailInfoDto queryCouponDetailInfoDTO = new QueryCouponDetailInfoDto();
        queryCouponDetailInfoDTO.setVin(vin);
        queryCouponDetailInfoDTO.setValid(1);
        queryCouponDetailInfoDTO.setTicketState(31061001);

        List<Long> couponIds = new ArrayList<>();

        if (tagCodes.contains(NbTagEnum.TAG009.getCode())) {

            queryLoyalCoupon(tagInfoVOList, couponIds);
        }

        NbTagEnum.queryNbTagEnum10_11_12().forEach(e->{
            if (tagCodes.contains(e.getCode())){
                logger.info("NB定制化标签 卡券ID tagCodes：{}",e.getCode());
                NbTagInfoVo nbTagInfoVo10_11_12 = new NbTagInfoVo();
                nbTagInfoVo10_11_12.setTagId(e.getCode());
                List<Long> couponIds1 = getCouponIds(e.getCode());
                if (couponIds1 != null) {
                    nbTagInfoVo10_11_12.setTagValue(couponIds1.toString());
                    couponIds.addAll(couponIds1);
                }
                tagInfoVOList.add(nbTagInfoVo10_11_12);
            }
        });

        if (CollectionUtils.isEmpty(couponIds)) {
            return tagInfoVOList;
        }

        //根据卡券ID去查询卡券
        queryCouponDetailInfoDTO.setCouponIds(couponIds);
        List<CouponDetailVO> couponDetailVOS = couponDetailService.queryCouponInfo(queryCouponDetailInfoDTO);
        logger.info("NB定制化标签 卡券信息 couponDetailVOS：{}",couponDetailVOS);

        if (CollectionUtils.isEmpty(couponDetailVOS)) {
            return tagInfoVOList;
        }

        NbTagEnum.queryNbTagEnum9_10_11_12().forEach(e->{
            if (tagCodes.contains(e.getCode())){
                validCouponSize(tagInfoVOList, couponDetailVOS,e.getCode());
            }
        });

        logger.info("validCoupon end... NB定制化标签 卡券信息 tagInfoVOList：{}",tagInfoVOList);
        return tagInfoVOList;
    }

    private void queryLoyalCoupon(List<NbTagInfoVo> tagInfoVOList, List<Long> couponIds) {
        NbTagInfoVo nbTagInfoVo09 = new NbTagInfoVo();
        nbTagInfoVo09.setTagId(NbTagEnum.TAG009.getCode());
        //忠诚守候券
        DmsResponse<List<LoyalCouponDto>> listDmsResponse1 = dmscloudServiceFegin.selectAllLoyalCoupon();
        logger.info("NB定制化标签 忠诚守候券 listDmsResponse1：{}",listDmsResponse1);
        if (listDmsResponse1.isFail()) {
            return;
        }

        if (CollectionUtils.isEmpty(listDmsResponse1.getData())) {
            return;
        }

        List<LoyalCouponDto> data = listDmsResponse1.getData();
        logger.info("NB定制化标签 忠诚守候券 listDmsResponse1：{}",listDmsResponse1);


        List<Long> configList009 = data.stream()
                .map(LoyalCouponDto::getCouponId)
                .collect(Collectors.toList());
        nbTagInfoVo09.setTagValue(configList009.toString());
        tagInfoVOList.add(nbTagInfoVo09);
        couponIds.addAll(configList009);
    }

    private void validCouponSize(List<NbTagInfoVo> tagInfoVOList, List<CouponDetailVO> couponDetailVOS, String targetTagId) {
        logger.info("NB定制化标签 获取对应卡券信息 targetTagId：{}",targetTagId);
        //根据标签Code 从标签集合中获取对应标签对象
        Optional<NbTagInfoVo> nbTagInfoVoOptional = tagInfoVOList.stream()
                .filter(tagInfoVO -> tagInfoVO.getTagId().equals(targetTagId))
                .findFirst();

        logger.info("NB定制化标签 获取对应卡券信息 nbTagInfoVoOptional：{}",nbTagInfoVoOptional);

        if (!nbTagInfoVoOptional.isPresent()){
            return;
        }

        NbTagInfoVo nbTagInfoVo = nbTagInfoVoOptional.get();
        logger.info("NB定制化标签 获取对应卡券信息 nbTagInfoVo：{}",nbTagInfoVo);

        //将标签对象中存的couponIds取出
        List<Long> couponIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(nbTagInfoVo.getTagValue()) ) {
            couponIdList = Arrays.stream(nbTagInfoVo.getTagValue().replace("[", " ").replace("]", " ").split(","))
                    .map(String::trim)  // 增加trim()方法
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }

        logger.info("NB定制化标签 获取对应卡券信息 couponIdList：{}",couponIdList);

        //根据couponIds从卡券列表中 取出对应卡券信息
        nbTagInfoVo.setCouponDetailVO(couponIdList.stream()
                .flatMap(couponId -> couponDetailVOS.stream()
                        .filter(coupon -> Objects.equals(coupon.getCouponId(), couponId.intValue())))
                .collect(Collectors.toList()));

        logger.info("NB定制化标签 获取对应卡券信息 nbTagInfoVo：{}",nbTagInfoVo);
        nbTagInfoVo.setTagValue(nbTagInfoVo.getCouponDetailVO().size()+"");
        processUserResponse(nbTagInfoVo);
    }

    /**
     *
     */
    private List<NbTagInfoVo> vinValidCoupon(String vin, String ownerCode, List<String> tagCodes) {
        logger.info("vinValidCoupon start...");
        List<NbTagInfoVo> tagInfoVOList = new ArrayList<>();

        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG008.getCode());

        QueryCouponDetailInfoDto queryCouponDetailInfoDTO = new QueryCouponDetailInfoDto();
        queryCouponDetailInfoDTO.setVin(vin);
        queryCouponDetailInfoDTO.setValid(1);
        queryCouponDetailInfoDTO.setTicketState(31061001);
        //卡券数量
        List<CouponDetailVO> couponDetailVOS = couponDetailService.queryCouponInfo(queryCouponDetailInfoDTO);

        Boolean b;
        if (StringUtils.isNotEmpty(ownerCode)) {
            b = commonMethodService.checkWhitelist(ownerCode, 91111043, 0, "", "");
        }else {
            b = true;
        }
        List<CouponDetailVO> delCouponDetailVOS = delCoupon(vin, ownerCode);
        if (CollectionUtils.isEmpty(couponDetailVOS)){
            if (!CollectionUtils.isEmpty(delCouponDetailVOS) && b){
                nbTagInfoVO.setTagValue(delCouponDetailVOS.size()+"");
                nbTagInfoVO.setCouponDetailVO(delCouponDetailVOS);
                processUserResponse(nbTagInfoVO);
            }
            tagInfoVOList.add(nbTagInfoVO);
            return tagInfoVOList;
        }

        nbTagInfoVO.setTagValue(couponDetailVOS.size()+"");
        nbTagInfoVO.setCouponDetailVO(couponDetailVOS);
        tagInfoVOList.add(nbTagInfoVO);

        if (tagCodes.contains(NbTagEnum.TAG009.getCode())) {
            getLoyalCoupon(tagInfoVOList, couponDetailVOS);
        }

        if (tagCodes.contains(NbTagEnum.TAG010.getCode())) {

            NbTagInfoVo nbTagInfoVo1 = new NbTagInfoVo();
            nbTagInfoVo1.setTagId(NbTagEnum.TAG010.getCode());
            //取送车卡券数
            getCouponId(tagInfoVOList, couponDetailVOS, nbTagInfoVo1,NbTagEnum.TAG010.getCode());
        }

        if (tagCodes.contains(NbTagEnum.TAG011.getCode())) {

            NbTagInfoVo nbTagInfoVo1 = new NbTagInfoVo();
            nbTagInfoVo1.setTagId(NbTagEnum.TAG011.getCode());

            //低车龄保养套餐抵扣券数量
            getCouponId(tagInfoVOList, couponDetailVOS, nbTagInfoVo1,NbTagEnum.TAG011.getCode());

        }

        if (tagCodes.contains(NbTagEnum.TAG012.getCode())) {

            NbTagInfoVo nbTagInfoVo1 = new NbTagInfoVo();
            nbTagInfoVo1.setTagId(NbTagEnum.TAG012.getCode());
            //流失客户保养抵扣券数量
            getCouponId(tagInfoVOList, couponDetailVOS, nbTagInfoVo1,NbTagEnum.TAG012.getCode());
        }
        logger.info("vinValidCoupon end...");

        if (!CollectionUtils.isEmpty(delCouponDetailVOS) && b){
            nbTagInfoVO.setTagValue(delCouponDetailVOS.size()+couponDetailVOS.size()+"");
            couponDetailVOS.addAll(delCouponDetailVOS);
            nbTagInfoVO.setCouponDetailVO(couponDetailVOS);
            processUserResponse(nbTagInfoVO);
        }

        return tagInfoVOList;
    }

    private void couponSort(List<NbTagInfoVo> nbTagInfoVOs){
        if(!CollectionUtils.isEmpty(nbTagInfoVOs)){
            nbTagInfoVOs.forEach(nbTagInfoVO -> {
                List<CouponDetailVO> couponDetailVO = nbTagInfoVO.getCouponDetailVO();
                if(!CollectionUtils.isEmpty(couponDetailVO)){
                    couponDetailVO.sort(Comparator.comparing(e -> DateUtil.parseDateTime(e.getExpirationDate()),Comparator.nullsFirst(Comparator.naturalOrder())));
                }
            });
        }
    }

    private void getLoyalCoupon(List<NbTagInfoVo> tagInfoVOList, List<CouponDetailVO> couponDetailVOS) {
        NbTagInfoVo nbTagInfoVo1 = new NbTagInfoVo();
        nbTagInfoVo1.setTagId(NbTagEnum.TAG009.getCode());

        //忠诚守候券
        DmsResponse<List<LoyalCouponDto>> listDmsResponse1 = dmscloudServiceFegin.selectAllLoyalCoupon();
        logger.info("NB定制化标签 忠诚守候券 listDmsResponse1：{}",listDmsResponse1);
        if (listDmsResponse1.isFail()) {
            tagInfoVOList.add(nbTagInfoVo1);
            return;
        }

        if (CollectionUtils.isEmpty(listDmsResponse1.getData())) {
            tagInfoVOList.add(nbTagInfoVo1);
            return;
        }

        List<LoyalCouponDto> data = listDmsResponse1.getData();

        List<CouponDetailVO> filteredList = data.stream()
                .map(LoyalCouponDto::getCouponId)
                .flatMap(couponId -> couponDetailVOS.stream()
                        .filter(coupon -> Objects.equals(coupon.getCouponId(), couponId.intValue())))
                .collect(Collectors.toList());

        logger.info("NB定制化标签 忠诚守候券 filteredList：{}",filteredList);

        nbTagInfoVo1.setTagValue(filteredList.size()+"");
        nbTagInfoVo1.setCouponDetailVO(filteredList);
        processUserResponse(nbTagInfoVo1);
        tagInfoVOList.add(nbTagInfoVo1);
    }

    private void getDelCoupon(List<NbTagInfoVo> tagInfoVOList, List<CouponDetailVO> couponDetailVOS, NbTagEnum nbTagEnum) {
        logger.info("NB定制化标签 经销商卡券标签处理 nbTagEnum：{}",nbTagEnum);
        NbTagInfoVo nbTagInfoVo = new NbTagInfoVo();
        nbTagInfoVo.setTagId(nbTagEnum.getCode());
        if (CollectionUtils.isEmpty(couponDetailVOS)) {
            tagInfoVOList.add(nbTagInfoVo);
            return;
        }
        List<CouponDetailVO> couponDetailInfoVOS = filterCouponsByTypeAndOfferType(couponDetailVOS, nbTagEnum.getCouponType(), nbTagEnum.getOfferType());
        logger.info("NB定制化标签 {} couponDetailInfoVOS：{}", nbTagEnum.getName(), couponDetailInfoVOS);
        if (CollectionUtils.isEmpty(couponDetailInfoVOS)) {
            tagInfoVOList.add(nbTagInfoVo);
            return;
        }
        nbTagInfoVo.setTagValue(couponDetailInfoVOS.size()+"");
        nbTagInfoVo.setCouponDetailVO(couponDetailInfoVOS);
        processUserResponse(nbTagInfoVo);
        tagInfoVOList.add(nbTagInfoVo);
    }

    public List<CouponDetailVO> filterCouponsByTypeAndOfferType(List<CouponDetailVO> couponDetailVOS, String couponType, Integer offerType) {
        // 使用 Stream API 过滤出符合条件的优惠券，并排除 null 值
        List<CouponDetailVO> collect = couponDetailVOS.stream()
                .filter(coupon -> coupon != null && coupon.getCouponType() != null && coupon.getCouponType().equals(couponType))
                .collect(Collectors.toList());
        logger.info("NB定制化标签 {} collect：{}", couponType, collect);

        if (ObjectUtils.isNotEmpty(offerType)){
            return collect.stream()
                    .filter(coupon -> coupon.getOfferType() != null && coupon.getOfferType().equals(offerType))
                    .collect(Collectors.toList());
        }

        return collect;
    }

    private List<Long> getCouponIds(String groupType) {
        DmsResponse<CommonConfigDto> cdpTagID = dmscloudServiceFegin.selectCommonConfig(groupType, groupType);
        logger.info("NB定制化标签 通用配置表信息 tagCode：{}",groupType);
        if (cdpTagID.isFail()) {
            return null;
        }
        if (ObjectUtils.isEmpty(cdpTagID.getData())) {
            return null;
        }

        CommonConfigDto data = cdpTagID.getData();

        return Arrays.stream(data.getConfigValue().split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    private void getCouponId(List<NbTagInfoVo> tagInfoVOList, List<CouponDetailVO> couponDetailVOS, NbTagInfoVo nbTagInfoVo1, String groupType) {

        DmsResponse<CommonConfigDto> cdpTagID = dmscloudServiceFegin.selectCommonConfig(groupType, groupType);
        logger.info("NB定制化标签 通用配置表信息 tagCode：{}",groupType);
        if (cdpTagID.isFail()) {
            tagInfoVOList.add(nbTagInfoVo1);
            return;
        }

        if (ObjectUtils.isEmpty(cdpTagID.getData())) {
            tagInfoVOList.add(nbTagInfoVo1);
            return;
        }

        CommonConfigDto data = cdpTagID.getData();

        List<CouponDetailVO> filteredList = Arrays.stream(data.getConfigValue().split(","))
                .map(Integer::valueOf)
                .flatMap(couponId -> couponDetailVOS.stream()
                        .filter(coupon -> coupon.getCouponId().equals(couponId)))
                .collect(Collectors.toList());

        nbTagInfoVo1.setTagValue(filteredList.size()+"");
        nbTagInfoVo1.setCouponDetailVO(filteredList);
        processUserResponse(nbTagInfoVo1);
        tagInfoVOList.add(nbTagInfoVo1);

    }

    /**
     * 清除预览配置
     */
    @Override
    public Integer clearPreviewConfiguration() {
        DmsResponse<Integer> restResultResponse = maintainLeadsFegin.clearPreviewConfiguration();
        logger.info("clearPreviewConfigurationResponseParams：{}",restResultResponse);
        if (restResultResponse.isFail()) {
            throw new ServiceBizException("清除预览配置失败!");
        }
        return restResultResponse.getData();
    }

    /**
     *  清除配置
     */
    @Override
    public Integer clearConfiguration(String tagId) {
        DmsResponse<Integer> restResultResponse = maintainLeadsFegin.clearConfiguration(tagId);
        logger.info("clearConfigurationResponseParams：{}",restResultResponse);
        if (restResultResponse.isFail()) {
            throw new ServiceBizException("清除配置失败!");
        }
        return restResultResponse.getData();
    }

    /**
     * 预览&预览保存
     */
    @Override
        public List<CompleteTagInfoVo> previewTempData() {
        //获取临时表数据
        DmsResponse<List<TagConfigTempDto>> restResultResponse = queryConfigTempList();
        //获取字典中心数据
        List<PlateSortDictDto> midDictionaryFirstData = queryMidDictList();
        List<Integer> codeList =midDictionaryFirstData.stream().map(PlateSortDictDto::getCodeId).collect(Collectors.toList());
        List<TagConfigTempDto> existCodeTagConfigTempList = new ArrayList<>();
        //临时表数据中一级板块是否包含字典值
        restResultResponse.getData().forEach(tagConfigTempDto -> {
            if (codeList.contains(tagConfigTempDto.getShowFirstBlock())){
                existCodeTagConfigTempList.add(tagConfigTempDto);
            }
        });
        List<TagInfoVo> tagInfoVOS = BeanMapperUtil.copyList(existCodeTagConfigTempList, TagInfoVo.class);
        List<TagInfoVo> sortTagConfigTempList = tagInfoVOS.stream().sorted(Comparator.comparing(TagInfoVo::getShowSort)).collect(Collectors.toList());
        sortTagConfigTempList.forEach(s->{
            TagValueRuleDto tagValueRuleDto = JSONObject.parseObject(s.getValueRule(), TagValueRuleDto.class);
            s.setShowType(tagValueRuleDto.getShowType());
            s.setGetType(tagValueRuleDto.getGetType());
        });
        return tagInfoService.assembleData(codeList,sortTagConfigTempList);
    }

    /**
     * 获取字典中心数据
     */
    private List<PlateSortDictDto> queryMidDictList() {
        //获取字典中心数据
        MidResponse<List<PlateSortDictDto>> midDictionaryFirstDataResponse = midEndDictCenterFeign.queryCodeDataType(CommonConstant.FIRST_BLOCK_CODE);
        logger.info("previewOrSave->midDictionaryFirstData:{}", midDictionaryFirstDataResponse);
        if (!midDictionaryFirstDataResponse.isSuccess() && Objects.nonNull(midDictionaryFirstDataResponse.getData())) {
           throw new ServiceBizException("获取字典中心数据失败!");
        }
        return midDictionaryFirstDataResponse.getData();
    }

    /**
     * 获取maintainLeads 临时表数据
     */
    private DmsResponse<List<TagConfigTempDto>> queryConfigTempList() {
        //获取所有temp数据。
        DmsResponse<List<TagConfigTempDto>> restResultResponse = maintainLeadsFegin.queryConfigList();
        logger.info("previewOrSaveResponseParams：{}",restResultResponse);
        if (restResultResponse.isFail()) {
            throw new ServiceBizException("获取预览配置信息失败！");
        }
        return restResultResponse;
    }

    /**
     * 预览&预览保存同步数据
     */
    @Override
    public Integer previewOrSaveSynchronizeData() {
        DmsResponse<Integer> restResultResponse = maintainLeadsFegin.previewOrSaveSynchronizeData();
        logger.info("previewOrSaveSynchronizeDataResponseParams：{}",restResultResponse);
        if (restResultResponse.isFail()) {
            throw new ServiceBizException("当前未做任何预览变更，保存无意义");
        }
        return restResultResponse.getData();
    }

    private NbTagInfoVo getCdpTagInfoVOS(String mobile) {
        logger.info("getCdpTagInfoVOS start...");
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG002.getCode());
        nbTagInfoVO.setCdpTagInfoVOS(cdpTagInfoService.customCdpTagList(mobile));
        logger.info("getCdpTagInfoVOS end...");
        return nbTagInfoVO;
    }

    private NbTagInfoVo oneIdCouponData(CustomizedLabelDto customizedLabelDto, String mobile) {
        logger.info("oneIdCouponData start...");
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG004.getCode());

        if (StringUtils.isEmpty(mobile)){
            return nbTagInfoVO;
        }

        //根据memberId获取卡券信息
        List<CouponDetailVO> memberIdCouponDetailVOS = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(customizedLabelDto.getMemberId())){
            QueryCouponDetailInfoDto queryCouponDetailInfoDTO = new QueryCouponDetailInfoDto();
            queryCouponDetailInfoDTO.setMemberId(customizedLabelDto.getMemberId());
            queryCouponDetailInfoDTO.setValid(1);
            queryCouponDetailInfoDTO.setTicketState(31061001);
            //有效卡券数量
            memberIdCouponDetailVOS = couponDetailService.queryCouponInfo(queryCouponDetailInfoDTO);
            logger.info("NB定制化标签查询 获取会员ID卡券信息 memberIdCouponDetailVOS :{}",memberIdCouponDetailVOS);
        }
        if (CollectionUtils.isEmpty(memberIdCouponDetailVOS)){
            logger.info("oneIdCouponData,memberIdCouponDetailVOS is null");
            return nbTagInfoVO;
        }

        nbTagInfoVO.setTagValue(memberIdCouponDetailVOS.size()+"");
        nbTagInfoVO.setCouponDetailVO(memberIdCouponDetailVOS);
        processUserResponse(nbTagInfoVO);
        logger.info(" queryCustomizedLabel -》 oneIdCouponData：{}",nbTagInfoVO);
        logger.info("oneIdCouponData end...");
        return nbTagInfoVO;
    }

    private Long getOneId(String mobile) {

        logger.info("NB定制化标签查询 获取oneId getOneId->mobile:{}",mobile);
        List<String> mobileList = new ArrayList<>();
        mobileList.add(mobile);
        MobileToOneIdDto mobileToOneIdDto = new MobileToOneIdDto();
        mobileToOneIdDto.setList(mobileList);
        //调用会员接口查询会员信息
        ResponseDto<List<CustomerMobileListReturnVo>> memberInformationByMobile = mIdEndMemberCenterFeign.getMemberInformationByMobile(mobileToOneIdDto);
        logger.info("NB定制化标签查询 获取oneId getOneId->memberInformationByMobile:{}",memberInformationByMobile);
        if (memberInformationByMobile == null || !CommonConstant.SUCCESS_CODE.equals(memberInformationByMobile.getReturnCode())) {
            return null;
        }

        List<CustomerMobileListReturnVo> data = memberInformationByMobile.getData();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        CustomerMobileListReturnVo customerMobileListReturnVo = data.get(0);

        logger.info("NB定制化标签查询 获取oneId getOneId->data:{}",data);

        return customerMobileListReturnVo.getId();
    }

    private NbTagInfoVo selectInsureHistory(String vin, String selectType) {
        logger.info("selectInsureHistory start...");
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG019.getCode());
        //1-后端由其他接口调用 0-前端直接调用
        if ("1".equals(selectType)) {
            logger.info("NB定制化标签查询 保险起止时间  queryExtendedWarrantyInfo->vin:{}",vin);
            DmsResponse<InsuranceBillVo> insuranceBillVODmsResponse = dmscusRepairFeign.queryInsuranceDateByVin(vin);

            //保险信息记录
            if (insuranceBillVODmsResponse == null || !CommonConstant.SUCCESS_NEWBIE_CODE.equals(insuranceBillVODmsResponse.getResultCode())) {
                return nbTagInfoVO;
            }
            InsuranceBillVo insuranceBillVO = insuranceBillVODmsResponse.getData();
            logger.info("NB定制化标签查询 保险起止时间 queryExtendedWarrantyInfo->data:{}",insuranceBillVO);

            //保险信息记录
            if(ObjectUtils.isEmpty(insuranceBillVO)){
                return nbTagInfoVO;
            }

            nbTagInfoVO.setTagValue(insuranceBillVO.getInsuranceEnd());
        } else {
            logger.info("NB定制化标签查询 保险起止时间  明细 queryExtendedWarrantyInfo->vin:{}",vin);
            DmsResponse<List<InsuranceBillListDto>> insuranceBillListDtoDmsResponse = dmscusRepairFeign.selectInsureHistory(vin);

            logger.info("NB定制化标签查询 保险起止时间 queryExtendedWarrantyInfo->insuranceBillListDtoDmsResponse:{}",insuranceBillListDtoDmsResponse);

            //保险信息记录
            if (insuranceBillListDtoDmsResponse == null || !CommonConstant.SUCCESS_NEWBIE_CODE.equals(insuranceBillListDtoDmsResponse.getResultCode())) {
                return nbTagInfoVO;
            }
            List<InsuranceBillListDto> insuranceBillListDto = insuranceBillListDtoDmsResponse.getData();
            logger.info("NB定制化标签查询 保险起止时间 queryExtendedWarrantyInfo->data:{}",insuranceBillListDto);

            //保险信息记录
            if(CollectionUtils.isEmpty(insuranceBillListDto)){
                return nbTagInfoVO;
            }

            nbTagInfoVO.setInsuranceBillList(insuranceBillListDto);
        }
        logger.info("selectInsureHistory end...");
        return nbTagInfoVO;
    }

    private NbTagInfoVo queryExtendedWarrantyInfo(String vin) {
        logger.info("queryExtendedWarrantyInfo start...");
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG018.getCode());
        nbTagInfoVO.setTagValue(TAG_FALSE);


        logger.info("NB定制化标签查询 效延保购买记录 queryExtendedWarrantyInfo->vin:{}",vin);

        DmsResponse<List<ExtendedWarrantyVo>> extendedWarrantyInfo = dmscusRepairFeign.queryExtendedWarrantyInfo(vin);

        logger.info("NB定制化标签查询 效延保购买记录 queryExtendedWarrantyInfo->extendedWarrantyInfo:{}",extendedWarrantyInfo);

        //效延保购买记录
        if (extendedWarrantyInfo == null || !CommonConstant.SUCCESS_NEWBIE_CODE.equals(extendedWarrantyInfo.getResultCode())) {
            return nbTagInfoVO;
        }
        List<ExtendedWarrantyVo> warrantyInfoData = extendedWarrantyInfo.getData();
        logger.info("NB定制化标签查询 效延保购买记录 selectCareBuy->warrantyInfoData:{}",warrantyInfoData);

        //效延保购买记录
        if(CollectionUtils.isEmpty(warrantyInfoData)){
            return nbTagInfoVO;
        }

        nbTagInfoVO.setTagValue(TAG_TRUE);
        nbTagInfoVO.setWarrantyInfoData(warrantyInfoData);
        logger.info("queryExtendedWarrantyInfo end...");
        return nbTagInfoVO;

    }
    /**
     * 将"质保”/“道路救援”/“二手车”/“保险"、终身权益、保养套餐、延保、非车险 8类权益信息合一
     * @return
     */
    private void query8n1Info(List<NbTagInfoVo> tagInfoVOS) {
        logger.info("query8n1Info start:");
        List<EquityInformationVo> equityInformationList = new ArrayList<>();
        // 取出 "质保”/“道路救援”/“二手车”/“保险"、终身权益 信息
        Optional<NbTagInfoVo> first = tagInfoVOS.stream().filter(tagInfoVO -> tagInfoVO.getTagId().equals(NbTagEnum.TAG028.getCode())).findFirst();
        first.ifPresent(item -> {
            Optional.ofNullable(item.getEquityInformationList()).ifPresent(equityInformationList::addAll);
        });
        // 取出 保养套餐
        Optional<NbTagInfoVo> second = tagInfoVOS.stream().filter(tagInfoVO -> tagInfoVO.getTagId().equals(NbTagEnum.TAG017.getCode())).findFirst();
        second.ifPresent(item -> {
            List<CareBuyedVo> careBuyedDtoList = item.getCareBuyedDtoList();
            if(!CollectionUtils.isEmpty(careBuyedDtoList)){
                careBuyedDtoList.forEach(careBuyedVo -> {
                    EquityInformationVo vo = new EquityInformationVo();
                    vo.setLevel(NUM_2);
                    vo.setProductType("保养套餐");
                    vo.setProductName(careBuyedVo.getActivityName());
                    vo.setVin(careBuyedVo.getVin());
                    BeanUtils.copyProperties(careBuyedVo,vo);
                    vo.setEffectiveDate(StringUtil.dateSubStrin(careBuyedVo.getEffectiveDate()));
                    vo.setExpireDate(StringUtil.dateSubStrin(careBuyedVo.getExpirationDate()));
                    equityInformationList.add(vo);
                });
            }
        });
        // 取出 延保
        Optional<NbTagInfoVo> third = tagInfoVOS.stream().filter(tagInfoVO -> tagInfoVO.getTagId().equals(NbTagEnum.TAG018.getCode())).findFirst();
        third.ifPresent(item -> {
            List<ExtendedWarrantyVo> warrantyInfoData = item.getWarrantyInfoData();
            if(!CollectionUtils.isEmpty(warrantyInfoData)){
                warrantyInfoData.forEach(extendedWarrantyVo -> {
                    EquityInformationVo vo = new EquityInformationVo();
                    vo.setLevel(NUM_0);
                    vo.setProductType("延保");
                    vo.setProductName(extendedWarrantyVo.getProductName());
                    BeanUtils.copyProperties(extendedWarrantyVo,vo);
                    String effectiveExpireDate = extendedWarrantyVo.getEffectiveExpireDate();
                    String[] split = effectiveExpireDate.split("--");
                    vo.setEffectiveDate(split[NUM_0]);
                    vo.setExpireDate(split[NUM_1]);
                    equityInformationList.add(vo);
                });
            }
        });
        // 取出 非车险
        Optional<NbTagInfoVo> fourth = tagInfoVOS.stream().filter(tagInfoVO -> tagInfoVO.getTagId().equals(NbTagEnum.TAG027.getCode())).findFirst();
        fourth.ifPresent(item -> {
            List<NonCarInsuranceVo> nonCarInsuranceVoList = item.getNonCarInsuranceVoList();
            if(!CollectionUtils.isEmpty(nonCarInsuranceVoList)){
                nonCarInsuranceVoList.forEach(nonCarInsuranceVo -> {
                    EquityInformationVo vo = new EquityInformationVo();
                    vo.setLevel(NUM_3);
                    vo.setProductType("非车险");
                    BeanUtils.copyProperties(nonCarInsuranceVo,vo);
                    equityInformationList.add(vo);
                });
            }
        });
        tagInfoVOS.forEach(tagInfoVO -> {
            if(tagInfoVO.getTagId().equals(NbTagEnum.TAG028.getCode())){
                // 将"质保”/“道路救援”/“二手车”/“保险"、终身权益、保养套餐、延保、非车险 8类权益信息合一
                // 的信息重新赋值到TAG028里的equityInformationList
                equityInformationList.sort(Comparator.comparing(EquityInformationVo::getLevel));// 排序
                tagInfoVO.setEquityInformationList(equityInformationList);
            }
        });

    }
    /**
     * 查询"质保”/“道路救援”/“二手车”/“保险" 4类权益信息
     * @param vin
     * @return
     */
    private NbTagInfoVo query5n1Info(String vin) {
        logger.info("query5n1Info start:{},{}", vin);
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG028.getCode());
        nbTagInfoVO.setTagValue(TAG_FALSE);

        logger.info("query5n1Info->vin:{}",vin);
        EmpowerDetailQueryDTO dto = new EmpowerDetailQueryDTO();
        dto.setVin(vin);
        dto.setIsPaging(NUM_0);
        dto.setUserRightsStatus(Arrays.asList(NUM_1));
        ResponseDto<List<EmpowerDetailDTO>> empowerDetailList = carRightsServiceFeign.getEmpowerDetailList(dto);
        logger.info("query5n1Info->getEmpowerDetailList:{}", JSON.toJSONString(empowerDetailList));
        //"质保”/“道路救援”/“二手车”/“保险"
        List<EmpowerDetailDTO> data = empowerDetailList.getData();
        List<EquityInformationVo> equityInformationList = new ArrayList<>();
        if (Objects.nonNull(empowerDetailList) && !CollectionUtils.isEmpty(data)) {
            // 过滤出"质保1005”/“道路救援1006”/“二手车1184”/“保险1002"
            List<EmpowerDetailDTO> collect = data.stream()
                    .filter(item -> Objects.nonNull(item.getFirstCategoryCode())
                            && ((item.getFirstCategoryCode().equals(WARRANTY_PERIOD))
                            || (item.getFirstCategoryCode().equals(INSURANCE_PERIOD))
                            || (item.getFirstCategoryCode().equals(USED_CAR_PERIOD))
                            || (item.getFirstCategoryCode().equals(ROAD_RESCUE_PERIOD)))).collect(Collectors.toList());
            // 封装"质保1005”/“道路救援1006”/“二手车1184”/“保险1002"的数据
            if(!CollectionUtils.isEmpty(collect)){
                collect.forEach(item -> {
                    EquityInformationVo vo = new EquityInformationVo();
                    vo.setLevel(LevelEnum.getCodeByLevel(item.getFirstCategoryCode()));
                    vo.setProductType(item.getFirstCategoryName());
                    vo.setProductName(item.getRightsName());
                    vo.setVin(item.getVin());
                    vo.setEffectiveDate(StringUtil.dateSubStrin(item.getEffectiveDate()));
                    vo.setExpireDate(StringUtil.dateSubStrin(item.getExpirationDate()));
                    equityInformationList.add(vo);
                });
            }
        }
        // 查询是否是终身保养权益
        try {
            RequestDto requestDto = new RequestDto();
            RecordDetailedListDTO dto2 = new RecordDetailedListDTO();
            dto2.setVinList(Arrays.asList(vin));
            dto2.setRewardStatus(String.valueOf(NUM_1));
            requestDto.setData(dto2);
            ResponseDto<RewardSendResponseVO> lifetimeMaintenance = businessRulesServiceFeign.getLifetimeMaintenance(requestDto);
            logger.info("query5n1Info->getLifetimeMaintenance:{}", JSON.toJSONString(lifetimeMaintenance));
            if(Objects.nonNull(lifetimeMaintenance)
                    && Objects.nonNull(lifetimeMaintenance.getData())
                    && !CollectionUtils.isEmpty(lifetimeMaintenance.getData().getList())){

                RewardSendVO rewardSendVO = lifetimeMaintenance.getData().getList().get(0);
                EquityInformationVo vo = new EquityInformationVo();
                vo.setLevel(NUM_1);
                vo.setProductType("终身保养");
                vo.setProductName("终身保养");
                vo.setVin(vin);
                vo.setEffectiveDate(StringUtil.dateSubStrin(rewardSendVO.getCreateTime()));
                vo.setExpireDate("-");
                equityInformationList.add(vo);
            }
        }catch (Exception e){
            // try起来的原因是，接口提供方说，正常情况下，一个vin只会有一条数据，异常情况可能会有多条，既然都异常了，那我就不管他了
            logger.info("query5n1Info->getLifetimeMaintenance:{}",e);
        }
        nbTagInfoVO.setTagValue(TAG_TRUE);
        nbTagInfoVO.setEquityInformationList(equityInformationList);
        logger.info("query5n1Info end...");
        return nbTagInfoVO;

    }
    /**
     * 非车险信息查询
     * @param vin
     * @return
     */
    private NbTagInfoVo queryNonCarInsuranceInfo(String vin, String ownerCode) {
        logger.info("queryNonCarInsuranceInfo start...:{},{}", vin, ownerCode);
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG027.getCode());
        nbTagInfoVO.setTagValue(TAG_FALSE);


        logger.info("NB定制化标签查询 非车险购买记录 queryNonCarInsuranceInfo->vin:{}",vin);

        DmsResponse<List<NonCarInsuranceVo>> nonCarInsuranceVoInfo = dmscusRepairFeign.queryNonCarInsuranceInfo(vin, ownerCode);

        logger.info("NB定制化标签查询 非车险购买记录 queryNonCarInsuranceInfo->queryNonCarInsuranceInfo:{}",nonCarInsuranceVoInfo);

        //非车险购买记录
        if (nonCarInsuranceVoInfo == null || !CommonConstant.SUCCESS_NEWBIE_CODE.equals(nonCarInsuranceVoInfo.getResultCode())) {
            return nbTagInfoVO;
        }
        List<NonCarInsuranceVo> warrantyInfoData = nonCarInsuranceVoInfo.getData();
        logger.info("NB定制化标签查询 非车险购买记录 queryNonCarInsuranceInfo->queryNonCarInsuranceInfo:{}",warrantyInfoData);

        //非车险购买记录
        if(CollectionUtils.isEmpty(warrantyInfoData)){
            return nbTagInfoVO;
        }

        nbTagInfoVO.setTagValue(TAG_TRUE);
        nbTagInfoVO.setNonCarInsuranceVoList(warrantyInfoData);
        logger.info("queryNonCarInsuranceInfo end...");
        return nbTagInfoVO;

    }

    private NbTagInfoVo selectCareBuy(String vin) {
        logger.info("selectCareBuy start...");
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG017.getCode());
        nbTagInfoVO.setTagValue(TAG_FALSE);

        logger.info("NB定制化标签查询 保养权益 selectCareBuy->vin:{}",vin);

        //保养权益
        SaveCareBuyedDto saveCareBuyedDTO = new SaveCareBuyedDto();
        saveCareBuyedDTO.setVin(vin);
        RequestDto<SaveCareBuyedDto> requestDTO = new RequestDto<>();
        requestDTO.setData(saveCareBuyedDTO);
        requestDTO.setPage(0L);
        requestDTO.setPageSize(50L);
        DmsResponse<List<CareBuyedVo>> listDmsResponse = midEndVehicleCenterFeign.careBoughtPage(requestDTO);
        logger.info("NB定制化标签查询 保养权益 selectCareBuy->listDmsResponse:{}",listDmsResponse);

        //保养权益
        if (listDmsResponse == null || !CommonConstant.SUCCESS_CODE.equals(listDmsResponse.getReturnCode())) {
            return nbTagInfoVO;
        }
        List<CareBuyedVo> careBuyedDtoList = listDmsResponse.getData();
        logger.info("NB定制化标签查询 保养权益 selectCareBuy->careBoughtDtoList:{}",careBuyedDtoList);

        //保养权益
        if(CollectionUtils.isEmpty(careBuyedDtoList)){
            return nbTagInfoVO;
        }
        LocalDateTime currentDateTime = LocalDateTime.now(); // 获取当前日期

        List<CareBuyedVo> careBoughtDos = careBuyedDtoList.stream()
                .filter(dto -> {
                    LocalDateTime expirationDateTime = LocalDateTime.parse(dto.getExpirationDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    return currentDateTime.isBefore(expirationDateTime);
                })
                .collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(careBoughtDos)){
            setUseScope(careBoughtDos);
        }

        nbTagInfoVO.setTagValue(TAG_TRUE);
        nbTagInfoVO.setCareBuyedDtoList(careBoughtDos);
        logger.info("selectCareBuy end...");
        return nbTagInfoVO;

    }

    private void setUseScope(List<CareBuyedVo> careBoughtDos) {
        List<CareBuyedExtDto> useScopeList = careBoughtDos.stream().map(e -> {
            CareBuyedExtDto extDto = new CareBuyedExtDto();
            extDto.setCareBuyedId(e.getId().longValue());
            extDto.setSaleDealer(e.getSaleDealer());
            return extDto;
        }).collect(Collectors.toList());
        DmsResponse<List<CareBuyedExtDto>> useScopeRes = dmscloudServiceFeign.careBuyedUseScopeList(useScopeList);
        logger.info("useScopeRes:{}",useScopeRes);
        List<CareBuyedExtDto> careBuyedUseScopeList = useScopeRes.getData();
        if(!CollectionUtils.isEmpty(careBuyedUseScopeList)){
            Map<String, CareBuyedExtDto> useScopeMap = careBuyedUseScopeList.stream().collect(Collectors.toMap(e -> e.getCareBuyedId().toString(), Function.identity(), (e1, e2) -> e1));
            careBoughtDos.forEach(careBuyedVo -> {
                if(Objects.nonNull(careBuyedVo.getId())) {
                    Optional.ofNullable(useScopeMap.get(careBuyedVo.getId().toString())).ifPresent(extDto -> {
                        careBuyedVo.setUseScope(extDto.getUseScope());
                        careBuyedVo.setUseScopeName(extDto.getUseScopeName());
                        careBuyedVo.setSaleDealer(extDto.getSaleDealer());
                        careBuyedVo.setSaleDealerName(extDto.getSaleDealerName());
                    });
                }
            });
        }
    }

    private List<NbTagInfoVo> selectInviteClueTag(String vin, List<String> tagCodes, TmVehicleDto tmVehicleDTO) {
        logger.info("selectInviteClueTag start...");
        List<NbTagInfoVo> tagInfoVOList = new ArrayList<>();
        //获取线索
        List<InviteClueResultDto> inviteClueResultDtos = selectInviteClueTag(vin);

        //判断标签类别是否包含TAG013，包含走流失客户逻辑去查询详细的标签信息
        if (tagCodes.contains(NbTagEnum.TAG013.getCode())){
            tagInfoVOList.add(getRecordTypeTag(inviteClueResultDtos));
        }

        //判断标签类别是否包含TAG013，包含走流失客户逻辑去查询详细的标签信息
        if (tagCodes.contains(NbTagEnum.TAG014.getCode())){
            tagInfoVOList.add(getLossTag(tmVehicleDTO,inviteClueResultDtos));
        }
        logger.info("selectInviteClueTag end...");
        return tagInfoVOList;
    }

    private NbTagInfoVo getLossTag(TmVehicleDto tmVehicleDTO, List<InviteClueResultDto> inviteClueResultDtos) {
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG014.getCode());
        nbTagInfoVO.setTagValue(TAG_FALSE);
        //判断是否水货车
        boolean wwCar = checkWwCar(tmVehicleDTO);

        //是水货车 标签内容 直接返回否
        if (wwCar){
            return nbTagInfoVO;
        }

        //判断线索是否为空 为空 标签内容 直接返回否
        if(CollectionUtils.isEmpty(inviteClueResultDtos)){
            return nbTagInfoVO;
        }

        //判断线索类型与建议进场时间
        boolean result = inviteClueResultDtos.stream()
                .anyMatch(dto -> {
                    int inviteType = dto.getInviteType();
                    Date adviseInDate = dto.getAdviseInDate();
                    LocalDate targetDate = LocalDate.of(2020, 1, 1);
                    LocalDate convertedDate = adviseInDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    return inviteType == 82381006 && convertedDate.isAfter(targetDate);
                });

        if(result){
            nbTagInfoVO.setTagValue(TAG_TRUE);
        }

        return nbTagInfoVO;
    }

    private NbTagInfoVo getRecordTypeTag(List<InviteClueResultDto> inviteClueResultDtos) {
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG013.getCode());
        nbTagInfoVO.setTagValue(TAG_FALSE);

        if (CollectionUtils.isEmpty(inviteClueResultDtos)){
            logger.warn("NB定制化标签查询结束 标签列表为空");
            return nbTagInfoVO;
        }

        boolean result = inviteClueResultDtos.stream()
                .filter(dto -> dto.getRecordType() != null) // 过滤recordType为空的元素
                .anyMatch(dto -> {
                    int inviteType = dto.getInviteType();
                    int recordType = dto.getRecordType();
                    return (inviteType == 82381001 || inviteType == 82381002 || inviteType == 82381006 || inviteType == 82381012)
                            && recordType == 87891003;
                });

        if (result){
            nbTagInfoVO.setTagValue(TAG_TRUE);
        }

        return nbTagInfoVO;
    }

    private List<InviteClueResultDto> selectInviteClueTag(String vin) {
        InviteClueParamDto inviteClueParamDTO = new InviteClueParamDto();

        inviteClueParamDTO.setOrderStatus(Collections.singletonList(82411002));

        inviteClueParamDTO.setVin(Collections.singletonList(vin));

        DmsResponse<List<InviteClueResultDto>> listDmsResponse = dmscusCustomerFeign.selectInviteClueTag(inviteClueParamDTO);

        logger.info("NB定制化标签查询 获取线索 selectInviteClueTag->listDmsResponse:{}",listDmsResponse);

        //线索类型
        if (listDmsResponse == null || !CommonConstant.SUCCESS_NEWBIE_CODE.equals(listDmsResponse.getResultCode())) {
            return null;
        }
        List<InviteClueResultDto> inviteClueResultDtos = listDmsResponse.getData();
        logger.info("NB定制化标签查询 获取线索 selectInviteClueTag->inviteClueResultDtoS:{}",inviteClueResultDtos);

        return inviteClueResultDtos;
    }

    private TmVehicleDto queryVehicle(String vin) {
        //获取车辆信息
        DmsResponse<TmVehicleDto> vehicleResponse = midEndVehicleCenterFeign.getVehicleByVIN(vin);
        logger.info("NB定制化标签查询 车辆信息 selectInviteClueTag->vehicleResponse:{}",vehicleResponse);
        if(vehicleResponse == null || !CommonConstant.SUCCESS_CODE.equals(vehicleResponse.getReturnCode()) || ObjectUtils.isEmpty(vehicleResponse.getData())){
            return null;
        }
        return vehicleResponse.getData();
    }

    private NbTagInfoVo checkVip(String licensePlate, TmVehicleDto tmVehicleDTO) {
        logger.info("checkVip start...");
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG001.getCode());
        nbTagInfoVO.setTagValue(TAG_FALSE);

        if (StringUtils.isEmpty(licensePlate) && ObjectUtils.isNotEmpty(tmVehicleDTO)){
            licensePlate = tmVehicleDTO.getPlateNumber();
        }

        if (StringUtils.isEmpty(licensePlate)){
            return nbTagInfoVO;
        }
        DmsResponse<Boolean> dmsResponse = maintainLeadsFegin.queryVipByVin(licensePlate);
        logger.info("车辆VIP信息，dmsResponse:{}", dmsResponse);
        Boolean data = dmsResponse.getData();
        if (data) {
            nbTagInfoVO.setTagValue(TAG_TRUE);
        }
        logger.info("checkVip end...");
        return nbTagInfoVO;
    }

    private NbTagInfoVo checkFleetCode(String vin) {
        logger.info("checkFleetCode start...");
        NbTagInfoVo nbTagInfoVO = new NbTagInfoVo();
        nbTagInfoVO.setTagId(NbTagEnum.TAG015.getCode());
        nbTagInfoVO.setTagValue(TAG_FALSE);
        DmsResponse<Integer> fleetCodeByVin = dmscusCustomerFeign.getFleetCodeByVin(vin);
        logger.info("NB定制化标签查询 大客户 checkFleetCode->fleetCodeByVin:{}",fleetCodeByVin);

        //是否大客户
        if(fleetCodeByVin == null || !CommonConstant.SUCCESS_NEWBIE_CODE.equals(fleetCodeByVin.getResultCode())){
            return nbTagInfoVO;
        }

        if(fleetCodeByVin.getData() == CommonConstant.DICT_IS_YES){
            nbTagInfoVO.setTagValue(TAG_TRUE);
        }
        logger.info("checkFleetCode end...");
        return nbTagInfoVO;
    }

    private boolean checkWwCar(TmVehicleDto vehicleDTO) {

        if(ObjectUtils.isEmpty(vehicleDTO) || ObjectUtils.isEmpty(vehicleDTO.getWwCar()) || vehicleDTO.getWwCar() == 1 ){
            return true;
        }
        String configYear = vehicleDTO.getConfigYear();
        String vin = vehicleDTO.getVin();
        logger.info("getLossTag configYear:{}", configYear);
        int year;
        try {
            year = Integer.parseInt(configYear);
        } catch (NumberFormatException e) {
            logger.error("年款转换失败 year:{}", configYear);
            return true;
        }
        if (year < TWO_THOUSAND_AND_FIFTEEN) {
            logger.info("year < 2015 year:{}", year);
            return true;
        }

        return vin.contains("LPS");
    }

    private boolean isWhite(String dealer) {
        logger.info("Renewal of insurance isWhite dealer:{}", dealer);
        DmsResponse<Object> response = domainMaintainAuthFeign.checkWhitelist(dealer, CommonConstant.RENEWAL_INSURANCE_WHITE, CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE, "");
        logger.info("Renewal of insurance isWhite response:{}",response);
        if (Objects.isNull(response) || response.isFail()){
            logger.info("Renewal of insurance isWhite error");
            return false;
        }
        Object data = response.getData();
        if (null == data) {
            logger.info("Renewal of insurance isWhite data isnull");
            return false;
        }
        try {
            return Boolean.parseBoolean(data.toString());
        } catch (Exception e) {
            logger.info("Renewal of insurance isWhite e:{}", e);
            return false;
        }
    }

}
