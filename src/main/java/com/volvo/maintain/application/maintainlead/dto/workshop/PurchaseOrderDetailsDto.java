package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 存储Mao值
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PurchaseOrderDetailsDto implements Serializable {

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 零件集合
     */
    private List<String> partList;
}
