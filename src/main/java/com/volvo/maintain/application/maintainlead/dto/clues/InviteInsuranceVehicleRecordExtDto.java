package com.volvo.maintain.application.maintainlead.dto.clues;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 车辆邀约续保记录扩展表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InviteInsuranceVehicleRecordExtDto {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 线索ID
     */
    private Long clueId;

    /**
     * 外部线索ID 默认-1
     */
    private Integer externalClueId;

    /**
     * 经销商代码
     */
    private String ownerCode;

    /**
     * 经销商类型 投保经销商/工单经销商/销售经销商/自店导入经销商
     */
    private Integer ownerType;

    /**
     * 保单号
     */
    private String insuranceNo;

    /**
     * 保险公司
     */
    private String insuranceName;

    /**
     * 厂端保险到期日期
     */
    private Date factoryInsuranceExpiryDate;

    /**
     * 店端保险到期日期
     */
    private Date storeInsuranceExpiryDate;

    /**
     * 提前出单日期
     */
    private Date advanceIssuanceDate;

    /**
     * 建议关怀日期
     */
    private Date suggestedCareDate;

    /**
     * 线索下发日期
     */
    private Date clueIssuanceDate;

    /**
     * 线索数据源
     */
    private Integer clueDataSource;

    /**
     * 线索下发类型
     */
    private Integer clueIssuanceType;

    /**
     * 是否有自店线索升级
     */
    private Integer hasInstoreUpgrade;

    /**
     * 自店线索升级时间
     */
    private Date instoreUpgradeTime;

    /**
     * 线索生成方
     */
    private Integer clueGenerator;

    /**
     * 是否有自店修改保险到期日期
     */
    private Integer hasInstoreModified;

    /**
     * 自店修改时间
     */
    private Date instoreModificationTime;

    /**
     * 自店修改人
     */
    private Integer instoreModificationUser;

    /**
     * 保单创建日期
     */
    private Date policyCreationDate;

    /**
     * 保单来源 投保经销商/工单经销商/销售经销商
     */
    private Integer policySource;

    /**
     * 新保单生效日期
     */
    private Date newPolicyEffectiveDate;

    /**
     * 投保人姓名
     */
    private String insuredName;

    /**
     * 投保人电话
     */
    private String insuredPhone;

    /**
     * 工单送修人姓名
     */
    private String orderRepairerName;

    /**
     * 工单送修人电话
     */
    private String orderRepairerPhone;

    /**
     * 保单失效日期
     */
    private Date policyExpirationDate;

    /**
     * 商业险产品类型
     */
    private String commercialInsuranceType;

    /**
     * 销售日期
     */
    private Date salesDate;

    /**
     * 线索判断来源依据
     */
    private String clueJudgmentBasis;

    /**
     * 是否开启单店保护
     */
    private Integer singleStoreProtection;

    /**
     * 提前出单周期
     */
    private Integer advanceIssuePeriod;

    /**
     * 保单经销商
     */
    private String policyDealer;

    /**
     * 最近一次非停业工单经销商代码
     */
    private String lastNonBusinessDealerCode;

    /**
     * 销售门店代码
     */
    private String salesStoreCode;

    /**
     * 删除标识（0-未删除，1-已删除）
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建sql人
     */
    private String createSqlby;

    /**
     * 更新sql人
     */
    private String updateSqlby;

    /**
     * 投保人姓名
     */
    private String policyHolderName;

    /**
     * 投保人电话
     */
    private String policyHolderPhone;

    /**
     * 投保人手机
     */
    private String policyHolderMobile;

    /**
     * 投保人邮编
     */
    private String policyHolderZipcode;

    /**
     * 投保人地址
     */
    private String policyHolderAddress;
}