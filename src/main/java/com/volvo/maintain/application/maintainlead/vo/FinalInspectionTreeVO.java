package com.volvo.maintain.application.maintainlead.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 终检配件/故障树结构总VO
 */
@Data
public class FinalInspectionTreeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 维修部位 */
    private List<RepairPartTreeItemVO> repairParts;

    /** 故障现象 */
    private List<RepairPartTreeItemVO> faultPhenomenon;
} 