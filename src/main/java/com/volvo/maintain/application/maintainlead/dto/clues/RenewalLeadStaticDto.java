package com.volvo.maintain.application.maintainlead.dto.clues;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class RenewalLeadStaticDto  implements Serializable {

    private Long inviteId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 经销商
     */
    private String ownerCode;

    /**
     * vin
     */
    private String vin;

    /**
     * 车牌
     */
    private String licensePlate;

    /**
     * 保险到期日期
     */
    private Date insuranceExpiryDate;

    /**
     * 续保客户类型
     */
    private String renewalCustomerType;

    /**
     * 跟进时间
     */
    private Date followUpTime;

    /**
     * 跟进人员
     */
    private String followUpPerson;


    /**
     * 跟进内容
     */
    private String followUpContent;


    /**
     * 跟进结果
     */
    private String followUpResult;


    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 是否AI外呼
     */
    private Integer isAiCall;

    /**
     * 是否接通
     */
    private Integer isConnected;

    /**
     * AI得分
     */
    private BigDecimal aiScore;

    /**
     * 通话时间
     */
    private Date callTime;

    /**
     * 通话时长
     */
    private Integer callDuration;
}
