package com.volvo.maintain.application.maintainlead.service.customerProfile;


import com.volvo.maintain.interfaces.vo.booking.BookingEm90Vo;

import java.util.List;

public interface BookingRegisterService {

    void em90BookingOrderPush(int beginDate, int endDate);

    void em90BookingOrderPushCs(List<BookingEm90Vo> bookingEm90VoList);
    /**
     * em90取送车未确认通知
     */
    void em90TakeDeliverCarUnconfirmed(int beginDate, int endDate);
}
