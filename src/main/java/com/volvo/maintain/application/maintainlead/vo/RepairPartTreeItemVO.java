package com.volvo.maintain.application.maintainlead.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 配件/故障树结构递归节点
 */
@Data
public class RepairPartTreeItemVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 父级ID（根节点为null或0） */
    private Long parentId;

    /** 唯一编码 */
    private String onlyCode;

    /** 描述 */
    private String describe;
    
    /** 文件类型 */
    private List<String> fileTypeList;

    /** 子节点 */
    private List<RepairPartTreeItemVO> items;
} 