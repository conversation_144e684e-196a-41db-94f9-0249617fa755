package com.volvo.maintain.application.maintainlead.service;

import com.volvo.maintain.application.maintainlead.dto.accidentclue.AccidentClueCrmInfoMqDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.OwnerRuleDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.SaveOwnerRuleDto;

import java.util.List;

public interface AccidentCluesOwnerRuleService {
	void selectOwnerRules(List<String> ownerCodes, Integer ruleType);

	List<OwnerRuleDto> selectOwnerRule(String ownerCode, Integer ruleType);

	Integer deleteOwnerRule(String ids);

	void saveOwnerRule(SaveOwnerRuleDto saveOwnerRuleDto);

	List<OwnerRuleDto> selectOwnerRuleNew(String ownerCode, Integer ruleType);

	OwnerRuleDto selectAllocatedInfo(String ownerCode);

	List<OwnerRuleDto> selectMessageInfo(String ownerCode);

	void newbieClueReminder(List<AccidentClueCrmInfoMqDto> list);
}
