package com.volvo.maintain.application.maintainlead.service.strategy.rights;

import com.volvo.maintain.application.maintainlead.dto.rights.PurchaseEligibilityCheckResponseDto;
import com.volvo.maintain.application.maintainlead.emums.PurchaseConditionsTypeEnum;
import com.volvo.maintain.application.maintainlead.service.strategy.RightsExecuteStrategy;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 权益抽象
 */
public abstract class AbsRightsExecuteStrategy implements RightsExecuteStrategy {

    protected void setErrorMsg(List<PurchaseEligibilityCheckResponseDto> dtos){
        if(CollectionUtils.isNotEmpty(dtos)){
            dtos.forEach(dto -> {
                if(StringUtils.isNotEmpty(dto.getCode())){
                    // 本次针对出险无忧复购：您的权益当前有效，此商品将在权益到期前XX天开放复购
                    // 故此需要获取定制化msg从而无法通过PurchaseConditionsTypeEnum进行获取
                    List<String> checks = Arrays.asList(PurchaseConditionsTypeEnum.CXWY_REPURCHASE_CHECK.getErrorCode(),
                            PurchaseConditionsTypeEnum.CXWY_GIVESTATUS_CHECK.getErrorCode());
                    if (checks.contains(dto.getCode())) {
                        dto.setMsg(dto.getMsg());
                    } else {
                        dto.setMsg(PurchaseConditionsTypeEnum.getMessageByErrorCode(dto.getCode()));
                    }
                }
            });
        }
    }
}
