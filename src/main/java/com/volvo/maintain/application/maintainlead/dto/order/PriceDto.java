package com.volvo.maintain.application.maintainlead.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * dacon
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "PriceDTO 对象", description = "PriceDTO")
public class PriceDto {

    @ApiModelProperty(value = "出发地经度", required = true)
    private Double startLng;
    @ApiModelProperty(value = "出发地纬度", required = true)
    private Double startLat;
    @ApiModelProperty(value = "目的地经度", required = true)
    private Double endLng;
    @ApiModelProperty(value = "目的地纬度", required = true)
    private Double endLat;
    @ApiModelProperty(value = "中转地经度 有暂存点需要传的参数")
    private Double middleLng;
    @ApiModelProperty(value = "中转地纬度 有暂存点需要传的参数")
    private Double middleLat;
    @ApiModelProperty(value = "预约时间 (yyyy-MM-dd HH:mm:ss)", required = true)
    private String bookingTime;

    @ApiModelProperty(value = "经销商", required = true)
    private String ownerCode;
    
    @ApiModelProperty(value = "手机号")
    private String phone;
    
    @ApiModelProperty(value = "订单号")
    private String orderCode;

}