package com.volvo.maintain.application.maintainlead.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TireInfoVo {

    private String brand;

    private String specs;

    private String sequence;

    private String fileId;



    private String ownerCode;

    private String productNo;

    private String vin;

    private Integer itemType;



    // 给易保的参数属性
    /**
     * 轮胎序列号
     */
    private String tireSerialNo;

    /**
     * 轮胎品牌
     */
    private String tireModel;

    /**
     * 轮胎规格
     */
    private String tireSize;

    /**
     * 轮胎批次号
     */
    private String batchNo;

    private String tireImg;






}
