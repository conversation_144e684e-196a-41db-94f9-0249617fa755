package com.volvo.maintain.application.maintainlead.vo;

import lombok.Data;
import java.io.Serializable;

/**
 * 终检内容清单子项VO
 */
@Data
public class FinalInspectionContentItemVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 文件清单类型 */
    private String contentType;

    /** 唯一code值 */
    private String onlyCode;

    /** 描述 */
    private String describe;

    /** 值 */
    private String value;
} 