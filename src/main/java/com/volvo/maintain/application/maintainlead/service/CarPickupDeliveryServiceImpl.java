package com.volvo.maintain.application.maintainlead.service;


import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.CarPickupCarPhotosResDto;
import com.volvo.maintain.application.maintainlead.dto.CarPickupDeliveryBalanceResDto;
import com.volvo.maintain.application.maintainlead.dto.CarPickupDeliveryCheckBalanceResDto;
import com.volvo.maintain.application.maintainlead.dto.RequestDto;
import com.volvo.maintain.application.maintainlead.dto.order.FindMerchantPriceDTO;
import com.volvo.maintain.application.maintainlead.dto.order.PriceDto;
import com.volvo.maintain.application.maintainlead.vo.order.PriceVo;
import com.volvo.maintain.infrastructure.gateway.DominAutoFeign;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import com.yonyou.cyx.function.exception.ServiceBizException;

import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;


@ApiModel
@Service
@Slf4j
public class CarPickupDeliveryServiceImpl implements CarPickupDeliveryService {
	
	@Resource
	private DominAutoFeign dominAutoFeign;
	
	@Override
	public CarPickupDeliveryCheckBalanceResDto checkBalance(PriceDto priceDto) {
		log.info("CarPickupDeliveryServiceImpl-checkBalance: PriceDto: {}", JSON.toJSONString(priceDto));
		// 查询余额
		List<CarPickupDeliveryBalanceResDto> queryBalance = queryBalance(priceDto.getOwnerCode());
		List<CarPickupDeliveryBalanceResDto> eDaijia = queryBalance.stream().filter(Objects::nonNull).filter(obj->Objects.equals(obj.getType(), "1")).collect(Collectors.toList());
		CarPickupDeliveryCheckBalanceResDto carPickupDeliveryCheckBalanceResDto = new CarPickupDeliveryCheckBalanceResDto();
		if(CollectionUtils.isEmpty(eDaijia)) {
			carPickupDeliveryCheckBalanceResDto.setPopupMessage("");
			carPickupDeliveryCheckBalanceResDto.setType("84141001");
			log.info("滴滴未开通默认不弹窗: {}", JSON.toJSONString(carPickupDeliveryCheckBalanceResDto));
			return carPickupDeliveryCheckBalanceResDto;
		}
		// 查询预估金额接口
		RequestDto<PriceDto> requestDto = new RequestDto<>();
		requestDto.setData(priceDto);
		MidResponse<List<PriceVo>> estimatePrice = dominAutoFeign.estimatePrice(requestDto.getData());
		if (estimatePrice.isFail()) {
			throw new ServiceBizException("订单中心异常:" + estimatePrice.getReturnMessage());
		}
		List<PriceVo> data = estimatePrice.getData();
		log.info("CarPickupDeliveryServiceImpl-estimatePrice: PriceVo: {}", JSON.toJSONString(data));

		Map<String, PriceVo> collect = data.stream().filter(Objects::nonNull).collect(Collectors.toMap(PriceVo::getType, Function.identity(), (k1,k2)->k1));
		
		boolean flagDiDi = false;
		boolean flagEdj = false;
		
		for (CarPickupDeliveryBalanceResDto carPickupDeliveryBalanceRes : queryBalance) {			
			PriceVo priceVo = collect.get(carPickupDeliveryBalanceRes.getType());
			BigDecimal totalFee = priceVo.getTotalFee();
			String price = carPickupDeliveryBalanceRes.getPrice();
			if(Objects.equals(carPickupDeliveryBalanceRes.getType(), "0") && totalFee.compareTo(new BigDecimal(price))>0) {
				flagEdj = true;
			} else if(new BigDecimal(price).compareTo(BigDecimal.ZERO)<=0) {
				flagDiDi = true;
			}
		}
		
		if(flagEdj && !flagDiDi) {
			carPickupDeliveryCheckBalanceResDto.setPopupMessage("e代驾账户余额不足，当前只能向滴滴服务商进行下单，还请确认是否继续下单？");
			carPickupDeliveryCheckBalanceResDto.setType("84141002");
		}
		if(!flagEdj && flagDiDi) {
			carPickupDeliveryCheckBalanceResDto.setPopupMessage("滴滴账户余额不足，当前只能向e代驾服务商进行下单，还请确认是否继续下单？");
			carPickupDeliveryCheckBalanceResDto.setType("84141002");
		}
		if(flagEdj && flagDiDi) {
			carPickupDeliveryCheckBalanceResDto.setPopupMessage("e代驾&滴滴账户余额不足，无法下单");
			carPickupDeliveryCheckBalanceResDto.setType("84141003");
		}
		if(!flagEdj && !flagDiDi) {
			carPickupDeliveryCheckBalanceResDto.setPopupMessage("");
			carPickupDeliveryCheckBalanceResDto.setType("84141001");
		}
		log.info("carPickupDeliveryCheckBalanceResDto: {}", JSON.toJSONString(carPickupDeliveryCheckBalanceResDto));
		return carPickupDeliveryCheckBalanceResDto;
	}

	@Override
	public List<CarPickupDeliveryBalanceResDto> queryBalance(String ownerCode) {
		log.info("CarPickupDeliveryServiceImpl-queryBalance: ownerCode: {}", ownerCode);
		// 查询余额
		
		FindMerchantPriceDTO findMerchantPriceDTO = new FindMerchantPriceDTO();
		findMerchantPriceDTO.setOwnerCode(ownerCode);
		
		MidResponse<List<CarPickupDeliveryBalanceResDto>> findMerchantPrice = dominAutoFeign.findMerchantPrice(findMerchantPriceDTO);
		if (findMerchantPrice.isFail()) {
			throw new ServiceBizException("订单中心异常:" + findMerchantPrice.getReturnMessage());
		}
		
		log.info("CarPickupDeliveryServiceImpl-queryBalance: response: {}", JSON.toJSONString(findMerchantPrice));
		return findMerchantPrice.getData();
	}

	@Override
	public CarPickupCarPhotosResDto getCarPhotos(String orderId) {
		log.info("CarPickupDeliveryServiceImpl-getCarPhotos: orderId: {}", orderId);
		MidResponse<CarPickupCarPhotosResDto> carPhotos = dominAutoFeign.getCarPhotos(orderId);
		if (carPhotos.isFail()) {
			throw new ServiceBizException("订单中心异常:" + carPhotos.getReturnMessage());
		}
		//补充其他字段查询
		
		return carPhotos.getData();
	}
	
}




