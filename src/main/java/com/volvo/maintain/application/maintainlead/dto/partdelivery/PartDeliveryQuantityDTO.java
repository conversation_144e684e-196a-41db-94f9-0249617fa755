package com.volvo.maintain.application.maintainlead.dto.partdelivery;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PartDeliveryQuantityDTO {

	/**
	 * 经销商
	 */
    private String ownerCode;
    /**
     * 采购订单号
     */
    private String purchaseNo;
    
    /**
     * 入库单号
     */
    private String stockInNo;
    
    /**
     * 类型
     */
    private String type;
    
    /**
     * 零件明细
     */
	@ApiModelProperty(value = "零件明细", name = "partDetails")
    private List<QueryBuyPartInfoRepDTO> partDetails;
}
