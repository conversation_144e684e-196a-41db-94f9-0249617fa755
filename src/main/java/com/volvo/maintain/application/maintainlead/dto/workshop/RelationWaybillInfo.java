package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RelationWaybillInfo {

    /**
     * 改址新单号，长度1-50；如果该字段有值，说明存在关联的改址单
     */
    private String readdressWaybillNos;

    /**
     * 逆向新单号，长度1-100；如果该字段有值，说明存在关联的逆向单，如果存在多次逆向，会返回多个逆向单，且逗号分隔
     */
    private String reverseWaybillNos;


}
