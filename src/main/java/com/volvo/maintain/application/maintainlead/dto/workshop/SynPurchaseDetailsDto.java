package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SynPurchaseDetailsDto implements Serializable {

    private String ownerCode;

    private String purchaseNo;

    private String partNo;

    private BigDecimal intoQuantity;
}
