package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 功能描述：字段转译修改信息
 *
 * <AUTHOR>
 * @date 2024/01/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("字段转译修改信息")
public class FieldTranslationDto {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 标签code
     */
    private String tagId;
    /**
     * 显示名称
     */
    private String showName;
    /**
     * 标签类型
     */
    private String tagType;
    /**
     * 转译规则
     */
    private TagValueRuleDto.ConvertDto convert;

}
