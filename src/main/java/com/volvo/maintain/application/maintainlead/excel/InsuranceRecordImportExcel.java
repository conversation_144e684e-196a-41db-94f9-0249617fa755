package com.volvo.maintain.application.maintainlead.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "新续保线索Excel导入实体")
public class InsuranceRecordImportExcel {

	/**
	 * VIN
	 */
	@Excel(name = "* VIN", orderNum = "1")
	private String vin;
	/**
	 * 客户姓名
	 */
	@Excel(name = "* 客户姓名", orderNum = "2")
	private String name;
	/**
	 * 客户电话
	 */
	@Excel(name = "* 电话", orderNum = "3")
	private String tel;

	/**
	 * 商业险到期日期
	 */
	@Excel(name = "* 店端续保到期日期", orderNum = "4")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date storeInsuranceExpiryDate;

	/**
	 * 续保客户类型(81761001 新保客户  81761002 新转续  81761003  续转续   81761004 在修不在保)
	 */
	@Excel(name = "* 续保客户类型", orderNum = "5")
	private String insuranceType;

	/**
	 * 保险公司名称
	 */
	@Excel(name = "* 保险公司", orderNum = "6")
	private String insuranceName;

	/**
	 * 跟进人员
	 */
	@Excel(name = "跟进人员", orderNum = "7")
	private String saId;

}
