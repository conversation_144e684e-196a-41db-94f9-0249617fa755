package com.volvo.maintain.application.maintainlead.vo.workshop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ETADocumentParamsVo {

    private String ownerCode;

    /**
     * 采购订单号
     */
    private String purchaseNo;

    /**
     * 零件状态,替换件;block 81181001 替换件   81181002 block
     */
    private Integer partsStatus;

    /**
     * 替换件号
     */
    private String replaceParts;

    /**
     * ETA时间
     */
    private String expectedDeliveryTime;

    /**
     * 零件号
     */
    private String partsNo;

    private Long id;


}
