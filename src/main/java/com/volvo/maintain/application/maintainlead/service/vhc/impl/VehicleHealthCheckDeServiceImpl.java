package com.volvo.maintain.application.maintainlead.service.vhc.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.volvo.exception.ServiceBizException;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.*;
import com.volvo.maintain.application.maintainlead.dto.workshop.SceneMessageRemindDto;
import com.volvo.maintain.application.maintainlead.mq.producer.WorkshopMessageReminderProducer;
import com.volvo.maintain.application.maintainlead.service.vhc.VehicleHealthCheckDeService;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.constants.VhcConstants;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.CommonConfigVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 车辆健康检查
 * @Date 2024/9/25 15:56
 */
@Service
@AllArgsConstructor
@Slf4j
public class VehicleHealthCheckDeServiceImpl implements VehicleHealthCheckDeService {


    private final DomainMaintainOrdersFeign domainMaintainOrdersFeign;
    private final DmscloudServiceFeign dmscloudServiceFeign;
    private final WorkshopMessageReminderProducer workshopMessageReminderProducer;

    @Override
    public VehicleHealthCheckDetailDto getVehicleHealthCheckDetail(VehicleHealthCheckDetailParamDto vehicleHealthCheckDetailParamDto) {
        //查询维修项目代码
        DmsResponse<CommonConfigDto> configByKey = dmscloudServiceFeign.getConfigByKey(VhcConstants.VHC_LABOUR_CODE,VhcConstants.VHC_CODE);
        log.info("getVehicleHealthCheckDetail configByKey :{}",configByKey);
        if (ObjectUtils.isEmpty(configByKey) && Objects.isNull(configByKey.getData())) {
            log.info("getVehicleHealthCheckDetail 查询VHC工时code为空");
            return null;
        }
        vehicleHealthCheckDetailParamDto.setLabourCode(configByKey.getData().getConfigValue());
        //查询特殊大类，其它小类，以及类目排序信息
        DmsResponse<List<CommonConfigVO>> notNormalClassIdConfigDto = dmscloudServiceFeign.getConfigList(VhcConstants.VHC_NOT_NORMAL_CONFIG_CLASS_ID,"");
        log.info("notNormalClassIdConfigDto :{}", JSON.toJSONString(notNormalClassIdConfigDto));
        if (notNormalClassIdConfigDto.isFail() || CollectionUtils.isEmpty(notNormalClassIdConfigDto.getData())) {
            throw new ServiceBizException("请配置特殊以及其它配置大类id");
        }
        List<Integer> notNormalConfigClassId = notNormalClassIdConfigDto.getData().stream().map(e -> Arrays.asList(e.getConfigValue().split(","))).flatMap(List::stream).map(Integer::parseInt).collect(Collectors.toList());
        vehicleHealthCheckDetailParamDto.setNotNormalConfigClassId(notNormalConfigClassId);
        //查询排序信息
        DmsResponse<List<CommonConfigVO>> vhcClassTypeOrderList = dmscloudServiceFeign.getConfigList(VhcConstants.VHC_VHC_CLASS_TYPE_ORDER,"");
        log.info("vhcClassTypeOrderList :{}",JSON.toJSONString(vhcClassTypeOrderList));
        if (vhcClassTypeOrderList.isFail() || CollectionUtils.isEmpty(vhcClassTypeOrderList.getData())) {
            throw new ServiceBizException("请配置车辆健康检查类目排序信息");
        }
        Map<String, Integer> vhcClassSortMap = vhcClassTypeOrderList.getData().stream().collect(Collectors.toMap(CommonConfigVO::getConfigDesc, e -> Integer.parseInt(e.getConfigValue())));
        vehicleHealthCheckDetailParamDto.setVhcClassSortMap(vhcClassSortMap);
        return domainMaintainOrdersFeign.getVehicleHealthCheckDetail(vehicleHealthCheckDetailParamDto).getData();
    }

    @Override
    public List<VhcItemConfigInfoDto> getVhcItemInfoByClassId(Integer classId, String configClassId) {
        return domainMaintainOrdersFeign.getVhcItemInfoByClassId(classId, configClassId).getData();
    }

    @Override
    public void saveVehicleHealthCheckInfo(VehicleHealthCheckInfoDto vehicleHealthCheckInfoDto) {
        List<VhcClassInfoDto> vhcClassInfoDtoList = vehicleHealthCheckInfoDto.getVhcClassInfoDtoList();
        Assert.notEmpty(vhcClassInfoDtoList, "大类不允许为空");
        if("1".equals(vehicleHealthCheckInfoDto.getCompleFlag())){
            //2生成车辆健康检查编号
            DmsResponse<VhcNoDto> vhcNoResponse = dmscloudServiceFeign.createVhcNo(vhcClassInfoDtoList.get(0).getOwnerCode(),VhcConstants.VHC_BJ_NO);
            log.info("vhcNoResponse :{}", vhcNoResponse);
            if (Objects.isNull(vhcNoResponse) || vhcNoResponse.isFail() || Objects.isNull(vhcNoResponse.getData())) {
                log.info("生成车辆报价单编号异常");
                return;
            }
            vehicleHealthCheckInfoDto.setVhcPricesheetNo(vhcNoResponse.getData().getVhcNo());
        }
        //查询特殊大类，其它小类，以及类目排序信息
        DmsResponse<List<CommonConfigVO>> notNormalClassIdConfigDto = dmscloudServiceFeign.getConfigList(VhcConstants.VHC_NOT_NORMAL_CONFIG_CLASS_ID,"");
        log.info("notNormalClassIdConfigDto :{}", JSON.toJSONString(notNormalClassIdConfigDto));
        if (notNormalClassIdConfigDto.isFail() || CollectionUtils.isEmpty(notNormalClassIdConfigDto.getData())) {
            throw new ServiceBizException("请配置特殊以及其它配置大类id");
        }
        Map<String, List<Integer>> notNormalConfigClassIdMap = notNormalClassIdConfigDto.getData().stream()
                .collect(Collectors.toMap(CommonConfigVO::getConfigKey, commonConfigVO ->
                        Arrays.stream(commonConfigVO.getConfigValue().split(","))
                                .map(Integer::parseInt)
                                .collect(Collectors.toList()))
                );
        vehicleHealthCheckInfoDto.setNotNormalConfigClassIdMap(notNormalConfigClassIdMap);
        domainMaintainOrdersFeign.saveVehicleHealthCheckInfo(vehicleHealthCheckInfoDto);
        if("1".equals(vehicleHealthCheckInfoDto.getCompleFlag())){
            sendTouchpointMessage(vehicleHealthCheckInfoDto.getOwnerCode(),vehicleHealthCheckInfoDto.getRoNo(), CommonConstant.WORK_SHOP_VHC_INSPECT);
        }
    }

    private void sendTouchpointMessage(String ownerCode, String roNo,String sceneType) {
        //发送VHC报价
        SceneMessageRemindDto sceneMessageRemindDto = new SceneMessageRemindDto();
        sceneMessageRemindDto.setOwnerCode(ownerCode);
        sceneMessageRemindDto.setBusinessId(roNo);
        sceneMessageRemindDto.setSceneType(sceneType);
        sceneMessageRemindDto.setBusinessParameter(new JSONObject());
        // 发送消息
        workshopMessageReminderProducer.sendOrderMsg(sceneMessageRemindDto, roNo);
    }
}
