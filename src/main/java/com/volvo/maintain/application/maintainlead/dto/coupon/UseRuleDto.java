package com.volvo.maintain.application.maintainlead.dto.coupon;

import com.yonyou.cyx.framework.bean.dto.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * UseRule
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-30
 */
@Data
@ApiModel(value = "UseRule", description = "UseRule")
public class UseRuleDto extends BaseDTO {

    @ApiModelProperty(value = "限制经销商(空表示全部)")
    private List<String> limitDealer;
    @ApiModelProperty(value = "维修类型代码(空表示全部)（G:保修 I:事故 M:保养 N:机电维修 P:PDS S:零售）")
    private List<String> limitRepairType;
    @ApiModelProperty(value = "使用门槛（空表示无门槛）")
    private Double useThreshold;
    @ApiModelProperty(value = "使用规则-是否可叠加优惠券（1:不可叠加 2:可无限叠加 3:可有限叠加）")
    private Integer superimposedCoupons;
    @ApiModelProperty(value = "使用规则-是否可叠加同类优惠券（1:不可叠加 2:可叠加）")
    private Integer superimposeSimilarCoupons;
    @ApiModelProperty(value = "可叠加卡券（空表示无可叠加卡券）")
    private List<String> overlapCoupon;
    @ApiModelProperty(value = "需要激活（1：是）")
    private Integer needActive;
    @ApiModelProperty(value = "车型（空表示全部车型 否则传入车型code列表）")
    private List<String> model;
    @ApiModelProperty(value = "适用车辆范围（车辆vin列表）")
    private List<String> vehicleRange;
    @ApiModelProperty(value = "有效类目（L1[],L2[],L3[]）")
    private List<List<String>> validCategory;
    @ApiModelProperty(value = "激活条件（字典）")
    private Integer activationConditions;
    @ApiModelProperty(value = "使用规则(最小车龄-含)")
    private Integer useruleMincarage;
    @ApiModelProperty(value = "使用规则(最大车龄-不含)")
    private Integer useruleMaxcarage;
    @ApiModelProperty(value = "使用规则(高级设置)")
    private UseRuleAdvancedSettingDto useruleAdvancedsettings;
}