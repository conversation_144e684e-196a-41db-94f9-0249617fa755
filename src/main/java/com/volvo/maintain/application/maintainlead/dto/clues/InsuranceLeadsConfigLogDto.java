package com.volvo.maintain.application.maintainlead.dto.clues;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 续保经销商规则修改记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InsuranceLeadsConfigLogDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 原线索分配类型
     * 线索分配类型 96061001 单店保护, 96061002 解除单店保护
     */
    private Integer oldAllocationType;

    /**
     * 原提前天数
     */
    private Integer oldAdvanceDays;

    /**
     * 原更新时间
     */
    private String oldUpdateDate;

    /**
     * 原修改人
     */
    private String oldCreatedBy;

    /**
     * 新线索分配类型
     * 线索分配类型 96061001 单店保护, 96061002 解除单店保护
     */
    private Integer allocationType;

    /**
     * 新提前天数
     */
    private Integer advanceDays;

    /**
     * 新更新时间
     */
    private String updateDate;

    /**
     * 新修改人
     */
    private String createdBy;


}