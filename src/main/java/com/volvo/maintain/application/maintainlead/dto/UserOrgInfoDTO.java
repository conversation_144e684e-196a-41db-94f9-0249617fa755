package com.volvo.maintain.application.maintainlead.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserOrgInfoDTO  implements Serializable {

    private Long userId;

    private String account;

    private String username;

    private String employeeNameEn;

    private String idcardNumber;

    private String phone;

    private String email;

    private String createBy;

    private Date createTime;

    private String updateBy;

    private Date updateTime;

    private Integer accountStatus;

    private String province;

    private Integer gender;

    private String country;

    private Integer score;

    private String heardImgUrl;

    private Integer workYears;

    private String unionId;

    private String roleCode;

    private String roleName;

    private Long  	companyId;

    private String  orgCode;

    private String  orgName;

    private String  orgShortName;

    private String  orgDesc;

    private Integer orgType;

    private String    parentOrgId;

    private Integer dataType;

    private Integer dataSource;

    private Integer isValid;

    private Long    orgid;

    private String providerCode;

    private String providerAccount;

    private String cdsid;

}
