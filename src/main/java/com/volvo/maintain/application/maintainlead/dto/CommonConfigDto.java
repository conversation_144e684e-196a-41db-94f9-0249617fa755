package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("公共配置查询")
public class CommonConfigDto {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", name = "id")
    private Long id;

    /**
     * 配置代码
     */
    @ApiModelProperty(value = "配置代码",name = "config_code")
    private String configCode;

    /**
     * 分组类型
     */
    @ApiModelProperty(value = "分组类型",name = "group_type")
    private String groupType;

    /**
     * 配置key
     */
    @ApiModelProperty(value = "配置key",name = "config_key")
    private String configKey;

    /**
     * 配置值
     */
    @ApiModelProperty(value = "配置值",name = "config_value")
    private String configValue;

    /**
     * 配置描述
     */
    @ApiModelProperty(value = "配置描述",name = "config_desc")
    private String configDesc;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1",name = "config_ext1")
    private String configExt1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2",name = "config_ext2")
    private String configExt2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3",name = "config_ext3")
    private String configExt3;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注",name = "remark")
    private String remark;


    /**
     * 是否删除:1，删除；0，未删除
     */
    @ApiModelProperty("是否删除:1，删除；0，未删除")
    private Integer isDeleted;

    /**
     * 数据创建时间
     */
    @ApiModelProperty("数据创建时间")
    private Date createdAt;

    /**
     * 数据创建人
     */
    @ApiModelProperty("数据创建人")
    private String createdBy;

    /**
     * 数据修改时间
     */
    @ApiModelProperty("数据修改时间")
    private Date updatedAt;

    /**
     * 数据修改人
     */
    @ApiModelProperty("数据修改人")
    private String updatedBy;

    /**
     * 创建sql人
     */
    @ApiModelProperty("创建sql人")
    private String createSqlby;

    /**
     * 更新人sql人
     */
    @ApiModelProperty("更新人sql人")
    private String updateSqlby;

    private String dmsDefault;
}
