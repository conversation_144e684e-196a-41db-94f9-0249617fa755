package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.FollowUpPageDto;
import com.volvo.maintain.application.maintainlead.dto.inviteClue.InvitationScripDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.InvitationService;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainLeadFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.yonyou.cyx.function.exception.ServiceBizException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class InvitationServiceImpl implements InvitationService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private DomainMaintainLeadFeign domainMaintainLeadFeign;

    /**
     * 邀约话术管理查询
     */
    @Override
    public Page<InvitationScripDto> invitationScriptManageQuery(Integer current, Integer size, String typeCodeId, Integer isEnable, String title, String tagTalkskill) {
        List<Integer> list = null;
        if (StringUtils.isNotBlank(typeCodeId)) {
            list = Arrays.stream(typeCodeId.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        }
        DmsResponse<Page<InvitationScripDto>> result = domainMaintainLeadFeign.invitationScriptManageQuery(current, size, list, isEnable, title, tagTalkskill);
        logger.info("invitationScriptManageQuery->domainMaintainLeadFeign:{}",result);
        if (result.isFail()) {
            throw new ServiceBizException("获取邀约话术管理失败");
        }
        return result.getData();
    }

    /**
     * 邀约话术跟进页面渲染
     * @param followUpPageDto 渲染页面对象
     * @return 返回话术
     */
    @Override
    public List<InvitationScripDto> invitationScriptFollowUpPage(FollowUpPageDto followUpPageDto) {
        if (CollectionUtils.isEmpty(followUpPageDto.getCodes())) {
            return Collections.emptyList();
        }
        DmsResponse<List<InvitationScripDto>> result = domainMaintainLeadFeign.invitationScriptFollowUpPage(followUpPageDto);
        logger.info("invitationScriptFollowUpPage->domainMaintainLeadFeign:{}", result);
        if (result.isFail() || CollectionUtils.isEmpty(result.getData())) {
            logger.info("invitationScriptFollowUpPageResult:{}", result);
            return Collections.emptyList();
        }
        // 去重合并
        List<InvitationScripDto> datas = result.getData().stream().filter(dto -> isAtLeastOneNotBlank(dto.getVehicleTagIds(), dto.getCustomerTagIds())).map(i -> {
            String c = i.getCustomerTagIds();
            String v = i.getVehicleTagIds();
            String[] cts = StringUtils.isBlank(c) ? new String[0] : c.split(",");
            String[] vts = StringUtils.isBlank(v) ? new String[0] : v.split(",");
            // 使用Set去重
            Set<String> uniqueStrings = new HashSet<>();
            uniqueStrings.addAll(Arrays.asList(cts));
            uniqueStrings.addAll(Arrays.asList(vts));
            // 将Set转换为数组
            String[] mergedArray = uniqueStrings.toArray(new String[0]);
            // 写入新的合并标签信息
            i.setMergeTagIds(arrayToString(mergedArray));
            return i;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(datas)) {
            logger.info("invitationScriptFollowUpPageResult datas isEmpty");
            return Collections.emptyList();
        }
        // 权重匹配
        return weight(followUpPageDto.getCodes(), datas);
    }

    public  String arrayToString(String[] arr) {
        // 使用StringBuilder将数组转换为字符串
        StringBuilder sb = new StringBuilder();
        int index = 0;
        for (String s : arr) {
            index++;
            sb.append(s);
            if (index < arr.length) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    private  List<InvitationScripDto> weight(List<String> codes, List<InvitationScripDto> pms) {
        String str = String.join(",", codes);
        List<InvitationScripDto> sor = new ArrayList<>();
        //按长度排序,最长的排在前面
        List<InvitationScripDto> sortedList = pms.stream()
                .sorted((s1, s2) -> Integer.compare(s2.getMergeTagIds().length(), s1.getMergeTagIds().length()))
                .collect(Collectors.toList());
        for (InvitationScripDto s : sortedList) {
            if (str.length() >= s.getMergeTagIds().length() && containsAll(str, s.getMergeTagIds())) {
                add(sor, s);
            }
        }
        return sor;
    }

    private  boolean containsAll(String source, String target) {
        List<String> sourceList = Arrays.asList(source.split(","));
        List<String> targetList = Arrays.asList(target.split(","));
        return new HashSet<>(sourceList).containsAll(targetList);
    }

    private void add(List<InvitationScripDto> list, InvitationScripDto str) {
        if (list.size() == 0) {
            list.add(str);
            return;
        }
        boolean flag = true;
        for (InvitationScripDto s : list) {
            if (containsAll(s.getMergeTagIds(), str.getMergeTagIds()) && s.getMergeTagIds().length() != str.getMergeTagIds().length()) {
                flag = false;
            }
        }
        if (flag) {
            list.add(str);
        }
    }

    /**
     * 检查至少有一个提供的字符串是否不为空，且不是仅由空白字符组成。
     *
     * @param strings 要检查的字符串
     * @return 如果至少有一个字符串不为空且不是空白则返回true，否则返回false
     */
    public static boolean isAtLeastOneNotBlank(String... strings) {
        return strings != null && Arrays.stream(strings)
                .anyMatch(str -> str != null && !str.trim().isEmpty());
    }



    /**
     * 邀约话术管理查询详情
     * @param id
     * @return
     */
    @Override
    public InvitationScripDto invitationScriptManageDetail(Integer id) {
        DmsResponse<InvitationScripDto> result = domainMaintainLeadFeign.invitationScriptManageDetail(id);
        logger.info("invitationScriptManageDetail->domainMaintainLeadFeign:{}", result);
        if (result.isFail()) {
            throw new ServiceBizException("获取邀约话术管理详情失败");
        }
        return result.getData();
    }

    /**
     * 邀约话术保存
     * @param invitationScripDto
     * @return
     */
    @Override
    public Integer saveInvitationScript(InvitationScripDto invitationScripDto) {

        DmsResponse<Integer> result = domainMaintainLeadFeign.saveInvitationScript(invitationScripDto);
        if (result.isFail()) {
            throw new ServiceBizException("邀约话术管理保存失败");
        }
        return result.getData();
    }

    @Override
    public Integer updateInvitationScript(InvitationScripDto invitationScripDto) {
        DmsResponse<Integer> result = domainMaintainLeadFeign.updateInvitationScript(invitationScripDto);
        if (result.isFail()) {
            throw new ServiceBizException("邀约话术管理保存失败");
        }
        return result.getData();    }

    @Override
    public Integer deleteInvitationScript(Integer id) {
        DmsResponse<Integer> result = domainMaintainLeadFeign.deleteInvitationScript(id);
        if (result.isFail()) {
            throw new ServiceBizException("邀约话术管理保存失败");
        }
        return result.getData();
    }

    @Override
    public List<InvitationScripDto> scriptManageQuery(List<String> tags, String typeCodeId) {
        if (CollectionUtils.isEmpty(tags) || StringUtils.isBlank(typeCodeId)) {
            return Collections.emptyList();
        }
        logger.info("scriptManageQuery->tags:" + tags);
        DmsResponse<List<InvitationScripDto>> result = domainMaintainLeadFeign.scriptManageQuery(tags, typeCodeId);
        logger.info("scriptManageQuery->domainMaintainLeadFeign:{}", result);
        if (result.isFail()) {
            return Collections.emptyList();
        }
        return result.getData();
    }
}
