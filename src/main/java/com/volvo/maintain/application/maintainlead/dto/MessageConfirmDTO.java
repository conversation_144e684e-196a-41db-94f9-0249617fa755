package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("弹窗确认dto")
public class MessageConfirmDTO {
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务id
     */
    private String businessId;
    /**
     * 经销商code
     */
    private String ownerCode;
    /**
     * 弹窗内容
     */
    private String popupContent;
    /**
     * 群组id
     */
    private String groupId;

}