package com.volvo.maintain.application.maintainlead.service.faultLight.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.ClueDataDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadOperationResultDto;
import com.volvo.maintain.application.maintainlead.dto.company.CompanyNewSelectDto;
import com.volvo.maintain.application.maintainlead.dto.faultlight.ClueNotifyDto;
import com.volvo.maintain.application.maintainlead.dto.faultlight.FaultLightClueResponseDto;
import com.volvo.maintain.application.maintainlead.dto.faultlight.FaultLightTopicDto;
import com.volvo.maintain.application.maintainlead.dto.faultlight.TtFaultLightCluePo;
import com.volvo.maintain.application.maintainlead.service.faultLight.FaultLightService;
import com.volvo.maintain.infrastructure.enums.FullLeadsErrorEnum;
import com.volvo.maintain.infrastructure.enums.RedisEnum;
import com.volvo.maintain.infrastructure.enums.faultLight.FaultClueStateEnum;
import com.volvo.maintain.infrastructure.enums.faultLight.FaultFollowStateEnum;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainLeadFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndOrgCenterFeign;
import com.volvo.maintain.infrastructure.gateway.response.CompanyDetailDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.utils.RedisLockUtil.DistributedLockUtil;
import com.volvo.maintain.infrastructure.utils.RedisLockUtil.IDistributedLock;
import com.yonyou.cyx.function.exception.DALException;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RefreshScope
public class FaultLightServiceImpl implements FaultLightService {

    private static final int MAX_LENGTH = 200;

    @Resource
    private DomainMaintainLeadFeign domainMaintainLeadFeign;

    @Resource
    private MidEndOrgCenterFeign midEndOrgCenterFeign;

    @Autowired
    private FaultLightClueServiceHelper faultLightClueServiceHelper;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${topic.faultLight:TOPIC_FAULT_LIGHT}")
    private String faultLightTopic;

    @Override
    public void subComments(LeadOperationResultDto dto) {
        ClueDataDto.CallInfo callInfo = dto.getData().getCallInfo();
        if (ObjectUtils.isEmpty(callInfo)) {
            return;
        }
        String comments = callInfo.getComments();
        if (ObjectUtils.isNotEmpty(comments)) {
            comments = StrUtil.maxLength(comments, MAX_LENGTH);
            callInfo.setComments(comments);
        }
    }

    /**
     * 线索同步接口
     *
     * @param dto ClueDataSynchroDTO
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean doClueDataSynchro(LeadOperationResultDto dto,String dealer) {
        log.info("doClueDataSynchro,start,dto:{}", JSON.toJSONString(dto));
        long startTime = new Date().getTime();
        Long icmId = dto.getId();
        int num = 0;
        if (icmId == null) {
            throw new ServiceBizException("icmId不正确");
        }

        IDistributedLock lock = DistributedLockUtil.getDistributedLock(
                RedisEnum.CLUE_DATA_SYNCHRO.getKey(icmId), RedisEnum.CLUE_DATA_SYNCHRO.getTimeoutMsecs());

        if (lock.acquire()) {//获取锁
            try {
                //备注字段截取长度255,存200
                subComments(dto);
                //数据快照
                num = this.addClueCompensate(dto,dealer);
            } catch (Exception e) {
                log.error("doClueDataSynchro: ", e);
                throw new RuntimeException(e);
            } finally {
                lock.release(); //释放
                long endTime = new Date().getTime();
                log.info("doClueDataSynchro,lock,end:{}", endTime - startTime);
            }
        }
        return num > 0;
    }

    /**
     * 线索录入补偿
     *
     * @param dto ClueDataSynchroDTO
     * @return int
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addClueCompensate(LeadOperationResultDto dto, String dealer) {
        setCompanyDetail(dto, dealer);

        DmsResponse<FaultLightClueResponseDto> integerDmsResponse = domainMaintainLeadFeign.addClueCompensate(dto);
        if (integerDmsResponse.isFail()) {
            throw new ServiceBizException("调用领域线索失败！");
        }
        FaultLightClueResponseDto data = integerDmsResponse.getData();
        //待预约MQ发送,邮件短信通知
        doMess(dto, data.getDate(), data.getTtFaultLightCluePo(), data.isFlag(), data.getComments(), dealer);
        log.info("doClueDataSynchro,end:{}", 1);
        return 1;
    }

    @Override
    public void setCompanyDetail(LeadOperationResultDto dto, String dealer) {
        //查询经销商对于地区
        List<CompanyDetailDTO> listCode = this.getCompanyInfoListByDealerCodeList(
                Stream.of(dealer).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(listCode)) {
            dto.setCompanyDetailDto(listCode.get(0));
            log.info("doClueDataSynchro,comDTO:{}", JSON.toJSONString(dto.getCompanyDetailDto()));
        }
    }

    @Override
    public void doMess(LeadOperationResultDto dto, Date date, TtFaultLightCluePo cluePO, boolean flag, String comments, String dealer) {
        if (flag) {
            log.info("doMess,邮件短信通知,date:{},cluePO:{},comments:{},dealer:{}", date, cluePO, comments, dealer);
            ClueNotifyDto notifyDto = ClueNotifyDto.builder()
                    .dealerCode(dealer)
                    .vehicleVin(cluePO.getVin())
                    .faultCityName(cluePO.getFaultCityName())
                    .clueGenTimeDate(cluePO.getClueGenTime())
                    .warningName(cluePO.getFaultCn())
                    .clueDisTimeDate(date)
                    .comments(comments)
                    .build();
            log.info("doMess,notifyDto:{}", notifyDto);
            faultLightClueServiceHelper.doClueNotify(notifyDto);
            log.info("doMess,待预约MQ发送");
            //MQ状态同步
            this.pushMessage(cluePO.getIcmId(),
                    String.valueOf(cluePO.getFollowStatus()),
                    String.valueOf(cluePO.getClueStatus()),
                    date);
        }
    }

    private List<CompanyDetailDTO> getCompanyInfoListByDealerCodeList(List<String> dealerCodeList) {
        if ( CollectionUtils.isEmpty( dealerCodeList ) ) {
            throw new DALException( "经销商代码列表为空" );
        }
        CompanyNewSelectDto companyNewSelectDto = new CompanyNewSelectDto();
        companyNewSelectDto.setCompanyCodes( dealerCodeList );
        RestResultResponse<List<CompanyDetailDTO>> response = midEndOrgCenterFeign.companyInfo(companyNewSelectDto);
        log.info("区域信息获取结果：{}", JSONObject.toJSONString(response));
        if (org.springframework.util.ObjectUtils.isEmpty(response)) {
            throw new ServiceBizException(FullLeadsErrorEnum.ERROR_QUERY_AREA.getDesc());
        }
        return response.getData();
    }


    @Override
    public void pushMessage(Long id, String followUpStatus, String bizStatus, Date updateTime) {
        log.info("pushMessage,start,id: {},followUpStatus: {},bizStatus: {},updateTime: {}", id, followUpStatus, bizStatus, updateTime);
        FaultLightTopicDto dto = new FaultLightTopicDto();
        dto.setBizStatus(bizStatus);
        dto.setFollowUpStatus(followUpStatus);
        dto.setId(id);
        dto.setUpdateTime(updateTime);
        this.push(dto);
    }

    private void push(Object body) {
        try {
            rocketMQTemplate.asyncSend(faultLightTopic, new Message<String>() {
                @Override
                public String getPayload() {
                    return JSON.toJSONString(body);
                }

                @Override
                public MessageHeaders getHeaders() {
                    HashMap<String, Object> headers = Maps.newHashMap();
                    return new MessageHeaders(headers);
                }
            }, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("pushMessage,消息投递成功,result:{}", JSON.toJSONString(sendResult));
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("pushMessage,消息投递失败:{}", throwable);
                }
            });
        } catch (Exception e) {
            log.error("pushMessage,推送消息失败:{}", e);
        }
    }

}
