package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("cdp查询会员")
public class CdpCustomerParamsDto {
    //请求业务终端（CIC、liteCrm、NewBieMall、newbie）
    private String appid;
    //访问端权限标识
    private String token;
    //时间戳
    private long access_timestamp;
    /**
     * 档案类型:客户档案或车辆档案
     * 客户档案名称：customer_profilebase
     * 车辆档案名称：vehicle_profilebase
     */
    private String subject_profile_type;
    //需要查询的vin, 校验是否17位，非17位时返回错误信息"输入vin 码有误"
    private String subject_id;
    //subject_id支持的Id的name，vin 明文(暂仅支持“车辆VIN码”)
    private String subject_id_name;
    /**
     * 需要关联的档案类型:客户档案或车辆档案
     * 客户档案名称：customer_profilebase
     * 车辆档案名称：vehicle_profilebase
     */
    private String object_profile_type;
    //需要返回的Id的name，目前包括：id_member_id，id_mobile（解密后的），customer_oneid
    private String object_profile_name;
    //人车关系类型，大小写不敏感，如果为空则返回存在的所有关系，或者不在type范围内则返回无效关系类型，并在message里说明。否则会返回有效关系类型的结果。
    private List<String> relation_type;

    private List<String> mobile;




}
