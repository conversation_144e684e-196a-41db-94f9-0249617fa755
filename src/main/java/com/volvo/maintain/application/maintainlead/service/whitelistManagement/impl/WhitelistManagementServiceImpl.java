package com.volvo.maintain.application.maintainlead.service.whitelistManagement.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.maintain.application.maintainlead.dto.CheckVehicleDTO;
import com.volvo.maintain.application.maintainlead.dto.RequestDto;
import com.volvo.maintain.application.maintainlead.dto.company.DataInputVo;
import com.volvo.maintain.application.maintainlead.dto.company.TePhoneImportDto;
import com.volvo.maintain.application.maintainlead.dto.company.TeVehicleImportDto;
import com.volvo.maintain.application.maintainlead.dto.white.WhiteListDto;
import com.volvo.maintain.application.maintainlead.service.whitelistManagement.WhitelistManagementService;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainAuthFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndVehicleCenterFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.CheckVehicleExistsVo;
import com.volvo.maintain.interfaces.vo.white.VehicleHealthCheckWhiteListVo;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import com.yonyou.cyx.function.utils.common.CommonUtils;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Service
@Slf4j
public class WhitelistManagementServiceImpl implements WhitelistManagementService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * fegin
     */
    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;

    @Autowired
    private DomainMaintainAuthFeign maintainAuthFeign;

    @Override
    public Page<VehicleHealthCheckWhiteListVo> selectWhitelist(String modType, String itemCode, String isDeleted, int currentPage, int pageSize) {
        logger.info("selectWhitelist,modType:{},itemCode:{},isDeleted:{}", modType, itemCode, isDeleted);
        try {
            // 调用Feign接口
            DmsResponse<Page<VehicleHealthCheckWhiteListVo>> listDmsResponse = maintainAuthFeign.selectWhitelist(modType, itemCode, isDeleted, currentPage, pageSize);

            // 检查响应是否成功
            if (listDmsResponse.isFail()) {
                logger.error("Failed to fetch whitelist. Response Code={}, Response Message={}", listDmsResponse.getResultCode(), listDmsResponse.getErrMsg());
            }

            Page<VehicleHealthCheckWhiteListVo> dataList = listDmsResponse.getData();
            // 对返回数据的非空和非空列表校验
            if (ObjectUtils.isEmpty(dataList)) {
                return null;
            } else {
                return dataList;
            }
        } catch (Exception e) {
            // 处理Feign调用异常
            logger.error("Feign exception when fetching whitelist. modType={}, itemCode={}, isDeleted={}", modType, itemCode, isDeleted, e);
        }
        return null;
    }

    @Override
    public List<VehicleHealthCheckWhiteListVo> selectWhitelistDetail(String itemCode, String rosterType) {
        logger.info("selectWhitelistDetail, itemCode:{}, rosterType:{}", itemCode, rosterType);
        try {
            // 调用Feign接口
            DmsResponse<List<VehicleHealthCheckWhiteListVo>> listDmsResponse = maintainAuthFeign.selectWhitelistDetail(itemCode, rosterType);

            // 检查响应是否成功
            if (listDmsResponse.isFail()) {
                logger.error("selectWhitelistDetail,listDmsResponse. Response Code={}, Response Message={}", listDmsResponse.getResultCode(), listDmsResponse.getErrMsg());
                return Collections.emptyList(); // 返回空列表，而不是null
            }

            List<VehicleHealthCheckWhiteListVo> dataList = listDmsResponse.getData();
            // 对返回数据的非空和非空列表校验
            if (CollectionUtils.isEmpty(dataList)) {
                logger.info("The whitelist data returned is null. itemCode={}", itemCode);
                return Collections.emptyList(); // 返回空列表，而不是null
            } else {
                return dataList;
            }
        } catch (FeignException e) {
            // 处理Feign调用异常
            logger.error("Feign exception when fetching whitelist. itemCode={}", itemCode, e);
        }
        return null;
    }

    @Override
    public int insertWhitelist(WhiteListDto whiteListDto) {
        logger.info("insertWhitelist,ModType:{}, isDeleted:{}, rosterType:{}", whiteListDto.getModType(), whiteListDto.getIsDeleted(), whiteListDto.getRosterType());
        // 获取登录用户信息
        CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();
        try {
            whiteListDto.setCreatedBy(loginInfoDto.getUserId());
            // 调用Feign接口
            DmsResponse<Integer> integerDmsResponse = maintainAuthFeign.insertWhitelist(whiteListDto);

            // 检查响应是否成功
            if (integerDmsResponse.isFail()) {
                logger.error("insertWhitelist,Code={}, Response Message={}", integerDmsResponse.getResultCode(), integerDmsResponse.getErrMsg());
                return 0; // 返回空列表，而不是null
            }

            Integer data = integerDmsResponse.getData();
            // 对返回数据的非空和非空列表校验
            if (ObjectUtils.isEmpty(data)) {
                logger.info("insertWhitelist.whiteListDto={}", whiteListDto);
                return 0; // 返回空列表，而不是null
            } else {
                return data;
            }
        } catch (FeignException e) {
            // 处理Feign调用异常
            logger.error("Feign exception when fetching insertWhitelist. whiteListDto={}", whiteListDto, e);
        }
        return 0;
    }

    @Override
    public int toggleWhitelistActivation(String itemCode, String isDeleted, String rosterType) {
        logger.info("toggleWhitelistActivation,itemCode:{}, isDeleted:{}, rosterType:{}", itemCode, isDeleted,rosterType);
        try {
            // 获取登录用户信息
            CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();
            // 调用Feign接口
            DmsResponse<Integer> toggleWhitelistActivation = maintainAuthFeign.toggleWhitelistActivation(itemCode, isDeleted,loginInfoDto.getUserId(),rosterType);
            logger.error("toggleWhitelistActivation,Response, toggleWhitelistActivation:{}", toggleWhitelistActivation);
            // 检查响应是否成功
            if (toggleWhitelistActivation.isFail()) {
                logger.error("selectWhitelistDetail,toggleWhitelistActivation. Response Code={}, Response Message={}", toggleWhitelistActivation.getResultCode(), toggleWhitelistActivation.getErrMsg());
                return 0; // 返回空列表，而不是null
            }

            Integer data = toggleWhitelistActivation.getData();
            // 对返回数据的非空和非空列表校验
            if (ObjectUtils.isEmpty(data)) {
                logger.info("insertWhitelist.itemCode={}", itemCode);
                return 0; // 返回空列表，而不是null
            } else {
                return data;
            }
        } catch (FeignException e) {
            // 处理Feign调用异常
            logger.error("Feign exception when fetching insertWhitelist. itemCode={}", itemCode, e);
        }
        return 0;
    }

    @Override
    public List<DataInputVo> vehicleOrPhoneImport(String modType, String vehicleOrPhone, MultipartFile importFile) {
        logger.info("开始导入临时数据...");
        if (StringUtils.isBlank(modType)){
            throw new ServiceBizException("业务主题不可为空");
        }
        if (StringUtils.isBlank(vehicleOrPhone)){
            throw new ServiceBizException("导入类型不可为空");
        }
        if (Objects.equals(vehicleOrPhone,"VIN")){
            List<DataInputVo> resList = getVinDataInputVos(importFile);
            if (resList != null) return resList;
        }else if (Objects.equals(vehicleOrPhone,"PHONE")){
            List<DataInputVo> resList = getPhoneDataInputVos(importFile);
            if (resList != null) return resList;
        }else {
            throw new ServiceBizException("导入类型异常");
        }

        return Collections.emptyList();
    }

    private List<DataInputVo> getPhoneDataInputVos(MultipartFile importFile) {
        List<TePhoneImportDto> parsingDataList;
        try {
            ImportParams importParams = new ImportParams();
            importParams.setNeedVerify(true);
            parsingDataList = ExcelImportUtil.importExcel(importFile.getInputStream(), TePhoneImportDto.class, importParams);
            // 解析数据
            logger.info("手机号导入数据：{}", parsingDataList.size());
        } catch (Exception e) {
            logger.error("手机号导入数据异常", e);
            throw new ServiceBizException("手机号导入数据异常");
        }

        //是否成功
        if (CollectionUtils.isEmpty(parsingDataList)) {
            logger.info("手机号导入数据为空");
            throw new ServiceBizException("手机号导入数据为空");
        }

        List<DataInputVo> emptyVinList = new ArrayList<>();
        parsingDataList
                .forEach(teVehicleImportDto -> {
                    if (StringUtils.isEmpty(teVehicleImportDto.getPhone())) {
                        emptyVinList.add(new DataInputVo(teVehicleImportDto.getPhone(), "手机号不能为空", teVehicleImportDto.getRowNum()));
                    } else if (isValidPhoneFormat(teVehicleImportDto.getPhone())) {
                        emptyVinList.add(new DataInputVo(teVehicleImportDto.getPhone(), "手机号格式不正确", teVehicleImportDto.getRowNum()));
                    }
                });
        if (CollectionUtils.isEmpty(emptyVinList)) {

            // 创建 WhiteListDto 对象
            WhiteListDto whiteListDto = new WhiteListDto();
            // 使用 Lambda 表达式将 TePhoneImportDto 中的 phone 转存到 WhiteListDto 的 phoneNumber 列表中
            List<String> phoneNumberList = parsingDataList.stream()
                    .map(TePhoneImportDto::getPhone) // 使用方法引用获取 phone 字段值
                    .collect(Collectors.toList()); // 将结果收集到列表中

            // 将 phoneNumberList 列表设置到 WhiteListDto 对象中
            whiteListDto.setPhoneNumber(phoneNumberList);
            whiteListDto.setVehicleOrPhone("PHONE");
            saveSetMainData(whiteListDto);
        } else {
            return emptyVinList;
        }
        return null;
    }

    private List<DataInputVo> getVinDataInputVos(MultipartFile importFile) {

        logger.info("开始导入临时数据 开始处理...");

        ImportParams importParams = new ImportParams();
        importParams.setNeedVerify(true);
        List<TeVehicleImportDto> parsingDataList;
        try {
            parsingDataList = ExcelImportUtil.importExcel(importFile.getInputStream(), TeVehicleImportDto.class, importParams);
            // 解析数据
            logger.info("VIN导入数据：{}", parsingDataList.size());
        } catch (Exception e) {
            logger.error("VIN导入数据异常",e);
            throw new ServiceBizException("VIN导入数据异常");
        }

        //是否成功
        if (CollectionUtils.isEmpty(parsingDataList)) {
            logger.info("VIN导入数据为空");
            throw new ServiceBizException("VIN导入数据为空");
        }

        List<DataInputVo> emptyVinList = new ArrayList<>();
        List<TeVehicleImportDto> notEmptyVinList = parsingDataList.stream()
                .peek(teVehicleImportDto -> {
                    if(StringUtils.isEmpty(teVehicleImportDto.getVin())) {
                        emptyVinList.add(new DataInputVo(teVehicleImportDto.getVin(), "VIN不能为空", teVehicleImportDto.getRowNum()));
                    }
                })
                .filter(teVehicleImportDto -> !StringUtils.isEmpty(teVehicleImportDto.getVin()))
                .collect(Collectors.toList());

        List<String> vinList = notEmptyVinList.stream().map(TeVehicleImportDto::getVin).collect(Collectors.toList());
        CheckVehicleDTO checkVehicleDTO = new CheckVehicleDTO();
        checkVehicleDTO.setVinList(vinList);
        RequestDto<CheckVehicleDTO> requestDTO = new RequestDto<>();
        requestDTO.setData(checkVehicleDTO);
        DmsResponse<CheckVehicleExistsVo> listDmsResponse = midEndVehicleCenterFeign.vehicleVinList(requestDTO);

        CheckVehicleExistsVo checkVehicleExistsVo = listDmsResponse.getData();
        if (!ObjectUtils.isEmpty(checkVehicleExistsVo)) {
            if (!checkVehicleExistsVo.isAllExists()) {
                //不是完全存在
                List<String> notAllExistsVehicleList = checkVehicleExistsVo.getNotExistVinList();
                if (!CommonUtils.isNullOrEmpty(notAllExistsVehicleList)) {
                    parsingDataList.stream()
                            .filter(teVehicleImportDto -> notAllExistsVehicleList.contains(teVehicleImportDto.getVin()))
                            .forEach(teVehicleImportDto -> emptyVinList.add(new DataInputVo(teVehicleImportDto.getVin(), "VIN不存在", teVehicleImportDto.getRowNum())));
                }
            }
        }

        if (CollectionUtils.isEmpty(emptyVinList)) {

            // 创建 WhiteListDto 对象
            WhiteListDto whiteListDto = new WhiteListDto();
            // 使用 Lambda 表达式将 TePhoneImportDto 中的 phone 转存到 WhiteListDto 的 phoneNumber 列表中
            List<String> vinsList = parsingDataList.stream()
                    .map(TeVehicleImportDto::getVin) // 使用方法引用获取 phone 字段值
                    .collect(Collectors.toList()); // 将结果收集到列表中
            // 将 veinsList 列表设置到 WhiteListDto 对象中
            whiteListDto.setVin(vinsList);
            whiteListDto.setVehicleOrPhone("VIN");
            saveSetMainData(whiteListDto);
        } else {
            return emptyVinList;
        }
        return null;
    }

    private void saveSetMainData(WhiteListDto whiteListDto) {
        // 获取登录用户信息
        CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();
        whiteListDto.setCreatedBy(loginInfoDto.getUserId());
        // 调用Feign接口
        DmsResponse<Integer> toggleWhitelistActivation = maintainAuthFeign.insertWhitelistImport(whiteListDto);
        if (toggleWhitelistActivation.isFail()){
            throw new ServiceBizException("白名单导入失败");
        }
    }

    private boolean isValidPhoneFormat(String phoneNumber) {
        String regex = "^1[3-9]\\d{9}$"; // 定义手机号正则表达式
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNumber);
        return !matcher.matches();
    }

    /**
     * 根据类型查询存在的经销商白名单
     * @param vo 参数白名单类型，白名单 0 roseType
     * @return 返回该名单单类型的经销商
     */
    @Override
    public List<String> queryWhiteListByParams(VehicleHealthCheckWhiteListVo vo) {
        if (null == vo.getModType() || null == vo.getRosterType()) {
            return Collections.emptyList();
        }
        DmsResponse<List<String>> whitelistedDealersResponse = maintainAuthFeign.getWhitelistedDealers(vo.getModType(), vo.getRosterType());
        if (whitelistedDealersResponse.isFail()) {
            throw new ServiceBizException("获取领域权限失败");
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(whitelistedDealersResponse.getData())) {
            return Collections.emptyList();
        }
        return whitelistedDealersResponse.getData();
    }

    /**
     * 检验白名单是否存在
     * @param modType 白名单类型
     * @return true / false
     */
    @Override
    public Boolean checkIfWhitelistEnabled(Integer modType) {
        if (null == modType) {
            return false;
        }
        DmsResponse<Boolean> response = maintainAuthFeign.checkIfWhitelistEnabled(modType);
        if (response.isFail()) {
            throw new ServiceBizException("获取领域权限失败");
        }
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(response.getData())) {
            return false;
        }
        return response.getData();
    }

}
