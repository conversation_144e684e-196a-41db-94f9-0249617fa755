package com.volvo.maintain.application.maintainlead.service.dtcclues.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.DownloadDto;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.*;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.infoInherit.DtcCluesCategoryInheritDto;
import com.volvo.maintain.application.maintainlead.service.dtcclues.DataProcessingService;
import com.volvo.maintain.application.maintainlead.service.dtcclues.DtcCluesService;
import com.volvo.maintain.application.maintainlead.service.mid.MidService;
import com.volvo.maintain.infrastructure.constants.DtcCluesExportConstant;
import com.volvo.maintain.infrastructure.enums.DtcConfirmStatusEnum;
import com.volvo.maintain.infrastructure.enums.DtcIndicatorStatusEnum;
import com.volvo.maintain.infrastructure.gateway.DownloadServiceFeign;
import com.volvo.maintain.infrastructure.gateway.MaintainDtcCluesFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.utils.BeanMapperUtil;
import com.yonyou.cyx.framework.service.excel.ExcelExportColumn;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DtcCluesServiceImpl implements DtcCluesService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private MaintainDtcCluesFeign maintainDtcCluesFeign;
    @Resource
    private DownloadServiceFeign downloadServiceFeign;
    @Resource
    private DataProcessingService dataProcessingService;
    @Resource
    private MidService midService;

    private static final String ALL_VEHICLE_MODEL = "All";

    @Override
    public Page<DtcCluesCategoryInheritDto> generateCategoryList(DtcCluesCategoryListDto listRequestDto) {
        log.info("generateCategoryList,listRequestDto:{}", JSON.toJSONString(listRequestDto));
        DmsResponse<Page<DtcCluesCategoryInheritDto>> resp = maintainDtcCluesFeign.generateCategoryList(listRequestDto);
        if(resp.isFail()) {
            log.error("generateCategoryList,query dtc generate category list failure: {}", resp.getErrMsg());
            throw new ServiceBizException("查询生成种类列表失败: " + resp.getErrMsg());
        }
        return getDtcCluesCategoryInheritDtoPage(resp);
    }

    @Override
    public Page<DtcCluesCategoryInheritDto> shieldCategoryList(DtcCluesCategoryListDto listRequestDto) {
        log.info("shieldCategoryList,listRequestDto:{}", JSON.toJSONString(listRequestDto));
        DmsResponse<Page<DtcCluesCategoryInheritDto>> resp = maintainDtcCluesFeign.shieldCategoryList(listRequestDto);
        if(resp.isFail()) {
            log.error("shieldCategoryList,query dtc shield category list failure: {}", resp.getErrMsg());
            throw new ServiceBizException("查询屏蔽种类列表失败: " + resp.getErrMsg());
        }
        return getDtcCluesCategoryInheritDtoPage(resp);
    }

    private Page<DtcCluesCategoryInheritDto> getDtcCluesCategoryInheritDtoPage(DmsResponse<Page<DtcCluesCategoryInheritDto>> resp) {
        log.info("getDtcCluesCategoryInheritDtoPage,开始转换车型名称");
        //车型名称
        Map<String, String> modelCodeToNameMap = midService.getModelIdToNameMap();
        // 获取分页数据并处理车型名称转换
        Page<DtcCluesCategoryInheritDto> resultPage = resp.getData();
        String vehicleModelIds;
        String vehicleModelNames;
        if (resultPage != null && !CollectionUtils.isEmpty(resultPage.getRecords())) {
            for (DtcCluesCategoryInheritDto dto : resultPage.getRecords()) {
                log.info("getDtcCluesCategoryInheritDtoPage,dto:{}", JSON.toJSONString(dto));
                // 转换车型ID为名称并设置到vehicleModels字段
                vehicleModelIds = dto.getVehicleModelIds();
                vehicleModelNames = resolveVehicleModelNames(vehicleModelIds, modelCodeToNameMap);
                dto.setVehicleModels(vehicleModelNames);
            }
        }
        return resultPage;
    }

    /**
     * 将车型ID字符串转换为名称字符串
     * @param vehicleModelIds 车型ID字符串（逗号分隔，可能为null或空）
     * @param modelIdToNameMap 车型ID到名称的映射表
     * @return 转换后的车型名称字符串（逗号分隔）
     */
    private String resolveVehicleModelNames(String vehicleModelIds, Map<String, String> modelIdToNameMap) {
        log.info("resolveVehicleModelNames,vehicleModelIds:{}", vehicleModelIds);
        // 处理空值或空字符串
        if (StringUtils.isEmpty(vehicleModelIds)) {
            return ALL_VEHICLE_MODEL;
        }
        // 处理全量车型标记
        String trimmedIds = vehicleModelIds.trim();
        if (ALL_VEHICLE_MODEL.equals(trimmedIds)) {
            return ALL_VEHICLE_MODEL;
        }
        // 分割ID并转换为名称
        String[] modelIdArray = trimmedIds.split(",");
        StringBuilder resultBuilder = new StringBuilder();
        for (String modelId : modelIdArray) {
            // 查找名称，无匹配则显示原始ID
            String modelName = modelIdToNameMap.getOrDefault(modelId, "未知车型");
            // 拼接时避免首位逗号，优化字符串操作性能
            if (resultBuilder.length() > 0) {
                resultBuilder.append(",");
            }
            resultBuilder.append(modelId).append("-").append(modelName);
        }
        String vehicleModelNames = resultBuilder.toString();
        log.info("resolveVehicleModelNames,vehicleModelNames:{}", vehicleModelNames);
        return vehicleModelNames;
    }

    @Override
    public List<Integer> queryCategoryPriority() {
        DmsResponse<List<Integer>> resp = maintainDtcCluesFeign.queryCategoryPriority();
        if(resp.isFail()) {
            log.error("query dtc generate category priority failure: {}", resp.getErrMsg());
            throw new ServiceBizException("查询DTC生成种类优先级失败: " + resp.getErrMsg());
        }
        return resp.getData();
    }

    @Override
    public Integer insertCluesCategory(DtcCluesCategoryDto dtcCluesCategoryDto) {
        log.info("insertCluesCategory,params: {}", JSON.toJSONString(dtcCluesCategoryDto));
        dataProcessingService.validateInsertParams(dtcCluesCategoryDto);
        if (Objects.isNull(dtcCluesCategoryDto.getPriority())) {
            throw new ServiceBizException("优先级不能为空");
        }
        if (Objects.isNull(dtcCluesCategoryDto.getFaultCategory())) {
            throw new ServiceBizException("故障类别不能为空");
        }
        dtcCluesCategoryDto.setCreatedBy(dataProcessingService.getLoginUserId());
        DmsResponse<Integer> resp = maintainDtcCluesFeign.insertCluesCategory(dtcCluesCategoryDto);
        if(resp.isFail()) {
            log.error("insert clues generate category failure: {}", resp.getErrMsg());
            throw new ServiceBizException("插入线索生成种类失败: " + resp.getErrMsg());
        }
        return resp.getData();
    }

    @Override
    public List<ImportResultInfoDto> importCluesCategory(MultipartFile importFile) {
        // 解析和校验
        List<ImportDtcCluesCategoryDto> categoryDtoList = dataProcessingService.parseImportDtcCluesCategoryData(importFile);
        List<ImportResultInfoDto> importResultInfoList = dataProcessingService.validateAndConvertImportData(categoryDtoList, 1);

        int distinctSize = (int) categoryDtoList.stream().map(ImportDtcCluesCategoryDto::getPriority).distinct().count();
        if (distinctSize != categoryDtoList.size()) {
            throw new ServiceBizException("优先级不能重复!");
        }

        if (CollectionUtils.isEmpty(importResultInfoList)) {
            String loginUserId = dataProcessingService.getLoginUserId();
            List<DtcCluesCategoryDto> dtcCluesCategoryDtoList = categoryDtoList.stream().map(v -> {
                DtcCluesCategoryDto dtcCluesCategoryDto = new DtcCluesCategoryDto();
                BeanUtils.copyProperties(v, dtcCluesCategoryDto);
                dtcCluesCategoryDto.setCreatedBy(loginUserId);
                return dtcCluesCategoryDto;
            }).collect(Collectors.toList());
            DmsResponse<Integer> resp = maintainDtcCluesFeign.importCluesCategory(dtcCluesCategoryDtoList);
            if (resp.isFail()) {
                logger.error("import dtc clues generate category failure: {}", resp.getErrMsg());
                throw new ServiceBizException("DTC线索生成类别导入失败: " + resp.getErrMsg());
            }
        }
        return importResultInfoList;
    }

    @Override
    public Integer updateCluesCategory(DtcCluesCategoryDto dtcCluesCategoryDto) {
        log.info("updateCluesCategory,dtcCluesCategoryDto:{}", dtcCluesCategoryDto);
        if (Objects.isNull(dtcCluesCategoryDto.getId())) {
            throw new ServiceBizException("主键ID不能为空");
        }
        dataProcessingService.validateInsertParams(dtcCluesCategoryDto);
        String loginUserId = dataProcessingService.getLoginUserId();
        dtcCluesCategoryDto.setUpdatedBy(loginUserId);
        DmsResponse<Integer> resp = maintainDtcCluesFeign.updateCluesCategory(dtcCluesCategoryDto);
        if (resp.isFail()) {
            logger.error("update dtc generate category failure: {}", resp.getErrMsg());
            throw new ServiceBizException("更新DTC生成种类失败: " + resp.getErrMsg());
        }
        return resp.getData();
    }

    @Override
    public Integer deleteCluesCategory(String ecu, String dtc) {
        if (StringUtils.isEmpty(ecu) || StringUtils.isEmpty(dtc)) {
            throw new ServiceBizException("ECU和DTC不能为空");
        }
        DmsResponse<Integer> resp = maintainDtcCluesFeign.deleteCluesCategory(ecu, dtc);
        if (resp.isFail()) {
            logger.error("delete dtc generate category failure: {}", resp.getErrMsg());
            throw new ServiceBizException("删除DTC生成种类失败: " + resp.getErrMsg());
        }
        return resp.getData();
    }

    @Override
    public Integer insertShieldCategory(DtcCluesCategoryDto dtcCluesCategoryDto) {
        log.info("insertShieldCategory,params: {}", JSON.toJSONString(dtcCluesCategoryDto));
        dataProcessingService.validateInsertParams(dtcCluesCategoryDto);
        dtcCluesCategoryDto.setCreatedBy(dataProcessingService.getLoginUserId());
        DmsResponse<Integer> resp = maintainDtcCluesFeign.insertShieldCategory(dtcCluesCategoryDto);
        if (resp.isFail()) {
            logger.error("insertShieldCategory,failure: {}", resp.getErrMsg());
            throw new ServiceBizException("新增DTC屏蔽种类失败: " + resp.getErrMsg());
        }
        return resp.getData();
    }

    @Override
    public List<ImportResultInfoDto> importShieldCategory(MultipartFile importFile) {
        // 解析和校验
        List<ImportDtcCluesCategoryDto> categoryDtoList = dataProcessingService.parseImportDtcCluesCategoryData(importFile);
        List<ImportResultInfoDto> importResultInfoList = dataProcessingService.validateAndConvertImportData(categoryDtoList, 2);

        if (CollectionUtils.isEmpty(importResultInfoList)) {
            String loginUserId = dataProcessingService.getLoginUserId();
            List<DtcCluesCategoryDto> dtcCluesCategoryDtoList = categoryDtoList.stream().map(v -> {
                DtcCluesCategoryDto dtcCluesCategoryDto = new DtcCluesCategoryDto();
                BeanUtils.copyProperties(v, dtcCluesCategoryDto);
                dtcCluesCategoryDto.setCreatedBy(loginUserId);
                return dtcCluesCategoryDto;
            }).collect(Collectors.toList());
            DmsResponse<Integer> resp = maintainDtcCluesFeign.importShieldCategory(dtcCluesCategoryDtoList);
            if (resp.isFail()) {
                logger.error("import dtc clues shield category failure: {}", resp.getErrMsg());
                throw new ServiceBizException("DTC线索屏蔽种类导入失败: " + resp.getErrMsg());
            }
        }
        return importResultInfoList;
    }

    @Override
    public Integer updateShieldCategory(DtcCluesCategoryDto dtcCluesCategoryDto) {
        log.info("updateShieldCategory,dtcCluesCategoryDto:{}", dtcCluesCategoryDto);
        if (Objects.isNull(dtcCluesCategoryDto.getId())) {
            throw new ServiceBizException("主键ID不能为空");
        }
        dataProcessingService.validateInsertParams(dtcCluesCategoryDto);
        String loginUserId = dataProcessingService.getLoginUserId();
        dtcCluesCategoryDto.setUpdatedBy(loginUserId);
        DmsResponse<Integer> resp = maintainDtcCluesFeign.updateShieldCategory(dtcCluesCategoryDto);
        if (resp.isFail()) {
            logger.error("updateShieldCategory,failure: {}", resp.getErrMsg());
            throw new ServiceBizException("更新DTC屏蔽种类失败: " + resp.getErrMsg());
        }
        return resp.getData();
    }

    @Override
    public Integer deleteShieldCategory(String ecu, String dtc) {
        if (StringUtils.isEmpty(ecu) || StringUtils.isEmpty(dtc)) {
            throw new ServiceBizException("ECU和DTC不能为空");
        }
        DmsResponse<Integer> resp = maintainDtcCluesFeign.deleteShieldCategory(ecu, dtc);
        if (resp.isFail()) {
            logger.error("delete dtc shield category failure: {}", resp.getErrMsg());
            throw new ServiceBizException("删除DTC屏蔽种类失败: " + resp.getErrMsg());
        }
        return resp.getData();
    }

    @Override
    public Map<String, List<EcuDtcRelationDto>> queryRelationEcuDtc(Integer category) {
        DmsResponse<Map<String, List<EcuDtcRelationDto>>> resp = maintainDtcCluesFeign.queryRelationEcuDtc(category);
        if (resp.isFail()) {
            logger.error("query ecu dtc relation failure: {}", resp.getErrMsg());
            throw new ServiceBizException("查询ECU和DTC联动关系失败: " + resp.getErrMsg());
        }
        return resp.getData();
    }

    @Override
    public void exportDtcCluesCategory(DtcCluesCategoryListDto dtcCluesCategoryListDto, Integer category) {
        log.info("exportDtcCluesCategory,params:{}", JSON.toJSONString(dtcCluesCategoryListDto) + "-" + category);
        // 获取导出字段
        List<ExcelExportColumn> exportColumnList = dataProcessingService.buildExcelColumn(category);

        // 组装下载中心 DTO
        DownloadDto dto = new DownloadDto();
        dto.setQueryParams(BeanMapperUtil.toMap(dtcCluesCategoryListDto));
        if (category == 1) {
            dto.setExcelName(DtcCluesExportConstant.EXCEL_NAME);
            dto.setSheetName(DtcCluesExportConstant.SHEET_NAME);
            dto.setServiceUrl(DtcCluesExportConstant.EXPORT_URL);
        } else {
            dto.setExcelName(DtcCluesExportConstant.SHIELD_EXCEL_NAME);
            dto.setSheetName(DtcCluesExportConstant.SHIELD_SHEET_NAME);
            dto.setServiceUrl(DtcCluesExportConstant.SHIELD_EXPORT_URL);
        }
        dto.setPostFlag(true);
        dto.setPostHeaderFlag(true);
        dto.setExcelExportColumnList(exportColumnList);

        log.info("exportDtcCluesCategory,params: {}", JSON.toJSONString(dto));
        downloadServiceFeign.downloadExportExcel(dto);
    }

    @Override
    public List<ExportDtcCluesCategoryDto> exportCluesCategoryCallback(DtcCluesCategoryListDto dtcCluesCategoryListDto, Integer category) {
        log.info("exportCluesCategoryCallback,params: {}", JSON.toJSONString(dtcCluesCategoryListDto));
        Page<DtcCluesCategoryInheritDto> dtcCluesCategoryDtoPage;
        if (category == 1) {
            dtcCluesCategoryDtoPage = this.generateCategoryList(dtcCluesCategoryListDto);
        } else {
            dtcCluesCategoryDtoPage = this.shieldCategoryList(dtcCluesCategoryListDto);
        }
        List<DtcCluesCategoryInheritDto> records = dtcCluesCategoryDtoPage.getRecords();
        log.info("exportCluesCategoryCallback,records: {}", JSON.toJSONString(records));
        List<ExportDtcCluesCategoryDto> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(records)) {
            AtomicInteger serialNumber = new AtomicInteger(1);
            records.forEach(v -> {
                ExportDtcCluesCategoryDto export = new ExportDtcCluesCategoryDto();
                BeanUtils.copyProperties(v, export);
                export.setConfirmStatusString(DtcConfirmStatusEnum.getNameByCode(v.getConfirmStatus()));
                export.setIndicatorStatusString(DtcIndicatorStatusEnum.getNameByCode(v.getIndicatorStatus()));
                export.setSerialNumber(serialNumber.getAndIncrement());
                resultList.add(export);
            });
        }
        log.info("exportCluesCategoryCallback,size: {}", CollectionUtils.isNotEmpty(resultList) ? resultList.size() : 0);
        log.info("exportCluesCategoryCallback,response: {}", CollectionUtils.isNotEmpty(resultList) ? JSON.toJSONString(resultList.get(0)) : null);
        return resultList;
    }
}
