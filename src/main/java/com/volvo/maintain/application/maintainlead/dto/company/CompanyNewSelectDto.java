package com.volvo.maintain.application.maintainlead.dto.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "经销商多条件查询", description="经销商多条件查询")
public class CompanyNewSelectDto {

	@ApiModelProperty(value = "经销商代码")
	private List<String>  companyCodes;

	@ApiModelProperty(value = "经销商代码IK")
	private String  companyCodeLike;

	@ApiModelProperty(value = "经销商名称中文")
	private String  companyNameCn;

	@ApiModelProperty(value = "省份(可多个,用','分隔)")
	private String provinceId;

	@ApiModelProperty(value = "城市(可多个,用','分隔)")
	private String cityId;

	@ApiModelProperty(value = "售后大区(可多个,用','分隔)")
	private String  afterBigArea;

	@ApiModelProperty(value = "售后小区(可多个,用','分隔)")
	private String  afterSmallArea;

}
