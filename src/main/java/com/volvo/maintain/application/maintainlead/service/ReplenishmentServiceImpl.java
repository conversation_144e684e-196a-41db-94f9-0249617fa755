package com.volvo.maintain.application.maintainlead.service;

import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.part.*;
import com.volvo.maintain.application.maintainlead.dto.workshop.SceneMessageRemindDto;
import com.volvo.maintain.application.maintainlead.emums.PurchaseOrderTransPortModeEnum;
import com.volvo.maintain.application.maintainlead.mq.producer.TransparentTocProducer;
import com.volvo.maintain.application.maintainlead.mq.producer.WorkshopMessageReminderProducer;
import com.volvo.maintain.application.maintainlead.vo.UserInfoVo;
import com.volvo.maintain.application.maintainlead.vo.order.OrderServiceInfoVO;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.constants.RenewalOfInsuranceCluesConstant;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.request.ETAOperationParamsDTO;
import com.volvo.maintain.infrastructure.gateway.request.PartPurchaseOrderDTO;
import com.volvo.maintain.infrastructure.gateway.request.PartPurchaseOrderDetailDTO;
import com.volvo.maintain.infrastructure.gateway.request.PartPurchaseOrderPushDTO;
import com.volvo.maintain.infrastructure.gateway.request.UpdateETAMappingDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ETAMappingReturnDTO;
import com.volvo.maintain.infrastructure.gateway.response.PartMasterFileParamsVo;
import com.volvo.maintain.infrastructure.gateway.response.PartPurchaseOrderDetaileDTO;
import com.volvo.maintain.infrastructure.gateway.response.ShortPartDto;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.framework.service.excel.ExcelDataType;
import com.yonyou.cyx.framework.service.excel.ExcelExportColumn;
import com.yonyou.cyx.function.exception.ServiceBizException;
import com.yonyou.cyx.function.utils.common.DateUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.DATA_SOURCES_83751005;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.LJKG_83261002;
import static com.volvo.maintain.infrastructure.constants.RedisConstants.KEY_REPLENISHMENT_LOCK;


@Slf4j
@Service
public class ReplenishmentServiceImpl implements ReplenishmentService {
    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
    @Autowired
    private DomainPurchaseFeign domainPurchaseFeign;
    
    @Autowired
    private DmscusPartFeign dmscusPartFeign;
    
    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    
    @Autowired
    private DomainPartsFeign domainPartsFeign;
    
    @Autowired
    private TransparentTocProducer transparentTocProducer;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Resource
    private DownloadServiceFeign downloadServiceFeign;
    @Resource
    private DmscloudReportFeign dmscloudReportFeign;
    @Autowired
    private MidEndAuthCenterFeign midEndAuthCenterFeign;
    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private WorkshopMessageReminderProducer workshopMessageReminderProducer;

    @Override
    public List<ShortPartResultDTO> checkReplenishment(List<PartDto> parts, boolean flag) {
        log.info("checkReplenishment: {}", JSON.toJSONString(parts));
        if(CollectionUtils.isEmpty(parts)){
            return new ArrayList<>();
        }
        List<String> collect2 = parts.stream().distinct().filter(item -> "OEMK".equals(item.getStorageCode()) && !"D".equals(item.getItemUpdateStatus()))
                .map(PartDto::getPartNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect2)) {
        	return Collections.emptyList();
        }
        // 调用零件领域层，查询零件的库存数,和在途数量
        DmsResponse<List<PartStockDTO>> listDmsResponse = domainPartsFeign.queryPartInfo(collect2);
        log.info("queryPartInfo res:{}", listDmsResponse);
        if (Objects.isNull(listDmsResponse) || listDmsResponse.isFail()) {
            throw new ServiceBizException("查询库存信息失败!");
        }
        List<PartStockDTO> data = listDmsResponse.getData();
        // 查询零件在途数量
        DmsResponse<List<PartsDTO>> listDmsResponse2 = domainPurchaseFeign.queryPartDeliveryQuantity(collect2);
        log.info("listDmsResponse2 res:{}", listDmsResponse2);
        if (Objects.isNull(listDmsResponse2) || listDmsResponse2.isFail()) {
            throw new ServiceBizException("查询零件在途数量失败!");
        }
        List<PartsDTO> data2 = listDmsResponse2.getData();
        if(CollectionUtils.isNotEmpty(data2)){
            Map<String, PartsDTO> collect = data2.stream().collect(Collectors.toMap(PartsDTO::getPartNo, Function.identity()));
            for (PartStockDTO pd: data) {
                if(collect.containsKey(pd.getPartNo())){
                    pd.setDeliveryQuantity(collect.get(pd.getPartNo()).getDeliveryQuantity());
                }
            }
        }
        Map<String, PartStockDTO> stockCollect = data.stream().collect(Collectors.toMap(PartStockDTO::getPartNo, Function.identity()));

        // 根据零件号查询未入库的工单对应的零件数量
        DmsResponse<List<PartOrderDto>> listDmsResponse1 = domainMaintainOrdersFeign.queryPartTotal(collect2);
        log.info("queryPartTotal res:{}", listDmsResponse1);
        if (Objects.isNull(listDmsResponse1) || listDmsResponse1.isFail()) {
            throw new ServiceBizException("查询未入库零件总数失败");
        }
        List<PartOrderDto> data1 = listDmsResponse1.getData();
        Map<String, PartOrderDto> orderCollect = data1.stream().collect(Collectors.toMap(PartOrderDto::getPartNo, Function.identity()));

        List<ShortPartResultDTO> shortPartResultDTOList = new ArrayList<>(); // 用于页面弹框信息

        for (PartDto part: parts) {
            PartStockDTO partStockDTO = stockCollect.get(part.getPartNo());
            PartOrderDto partOrderDto = orderCollect.get(part.getPartNo());
            if(Objects.isNull(partStockDTO)){
                log.info("零件号库存无记录：{}", part.getPartNo());
                continue;
            }
            if(Objects.isNull(partOrderDto)){
                log.info("未入库零件无记录：{}", part.getPartNo());
                partOrderDto = new PartOrderDto();
                partOrderDto.setPartNo(part.getPartNo());
                partOrderDto.setTotal(new BigDecimal("0.00"));
            }
            if(part.getItemUpdateStatus().equals("D")){
                continue;
            }
            this.convertData(shortPartResultDTOList, part, partStockDTO, partOrderDto, flag);
        }

        // 处理重复零件号的数据
        log.info("checkReplenishment>>{}", shortPartResultDTOList);
        Map<String, List<PartDto>> collect = parts.stream().filter(item -> "D".equals(item.getItemUpdateStatus())).collect(Collectors.groupingBy(PartDto::getPartNo));
        Map<String, List<PartDto>> collect4 = parts.stream().filter(item -> !"D".equals(item.getItemUpdateStatus())).collect(Collectors.groupingBy(PartDto::getPartNo));
        List<ShortPartResultDTO> shortPartResultDTOList02 = new ArrayList<>(); // 用于页面弹框信息
        if(CollectionUtils.isNotEmpty(shortPartResultDTOList)){
            Map<String, List<ShortPartResultDTO>> collect1 = shortPartResultDTOList.stream().collect(Collectors.groupingBy(ShortPartResultDTO::getPartNo));
            log.info("checkReplenishment-map>>{}", collect1);
            collect1.forEach((partNo, items) -> {
                if(items.size() > 1){
                    // 多条数据
                    BigDecimal materialShortageNum = new BigDecimal("0.00");// 缺料数
                    BigDecimal differenceNum = new BigDecimal("0.00");// 差异数
                    //重新计算缺料数
                    List<PartDto> partDtos1 = collect4.get(partNo);
                    BigDecimal practicalQuantity = new BigDecimal("0.00");// 零件总数
                    practicalQuantity = partDtos1.stream().map(PartDto::getPracticalQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    ShortPartResultDTO sprt = items.get(0);
                    List<ShortPartResultDTO> collect3 = items.stream().filter(item -> "U".equals(item.getItemUpdateStatus())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(collect3)){
                        sprt = collect3.get(0);
                    }
                    materialShortageNum = sprt.getWorkOrderTotal().subtract(sprt.getStockQuantity());
                    sprt.setMaterialShortageNum(materialShortageNum.compareTo(practicalQuantity) > 0 ? practicalQuantity : materialShortageNum);
                    // 补货数 > 零件数 取零件数
                    sprt.setReplenishmentNum(sprt.getReplenishmentNum().compareTo(practicalQuantity) > 0 ? practicalQuantity : sprt.getReplenishmentNum());
                    this.repeatDataHandle(shortPartResultDTOList02, collect, sprt, flag, collect4);
                }else{
                    ShortPartResultDTO sprt = items.get(0);
                    List<PartDto> partDtos1 = collect4.get(partNo);
                    BigDecimal practicalQuantity = new BigDecimal("0.00");// 零件总数
                    practicalQuantity = partDtos1.stream().map(PartDto::getPracticalQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    sprt.setReplenishmentNum(sprt.getReplenishmentNum().compareTo(practicalQuantity) > 0 ? practicalQuantity : sprt.getReplenishmentNum());
                    this.repeatDataHandle(shortPartResultDTOList02, collect, sprt, flag, collect4);
                }
            });
        }
        // 查询零件采购价格
        if(CollectionUtils.isNotEmpty(shortPartResultDTOList02)){
            List<String> claimPriceParts = shortPartResultDTOList02.stream().map(ShortPartResultDTO::getPartNo).collect(Collectors.toList());
            DmsResponse<List<PartOrderDto>> queryPartClaimPrice = domainMaintainOrdersFeign.queryPartClaimPrice(claimPriceParts);
            log.info("queryPartClaimPrice res:{}", queryPartClaimPrice);
            if (Objects.isNull(queryPartClaimPrice) || queryPartClaimPrice.isFail()) {
                throw new ServiceBizException("查询零件采购价格");
            }
            if(CollectionUtils.isNotEmpty(queryPartClaimPrice.getData())){
                Map<String, PartOrderDto> queryPartClaimPriceMap = queryPartClaimPrice.getData().stream().collect(Collectors.toMap(PartOrderDto::getPartNo, Function.identity()));
                shortPartResultDTOList02.stream().forEach(item -> {
                    if(Objects.nonNull(queryPartClaimPriceMap.get(item.getPartNo()))){
                        item.setClaimPrice(queryPartClaimPriceMap.get(item.getPartNo()).getClaimPrice());
                    }
                });
            }
            // 查询上次操作人
            this.setUpdateByName(shortPartResultDTOList02);
            // 查询零件的埋点标识
            PartDto partDto = parts.get(0);
            // 如果传参没有工单号，则直接返回
            if(StringUtils.isEmpty(partDto.getRoNo())){
                return shortPartResultDTOList02;
            }
            // 查询零件的埋点标识
            this.setPartBuryingPoint(shortPartResultDTOList02, partDto.getRoNo());
            // 根据补货数进行倒序，再根据排序标识进行倒序，达到
            shortPartResultDTOList02.sort( Comparator.comparing(ShortPartResultDTO::getReplenishmentNum).reversed());
            shortPartResultDTOList02.sort( Comparator.comparing(ShortPartResultDTO::getSort).reversed());

        }

        return shortPartResultDTOList02;
    }

    /**
     * 给更新人赋值中文
     */
    private void setUpdateByName(List<ShortPartResultDTO> shortPartResultDTOList02){
        List<Long> updateByIds = shortPartResultDTOList02.stream()
                .filter(dto -> Validator.isNumber(dto.getUpdateBy()))
                .map(config -> Long.parseLong(config.getUpdateBy())).collect(Collectors.toList());
        log.info("setUpdateByName:{}", JSONObject.toJSONString(updateByIds));
        if(CollectionUtils.isNotEmpty(updateByIds)){
            // 2. 使用midEndAuthCenterFeign.queryUserInfoByIds查询用户信息
            UserInfoByUserIdsDto buildUserInfoIds = UserInfoByUserIdsDto.builder().userIds(updateByIds).build();
            RequestDto<UserInfoByUserIdsDto> requestDto = new RequestDto<>();
            requestDto.setData(buildUserInfoIds);
            ResponseDto<List<UserInfoVo>> userInfoList = midEndAuthCenterFeign.queryUserInfoByIds(requestDto);
            if (userInfoList.isFail()) {
                throw new ServiceBizException("获取组织中心用户信息失败！");
            }
            // 3. 将查询回来的用户信息赋值给 shortPartResultDTOList02 中的每一个对象
            Map<Integer, UserInfoVo> userInfoMap = userInfoList.getData().stream()
                    .collect(Collectors.toMap(UserInfoVo::getUserId, Function.identity(), (k1, k2) -> k1));

            shortPartResultDTOList02.stream()
                    .filter(dto -> Validator.isNumber(dto.getUpdateBy()))
                    .forEach(config -> {
                        UserInfoVo userInfo = userInfoMap.get(Integer.parseInt(config.getUpdateBy()));
                        if (userInfo != null) {
                            config.setUpdateBy(userInfo.getEmployeeName());
                        }
                        if (config.getUpdateBy().equals("-1")) {
                            config.setUpdateBy(null);
                        }
                    });
        }
    }

    /**
     * 查询工单零件埋点标识
     */
    private void setPartBuryingPoint(List<ShortPartResultDTO> shortPartResultDTOList02, String roNo){
        CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();
        if(Objects.isNull(loginInfoDto) || StringUtils.isEmpty(loginInfoDto.getOwnerCode())){
            throw new ServiceBizException("工单零件埋点标识,获取登录信息失败");
        }
        String partNos = shortPartResultDTOList02.stream().map(ShortPartResultDTO::getPartNo).collect(Collectors.joining(","));
        // 查询工单零件埋点标识
        DmsResponse<List<RoRepairOrderPartUpdateStatusDto>> partBuryingPoint = domainMaintainOrdersFeign.getPartBuryingPoint(loginInfoDto.getOwnerCode(), roNo, partNos);
        log.info("partBuryingPoint res:{}", JSON.toJSONString(partBuryingPoint));
        if (Objects.isNull(partBuryingPoint) || partBuryingPoint.isFail()) {
            return;
        }
        if(CollectionUtils.isNotEmpty(partBuryingPoint.getData())){
            // 查询到了工单对应的零件，
            // 但存在一个零件号对应多条数据的情况，
            // 因此要判断下，是否多条的埋点标识都为空，如果都为空则为新增，否则只要有一条不为空则取不为空这条的埋点标识
            Map<String, List<RoRepairOrderPartUpdateStatusDto>> groupedByPartNo = partBuryingPoint.getData().stream()
                    .filter(Objects::nonNull)
                    .filter(dto -> Objects.nonNull(dto.getPartUpdateStatus())).collect(Collectors.groupingBy(RoRepairOrderPartUpdateStatusDto::getPartNo));
            shortPartResultDTOList02.stream().forEach(item -> {
                if(Objects.nonNull(groupedByPartNo.get(item.getPartNo()))){
                    item.setPartUpdateStatus(groupedByPartNo.get(item.getPartNo()).get(0).getPartUpdateStatus());
                }else{
                    // 没有查询到工单对应的零件，肯定是新增
                    item.setPartUpdateStatus("A");
                }
            });
        }
    }

    /**
     * 重复零件号处理
     * @param shortPartResultDTOList02
     * @param collect
     * @param sprt
     * @param flag ture弹框调用 false缺料补货接口调用
     */
    private void repeatDataHandle(List<ShortPartResultDTO> shortPartResultDTOList02, Map<String, List<PartDto>> collect, ShortPartResultDTO sprt, boolean flag, Map<String, List<PartDto>> collect4){
        log.info("repeatDataHandle重复零件处理：{};{}", collect.containsKey(sprt.getPartNo()), flag);
        if(collect.containsKey(sprt.getPartNo()) && flag){
            log.info("重复零件处理：{};{}", JSON.toJSONString(sprt), JSON.toJSONString(collect));
            List<PartDto> partDtos = collect.get(sprt.getPartNo());
            List<PartDto> partDtos1 = collect4.get(sprt.getPartNo());
            BigDecimal deliveryQuantity = new BigDecimal("0.00");// 在途数
            BigDecimal stockQuantity = new BigDecimal("0.00");// 库存数
            BigDecimal replenishmentNum = new BigDecimal("0.00");// 补货数
            BigDecimal materialShortageNum = new BigDecimal("0.00");// 缺料数
            BigDecimal practicalQuantity = new BigDecimal("0.00");// 零件总数
            for (PartDto pd: partDtos) {
                sprt.setWorkOrderTotal(sprt.getWorkOrderTotal().subtract(pd.getPracticalQuantity()));
            }
            practicalQuantity = partDtos1.stream().map(PartDto::getPracticalQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 减去删除的零件后需要重新计算 缺料数和 补货数
            if(Objects.nonNull(sprt.getStockQuantity())){
                stockQuantity = sprt.getStockQuantity();
            }
            if(Objects.nonNull(sprt.getDeliveryQuantity())){
                deliveryQuantity = sprt.getDeliveryQuantity();
            }
            replenishmentNum = sprt.getWorkOrderTotal().subtract(stockQuantity).subtract(deliveryQuantity);
            materialShortageNum = sprt.getWorkOrderTotal().subtract(stockQuantity);
            log.info("repeatDataHandle>>>replenishmentNum:{}", replenishmentNum);
            log.info("repeatDataHandle>>>practicalQuantity:{}", practicalQuantity);
            log.info("repeatDataHandle>>>materialShortageNum:{}", materialShortageNum);
            sprt.setMaterialShortageNum(materialShortageNum.compareTo(practicalQuantity) > 0 ? practicalQuantity : materialShortageNum);
            sprt.setReplenishmentNum(replenishmentNum.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : replenishmentNum);
            // 补货数 > 零件数 取零件数
            sprt.setReplenishmentNum(sprt.getReplenishmentNum().compareTo(practicalQuantity) > 0 ? practicalQuantity : sprt.getReplenishmentNum());
        }
        shortPartResultDTOList02.add(sprt);
//        if(sprt.getMaterialShortageNum().compareTo(BigDecimal.ZERO) > 0){
//
//        }
        log.info("重复零件减去删除的零件数量：{}", sprt);
    }
    @Override
    public Long shortageReplenishment(ShortPartReplenishmentDTO shortPartDto) {
        log.info("application>shortageReplenishment:{}", JSON.toJSONString(shortPartDto));
        List<ShortPartDTO> shortPartDTOList = new ArrayList<>(); // 用于记录缺料明细的集合
        // 工单缺料数据
        List<ShortPartResultDTO> shortages = shortPartDto.getShortages();
        // 工单零件数据
        List<PartDto> parts = shortPartDto.getParts();
        Map<String, PartDto> collect = parts.stream().filter(item -> !"D".equals(item.getItemUpdateStatus())).collect(Collectors.toMap(PartDto::getPartNo, Function.identity(), (P1,P2) -> P1));

        // 从新查校验一遍是否缺料，因为该接口在工单保存完之后调用，所以需要将新增的零件改为修改（不能修改原集合中的数据），然后重新去校验是否缺料
        List<PartDto> parts2 = new ArrayList<>();
        for (PartDto partd: parts) {
            PartDto d = new PartDto();
            BeanUtils.copyProperties(partd, d);
            if("A".equals(d.getItemUpdateStatus())){
                d.setItemUpdateStatus("U");
            }
            parts2.add(d);
        }
        List<ShortPartResultDTO> shortPartResultDTOS = this.checkReplenishment(parts2, false);
        log.info("application>>shortPartResultDTOS:{}", JSON.toJSONString(shortPartResultDTOS));
        Map<String, List<PartDto>> collect3 = parts.stream().filter(item -> !"D".equals(item.getItemUpdateStatus())).collect(Collectors.groupingBy(PartDto::getPartNo));
        if(CollectionUtils.isNotEmpty(shortPartResultDTOS)){
            // 这里处理的是 新增 修改 和 未修改 且缺料>0的
            shortPartResultDTOS.stream().forEach(item -> {
                PartDto partDto = collect.get(item.getPartNo());
                // 处理溯源件多条数据的问题
                List<PartDto> partDtos = collect3.get(item.getPartNo());
                String itemUpdateStatus = null;
                if(CollectionUtils.isNotEmpty(partDtos)){
                    itemUpdateStatus = partDtos.stream()
                            .filter(p -> "U".equals(p.getItemUpdateStatus()))
                            .map(p -> p.getItemUpdateStatus())
                            .findFirst()
                            .orElse(null);
                }
                this.convertShortPartData(shortPartDTOList, partDto, item, itemUpdateStatus);
            });
        }
        // 处理删除的 和 修改后不缺料的
        Map<String, ShortPartResultDTO> collect1 = shortPartResultDTOS.stream().collect(Collectors.toMap(ShortPartResultDTO::getPartNo, Function.identity(), (p1,p2) -> p1));
        parts.stream().forEach(item -> {
            if(item.getItemUpdateStatus().equals("D")){
                this.convertShortPartData(shortPartDTOList, item, null, null);
            }
            if(item.getItemUpdateStatus().equals("U") && !collect1.containsKey(item.getPartNo())){
                // 此处处理的是编辑过, 但不缺料的  如果不缺料（不编辑的情况下）>>不删除；如果不缺料（编辑的情况下）>>删除
                this.convertShortPartData(shortPartDTOList, item, null, null);
            }
        });
        // 开始调用domain-maintain-orders,处理缺料明细数据
        Map<String, ShortPartResultDTO> collect2 = shortages.stream().collect(Collectors.toMap(ShortPartResultDTO::getPartNo, Function.identity()));
        for (ShortPartDTO sp: shortPartDTOList) {
            if(collect2.containsKey(sp.getPartNo())){
                sp.setDifferenceNum(collect2.get(sp.getPartNo()).getDifferenceNum());
            }
        }
        updateErrorLinked(parts);
        log.info("application>>shortPartResultDTOS02:{}", shortPartDTOList);
        String roNo = "";
        if(CollectionUtils.isNotEmpty(parts)){
            roNo = parts.get(0).getRoNo();
        }
        // 调用缺料明细生成接口
        DmsResponse<Void> voidDmsResponse = domainMaintainOrdersFeign.saveOrUpdateShortPart(shortPartDTOList, roNo);
        log.info("queryPartInfo res:{}", voidDmsResponse);
        if (Objects.isNull(voidDmsResponse) || voidDmsResponse.isFail()) {
            log.error("缺料明细处理失败");
            throw new ServiceBizException("缺料明细生成失败！");
        }
        // 修改工单零件埋点标识,维修领料页面调用的话，工单零件埋点标识全部更新为S,
        // 生成缺料明细里也计算了埋点标识，主要是为了处理客户接待调用缺料生成，因为这个调用要再生成缺料之后
        if(StringUtils.isEmpty(shortPartDto.getSourceData())){
            String partNos = shortPartDTOList.stream().map(ShortPartDTO::getPartNo).collect(Collectors.joining(","));
            CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();
            if(Objects.isNull(loginInfoDto) || StringUtils.isEmpty(loginInfoDto.getOwnerCode())){
                throw new ServiceBizException("工单零件更新埋点标识,获取登录信息失败");
            }
            domainMaintainOrdersFeign.updatePartBuryingPoint(loginInfoDto.getOwnerCode(), roNo, partNos);
        }

        // 发送缺料相关mq数据
        Optional<String> findFirst = parts.stream().filter(Objects::nonNull).filter(obj->StringUtils.isNotBlank(obj.getRoNo())).map(PartDto::getRoNo).findFirst();
        if(!findFirst.isPresent()) {
        	return null;
        }
        sendTouchpointMessage(roNo);
        shortPartEtaHandle(parts, findFirst.get());
        if(CollectionUtils.isEmpty(shortages)) {
        	return null;
        }
        // 处理自动补货
        List<ShortPartResultDTO> needPurchaseList = shortages.stream().filter(Objects::nonNull).filter(obj-> obj.getIsSelect() == 1).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(needPurchaseList)) {
        	return null;
        }
        
        // 处理补货数量
        List<ShortPartResultDTO> shortPartResultList = shortages.stream().filter(Objects::nonNull).map(obj->{
            ShortPartResultDTO resultDTO = collect2.get(obj.getPartNo());
            BigDecimal replenishmentNum1 = resultDTO.getReplenishmentNum();
            BigDecimal newReplenishmentNum = resultDTO.getNewReplenishmentNum();
            if(Objects.nonNull(newReplenishmentNum) && newReplenishmentNum.compareTo(replenishmentNum1) != 0){
                obj.setReplenishmentNum(newReplenishmentNum);
            }
        	return obj;
        }).collect(Collectors.toList());
        Long res = autoPartPurchaseOrder(shortPartDto, shortPartResultList, findFirst.get());
        return res;
    }

    private void sendTouchpointMessage(String roNo) {
        //发送触点消息（工单缺料）
        CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();
        String ownerCode = null;
        if(Objects.nonNull(loginInfoDto)){
            ownerCode = loginInfoDto.getOwnerCode();
        }
        if(StringUtils.isEmpty(ownerCode)){
            log.error("sendTouchpointMessage ownerCode is null");
            return;
        }
        SceneMessageRemindDto sceneMessageRemindDto = new SceneMessageRemindDto();
        sceneMessageRemindDto.setOwnerCode(loginInfoDto.getOwnerCode());
        sceneMessageRemindDto.setSceneType(CommonConstant.WORK_ORDER_MATERIAL_SHORTAGE);
        sceneMessageRemindDto.setBusinessId(roNo);
        // 发送消息
        workshopMessageReminderProducer.sendOrderMsg(sceneMessageRemindDto, roNo);
    }

    /**
     * 处理缺料零件数据匹配发送消息
     * @param parts
     * @param roNo
     */
	private void shortPartEtaHandle(List<PartDto> parts, String roNo) {
		try {
            log.info("shortPartEtaHandle is begin");
			CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
			String ownerCode = currentLoginInfo.getOwnerCode();
			log.info("获取缺料明细数据：ownerCode:{}, roNo : {}", ownerCode, roNo);
			List<ShortPartDto> data = queryShortInfoBySheetNo(roNo, ownerCode);
			if(Objects.isNull(data)) {
				return;
			}
			Map<String, PartDto> partMap = parts.stream().filter(Objects::nonNull).filter(obj->"OEMK".equals(obj.getStorageCode()) && !"S".equals(obj.getItemUpdateStatus()) && !"D".equals(obj.getItemUpdateStatus())).collect(Collectors.toMap(PartDto::getPartNo, Function.identity(), (k1, k2)->k2));
			// 开发处理缺料ETA数据
			log.info("发送ETA触点：data:{}, roNo: {}, ownerCode: {}, partMap: {}", JSON.toJSONString(data), roNo , ownerCode, JSON.toJSONString(data));
			
			// 优先处理删除数据
			data.stream().filter(Objects::nonNull).filter(obj->obj.getIsDeleted()==1).forEach(obj->sendEtaShortMsg(ownerCode, roNo, partMap, obj));
			data.stream().filter(Objects::nonNull).filter(obj->obj.getIsDeleted()==0).forEach(obj->sendEtaShortMsg(ownerCode, roNo, partMap, obj));
		} catch (Exception e) {
			log.info("处理缺料零件数据消息发送失败：", e);
		}
	}

	private void updateErrorLinked(List<PartDto> parts) {
		try {			
			Optional<String> findFirst = parts.stream().filter(Objects::nonNull).filter(obj->StringUtils.isNotBlank(obj.getRoNo())).map(PartDto::getRoNo).findFirst();
			if(!findFirst.isPresent()) {
				return;
			}
			CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
			String ownerCode = currentLoginInfo.getOwnerCode();
			String roNo = findFirst.get();
			log.info("获取数据：ownerCode:{}, roNo : {}", ownerCode, roNo);
			List<ShortPartDto> data = queryShortInfoBySheetNo(roNo, ownerCode);
			if(Objects.isNull(data)) {
				return;
			}
			shortPartChangePurchaseErrorHandle(parts, data);
			log.info("updateErrorLinked 处理完毕");
		} catch (Exception e) {
			log.info("updateErrorLinked 处理error : ", e);
		}
	}

	/**
	 * 获取数数据
	 * @param roNo
	 * @param ownerCode
	 * @return
	 */
	private List<ShortPartDto> queryShortInfoBySheetNo(String roNo, String ownerCode) {
		DmsResponse<List<ShortPartDto>> queryShortInfoBySheetNo = domainMaintainOrdersFeign.queryShortInfoBySheetNo(ownerCode, roNo, null, null);
        log.info("queryShortInfoBySheetNo:{}", JSONObject.toJSONString(queryShortInfoBySheetNo));
		if(Objects.isNull(queryShortInfoBySheetNo) || queryShortInfoBySheetNo.isFail()) {
			log.info("查询缺料明细失败");
			return new ArrayList<>();
		}
		return queryShortInfoBySheetNo.getData();
	}

	/**
	 * 针对 采购单异常数据 对应的零件数据变更 解绑对应的采购数据关联
	 * @param parts
	 * @param data
	 */
	private void shortPartChangePurchaseErrorHandle(List<PartDto> parts, List<ShortPartDto> data) {
		try {
			// 识别零件是否被变更
			Set<String> partNoSet = parts.stream().filter(Objects::nonNull).filter(obj-> Objects.nonNull(obj.getItemUpdateStatus()) && "U".equals(obj.getItemUpdateStatus())).map(PartDto::getPartNo).collect(Collectors.toSet());
			// 找到对应缺料中已关联且未删除的数据
			Set<Long> ids = data.stream().filter(Objects::nonNull).filter(obj->0==obj.getIsDeleted()).map(ShortPartDto::getPurchaseOrderDetailId).collect(Collectors.toSet());
			// 查询对应采购数据
			if(ids.isEmpty()) {
				return;
			}
			DmsResponse<List<PartPurchaseOrderDetaileDTO>> queryPurchaseOrderById = domainPurchaseFeign.queryPurchaseOrderById(ids);
            log.info("queryPurchaseOrderById:{}", JSON.toJSONString(queryPurchaseOrderById));
			if(Objects.isNull(queryPurchaseOrderById) || queryPurchaseOrderById.isFail()) {
				return;
			}
			List<PartPurchaseOrderDetaileDTO> partPurchaseOrderDetaileList = queryPurchaseOrderById.getData();
			// 过滤出所有异常的采购订单数据明细id
			Set<Long> idSet = partPurchaseOrderDetaileList.stream().filter(Objects::nonNull).filter(obj->Objects.nonNull(obj.getPartsStatus())).map(PartPurchaseOrderDetaileDTO::getId).collect(Collectors.toSet());
			// 提取需要更新还原的数据并做入参对象转换
			UpdateETAMappingDTO updateETAMapping = new UpdateETAMappingDTO();
			
			List<ETAMappingReturnDTO> etaMappingReturnList = data.stream().filter(obj->partNoSet.contains(obj.getPartNo()) && idSet.contains(obj.getPurchaseOrderDetailId())).map(obj->{
				ETAMappingReturnDTO etaMappingReturn = new ETAMappingReturnDTO();
				etaMappingReturn.setIsLinked(10041002);
				etaMappingReturn.setShortId(obj.getShortId());
				etaMappingReturn.setPurchaseOrderDetailId(-1L);
				return etaMappingReturn;
			}).collect(Collectors.toList());
			if(etaMappingReturnList.isEmpty()) {
				return;
			}
			updateETAMapping.setEtaMappingReturnList(etaMappingReturnList);
			updateETAMapping.setForce(10041001);
			domainMaintainOrdersFeign.updateShortETAMappingLinked(updateETAMapping);
		} catch (Exception e) {
			log.info("特殊处理异常：", e);
		}
	}

    /**
     * 处理缺料ETA匹配数据消息
     * @param ownerCode
     * @param roNo
     * @param partMap
     * @param obj
     */
	private void sendEtaShortMsg(String ownerCode, String roNo, Map<String, PartDto> partMap, ShortPartDto obj) {
		try {
			PartDto partDto = partMap.get(obj.getPartNo());
			if(Objects.isNull(partDto) && obj.getIsDeleted()!=1) {
				return;
			}
			ETAOperationParamsDTO etaOperationParamsDTO = dataConvert(ownerCode, roNo, obj);
			if(obj.getIsDeleted()==1) {
				etaOperationParamsDTO.setSceneCode("2");            		
			} else if("A".equals(partDto.getItemUpdateStatus())) {            		
				etaOperationParamsDTO.setSceneCode("1");
			}
			long currentTimeMillis = System.currentTimeMillis();
			etaOperationParamsDTO.setMsgTimeStamp(currentTimeMillis);
			String id = String.join("", etaOperationParamsDTO.getOwnerCode(), etaOperationParamsDTO.getPartNo());
			transparentTocProducer.sendOrderMsg(etaOperationParamsDTO, id);
		} catch (Exception e) {
			log.info("ETA发送异常", e);
		}
	}

	/**
	 * 数据转换
	 * @param ownerCode
	 * @param roNo
	 * @param obj
	 * @return
	 */
	private ETAOperationParamsDTO dataConvert(String ownerCode, String roNo, ShortPartDto obj) {
		ETAOperationParamsDTO etaOperationParamsDTO = new ETAOperationParamsDTO();
		etaOperationParamsDTO.setOwnerCode(ownerCode);
		etaOperationParamsDTO.setIsLinked("10041002");
		etaOperationParamsDTO.setPartNo(obj.getPartNo());
		etaOperationParamsDTO.setPartQuantity(obj.getShortQuantity());
		etaOperationParamsDTO.setSheetNo(roNo);
		etaOperationParamsDTO.setShortId(obj.getShortId());
		etaOperationParamsDTO.setSceneCode("3");
		if(Objects.nonNull(obj.getIsLinked())) {				
			etaOperationParamsDTO.setIsLinked(String.valueOf(obj.getIsLinked()));
			if(10041001==obj.getIsLinked() && Objects.nonNull(obj.getPurchaseOrderDetailId())) {
				etaOperationParamsDTO.setPurchaseOrderDetailId(obj.getPurchaseOrderDetailId());
			}
		}
		return etaOperationParamsDTO;
	}

    /**
     * 自动补货
     * @param shortPartDto
     * @param shortages
     */
	private Long autoPartPurchaseOrder(ShortPartReplenishmentDTO shortPartDto, List<ShortPartResultDTO> shortages, String roNo) {
		log.info("autoPartPurchaseOrder ：shortPartDto : {}, shortages : {}", JSON.toJSONString(shortPartDto), JSON.toJSONString(shortages));
		// 保存接口
        // 转换预约时间
		Date parseDefaultDate = DateUtil.parseDefaultDate(shortPartDto.getReservationTime());
        // 请求零件接口
        List<String> partNos = shortages.stream().filter(Objects::nonNull).filter(obj-> obj.getIsSelect() == 1).map(ShortPartResultDTO::getPartNo).collect(Collectors.toList());
        // 批量查询零件订单内容
        List<PartsInfoDTO> data = queryPartOrderInfoList(shortPartDto, partNos);
        Map<String, PartsInfoDTO> partStockInfoMap = data.stream().filter(Objects::nonNull).collect(Collectors.toMap(PartsInfoDTO::getPartsNo, Function.identity(), (k1, k2)->k1));
        // 查询最小包装数
        String join = String.join(",", partNos);
        Map<String, BigDecimal> minOrderQty = getMinOrderQty(join);
        // 零件明细封装
        List<PartPurchaseOrderDetailDTO> partPurchaseOrderDetailList = shortages.stream().filter(Objects::nonNull).filter(obj-> obj.getIsSelect() == 1).map(obj->{
        	PartPurchaseOrderDetailDTO partPurchaseOrderDetail = new PartPurchaseOrderDetailDTO();
        	partPurchaseOrderDetail.setPartNo(obj.getPartNo());
        	partPurchaseOrderDetail.setPartName(obj.getPartName());
        	partPurchaseOrderDetail.setStockQuantity(obj.getStockQuantity()==null?0D:obj.getStockQuantity().doubleValue());
        	partPurchaseOrderDetail.setDeliveryQuantity(obj.getDeliveryQuantity()==null?0D:obj.getDeliveryQuantity().doubleValue());
        	partPurchaseOrderDetail.setDemandDate(parseDefaultDate);
        	partPurchaseOrderDetail.setVat(1.13D);
        	// 最小包装数
        	BigDecimal packageSize = minOrderQty.get(obj.getPartNo());
        	BigDecimal replenishmentNum = obj.getReplenishmentNum();
        	BigDecimal divided = replenishmentNum.divide(packageSize, RoundingMode.UP);
            BigDecimal rounded = divided.setScale(0, RoundingMode.UP).multiply(packageSize);
            // 需补货数量 需要和最小补货数交叉replenishmentNum
            partPurchaseOrderDetail.setOrderQuantity(rounded.intValue());
        	partPurchaseOrderDetail.setMinOrderQty(packageSize);
        	
        	PartsInfoDTO partsInfo = partStockInfoMap.get(obj.getPartNo());
        	if(Objects.nonNull(partsInfo)) {
        		partPurchaseOrderDetail.setBinding(partsInfo.getBinding());
        		partPurchaseOrderDetail.setPurchasePrice(partsInfo.getCls4());
        		partPurchaseOrderDetail.setRetailPrice(partsInfo.getUnitPrice());
        		partPurchaseOrderDetail.setPurchaseAmount(rounded.multiply(partsInfo.getCls4()).setScale(2, RoundingMode.HALF_UP));
        		partPurchaseOrderDetail.setRebatePercent(partsInfo.getRebatePercent());
        		partPurchaseOrderDetail.setPurchaseQuantity(partsInfo.getPurchaseQuantity());
        	}
        	partPurchaseOrderDetail.setActivityNo("");
        	return partPurchaseOrderDetail;
        }).collect(Collectors.toList());
        
        if(CollectionUtils.isEmpty(partPurchaseOrderDetailList)) {
        	return null;
        }
        // 保存参数封装
        BigDecimal purchaseAmount = partPurchaseOrderDetailList.stream().filter(Objects::nonNull).map(PartPurchaseOrderDetailDTO::getPurchaseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        PartPurchaseOrderDTO partPurchaseOrder = new PartPurchaseOrderDTO();
        partPurchaseOrder.setPurchasePaymentType(shortPartDto.getPurchasePaymentType());
        Integer orderType = shortPartDto.getOrderType();
        partPurchaseOrder.setOrderLevel(orderType);
        partPurchaseOrder.setTransportMode(Integer.valueOf(PurchaseOrderTransPortModeEnum.getTransPortMode(String.valueOf(orderType)).getTransPortMode()));
        partPurchaseOrder.setPurchaseItem(partPurchaseOrderDetailList.size());
        partPurchaseOrder.setPurchaseAmount(purchaseAmount);
        partPurchaseOrder.setPartList(partPurchaseOrderDetailList);
        // 查询是否LDC经销商
        int isLdc = queryDealerIsLdc();
        partPurchaseOrder.setIsLdc(isLdc);
        partPurchaseOrder.setIsGetGoods(false);
        partPurchaseOrder.setLinkedNumber(roNo);
        partPurchaseOrder.setOrderSource(DATA_SOURCES_83751005);
        log.info("接口保存入参：{}", partPurchaseOrder);
        // 保存接口
        DmsResponse<Long> save = dmscusPartFeign.save(partPurchaseOrder);
        if(save==null || Boolean.FALSE.equals(save.isSuccess())) {
        	log.info("保存失败：{}", save);
        	throw new ServiceBizException("自动补货失败,请前往采购订单页面，手动创建采购单！");
        }
        // 上传接口
        PartPurchaseOrderPushDTO partPurchaseOrderPush = new PartPurchaseOrderPushDTO();
        partPurchaseOrderPush.setId(save.getData());
        partPurchaseOrderPush.setPurchasePaymentType(String.valueOf(shortPartDto.getPurchasePaymentType()));
        log.info("接口保存入参：{}", partPurchaseOrder);
        DmsResponse<Integer> purchaseOrderUpload = dmscusPartFeign.purchaseOrderUpload(partPurchaseOrderPush);
        if(purchaseOrderUpload==null || Boolean.FALSE.equals(purchaseOrderUpload.isSuccess()) || purchaseOrderUpload.getData()!=1) {
        	throw new ServiceBizException("自动补货上传失败,请前往采购订单页面，修改并上传！");
        }
        return save.getData();
	}

	private List<PartsInfoDTO> queryPartOrderInfoList(ShortPartReplenishmentDTO shortPartDto, List<String> partNos) {
		PartsInfoDTO partsInfoDTO = new PartsInfoDTO();
        partsInfoDTO.setPageType(1);
        partsInfoDTO.setOrderLevel(String.valueOf(shortPartDto.getOrderType()));
        partsInfoDTO.setPartsNos(partNos);
        log.info("partNo{}", partNos);
        DmsResponse<List<PartsInfoDTO>> queryPartOrderInfoList = domainPartsFeign.queryPartOrderInfoList(partsInfoDTO);
        if(queryPartOrderInfoList==null || Boolean.FALSE.equals(queryPartOrderInfoList.isSuccess())) {
        	throw new ServiceBizException("自动补货失败,请前往采购订单页面，手动创建采购单！");
        }
        return queryPartOrderInfoList.getData();
	}

	private Integer queryDealerIsLdc() {
		try {
            String result = dmscusPartFeign.getDealerType();
            if(StringUtils.isNotBlank(result)) {
            	return 0;
            }
            DmsResponse<String> dealerType = JSON.parseObject(result, DmsResponse.class);
            if(dealerType==null || Boolean.FALSE.equals(dealerType.isSuccess())) {
            	return 0;
            }
            String data2 = dealerType.getData();
            return "LDC经销商".equals(data2)? 1:0;         
		} catch (Exception e) {
			log.info("LDC 调用异常：", e);
		}
		return 0;
	}
    
    @Override
    public Map<String, BigDecimal> getMinOrderQty(String partsNo) {
        Map<String,BigDecimal> partInfoMap=new HashMap<>();
        if(StringUtils.isBlank(partsNo)){
            return partInfoMap;
        }
        try {
            DmsResponse<List<PartMasterFileParamsVo>> result = dmscloudServiceFeign.getInfoByPartNo(partsNo);
            List<PartMasterFileParamsVo> partInfo = result.getData();
            log.info("零件主档信息:{}",partInfo);
            if(CollectionUtils.isNotEmpty(partInfo)){
                partInfoMap= partInfo.stream().filter(e->StringUtils.isNotBlank(e.getPartNo())).map(e->{
                    BigDecimal minOrderQty = e.getMinOrderQty();
                    if(minOrderQty==null||minOrderQty.compareTo(BigDecimal.ONE)<0){
                        e.setMinOrderQty(BigDecimal.ONE);
                    }
                    return e;
                }).collect(Collectors.toMap(PartMasterFileParamsVo::getPartNo, PartMasterFileParamsVo::getMinOrderQty,(k1,k2)->k1));
                log.info("零件主档map{}",partInfoMap);
            }
        } catch (Exception e) {
        	log.error("查询零件主档属性出错",e);
        }finally {
            String[] split = partsNo.split(",");
            for (String s : split) {
                if(!partInfoMap.containsKey(s)){
                	log.info("未查询到零件{}最小包装量",s);
                    partInfoMap.put(s,BigDecimal.ONE);
                }
            }
        }
        return partInfoMap;
    }

    /**
     * 处理数据
     * @param shortPartResultDTOList
     * @param part
     * @param partStockDTO
     * @param partOrderDto
     */
    private void convertData(List<ShortPartResultDTO> shortPartResultDTOList, PartDto part, PartStockDTO partStockDTO, PartOrderDto partOrderDto,boolean uflag){
        // 缺料数量=库存数 - (未出库工单零件总数+当前保存的订单零件数);
        BigDecimal subtract = partStockDTO.getStockQuantity().subtract(partOrderDto.getTotal().add(part.getPracticalQuantity()));
        ShortPartResultDTO spr = new ShortPartResultDTO();
        spr.setItemUpdateStatus(part.getItemUpdateStatus());
        if(part.getItemUpdateStatus().equals("U") || part.getItemUpdateStatus().equals("S")){
            log.info("convertData update start");
            if(uflag){
                DmsResponse<PartOrderDto> partOrderDtoDmsResponse = domainMaintainOrdersFeign.queryPartInfo(part.getItemId());
                log.info("convertData queryPartInfo res:{}", partOrderDtoDmsResponse);
                if (Objects.isNull(partOrderDtoDmsResponse) || partOrderDtoDmsResponse.isFail()) {
                    throw new ServiceBizException("查询更新前零件数量失败");
                }
                if (Objects.isNull(partOrderDtoDmsResponse.getData())) {
                    throw new ServiceBizException("该工单已更新,请刷新页面");
                }
                PartOrderDto data2 = partOrderDtoDmsResponse.getData();
                BigDecimal subtract2 = part.getPracticalQuantity().subtract(data2.getTotal());
                spr.setDifferenceNum(subtract2); // 差异数量
                subtract = partStockDTO.getStockQuantity().subtract(partOrderDto.getTotal().add(subtract2));
                spr.setWorkOrderTotal(partOrderDto.getTotal().add(subtract2));// 工单总数量
                spr.setUpdateBy(data2.getUpdateBy());// 操作人
            }else{
                subtract = partStockDTO.getStockQuantity().subtract(partOrderDto.getTotal());
                spr.setDifferenceNum(BigDecimal.ZERO); // 差异数量
                spr.setWorkOrderTotal(partOrderDto.getTotal());// 工单总数量
            }

        }else{
            spr.setWorkOrderTotal(partOrderDto.getTotal().add(part.getPracticalQuantity()));// 工单总数量
        }
        if(subtract.compareTo(BigDecimal.ZERO) < 0){
            // 小于0说明缺料
            subtract = BigDecimal.ZERO.subtract(subtract);
            spr.setPartNo(partStockDTO.getPartNo());
            spr.setPartName(partStockDTO.getPartName());
            if(subtract.compareTo(part.getPracticalQuantity()) > 0){
                spr.setMaterialShortageNum(part.getPracticalQuantity()); // 本次缺料数量
            }else{
                spr.setMaterialShortageNum(subtract); // 本次缺料数量
            }
            spr.setStockQuantity(partStockDTO.getStockQuantity()); // 库存数量
            if(Objects.isNull(partStockDTO.getDeliveryQuantity())){
                partStockDTO.setDeliveryQuantity(new BigDecimal("0.00"));
            }
            if(partStockDTO.getDeliveryQuantity().compareTo(subtract) >= 0){
                // 需补货数量: 在途数量 大于等于 缺料数，则无需补货
                spr.setReplenishmentNum(BigDecimal.ZERO);
            }else{
                spr.setReplenishmentNum(subtract.subtract(partStockDTO.getDeliveryQuantity())); // 需补货数量 = 本次缺料数量 - 在途数量
            }
            spr.setDeliveryQuantity(partStockDTO.getDeliveryQuantity());
            if(spr.getReplenishmentNum().compareTo(BigDecimal.ZERO) == 0 && !Objects.isNull(partStockDTO.getDeliveryQuantity()) && partStockDTO.getDeliveryQuantity().compareTo(BigDecimal.ZERO) > 0){
                spr.setShortageDescription("在途订单已满足"); // 缺料在途说明
                spr.setSort(1);
            }
            boolean flag = false;
            if(StringUtils.isNotBlank(part.getRoNo())){
                DmsResponse<Boolean> booleanDmsResponse = domainPurchaseFeign.checkPartPurchaseOrder(part.getRoNo(), part.getPartNo());
                log.info("checkPartPurchaseOrder res:{}", booleanDmsResponse);
                if (!Objects.isNull(booleanDmsResponse) && booleanDmsResponse.isSuccess()) {
                    flag = booleanDmsResponse.getData();
                }
            }
            if(spr.getReplenishmentNum().compareTo(BigDecimal.ZERO) == 0 && !Objects.isNull(partStockDTO.getDeliveryQuantity()) && partStockDTO.getDeliveryQuantity().compareTo(BigDecimal.ZERO) > 0 && flag){
                // 判断在途数量是否关联工单，如果关联了工单则显示
                spr.setShortageDescription("已存在在途订单"); // 缺料在途说明
                spr.setSort(1);
            }
            if(spr.getReplenishmentNum().compareTo(BigDecimal.ZERO) > 0 && !Objects.isNull(partStockDTO.getDeliveryQuantity()) && partStockDTO.getDeliveryQuantity().compareTo(BigDecimal.ZERO) > 0){
                spr.setShortageDescription("在途订单不足"); // 缺料在途说明
                spr.setSort(2);
            }
            if(spr.getReplenishmentNum().compareTo(BigDecimal.ZERO) > 0 && (Objects.isNull(partStockDTO.getDeliveryQuantity()) || partStockDTO.getDeliveryQuantity().compareTo(BigDecimal.ZERO) == 0)){
                spr.setShortageDescription("不存在在途订单"); // 缺料在途说明
                spr.setSort(2);
            }
            shortPartResultDTOList.add(spr);
        }else{
            // 大于或等于0，不缺料
            spr.setPartNo(partStockDTO.getPartNo());
            spr.setPartName(partStockDTO.getPartName());
            spr.setMaterialShortageNum(BigDecimal.ZERO); // 本次缺料数量
            spr.setStockQuantity(partStockDTO.getStockQuantity()); // 库存数量
            spr.setReplenishmentNum(BigDecimal.ZERO);// 需补货数
            spr.setDeliveryQuantity(partStockDTO.getDeliveryQuantity());// 在途数
            spr.setShortageDescription("现货已满足"); // 缺料在途说明
            spr.setSort(0);
            shortPartResultDTOList.add(spr);
        }
    }
    /**
     * 处理数据
     */
    private void convertShortPartData(List<ShortPartDTO> shortPartDTOList, PartDto partDto, ShortPartResultDTO sprd, String itemUpdateStatus){
        ShortPartDTO spdto = new ShortPartDTO();
        spdto.setPartNo(partDto.getPartNo());
        spdto.setPartName(partDto.getPartName());
        spdto.setSheetNo(partDto.getRoNo());
        spdto.setLicense(partDto.getLicense());
        spdto.setCustomerName(partDto.getOwnerName());
        spdto.setPhone(partDto.getPhone());
        spdto.setStorageCode(partDto.getStorageCode());
        spdto.setShortQuantity(Objects.isNull(sprd) ? BigDecimal.ZERO : sprd.getMaterialShortageNum());
        spdto.setItemUpdateStatus(Objects.isNull(itemUpdateStatus) ? partDto.getItemUpdateStatus() : itemUpdateStatus);
        spdto.setDifferenceNum(Objects.isNull(sprd) ? BigDecimal.ZERO : sprd.getDifferenceNum());
        log.info("convertShortPartData：{}", JSON.toJSONString(spdto));
        shortPartDTOList.add(spdto);
    }

	@Override
	public Page<Map<String, String>> findAll(Map<String, String> queryParams) {
		
		DmsResponse<Page<Map<String,String>>> findAll = dmscloudServiceFeign.findAll(queryParams);
		if(Objects.isNull(findAll) || findAll.isFail()) {
			throw new ServiceBizException(Objects.isNull(findAll) ? "系统异常" : findAll.getErrMsg());
		}
		Page<Map<String,String>> data = findAll.getData();
		if(Objects.isNull(data) || CollectionUtils.isEmpty(data.getRecords())) {
			return data;
		}
		shortPartSupplement(data);
		return data;
	}

    @Override
    public void queryShortPartExport(Map map) {
        log.info("queryShortPartExport Time: {}", LocalDateTime.now().format(formatter));
        try{
            //获取导出字段
            map.put("pageSize", 500);
            List<ExcelExportColumn> exportColumnList = buildExcelColumn();
            DownloadDto downloadDto = new DownloadDto();
            //batchNo转map
            downloadDto.setQueryParams(map);
            downloadDto.setExcelName("缺料明细导出.xlsx");
            downloadDto.setSheetName("缺料明细");
            downloadDto.setServiceUrl(RenewalOfInsuranceCluesConstant.SHORTPART_EXPORT);
            downloadDto.setExcelExportColumnList(exportColumnList);
            downloadServiceFeign.downloadExportExcel(downloadDto);
        } catch (Exception e){
            log.error("queryShortPartExport：fail",e);
            throw new ServiceBizException("缺料明细导出失败");
        }
    }

    @Override
    public List<Map> queryShortPart(Map map) {
        log.info("application-queryShortPart:{}", map);
        RestResultResponse<List<Map>> restResultResponse = dmscloudReportFeign.queryShortPart(map);
        if (!restResultResponse.isSuccess()) {
            throw new ServiceBizException("调用dmscloud-report失败！");
        }
        List<Map> data = restResultResponse.getData();
        log.info("application-queryShortPart:{}", data);
        if (CollectionUtils.isEmpty(data)) {
            log.info("import queryShortPart data is empty.");
            return Collections.emptyList();
        }
        // 填充服务顾问及技师
        this.setFWGWandJS(data);
        return data;
    }

    @Override
    public List<ShortPartResultDTO> checkReplenishmentNew(String ownerCode, String roNo, Integer orderFlag) {
        //根据工单号及经销商查询零件信息
        DmsResponse<List<PartDto>> listDmsResponse = domainMaintainOrdersFeign.queryOrderPartsInfo(ownerCode, roNo);
        log.info("application-checkReplenishmentNew:{}", JSON.toJSONString(listDmsResponse));
        if(Objects.isNull(listDmsResponse) || listDmsResponse.isFail()){
            throw new ServiceBizException("查询工单零件失败");
        }
        //组装缺料弹窗接口参数
        List<PartDto> data = listDmsResponse.getData();
        if(CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        PartDto partDto = data.get(0);
        if(StringUtils.isNotEmpty(partDto.getLockUser())){
            throw new ServiceBizException("该工单已被锁定,请解锁后再试");
        }
        //调用原缺料检查弹框接口
        List<ShortPartResultDTO> shortPartResultDTOS = this.checkReplenishment(data, true);
        //更新操作人(工单标识)
        domainMaintainOrdersFeign.updateRepairOrderExtOperate(ownerCode, roNo, orderFlag);
        return shortPartResultDTOS;
    }

    @Override
    public Long shortageReplenishmentNew(ShortPartReplenishmentDTO shortPartDto) {
        log.info("application-shortageReplenishmentNew:{}", JSON.toJSONString(shortPartDto));
        RLock lock = redissonClient.getLock(KEY_REPLENISHMENT_LOCK+":"+shortPartDto.getOwnerCode()+":"+shortPartDto.getRoNo());
        try {
            if (!lock.tryLock()) {
                throw new ServiceBizException("该工单正在操作,请稍后再试");
            }
            //根据工单号及经销商查询零件信息
            DmsResponse<List<PartDto>> listDmsResponse = domainMaintainOrdersFeign.queryOrderPartsInfo(shortPartDto.getOwnerCode(), shortPartDto.getRoNo());
            log.info("application-checkReplenishmentNew:{}", JSON.toJSONString(listDmsResponse));
            if(Objects.isNull(listDmsResponse) || listDmsResponse.isFail()){
                throw new ServiceBizException("查询工单零件失败");
            }
            List<PartDto> data = listDmsResponse.getData();
            shortPartDto.setParts(data);
            // 调原缺料补货接口
            Long aLong = this.shortageReplenishment(shortPartDto);
            return aLong;
        }catch (Exception e){
            log.error("缺料补货刷新失败", e);
            throw new ServiceBizException(e.getMessage());
        }finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    /**
     * 填充服务顾问及技师
     * @param data
     */
    private void setFWGWandJS(List<Map> data){
        // 处理服务顾问
        List<Long> serviceAdvisorIds = data.stream()
                .filter(dto -> Objects.nonNull(dto.get("service_advisor")))
                .map(config -> Long.parseLong(config.get("service_advisor").toString())).collect(Collectors.toList());
        log.info("setFWGWandJS export:{}", JSONObject.toJSONString(serviceAdvisorIds));
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(serviceAdvisorIds)){
            // 2. 使用midEndAuthCenterFeign.queryUserInfoByIds查询用户信息
            List<Long> requestServiceAdvisorIds = serviceAdvisorIds.stream().distinct().collect(Collectors.toList());
            UserInfoByUserIdsDto buildUserInfoIds = UserInfoByUserIdsDto.builder().userIds(requestServiceAdvisorIds).build();
            RequestDto<UserInfoByUserIdsDto> requestDto = new RequestDto<>();
            requestDto.setData(buildUserInfoIds);
            ResponseDto<List<UserInfoVo>> userInfoList = midEndAuthCenterFeign.queryUserInfoByIds(requestDto);
            if (userInfoList.isFail()) {
                throw new ServiceBizException("获取组织中心用户信息失败！");
            }
            // 3. 将查询回来的用户信息赋值给 shortPartResultDTOList02 中的每一个对象
            Map<Integer, UserInfoVo> userInfoMap = userInfoList.getData().stream()
                    .collect(Collectors.toMap(UserInfoVo::getUserId, Function.identity(), (k1, k2) -> k1));

            data.stream()
                    .filter(dto -> Objects.nonNull(dto.get("service_advisor")))
                    .forEach(config -> {
                        UserInfoVo userInfo = userInfoMap.get(Integer.parseInt(config.get("service_advisor").toString()));
                        if (userInfo != null) {
                            config.put("service_advisor", userInfo.getEmployeeName());
                        }
                    });
        }
        // 处理技师
        List<Long> technicianIds = new ArrayList<>();
        data.stream().filter(item -> Objects.nonNull(item.get("technician"))).forEach(item -> {
            String technician = item.get("technician").toString();
            String[] technicianSplit = technician.split(",");
            for (String technicianId: technicianSplit) {
                technicianIds.add(Long.valueOf(technicianId));
            }
        });
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(technicianIds)){
            // 2. 使用midEndAuthCenterFeign.queryUserInfoByIds查询用户信息
            List<Long> requestTechnicianIds = technicianIds.stream().distinct().collect(Collectors.toList());
            UserInfoByUserIdsDto buildUserInfoIds = UserInfoByUserIdsDto.builder().userIds(requestTechnicianIds).build();
            RequestDto<UserInfoByUserIdsDto> requestDto = new RequestDto<>();
            requestDto.setData(buildUserInfoIds);
            ResponseDto<List<UserInfoVo>> userInfoList = midEndAuthCenterFeign.queryUserInfoByIds(requestDto);
            if (userInfoList.isFail()) {
                throw new ServiceBizException("获取组织中心用户信息失败！");
            }
            // 3. 将查询回来的用户信息赋值给 shortPartResultDTOList02 中的每一个对象
            Map<Integer, UserInfoVo> userInfoMap = userInfoList.getData().stream()
                    .collect(Collectors.toMap(UserInfoVo::getUserId, Function.identity(), (k1, k2) -> k1));

            data.stream().filter(item -> Objects.nonNull(item.get("technician"))).forEach(item -> {
                String technician = item.get("technician").toString();
                String[] technicianSplit = technician.split(",");
                StringBuilder str = new StringBuilder();
                for (String technicianId: technicianSplit) {
                    UserInfoVo userInfo = userInfoMap.get(Integer.parseInt(technicianId));
                    if (userInfo != null) {
                        str.append(userInfo.getEmployeeName()+"/");
                    }
                }
                item.put("technician", str.length() > 0 ? str.substring(0, str.length() - 1) : str.toString());
            });
        }
    }
    /**
     * 导出字段
     */
    private List<ExcelExportColumn> buildExcelColumn() {
        List<ExcelExportColumn> exportColumnList = new ArrayList<>();
        exportColumnList.add(new ExcelExportColumn("sheet_no","单据号码"));
        exportColumnList.add(new ExcelExportColumn("is_urgent","是否急件",ExcelDataType.Dict));
        exportColumnList.add(new ExcelExportColumn("close_status","是否已结案",ExcelDataType.Dict));
        exportColumnList.add(new ExcelExportColumn("short_type","缺料类型",ExcelDataType.Dict));
        exportColumnList.add(new ExcelExportColumn("storage_name","仓库"));
        exportColumnList.add(new ExcelExportColumn("storage_position_code","库位代码"));
        exportColumnList.add(new ExcelExportColumn("license","车牌号"));
        exportColumnList.add(new ExcelExportColumn("part_no","零件号"));
        exportColumnList.add(new ExcelExportColumn("part_name","零件名称"));
        exportColumnList.add(new ExcelExportColumn("stock_quantity","当前库存量"));
        exportColumnList.add(new ExcelExportColumn("short_quantity","缺件数量"));
        exportColumnList.add(new ExcelExportColumn("customer_name","客户名称"));
        exportColumnList.add(new ExcelExportColumn("phone","电话"));
        exportColumnList.add(new ExcelExportColumn("in_out_type","出入库类型",ExcelDataType.Dict));
        exportColumnList.add(new ExcelExportColumn("purchase_no","采购订单号"));
        exportColumnList.add(new ExcelExportColumn("missing_parts_status","缺料状态"));
        exportColumnList.add(new ExcelExportColumn("expected_delivery_time","预计到货时间"));
        exportColumnList.add(new ExcelExportColumn("parts_status","零件状态"));
        exportColumnList.add(new ExcelExportColumn("replace_parts","替换件号"));
        exportColumnList.add(new ExcelExportColumn("service_advisor","服务顾问"));
        exportColumnList.add(new ExcelExportColumn("technician","技师"));
        return exportColumnList;
    }

    /**
	 * 缺料数据补充
	 * @param data
	 */
	private void shortPartSupplement(Page<Map<String, String>> data) {
		try {			
			List<Map<String,String>> records = data.getRecords();
			Map<String, String> map = new HashMap<>();
            Map<String, List<OrderServiceInfoVO>> groupedData = new HashMap<>();
			List<ShortPartDto> collect = records.stream().filter(Objects::nonNull).map(obj->{
				ShortPartDto shortPartDto = new ShortPartDto();
				String roNo = obj.get("RO_NO");
				String ownerCode = obj.get("OWNER_CODE");
				shortPartDto.setOwnerCode(ownerCode);
				shortPartDto.setSheetNo(roNo);
				return shortPartDto;
			}).collect(Collectors.toList());
            // 查询服务过程信息
            DmsResponse<List<OrderServiceInfoVO>> serviceInfoData = domainMaintainOrdersFeign.queryServiceInfomation(collect);
            if(Objects.nonNull(serviceInfoData) && serviceInfoData.isSuccess()) {
                List<OrderServiceInfoVO> data1 = serviceInfoData.getData();
                if (data1 != null && !data1.isEmpty()) {
                    // 按照 ownerCode + orderNo 进行分组
                    groupedData = data1.stream()
                            .collect(Collectors.groupingBy(item -> item.getOwnerCode() + item.getOrderNo()));
                }
            }

            DmsResponse<List<ShortPartDto>> batchShortInfoBySheetNo = domainMaintainOrdersFeign.batchShortInfoBySheetNo(collect);
			if(Objects.nonNull(batchShortInfoBySheetNo) && batchShortInfoBySheetNo.isSuccess()) {
				List<ShortPartDto> shortPartList = batchShortInfoBySheetNo.getData();
				shortPartList.stream().filter(Objects::nonNull).forEach(obj->{
					String key = String.join("", obj.getOwnerCode(), obj.getSheetNo());
					map.put(key, "-1");
				});
				
				Set<Long> ids = shortPartList.stream().filter(Objects::nonNull).map(ShortPartDto::getPurchaseOrderDetailId).filter(Objects::nonNull).collect(Collectors.toSet());
				if(!ids.isEmpty()) {
					DmsResponse<List<PartPurchaseOrderDetaileDTO>> queryPurchaseOrderById = domainPurchaseFeign.queryPurchaseOrderById(ids);
					if(Objects.nonNull(queryPurchaseOrderById) && queryPurchaseOrderById.isSuccess()) {
						List<PartPurchaseOrderDetaileDTO> partPurchaseOrderDetaileList = queryPurchaseOrderById.getData();
						Map<Long, PartPurchaseOrderDetaileDTO> partPurchaseOrderDetaileMap = partPurchaseOrderDetaileList.stream().filter(Objects::nonNull).collect(Collectors.toMap(PartPurchaseOrderDetaileDTO::getId, Function.identity(), (k1,k2)->k1));
						shortPartList.stream().filter(Objects::nonNull).forEach(shortPart->expectedDeliveryTimeAutoMate(partPurchaseOrderDetaileMap, map, shortPart));
					}
				}
			}
			log.info("map : {}", JSON.toJSONString(map));
            Map<String, List<OrderServiceInfoVO>> finalGroupedData = groupedData;
            records.forEach(obj->{
				String roNo = obj.get("RO_NO");
				String ownerCode = obj.get("OWNER_CODE");
				String key = String.join("", ownerCode, roNo);

				String expectedDeliveryTime = map.get(key);
				String isShortPart = StringUtils.isNotBlank(expectedDeliveryTime)?"10041001":"10041002";
				List<String> asList = Arrays.asList("-1","-2");
				if(Objects.nonNull(expectedDeliveryTime) && asList.contains(expectedDeliveryTime)) {
					expectedDeliveryTime = null;
				}
				obj.put("expectedDeliveryTime", expectedDeliveryTime);
				obj.put("isShortPart", isShortPart);
                // 填充服务过程信息
                List<OrderServiceInfoVO> orderServiceInfoVOS = finalGroupedData.get(key);
                if (orderServiceInfoVOS != null && !orderServiceInfoVOS.isEmpty()) {
                    obj.put("serviceInfomationList", JSON.toJSONString(orderServiceInfoVOS));
                }else{
                    obj.put("serviceInfomationList", "[]");
                }
            });
			data.setRecords(records);
		} catch (Exception e) {
			log.info("缺料数据补偿异常：{}");
		}
	}

	/**
	 * 预计到货时间选择自动匹配
	 * @param partPurchaseOrderDetaileMap
	 * @param map
	 * @param shortPart
	 */
	private void expectedDeliveryTimeAutoMate(Map<Long, PartPurchaseOrderDetaileDTO> partPurchaseOrderDetaileMap,
			Map<String, String> map, ShortPartDto shortPart) {
		Long purchaseOrderDetailId = shortPart.getPurchaseOrderDetailId();
		String key = String.join("", shortPart.getOwnerCode(), shortPart.getSheetNo());
		PartPurchaseOrderDetaileDTO partPurchaseOrderDetaile = partPurchaseOrderDetaileMap.get(purchaseOrderDetailId);
		List<Long> asList = Arrays.asList(81181001L,81181002L);
		String oldExpectedDeliveryTime = map.get(key);
		if(Objects.isNull(partPurchaseOrderDetaile) || asList.contains(partPurchaseOrderDetaile.getPartsStatus()) || "-2".equals(oldExpectedDeliveryTime)) {
			map.put(key, "-2");
			return;
		}
		String expectedDeliveryTime = partPurchaseOrderDetaile.getExpectedDeliveryTime();
		if(StringUtils.isBlank(expectedDeliveryTime)) {
			map.put(key, "-1");
		}
		if(compareTwoDateStr(oldExpectedDeliveryTime, expectedDeliveryTime)) {
			return;
		}
		map.put(key, expectedDeliveryTime);
	}

	/**
	 * 时间比较
	 * @param oldExpectedDeliveryTime
	 * @param expectedDeliveryTime
	 * @return
	 */
	private boolean compareTwoDateStr(String oldExpectedDeliveryTime, String expectedDeliveryTime) {
		try {
			if(StringUtils.isBlank(expectedDeliveryTime)) {
				return true;
			}
			if(StringUtils.isBlank(oldExpectedDeliveryTime)) {
				return false;
			}
			if("-1".equals(oldExpectedDeliveryTime)) {
				return false;
			}
			Date oldExpectedDeliveryTimeDate = DateUtil.parseDefaultDateTime(oldExpectedDeliveryTime);
			Date expectedDeliveryTimeDate = DateUtil.parseDefaultDateTime(expectedDeliveryTime);
			int compare = cn.hutool.core.date.DateUtil.compare(oldExpectedDeliveryTimeDate, expectedDeliveryTimeDate);
			return compare>=0;
		} catch (Exception e) {
			log.info("时间比较异常：{}", e);
		}
		return true;
	}
}
