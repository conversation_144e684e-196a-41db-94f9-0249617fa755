package com.volvo.maintain.application.maintainlead.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class FollowUpPageDto implements Serializable {

    List<String> codes;

    List<Integer> inviteTypes;

    List<String> vehicleTag;
    List<String> customerTag ;
}
