package com.volvo.maintain.application.maintainlead.mq.producer;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.workshop.SceneMessageRemindDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WorkshopMessageReminderProducer {

    @Autowired
    @Qualifier("tocTransparent")
    private RocketMQTemplate rocketMQTemplate;


    @Value("${rocketmq.workshopRemind.topic:NOTIFICATION_STATUS_CHANGE}")
    private String topic;


    /**
     *
     * @param sceneMessageRemindDto 消息体
     * @param id
     */
    public void sendOrderMsg(SceneMessageRemindDto sceneMessageRemindDto, String id) {
        log.info("发送触点消息：{}", JSON.toJSONString(sceneMessageRemindDto));
        String msg = JSON.toJSONString(sceneMessageRemindDto);
        sendMsg(topic.trim(), msg, id);
    }

    /**
     * 发送消息
     *
     * @param topic
     * @param msg
     */
    public void sendMsg(String topic, String msg, String id) {
        this.rocketMQTemplate.syncSendOrderly(topic,msg, String.valueOf(id));
    }

}
