package com.volvo.maintain.application.maintainlead.service.customerProfile;


import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.message.MessageSendDto;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;

import java.util.List;

public interface CommonMethodService {


    TmVehicleDto queryVehicle(String vin);

    Boolean pushSms(String key, PushMessageRecordVo pushMessageRecord, MessageSendDto messageSendDto);

    boolean pushSms(MessageSendDto messageSendDto);

    List<EmpByRoleCodeDto> getEmpByRoleCodeDtos(EmpByRoleCodeDto empByRoleCodeDto);

    Boolean pushEmail(String key, PushMessageRecordVo pushMessageRecord, EmailInfoDto emailInfoDto);

    boolean pushEmail(EmailInfoDto emailInfoDto);

    TmVehicleDto queryVehicleByVin(String vin);

    /**
     * 根据经销商查询角色信息
     * @param companyCode
     * @return
     */
    List<RoleInfoDto> queryRoleListByCompanyCode(String companyCode);

    Boolean checkWhitelist(String dealer, Integer modType, Integer rosterType, String vin, String groupCode);

    String getOwnerCode();

    List<CustomerInfoDto> queryCustomInfoListByVin(String vin, String ownerCode);
}
