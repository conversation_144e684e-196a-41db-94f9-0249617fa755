package com.volvo.maintain.application.maintainlead.service.customerProfile;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.FollowUpPageDto;
import com.volvo.maintain.application.maintainlead.dto.inviteClue.InvitationScripDto;

import java.util.List;

public interface InvitationService {

    /**
     * 邀约话术管理查询
     * @param current
     * @param size
     * @return
     */
    Page<InvitationScripDto> invitationScriptManageQuery(Integer current, Integer size, String typeCodeId,Integer isEnable,String title,String tagTalkskill);

    /**
     * 邀约话术渲染页面
     * @param followUpPageDto
     * @return
     */
    List<InvitationScripDto> invitationScriptFollowUpPage(FollowUpPageDto followUpPageDto);

    /**
     * 邀约话术管理查询详情
     * @param id
     * @return
     */
    InvitationScripDto invitationScriptManageDetail(Integer id);

    Integer saveInvitationScript(InvitationScripDto invitationScripDto);

    Integer updateInvitationScript(InvitationScripDto invitationScripDto);

    Integer deleteInvitationScript(Integer id);


    List<InvitationScripDto> scriptManageQuery(List<String> tags, String typeCodeId);
}
