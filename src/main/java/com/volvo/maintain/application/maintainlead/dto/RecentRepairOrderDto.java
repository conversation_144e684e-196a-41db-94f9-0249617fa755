package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@ApiModel("维修工单信息")
public class RecentRepairOrderDto {
    @ApiModelProperty("经销商code")
    private String ownerCode;
    @ApiModelProperty("工单号")
    private String roNo;
    @ApiModelProperty("车架号")
    private String vin;
    @ApiModelProperty("开单时间（格式：yyyy-MM-dd HH:mm:ss")
    private String roCreateDate;
    @ApiModelProperty("维修类型（'G','保修'、'I','事故'、'M','保养'、'N','机电维修'、'P','PDS'、'S','零售'")
    private String repairTypeCode;

}
