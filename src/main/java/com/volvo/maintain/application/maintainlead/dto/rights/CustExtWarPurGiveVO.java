package com.volvo.maintain.application.maintainlead.dto.rights;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "C端延保产品购买信息数据实体类", description = "C端延保产品购买信息数据实体类")
public class CustExtWarPurGiveVO {
    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * vin车架号
     */
    @ApiModelProperty(value = "车架号")
    private String vin;

    /**
     * 经销商编号
     */
    @ApiModelProperty(value = "经销商编号")
    private String dealerCode;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String licensePlateNum;

    /**
     * 延保零件号(产品件号)
     */
    @ApiModelProperty(value = "延保零件号(产品件号)")
    private String productNo;

    /**
     * 延保零件名称(产品名称)
     */
    @ApiModelProperty(value = "延保零件名称(产品名称)")
    private String productName;

    /**
     * 延保购买时间
     */
    @ApiModelProperty(value = "延保购买时间", example = "2019-12-06 13:00:00")
    private String purchaseDate;

    /**
     * 延保生效日期
     */
    @ApiModelProperty(value = "延保生效日期", example = "2019-12-06 13:00:00")
    private String effectiveDate;

    /**
     * 延保失效日期
     */
    @ApiModelProperty(value = "延保失效日期", example = "2019-12-06 13:00:00")
    private String expireDate;

    /**
     * 车辆销售日期
     */
    @ApiModelProperty(value = "车辆销售日期", example = "2019-12-06 13:00:00")
    private String sellDate;

    /**
     * 延保单更新日期
     */
    @ApiModelProperty(value = "延保单更新日期", example = "2019-12-06 13:00:00")
    private String updatedTime;

    /**
     * 延保提供方
     */
    @ApiModelProperty(value = "延保提供方（81541001：PICC  81541002：易保）")
    private Integer provider;

    /**
     * 延保类型
     */
    @ApiModelProperty(value = "延保类型（81501001：普通延保产品   81501002：新二手车延保  81501003：Evcar延保  83451001：出行无忧  83451002：钥匙险）")
    private Integer extensionType;

    /**
     * 延保年数
     */
    @ApiModelProperty(value = "延保年数(库里存放年限数字，不存枚举)")
    private Integer numberOfYears;

    /**
     * 延保购买记录来源
     */
    @ApiModelProperty(value = "延保购买记录来源(81561001：店端自购    81561002：厂端赠送)")
    private Integer source;

    /**
     * 产品大类(业务类型): 83441001:车险   83441002:非车险
     */
    @ApiModelProperty(value = "产品大类(业务类型): 83441001:车险   83441002:非车险")
    private Integer bizType;

    /**
     * 保单号
     * */
    @ApiModelProperty(value = "保单号")
    private String insuranceOrderNo;

    /**
     * 采购单号
     * */
    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNo;

    /**
     * 工单号
     * */
    @ApiModelProperty(value = "工单号")
    private String orderNo;

    /**
     * 扩展表信息 add by wlj 20230724
     */
    @ApiModelProperty(value = "订单编号")
    private String orderCode;
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;
    @ApiModelProperty(value = "状态")
    private Integer giveStatus;
    @ApiModelProperty(value = "渠道")
    private int giveChannel;
    @ApiModelProperty(value = "扩展字段,用于记录车辆里程、车型、开票日期等等")
    private String tocExtend;
    @ApiModelProperty(value = "店端名称")
    private String dealerName;
    @ApiModelProperty(value = "经销商代码")
    private String ownerCode;

    private List<TireInfoVo> tireInfoVos;
}
