package com.volvo.maintain.application.maintainlead.dto;

import com.volvo.maintain.infrastructure.constants.CommonConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("Response DTO")
public class ResponseDto<T> implements Serializable {
	private static final long serialVersionUID = 1L;

	private static String successCode = "200";
	private static String successMsg = "成功";

	private static String failCode = "500";
	private static String failMsg = "失败";

	@ApiModelProperty(value = "返回代码")
	private String returnCode;

	@ApiModelProperty(value = "返回描述")
	private String returnMessage;

	@ApiModelProperty(value = "返回数据")
	private T data;

	public ResponseDto success() {
		this.returnCode = successCode;
		this.returnMessage = successMsg;
		this.data = null;
		return this;
	}

	public ResponseDto successMsg(String msg) {
		this.returnCode = successCode;
		this.returnMessage = msg;
		this.data = null;
		return this;
	}

	public ResponseDto successData(T data) {
		this.returnCode = successCode;
		this.returnMessage = successMsg;
		this.data = data;
		return this;
	}

	public ResponseDto successMsgData(String msg, T data) {
		this.returnCode = successCode;
		this.returnMessage = msg;
		this.data = data;
		return this;
	}

	public ResponseDto fail() {
		this.returnCode = failCode;
		this.returnMessage = failMsg;
		this.data = null;
		return this;
	}

	public ResponseDto failMsg(String msg) {
		this.returnCode = failCode;
		this.returnMessage = msg;
		this.data = null;
		return this;
	}

	public ResponseDto failData(T data) {
		this.returnCode = failCode;
		this.returnMessage = failMsg;
		this.data = data;
		return this;
	}

	public ResponseDto failMsgData(String msg, T data) {
		this.returnCode = failCode;
		this.returnMessage = msg;
		this.data = data;
		return this;
	}

	public ResponseDto error(String msg) {
		this.returnCode = failCode;
		this.returnMessage = msg;
		return this;
	}

	public boolean isSuccess() {
		return Objects.equals(this.getReturnCode(), CommonConstant.SUCCESS_CODE);
	}

	public boolean isFail() {
		return !isSuccess();
	}


}
