package com.volvo.maintain.application.maintainlead.dto.vhc;

import com.volvo.po.BasePO;
import lombok.Data;

/**
 * <p>
 * 车辆健康检查项目表
 * </p>
 *
 * @since 2024-09-24
 */
@Data
public class VhcItemPoDTO extends BasePO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 经销商
     */
    private String ownerCode;

    /**
     * 车辆健康检查编号
     */
    private String vhcNo;

    /**
     * 检查项名称
     */
    private String itemName;

    /**
     * 检查项大类
     */
    private String itemClassId;

    /**
     * 检查项备注
     */
    private String itemContent;

    /**
     * 检查项照片
     */
    private String itemPhotograph;

    /**
     * 检查项数值
     */
    private String itemNumerical;

    /**
     * 检查项故障现象代码
     */
    private String itemMalfunctionCode;

    /**
     * 检查项故障现象
     */
    private String itemMalfunction;

    /**
     * 检查详细备注
     */
    private String itemTxt;

    /**
     * 配置表配置项id
     */
    private String configItemId;


    /**
     * 预计服务时长
     */
    private String workDate;

    /**
     * 确认状态
     */
    private String confirmState;

    /**
     * 不修原因反馈
     */
    private String noRepairCause;

    /**
     * 维修结果
     */
    private String classResult;

    /**
     * 不修原因反馈是否有效（10011001有效，10011002无效）
     */
    private String noRepairCauseValid;

    /**
     * 不修原因来源渠道（b端，c端）
     */
    private String noRepairCauseChannel;

    /**
     * 排序
     */
    private Integer itemSeq;

    /**
     * 是否删除
     */
    private Integer isDeleted;


}
