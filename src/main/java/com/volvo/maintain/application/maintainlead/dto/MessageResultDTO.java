package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("弹窗提示返回dto")
public class MessageResultDTO {
    /**
     * 类型：
     */
    private String businessType;
    /**
     * 单号
     */
    private String businessId;
    /**
     * 话术列表
     */
    private List<MessagePopupInfoDTO> popupContentList;
    /**
     * 是否弹窗过
     */
    private Boolean isPopup;
    /**
     * 群组id
     */
    private String groupId;
    /**
     * 内容
     */
    private String popupContent;
}