package com.volvo.maintain.application.maintainlead.service.workshop;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.*;

import java.util.List;

public interface LucencyWorkShopService {

    //开单页面查询
    Page<BeginOrderDTO> selectBeginOrderList(QueryParamDto queryParamDto);
    //查询派工数量
    List<StatusCountDTO> selectAssignStatusCount(QueryParamDto queryParamDto);
    //维修页面查询
    Page<WorkShopRepairOrderDTO> selectWorkshopRepairOrder(QueryParamDto queryParamDto);
    //查询打卡数量
    List<StatusCountDTO> selectIsPunchCount(QueryParamDto queryParamDto);
    //结算页面查询
    Page<WorkShopBalanceOrderDTO> selectWorkShopBalanceOrder(QueryParamDto queryParamDto);
    //查询交车数量
    List<StatusCountDTO> selectDeliveryTagCount(QueryParamDto queryParamDto);
    //看板交车
    Page<DeliveryDTO> selectDeliveryList(QueryParamDto queryParamDto);
    //企微菜单上的角标数量查询
    List<SignQuantityDTO> signQuantity(String ownerCode,String beginDate,String endDate);
    //各菜单今日数量
    List<SignQuantityDTO> menuOrderQuantity(String ownerCode,String beginDate,String endDate);

}
