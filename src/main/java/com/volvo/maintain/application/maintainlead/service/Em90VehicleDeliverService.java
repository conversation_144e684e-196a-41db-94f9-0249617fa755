package com.volvo.maintain.application.maintainlead.service;

import com.volvo.maintain.application.maintainlead.dto.TaskDeliverCarRequestDto;
import com.volvo.maintain.application.maintainlead.dto.TaskDeliverCarResponseDto;

/**
 * em90取送车提醒
 */
public interface Em90VehicleDeliverService {

    void em90Deliver(String message);
    /**
     * 创建/编辑取送车订单
     * @param taskDeliverCarRequestDto 取送车信息
     * @return TaskDeliverCarResponseDto
     */
    TaskDeliverCarResponseDto vehicleDeliver(TaskDeliverCarRequestDto taskDeliverCarRequestDto);

    void em90TakeDeliverCarUnconfirmed(String message);

    void em90TakeDeliverCarUnconfirmedPush(String orderId);
    /**
     * 编辑取送车订单
     * @param taskDeliverCarRequestDto 取送车信息
     * @return id
     */
    Integer updateVehicleDeliver(TaskDeliverCarRequestDto taskDeliverCarRequestDto);

    void ekEm90push(String key);

}
