package com.volvo.maintain.application.maintainlead.dto.company;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * 张善龙
 * 2024.1.11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("适用车辆或手机号导入")
public class TeVehicleImportDto implements Serializable, IExcelModel, IExcelDataModel {

    /**
     * VIN 码
     */
    @Excel(name = "VIN", orderNum = "1")
    private String vin;

    /**
     * remark(失败原因)
     */
    private String errorMsg;

    /**
     * VIN 码
     */
    private Integer rowNum;

}
