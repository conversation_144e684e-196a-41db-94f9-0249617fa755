package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("页面导入VIP客户群组DTO")
public class VipGroupImportDTO {

    @ApiModelProperty("vin")
    private String vin;

    @ApiModelProperty("车牌")
    private String licensePlate;

    @ApiModelProperty("vip群组，bizGroup，lb : 重点关照组   t: 重点维修组  90:重点服务组")
    private String bizGroup;
}
