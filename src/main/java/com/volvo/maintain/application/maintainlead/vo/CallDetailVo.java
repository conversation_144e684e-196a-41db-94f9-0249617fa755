package com.volvo.maintain.application.maintainlead.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 功能描述：通话详情vo对象
 *
 * <AUTHOR>
 * @since 2024/01/04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "CallDetailVo", description = "通话详情vo对象")
public class CallDetailVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "客户姓名")
    private String cusName;

    @ApiModelProperty(value = "客户手机号")
    private String cusNumber;

    @ApiModelProperty(value = "通话时长")
    private Integer callLength;

    @ApiModelProperty(value = "话单ID")
    private String sessionId;

    @ApiModelProperty(value = "通话开始时间")
    private String startTime;

    @ApiModelProperty(value = "得分")
    private Integer totalScore;

    @ApiModelProperty(value = "录音文件URL")
    private String voiceUrl;
}
