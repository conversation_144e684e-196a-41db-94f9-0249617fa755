package com.volvo.maintain.application.maintainlead.service.customerProfile;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.ChangeTagInfoDto;
import com.volvo.maintain.application.maintainlead.dto.FieldTranslationDto;
import com.volvo.maintain.application.maintainlead.dto.TagInfoDto;
import com.volvo.maintain.application.maintainlead.dto.TagValueRuleDto;
import com.volvo.maintain.interfaces.vo.CompleteTagInfoVo;
import com.volvo.maintain.interfaces.vo.FieldTranslationVo;
import com.volvo.maintain.interfaces.vo.TagInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 功能描述：标签管理接口
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface TagInfoService {

    /**
     * 功能描述：查询标签信息列表
     *
     * @param tagInfoDTO 标签信息dto对象
     * @return Page<TagInfoVO> 标签信息列表
     */
    Page<TagInfoVo> queryTagInfo(TagInfoDto tagInfoDTO);
    /**
     * 功能描述：查询标签详情
     *
     * @param id 标签id
     * @return TagInfoVO 标签信息
     */
    TagInfoVo queryTagInfoById(Long id);

    /**
     * 功能描述：更新标签信息
     *
     * @param changeTagInfoDTO 标签入参
     */
    Integer updateTagInfo(ChangeTagInfoDto changeTagInfoDTO);
    /**
     * 功能描述：同步CDP标签信息
     *
     */
    void syncCDPTagTree();
    /**
     * 功能描述：获取CDP标签及本地配置
     *
     * @param vin 车架号
     * @param mobile 手机号
     * @return CompleteTagInfoVO 全量标签信息
     */
    List<CompleteTagInfoVo> queryCdpTagInfoAndConfigure(String vin, String mobile, Integer memberId);

    List<CompleteTagInfoVo> assembleData(List<Integer> codeList, List<TagInfoVo> tagInfoVOList);

    /**
     * 功能描述：获取字段转译详情
     *
     * @param id 主键id
     * @return FieldTranslationVO 字段转译信息
     */
    FieldTranslationVo queryFieldTranslationDetail(Long id);
    /**
     * 功能描述：查询字段下拉框
     *
     * @param tagInfoDTO dto对象
     * @return List<TagInfoVO> 字段下拉框
     */
    List<TagInfoVo> queryFieldInfo(TagInfoDto tagInfoDTO);
    /**
     * 功能描述：添加/更新字段转译
     *
     * @param fieldTranslationDto 字段更新修改Dto
     */
    void updateFieldInfo(FieldTranslationDto fieldTranslationDto);
    /**
     * 功能描述：获取字段转译列表信息
     *
     * @param showName 展示名称
     * @param currentPage 当前页
     * @param pageSize 每页条数
     * @return TagInfoVO 字段转译列表信息
     */
    Page<TagInfoVo> queryFieldTranslation(String showName, Integer currentPage, Integer pageSize, String tagType);
    /**
     * 功能描述：删除字段转译
     *
     * @param fieldTranslationDto 字段更新修改Dto
     */
    void deleteFieldInfo(FieldTranslationDto fieldTranslationDto);


    void handleValueRule(TagValueRuleDto tagValueRuleDto, TagInfoVo item, Map<String, TagInfoVo> tagDataMap);

    List<TagInfoVo> queryDetailData(String tagId, List<TagInfoVo> tagInfoVOList);
}
