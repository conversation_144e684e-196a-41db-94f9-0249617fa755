package com.volvo.maintain.application.maintainlead.service.workshop;

import cn.hutool.core.collection.CollectionUtil;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.*;
import com.volvo.maintain.application.maintainlead.dto.workshop.BadgeCountSummaryDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.BookingStatusDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.MissingPartsStatusDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.VehicleEntranceCountDto;
import com.volvo.maintain.application.maintainlead.vo.workshop.BookingOrderVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.ShortPartItemVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.VehicleEntranceParamsVo;
import com.volvo.maintain.infrastructure.constants.RedisConstants;
import com.volvo.maintain.infrastructure.constants.WorkShopConstants;
import com.volvo.maintain.infrastructure.gateway.DmscloudReportFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LucencyWorkShopServiceImpl implements LucencyWorkShopService{

    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;

    @Autowired
    private TransparentWorkshopManageService transparentWorkshopManageService;

    @Autowired
    private DmscloudReportFeign dmscloudReportFeign;

    @Autowired
    private RedissonClient redissonClient;

    @Qualifier("threadSignQuantity")
    @Autowired
    private ThreadPoolTaskExecutor threadSignQuantity;


    @Override
    public Page<BeginOrderDTO> selectBeginOrderList(QueryParamDto queryParamDto) {
        DmsResponse<Page<BeginOrderDTO>> pageDmsResponse = domainMaintainOrdersFeign.selectBeginOrderList(queryParamDto);
        return pageDmsResponse.getData();
    }

    @Override
    public List<StatusCountDTO> selectAssignStatusCount(QueryParamDto queryParamDto) {
        DmsResponse<List<StatusCountDTO>> listDmsResponse = domainMaintainOrdersFeign.selectAssignStatusCount(queryParamDto);
        return listDmsResponse.getData();
    }

    @Override
    public Page<WorkShopRepairOrderDTO> selectWorkshopRepairOrder(QueryParamDto queryParamDto) {
        DmsResponse<Page<WorkShopRepairOrderDTO>> pageDmsResponse = domainMaintainOrdersFeign.selectWorkshopRepairOrder(queryParamDto);
        return pageDmsResponse.getData();
    }

    @Override
    public List<StatusCountDTO> selectIsPunchCount(QueryParamDto queryParamDto) {
        DmsResponse<List<StatusCountDTO>> listDmsResponse = domainMaintainOrdersFeign.selectIsPunchCount(queryParamDto);
        return listDmsResponse.getData();
    }

    @Override
    public Page<WorkShopBalanceOrderDTO> selectWorkShopBalanceOrder(QueryParamDto queryParamDto) {
        DmsResponse<Page<WorkShopBalanceOrderDTO>> pageDmsResponse = domainMaintainOrdersFeign.selectWorkShopBalanceOrder(queryParamDto);
        return pageDmsResponse.getData();
    }

    @Override
    public List<StatusCountDTO> selectDeliveryTagCount(QueryParamDto queryParamDto) {
        DmsResponse<List<StatusCountDTO>> listDmsResponse = domainMaintainOrdersFeign.selectDeliveryTagCount(queryParamDto);
        return listDmsResponse.getData();
    }

    @Override
    public Page<DeliveryDTO> selectDeliveryList(QueryParamDto queryParamDto) {
        DmsResponse<Page<DeliveryDTO>> pageDmsResponse = domainMaintainOrdersFeign.selectDeliveryList(queryParamDto);
        return pageDmsResponse.getData();
    }

    @SneakyThrows
    @Override
    public List<SignQuantityDTO> signQuantity(String ownerCode, String beginDate, String endDate) {
        log.info("signQuantity,开始:{},{},{}", ownerCode, beginDate, endDate);

        // 构建缓存键
        String cacheKey = RedisConstants.SIGN_QUANTITY_CACHE_KEY + ownerCode;
        // 锁
        String lockKey = RedisConstants.SIGN_QUANTITY_LOCK_KEY + ownerCode;
        // 异步标识
        String flagKey = RedisConstants.SIGN_QUANTITY_REFRESH_FLAG_KEY + ownerCode;

        // 尝试从缓存获取数据
        SignQuantityCacheDTO cacheData = getFromCache(cacheKey);

        if (cacheData != null) {
            tryAsyncRefresh(ownerCode, beginDate, endDate, lockKey, cacheData, flagKey);
            log.info("signQuantity,从缓存返回:{}", cacheData);
            return cacheData.getSignQuantityList();
        }
        // 缓存不存在或参数不匹配，尝试获取锁进行实时查询
        return getDataWithLock(ownerCode, beginDate, endDate, lockKey, cacheKey);
    }

    /**
     * 从缓存获取数据
     */
    private SignQuantityCacheDTO getFromCache(String cacheKey) {
        try {
            RBucket<SignQuantityCacheDTO> bucket = redissonClient.getBucket(cacheKey);
            return bucket.get();
        } catch (Exception e) {
            log.error("getFromCache,从缓存获取数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 尝试异步刷新缓存
     */
    private void tryAsyncRefresh(String ownerCode, String beginDate, String endDate, String lockKey, SignQuantityCacheDTO cacheData, String flagKey) {
        // 如果缓存需要刷新（超过5分钟），则异步刷新(flagKey防止无效线程)
        if (cacheData.needsRefresh()) {
            try {
                RBucket<String> flagBucket = redissonClient.getBucket(flagKey);

                // flagKey 有值时不走 asyncRefresh 方法
                if (flagBucket.get() != null) {
                    log.debug("tryAsyncRefresh,异步刷新已在进行中，跳过: ownerCode={}", ownerCode);
                    return;
                }
                flagBucket.set("true", 1, TimeUnit.MINUTES);
                log.info("tryAsyncRefresh,设置刷新标记成功，触发异步刷新: ownerCode={}", ownerCode);
                asyncRefresh(ownerCode, beginDate, endDate, lockKey);


            } catch (Exception e) {
                log.error("tryAsyncRefresh,检查刷新标记失败，降级执行刷新: ownerCode={}, error={}", ownerCode, e.getMessage());
            }
        }
    }

    private void asyncRefresh(String ownerCode, String beginDate, String endDate, String lockKey) {
        log.info("asyncRefresh,异步刷新缓存准备: ownerCode={}", ownerCode);
        // 不在主线程获取锁，在异步任务中获取
        CompletableFuture.runAsync(() -> {
            RLock lock = redissonClient.getLock(lockKey);
            try {
                if (lock.tryLock(1, -1, TimeUnit.SECONDS)) {
                    try {
                        log.info("asyncRefresh,异步刷新缓存开始: ownerCode={}", ownerCode);
                        List<SignQuantityDTO> freshData = doSignQuantityQuery(ownerCode, beginDate, endDate);
                        if (CollectionUtil.isEmpty(freshData)) {
                            log.info("asyncRefresh,异步刷新缓存无数据: ownerCode={}", ownerCode);
                            return;
                        }
                        saveToCache(RedisConstants.SIGN_QUANTITY_CACHE_KEY + ownerCode, ownerCode, beginDate, endDate, freshData);
                        log.info("asyncRefresh,异步刷新缓存完成: ownerCode={}", ownerCode);
                    } finally {
                        // 正确释放锁
                        if (lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                    }
                } else {
                    log.warn("asyncRefresh,获取分布式锁失败: ownerCode={}", ownerCode);
                }
            } catch (Exception e) {
                log.error("asyncRefresh,异步刷新缓存失败: ownerCode={}, error={}", ownerCode, e.getMessage(), e);
            }
        }, threadSignQuantity);
    }

    /**
     * 使用分布式锁获取数据
     */
    private List<SignQuantityDTO> getDataWithLock(String ownerCode, String beginDate, String endDate, String lockKey, String cacheKey) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，最多等待1秒
            if (lock.tryLock(1, -1, TimeUnit.SECONDS)) {
                try {
                    // 再次检查缓存（双重检查）
                    SignQuantityCacheDTO cacheData = getFromCache(cacheKey);
                    if (cacheData != null) {
                        log.info("getDataWithLock,双重检查从缓存返回: {}", ownerCode);
                        return cacheData.getSignQuantityList();
                    }
                    // 执行实时查询
                    log.info("getDataWithLock,执行实时查询: {}", ownerCode);
                    List<SignQuantityDTO> result = doSignQuantityQuery(ownerCode, beginDate, endDate);
                    // 保存到缓存
                    saveToCache(cacheKey, ownerCode, beginDate, endDate, result);
                    return result;
                } finally {
                    lock.unlock();
                }
            } else {
                // 获取锁失败，返回缓存数据（即使可能过期）
                log.warn("getDataWithLock,获取锁失败，返回缓存数据: {}", ownerCode);
                SignQuantityCacheDTO cacheData = getFromCache(cacheKey);
                if (cacheData != null && cacheData.getSignQuantityList() != null) {
                    return cacheData.getSignQuantityList();
                }
                // 如果缓存也没有,降级处理
                return Collections.emptyList();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("getDataWithLock,获取锁被中断: {}", e.getMessage());
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("getDataWithLock,获取数据失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 保存数据到缓存（永久存在）
     */
    private void saveToCache(String cacheKey, String ownerCode, String beginDate, String endDate, List<SignQuantityDTO> data) {
        try {
            SignQuantityCacheDTO cacheData = new SignQuantityCacheDTO(ownerCode, beginDate, endDate, data);
            log.info("saveToCache,cacheData:{}", cacheData);
            RBucket<SignQuantityCacheDTO> bucket = redissonClient.getBucket(cacheKey);
            bucket.set(cacheData); // 不设置过期时间，缓存永久存在
            log.info("saveToCache,数据已保存到缓存: {}", ownerCode);
        } catch (Exception e) {
            log.error("saveToCache,保存缓存失败: {}", e.getMessage(), e);
        }
    }

    private List<SignQuantityDTO> doSignQuantityQuery(String ownerCode, String beginDate, String endDate) {
        try {
            return this.executeSignQuantityQuery(ownerCode,beginDate,endDate);
        } catch (Exception e) {
            log.error("doSignQuantityQuery:", e);
            return Collections.emptyList();
        }
    }

    /**
     * 执行实际的签到数量查询（原来的实现逻辑）
     */
    private List<SignQuantityDTO> executeSignQuantityQuery(String ownerCode, String beginDate, String endDate) {
        List<SignQuantityDTO> result = new ArrayList<>();
        log.info("executeSignQuantityQuery,开始:{},{},{}", ownerCode, beginDate, endDate);

        //红色角标
        CompletableFuture<SignQuantityDTO> future1 = CompletableFuture.supplyAsync(() -> {
        	DmsResponse<SignQuantityDTO> queryMenuOrderQuantityAndSignQuantity = domainMaintainOrdersFeign.queryMenuOrderQuantityAndSignQuantity(ownerCode, beginDate, endDate, String.join("-", WorkShopConstants.SUBSCRIPT, WorkShopConstants.WORK_SHOP_MENU_ID_117745));
        	return queryMenuOrderQuantityAndSignQuantity.getData();
        }, threadSignQuantity).exceptionally(e -> {
            // 处理异常，可以记录日志或者返回默认值
            log.info("executeSignQuantityQuery,future1 error msg:",e);
            return new SignQuantityDTO(); // 返回默认的 SignQuantityDTO 对象
        });
        
        CompletableFuture<SignQuantityDTO> future2 = CompletableFuture.supplyAsync(() -> {
            DmsResponse<SignQuantityDTO> queryMenuOrderQuantityAndSignQuantity = domainMaintainOrdersFeign.queryMenuOrderQuantityAndSignQuantity(ownerCode, beginDate, endDate, String.join("-", WorkShopConstants.SUBSCRIPT, WorkShopConstants.WORK_SHOP_MENU_ID_117744));
            return queryMenuOrderQuantityAndSignQuantity.getData(); 
        }, threadSignQuantity).exceptionally(e -> {
            // 处理异常，可以记录日志或者返回默认值
            log.info("executeSignQuantityQuery,future2 error msg:",e);
            return new SignQuantityDTO(); // 返回默认的 SignQuantityDTO 对象
        });
        
        CompletableFuture<SignQuantityDTO> future3 = CompletableFuture.supplyAsync(() -> {
            DmsResponse<SignQuantityDTO> queryMenuOrderQuantityAndSignQuantity = domainMaintainOrdersFeign.queryMenuOrderQuantityAndSignQuantity(ownerCode, beginDate, endDate, String.join("-", WorkShopConstants.SUBSCRIPT, WorkShopConstants.WORK_SHOP_MENU_ID_117743));
            return queryMenuOrderQuantityAndSignQuantity.getData(); 
        }, threadSignQuantity).exceptionally(e -> {
            // 处理异常，可以记录日志或者返回默认值
            log.info("executeSignQuantityQuery,future3 error msg:",e);
            return new SignQuantityDTO(); // 返回默认的 SignQuantityDTO 对象
        });
        
        CompletableFuture<SignQuantityDTO> future4 = CompletableFuture.supplyAsync(() -> {
            DmsResponse<SignQuantityDTO> queryMenuOrderQuantityAndSignQuantity = domainMaintainOrdersFeign.queryMenuOrderQuantityAndSignQuantity(ownerCode, beginDate, endDate, String.join("-", WorkShopConstants.SUBSCRIPT, WorkShopConstants.WORK_SHOP_MENU_ID_117742));
            return queryMenuOrderQuantityAndSignQuantity.getData(); 
        }, threadSignQuantity).exceptionally(e -> {
            // 处理异常，可以记录日志或者返回默认值
            log.info("executeSignQuantityQuery,future4 error msg:",e);
            return new SignQuantityDTO(); // 返回默认的 SignQuantityDTO 对象
        });
        
        
        // 订单数量
        CompletableFuture<SignQuantityDTO> orders1 = CompletableFuture.supplyAsync(() -> {
            DmsResponse<SignQuantityDTO> queryMenuOrderQuantityAndSignQuantity = domainMaintainOrdersFeign.queryMenuOrderQuantityAndSignQuantity(ownerCode, beginDate, endDate, String.join("-", WorkShopConstants.ORDERS, WorkShopConstants.WORK_SHOP_MENU_ID_117742));
            return queryMenuOrderQuantityAndSignQuantity.getData(); 
        }, threadSignQuantity).exceptionally(e -> {
            // 处理异常，可以记录日志或者返回默认值
            log.info("executeSignQuantityQuery,orders1 error msg:",e);
            return new SignQuantityDTO(); // 返回默认的 SignQuantityDTO 对象
        });
        
        CompletableFuture<SignQuantityDTO> orders2 = CompletableFuture.supplyAsync(() -> {
            DmsResponse<SignQuantityDTO> queryMenuOrderQuantityAndSignQuantity = domainMaintainOrdersFeign.queryMenuOrderQuantityAndSignQuantity(ownerCode, beginDate, endDate, String.join("-", WorkShopConstants.ORDERS, WorkShopConstants.WORK_SHOP_MENU_ID_117746));
            return queryMenuOrderQuantityAndSignQuantity.getData(); 
        }, threadSignQuantity).exceptionally(e -> {
            // 处理异常，可以记录日志或者返回默认值
            log.info("executeSignQuantityQuery,orders2 error msg:",e);
            return new SignQuantityDTO(); // 返回默认的 SignQuantityDTO 对象
        });
        
        CompletableFuture<SignQuantityDTO> orders3 = CompletableFuture.supplyAsync(() -> {
            DmsResponse<SignQuantityDTO> queryMenuOrderQuantityAndSignQuantity = domainMaintainOrdersFeign.queryMenuOrderQuantityAndSignQuantity(ownerCode, beginDate, endDate, String.join("-", WorkShopConstants.ORDERS, WorkShopConstants.WORK_SHOP_MENU_ID_117745));
            return queryMenuOrderQuantityAndSignQuantity.getData(); 
        }, threadSignQuantity).exceptionally(e -> {
            // 处理异常，可以记录日志或者返回默认值
            log.info("executeSignQuantityQuery,orders3 error msg:",e);
            return new SignQuantityDTO(); // 返回默认的 SignQuantityDTO 对象
        });
        
        CompletableFuture<SignQuantityDTO> orders4 = CompletableFuture.supplyAsync(() -> {
            DmsResponse<SignQuantityDTO> queryMenuOrderQuantityAndSignQuantity = domainMaintainOrdersFeign.queryMenuOrderQuantityAndSignQuantity(ownerCode, beginDate, endDate, String.join("-", WorkShopConstants.ORDERS, WorkShopConstants.WORK_SHOP_MENU_ID_117744));
            return queryMenuOrderQuantityAndSignQuantity.getData(); 
        }, threadSignQuantity).exceptionally(e -> {
            // 处理异常，可以记录日志或者返回默认值
            log.info("executeSignQuantityQuery,orders4 error msg:",e);
            return new SignQuantityDTO(); // 返回默认的 SignQuantityDTO 对象
        });

        String todayMin = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String todayMax = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        VehicleEntranceParamsVo vo = new VehicleEntranceParamsVo();
        vo.setEntryTimeBegin(todayMin);
        vo.setEntryTimeEnd(todayMax);
        vo.setDealerCode(ownerCode);
        // 获取分拨
        CompletableFuture<VehicleEntranceCountDto> responseFuture = CompletableFuture.supplyAsync(() -> {
            DmsResponse<VehicleEntranceCountDto> response = dmscloudReportFeign.queryAllocatedCount(vo);
            return response.getData();
        }, threadSignQuantity).exceptionally(e -> {
            log.info("executeSignQuantityQuery,responseFuture error msg:",e);
            return new VehicleEntranceCountDto();
        });

        // 预约
        BookingOrderVo orderVo = new BookingOrderVo();
        orderVo.setOwnerCode(ownerCode);
        orderVo.setBookingComeTimeBegin(todayMin);
        orderVo.setBookingComeTimeEnd(todayMax);
        CompletableFuture<BookingStatusDto> bookingStatusDtoFuture = CompletableFuture.supplyAsync(() ->
                transparentWorkshopManageService.queryBookingStatus(orderVo), threadSignQuantity).exceptionally(e -> {
            log.info("executeSignQuantityQuery,bookingStatusDtoFuture error msg:", e);
            return new BookingStatusDto();
        });

        //缺件
        ShortPartItemVo itemVo = new ShortPartItemVo();
        itemVo.setOwnerCode(ownerCode);
        CompletableFuture<MissingPartsStatusDto> shortPartStatusDtoFuture = CompletableFuture.supplyAsync(() ->
                transparentWorkshopManageService.getShortPartStatus(itemVo), threadSignQuantity).exceptionally(e -> {
            log.info("executeSignQuantityQuery,shortPartStatusDtoFuture error msg:", e);
            return new MissingPartsStatusDto();
        });

        // 收集所有异步任务
        List<CompletableFuture<?>> allFutures = Arrays.asList(
            future1, future2, future3, future4,
            orders1, orders2, orders3, orders4,
            responseFuture, bookingStatusDtoFuture, shortPartStatusDtoFuture
        );

        // 等待所有任务完成
        CompletableFuture<Void> allOf = CompletableFuture.allOf(allFutures.toArray(new CompletableFuture[0]));
        allOf.join();

        // 处理数据 - 使用带超时的获取方法
        SignQuantityDTO signQuantity1 = getFutureResult(future1);
        SignQuantityDTO signQuantity2 = getFutureResult(future2);
        SignQuantityDTO signQuantity3 = getFutureResult(future3);
        SignQuantityDTO signQuantity4 = getFutureResult(future4);

        //交车
        SignQuantityDTO signQuantityDeliveryDTO=new SignQuantityDTO();
        signQuantityDeliveryDTO.setMenuId(WorkShopConstants.WORK_SHOP_MENU_ID_117746);
        signQuantityDeliveryDTO.setQuantity("0");
        //预约
        SignQuantityDTO signQuantityBookingDTO=new SignQuantityDTO();
        signQuantityBookingDTO.setMenuId(WorkShopConstants.WORK_SHOP_MENU_ID_117740);
        signQuantityBookingDTO.setQuantity("0");
        result.add(signQuantityDeliveryDTO);
        result.add(signQuantityBookingDTO);
        result.add(signQuantity1);
        result.add(signQuantity2);
        result.add(signQuantity3);
        result.add(signQuantity4);
        log.info("executeSignQuantityQuery,result: {},时间:{}", JSON.toJSONString(result), LocalDateTime.now());

        SignQuantityDTO signQuantityOrders1 = getFutureResult(orders1);
        SignQuantityDTO signQuantityOrders2 = getFutureResult(orders2);
        SignQuantityDTO signQuantityOrders3 = getFutureResult(orders3);
        SignQuantityDTO signQuantityOrders4 = getFutureResult(orders4);

        List<SignQuantityDTO> menuOrderQuantityList = new ArrayList<>();
        menuOrderQuantityList.add(signQuantityOrders1);
        menuOrderQuantityList.add(signQuantityOrders2);
        menuOrderQuantityList.add(signQuantityOrders3);
        menuOrderQuantityList.add(signQuantityOrders4);
        log.info("executeSignQuantityQuery,menuOrderQuantityList: {},时间:{}", JSON.toJSONString(menuOrderQuantityList), LocalDateTime.now());

        BadgeCountSummaryDto badgeCountSummaryDto = new BadgeCountSummaryDto();
        badgeCountSummaryDto.setBookingStatus(getFutureResultWithTimeout(bookingStatusDtoFuture, BookingStatusDto::new));
        badgeCountSummaryDto.setVehicleEntranceCount(getFutureResultWithTimeout(responseFuture, VehicleEntranceCountDto::new));
        badgeCountSummaryDto.setMissingPartsStatus(getFutureResultWithTimeout(shortPartStatusDtoFuture, MissingPartsStatusDto::new));
        log.info("executeSignQuantityQuery,今日单数 badgeCountSummaryDto :{},时间:{}", badgeCountSummaryDto, LocalDateTime.now());

        if (CollectionUtil.isNotEmpty(menuOrderQuantityList)) {
            Map<String, String> menuOrderQuantityMap = menuOrderQuantityList.stream()
                    .filter(Objects::nonNull)
                    .filter(dto -> dto.getMenuId() != null)
                    .collect(Collectors.toMap(SignQuantityDTO::getMenuId, SignQuantityDTO::getQuantity, (k1, k2) -> k2));
            Map<String, String> badgeCountSummaryMap = assembleBadgeCountSummaryDto(badgeCountSummaryDto,result);
            menuOrderQuantityMap.putAll(CollectionUtil.isEmpty(badgeCountSummaryMap) ? new HashMap<>() : badgeCountSummaryMap);
            log.info("executeSignQuantityQuery,menuOrderQuantityMap:{}", menuOrderQuantityMap);
            result.forEach(e -> {
                e.setOrderQuantity(menuOrderQuantityMap.get(e.getMenuId()));
            });
        }
        log.info("executeSignQuantityQuery,end result:{}",result);
        return result;
    }

    /**
     * 处理SignQuantityDTO类型Future结果获取的便捷方法（保持向后兼容）
     * @param future 待处理的Future对象
     * @return Future执行结果，异常时返回新的SignQuantityDTO实例
     */
    private SignQuantityDTO getFutureResult(CompletableFuture<SignQuantityDTO> future)  {
        return getFutureResultWithTimeout(future, SignQuantityDTO::new);
    }

    /**
     * 处理泛型Future结果获取，使用较短的超时时间（用于已经等待过的任务）
     * @param future 待处理的Future对象
     * @param defaultValueSupplier 异常时返回默认值的供应商
     * @param <T> Future结果的类型
     * @return Future执行结果，异常时返回defaultValue
     */
    private <T> T getFutureResultWithTimeout(CompletableFuture<? extends T> future, Supplier<T> defaultValueSupplier) {
        try {
            if (future.isDone()) {
                return future.get();
            } else {
                return future.get(50, TimeUnit.MILLISECONDS);
            }
        } catch (InterruptedException e) {
            log.warn("Future任务被中断，返回默认值", e);
        } catch (ExecutionException e) {
            log.warn("Future任务执行失败，返回默认值: {}", e.getCause().getMessage());
        } catch (TimeoutException e) {
            log.warn("Future任务超时，返回默认值");
        }
        return defaultValueSupplier.get();
    }

    @Override
    public List<SignQuantityDTO> menuOrderQuantity(String ownerCode, String beginDate, String endDate) {
        DmsResponse<List<SignQuantityDTO>> listDmsResponse = domainMaintainOrdersFeign.menuOrderQuantity(ownerCode, beginDate, endDate);
        return listDmsResponse.getData();
    }


    public Map<String,String> assembleBadgeCountSummaryDto (BadgeCountSummaryDto badgeCountSummaryDto,List<SignQuantityDTO> signQuantityDTOList){
        log.info("assembleBadgeCountSummaryDto badgeCountSummaryDto:{},signQuantityDTOList:{}",badgeCountSummaryDto,signQuantityDTOList);
        Map<String,String> result=new HashMap<>();
        if (Objects.isNull(badgeCountSummaryDto)){
            return null;
        }
        //预约数量
        result.put(WorkShopConstants.WORK_SHOP_MENU_ID_117740,Objects.isNull(badgeCountSummaryDto.getBookingStatus().getBookingAll()) ? "0": String.valueOf(badgeCountSummaryDto.getBookingStatus().getBookingAll()));
        //缺件数量
        result.put(WorkShopConstants.WORK_SHOP_MENU_ID_117743,Objects.isNull(badgeCountSummaryDto.getMissingPartsStatus()) ? "0":
                        Optional.ofNullable(badgeCountSummaryDto.getMissingPartsStatus().getUndeliveredCount()).orElse(0) +
                                Optional.ofNullable(badgeCountSummaryDto.getMissingPartsStatus().getDeliveredCount()).orElse(0) +
                                Optional.ofNullable(badgeCountSummaryDto.getMissingPartsStatus().getPartiallyDeliveredCount()).orElse(0) +"");
        //到店数量
        result.put(WorkShopConstants.WORK_SHOP_MENU_ID_117741,Objects.isNull(badgeCountSummaryDto.getVehicleEntranceCount().getAllCount())? "0" : String.valueOf(badgeCountSummaryDto.getVehicleEntranceCount().getAllCount()));
        //到店页面角标
        SignQuantityDTO repairSignQuantity = new SignQuantityDTO();
        repairSignQuantity.setMenuId(WorkShopConstants.WORK_SHOP_MENU_ID_117741);
        repairSignQuantity.setQuantity(Objects.isNull(badgeCountSummaryDto.getVehicleEntranceCount().getReceptionOverTime()) ? "0" : String.valueOf(badgeCountSummaryDto.getVehicleEntranceCount().getReceptionOverTime()));
        signQuantityDTOList.add(repairSignQuantity);
        log.info("assembleBadgeCountSummaryDto result :{},repairSignQuantity:{}",result,repairSignQuantity);
        return result;
    }
}
