package com.volvo.maintain.application.maintainlead.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 车辆健康检查所有信息
 * @Date 2024/10/10 17:45
 */
@Data
@ApiModel("车辆健康检查所有信息")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VehicleHealthCheckInfoDto {

    @ApiModelProperty("检查员")
    private String technician;

    @ApiModelProperty("报价单号")
    private String vhcPricesheetNo;

    @ApiModelProperty("工单号")
    private String roNo;

    @ApiModelProperty("VHC工单号")
    private String vhcNo;

    @ApiModelProperty("经销商")
    private String ownerCode;

    @ApiModelProperty("是否完成标识（0：临时保存，1：完成车辆健康检查")
    private String compleFlag;

    @ApiModelProperty("不正常的大类配置id")
    private Map<String, List<Integer>> notNormalConfigClassIdMap;

    @ApiModelProperty("检查大类小类信息")
    private List<VhcClassInfoDto> vhcClassInfoDtoList;
}
