package com.volvo.maintain.application.maintainlead.dto.message;

import com.volvo.maintain.application.maintainlead.dto.EmailInfoDto;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("通用短信发送DTO")
public class PushMessageSendDto {

    @ApiModelProperty(value = "短信发送Key",name = "key")
    private String key;

    @ApiModelProperty(value = "消息推送记录",name = "pushMessageRecord")
    private PushMessageRecordVo pushMessageRecord;

    @ApiModelProperty(value = "短信发送DTO",name = "messageSendDto")
    private MessageSendDto messageSendDto;

    @ApiModelProperty(value = "邮件发送DTO",name = "emailInfoDto")
    private EmailInfoDto emailInfoDto;
}
