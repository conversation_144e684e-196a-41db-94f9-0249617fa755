package com.volvo.maintain.application.maintainlead.service.vhc.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.*;
import com.volvo.maintain.application.maintainlead.service.vhc.VhcRepairService;
import com.volvo.maintain.infrastructure.constants.VhcConstants;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.utils.LoginInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class VhcRepairServiceImpl implements VhcRepairService {


    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;


    @Override
    public boolean verifyOrderGroupVHC(String ownerCode, String roNo, String setCode,String vin) {
        DmsResponse<CommonConfigDto> configByKey = dmscloudServiceFeign.getConfigByKey(VhcConstants.VHC_LABOUR_CODE,VhcConstants.VHC_CODE);
        log.info("selectVhcList configByKey :{}",configByKey);
        if (ObjectUtils.isEmpty(configByKey) && Objects.isNull(configByKey.getData())) {
            log.info("selectVhcList 查询VHC工时code为空");
            return false;
        }
        DmsResponse<Boolean> booleanDmsResponse = domainMaintainOrdersFeign.verifyOrderGroupVHC(ownerCode, roNo, setCode,vin,configByKey.getData().getConfigValue());
        return booleanDmsResponse.getData();
    }

    @Override
    public void createdVhcInfo(VhcInfoDTO vhcInfoDTO) {
        log.info("createdVhcInfo vhcInfoDTO入参:{}",vhcInfoDTO);
        //1查询车辆燃油类型
        DmsResponse<VhcNoDto> fuelTypeByVinResponse = dmscloudServiceFeign.getFuelTypeByVin(vhcInfoDTO.getVin());
        log.info("fuelTypeByVinResponse :{}",fuelTypeByVinResponse);
        if (Objects.isNull(fuelTypeByVinResponse) || fuelTypeByVinResponse.isFail() || Objects.isNull(fuelTypeByVinResponse.getData())) {
            log.info("查询车辆燃油类型异常");
            return;
        }
        //2生成车辆健康检查编号
        DmsResponse<VhcNoDto> vhcNoResponse = dmscloudServiceFeign.createVhcNo(vhcInfoDTO.getOwnerCode(),VhcConstants.VHC_JC_NO);
        log.info("vhcNoResponse :{}",vhcNoResponse);
        if (Objects.isNull(vhcNoResponse) || vhcNoResponse.isFail() || Objects.isNull(vhcNoResponse.getData())) {
            log.info("生成车辆健康检查编号异常");
            return;
        }
        vhcInfoDTO.setVhcNo(vhcNoResponse.getData().getVhcNo());

        //查询特殊大类，其它小类，以及类目排序信息
        DmsResponse<CommonConfigDto> configByKey = dmscloudServiceFeign.getConfigByKey(VhcConstants.VHC_CONFIG_CLASS_KEY_OTHER,VhcConstants.VHC_NOT_NORMAL_CONFIG_CLASS_ID);
        log.info("selectVhcList 套餐 configByKey :{}",configByKey);
        if (ObjectUtils.isEmpty(configByKey) && Objects.isNull(configByKey.getData())) {
            log.info("请配置特殊以及其它配置大类id");
            return;
        }
        List<Integer> otherIds = Stream.of(configByKey.getData().getConfigValue().split(",")).map(Integer::parseInt)
                .collect(Collectors.toList());
        vhcInfoDTO.setOtherClassIdList(otherIds);

        if (fuelTypeByVinResponse.getData().getFuelTypeByVin().equals(VhcConstants.FUEL_TYPE_C)){
            vhcInfoDTO.setVhcType(VhcConstants.VHC_TYPE_EV);
        }else {
            vhcInfoDTO.setVhcType(VhcConstants.VHC_TYPE_YC);
        }
        log.info("createdVhcInfo vhcInfoDTO拼装后:{}",vhcInfoDTO);
        domainMaintainOrdersFeign.createdVhcInfo(vhcInfoDTO);
    }

    @Override
    public Page<VhcDetailsDTO> selectVhcList(QueryVhcDto queryVhcDto) {
        DmsResponse<CommonConfigDto> configByKey = dmscloudServiceFeign.getConfigByKey(VhcConstants.VHC_LABOUR_CODE,VhcConstants.VHC_CODE);
        log.info("selectVhcList configByKey :{}",configByKey);
        if (ObjectUtils.isEmpty(configByKey) && Objects.isNull(configByKey.getData())) {
            log.info("selectVhcList 查询VHC工时code为空");
            return null;
        }
        queryVhcDto.setLabourCode(configByKey.getData().getConfigValue());
        DmsResponse<Page<VhcDetailsDTO>> iPageDmsResponse = domainMaintainOrdersFeign.selectVhcList(queryVhcDto);
        return iPageDmsResponse.getData();
    }

    @Override
    public Page<VhcPricesheetDetailsDTO> selectVhcPricesheetList(QueryVhcDto queryVhcDto) {
        DmsResponse<CommonConfigDto> configByKey = dmscloudServiceFeign.getConfigByKey(VhcConstants.VHC_LABOUR_CODE,VhcConstants.VHC_CODE);
        log.info("selectVhcPricesheetList configByKey :{}",configByKey);
        if (ObjectUtils.isEmpty(configByKey) && Objects.isNull(configByKey.getData())) {
            log.info("selectVhcPricesheetList 查询VHC工时code为空");
            return null;
        }
        queryVhcDto.setLabourCode(configByKey.getData().getConfigValue());
        DmsResponse<Page<VhcPricesheetDetailsDTO>> iPageDmsResponse = domainMaintainOrdersFeign.selectVhcPricesheetList(queryVhcDto);
        return iPageDmsResponse.getData();
    }

    @Override
    public List<NoRepairItemDto> selectNoRepairItem(String ownerCode, String vin,String roNo) {
        DmsResponse<List<NoRepairItemDto>> listDmsResponse = domainMaintainOrdersFeign.selectNoRepairItem(ownerCode, vin,roNo);
        return listDmsResponse.getData();
    }

    @Override
    public Map<String, List<VhcItemPoDTO>> select360VhcItem(String ownerCode, String vin) {
        DmsResponse<Map<String, List<VhcItemPoDTO>>> mapDmsResponse = domainMaintainOrdersFeign.select360VhcItem(ownerCode, vin);
        return mapDmsResponse.getData();
    }

    @Override
    public Page<VhcQueryLabourVo> queryLabourList(VhcQueryLabourDto vhcQueryLabourDto) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        vhcQueryLabourDto.setOwnerCode(currentLoginInfo.getOwnerCode());
        return domainMaintainOrdersFeign.queryLabourList(vhcQueryLabourDto).getData();
    }

    @Override
    public void vhcConnectBookOrder(String ownerCode, String bookingOrderNo, String vin) {
        domainMaintainOrdersFeign.vhcConnectBookOrder(ownerCode, bookingOrderNo, vin);
    }

    @Override
    public List<NoRepairItemDto> selectVhcConnectBookOrder(String ownerCode, String bookingOrderNo, String vin) {
        DmsResponse<List<NoRepairItemDto>> listDmsResponse = domainMaintainOrdersFeign.selectVhcConnectBookOrder(ownerCode, bookingOrderNo, vin);
        return listDmsResponse.getData();
    }

}
