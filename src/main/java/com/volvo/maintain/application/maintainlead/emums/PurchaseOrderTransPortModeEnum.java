package com.volvo.maintain.application.maintainlead.emums;

import lombok.Getter;

@Getter
public enum PurchaseOrderTransPortModeEnum {
	
	URGENT_ORDERS("81471001", "1-紧急订单", "81641001", "17-空运"),
	RESERVATION_ORDER("81471002", "3-预约订单", "81641002", "43-紧急陆运"),
	NON_WHOLESALE_ORDERS("81471003", "4-非批售订单", PurchaseOrderTransPortModeEnum.TRANS_PORT_MODE_ROAD, PurchaseOrderTransPortModeEnum.TRANS_PORT_MODE_DES_ROAD),
	WHOLESALE_ORDERS("81471004", "4-批售订单", PurchaseOrderTransPortModeEnum.TRANS_PORT_MODE_ROAD, PurchaseOrderTransPortModeEnum.TRANS_PORT_MODE_DES_ROAD),
	DDLS_ORDERS("81471005", "4-DDLS订单", PurchaseOrderTransPortModeEnum.TRANS_PORT_MODE_ROAD, PurchaseOrderTransPortModeEnum.TRANS_PORT_MODE_DES_ROAD);

	public static final String TRANS_PORT_MODE_ROAD = "81641003";

	public static final String TRANS_PORT_MODE_DES_ROAD = "20-公路";
	
    private final String code;

    private final String des;

    private final String transPortMode;
    
    private final String transPortModeDes;

    PurchaseOrderTransPortModeEnum(final String code, final String des, final String transPortMode, final String transPortModeDes) {
        this.code = code;
        this.des = des;
        this.transPortMode = transPortMode;
        this.transPortModeDes = transPortModeDes;
    }

    public static PurchaseOrderTransPortModeEnum getTransPortMode(String code) {
        for (PurchaseOrderTransPortModeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
