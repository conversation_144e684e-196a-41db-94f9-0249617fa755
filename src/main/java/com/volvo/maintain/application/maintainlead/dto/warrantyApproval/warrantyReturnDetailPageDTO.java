package com.volvo.maintain.application.maintainlead.dto.warrantyApproval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "延保回款查询明细列表", description = "延保回款查询明细列表")
public class warrantyReturnDetailPageDTO implements Serializable {
    @ApiModelProperty("工单号")
    private String orderNo;

    @ApiModelProperty("经销商代码")
    private String ownerCode;

    @ApiModelProperty("延保单号(Rep Order)")
    private String repOrder;

    @ApiModelProperty("申请工时总额(Labour Cost)")
    private BigDecimal labourAmt;

    @ApiModelProperty("申请零件总额(Material)")
    private BigDecimal materialAmt;

    @ApiModelProperty("申请总额")
    private BigDecimal totalAmt;

    @ApiModelProperty("案件号")
    private String caseNo;

    @ApiModelProperty("底盘号")
    private String chassis;

    @ApiModelProperty("变动金额")
    private BigDecimal changeAmt;

    @ApiModelProperty("实际总额")
    private BigDecimal realAmt;

    @ApiModelProperty("延保类别(Claim Type)")
    private String claimType;

    @ApiModelProperty("车型(type)")
    private String type;

    @ApiModelProperty("维修日期(Rep.Date)")
    private String repDate;

    @ApiModelProperty("年款(my)")
    private String my;

    @ApiModelProperty("承保渠道")
    private  Integer underwritingChannel;

    @ApiModelProperty("承保渠道")
    private String underwritingChannelStr;

}
