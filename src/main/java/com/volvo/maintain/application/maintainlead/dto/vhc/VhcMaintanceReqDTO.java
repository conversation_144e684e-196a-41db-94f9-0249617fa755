package com.volvo.maintain.application.maintainlead.dto.vhc;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * vhc报价-保存草稿&报价完成
 */
@Data
public class VhcMaintanceReqDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 报价单号
     */
    private String vhcPricesheetNo;
    /**
     * false保存草稿ture报价完成
     */
    private boolean flag;
    /**
     * 检查单号
     */
    private String vhcNo;
    /**
     * 工单号
     */
    private String roNo;
    /**
     * 经销商
     */
    private String ownerCode;
    /**
     * 二级类目检查集合
     */
    private List<VhcItemDTO> vhcItemList;
}
