package com.volvo.maintain.application.maintainlead.dto.dtcclues;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("导入返回信息")
public class ImportResultInfoDto {
    /**
     * remarkList(失败原因)
     */
    private List<String> remarkList;

    /**
     * 表格行号
     */
    private Integer rowNum;
}
