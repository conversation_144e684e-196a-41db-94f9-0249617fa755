package com.volvo.maintain.application.maintainlead.dto;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("上门取送车验车照片查询")
public class CarPickupCarPhotosResDto implements Serializable {
	
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty("服务前验车照片")
	private List<CarPhotosDetailsDto> beforeService;
	
	@ApiModelProperty("服务后验车照片")
	private List<CarPhotosDetailsDto> afterService;
	
}
