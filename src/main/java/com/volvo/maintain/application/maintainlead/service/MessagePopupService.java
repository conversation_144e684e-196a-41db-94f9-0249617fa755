package com.volvo.maintain.application.maintainlead.service;

import com.volvo.maintain.application.maintainlead.dto.MessageConfirmDTO;
import com.volvo.maintain.application.maintainlead.dto.MessageResultDTO;

import java.math.BigDecimal;

public interface MessagePopupService {
    /**
     * 查询弹窗内容
     * @param ownerCode
     * @param businessType
     * @param businessId
     * @return
     */
    MessageResultDTO selectPopupRecord(String ownerCode, String vin, String businessType, String businessId, BigDecimal mileage);

    /**
     * 确认弹窗
     * @param dto
     */
    void confirmMessage(MessageConfirmDTO dto);
}
