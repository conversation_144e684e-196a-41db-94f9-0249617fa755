package com.volvo.maintain.application.maintainlead.dto.workshop;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModelInfoDto implements Serializable {

    private String modelId; // 车型id

    private String modelCode; // 车型code

    private String modelName; // 车型名称

    private String modelYear; // 年款

    private String seriesId; // 车系id

    private String seriesCode; // 车系code

    private String seriesName; // 车系名称
}
