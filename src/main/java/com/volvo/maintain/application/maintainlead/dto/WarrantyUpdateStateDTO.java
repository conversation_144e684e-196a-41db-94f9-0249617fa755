package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 延保产品-激活、失效vo
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("延保产品-激活、失效")
@ToString
public class WarrantyUpdateStateDTO implements Serializable {
	@ApiModelProperty(name = "giveStatus", value = "状态")
	private Integer giveStatus;
	@ApiModelProperty(name = "orderCodeList", value = "订单编号")
	private List<String> orderCodeList;
}
