package com.volvo.maintain.application.maintainlead.service.workshop;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.OpenOrderTraceService.commonQueryOrderTrace.CommonOrderTraceResponse;
import com.volvo.maintain.application.maintainlead.dto.bookingOrder.BookingOrderDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.*;
import com.volvo.maintain.application.maintainlead.vo.workshop.BookingOrderVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.ShortPartItemVo;

import java.util.List;

public interface TransparentWorkshopManageService {


    IPage<ShortPartItemDto> queryShortPart(ShortPartItemVo shortPartItemVo);

    /**
     * 查看单据ETA时间接口
     * 因经销商会在OG中查看商品的基本信息和价格，以及希望能
     * 看到本地仓库库存和全国仓库的可用库存，一个经销商一次
     * 只能查一个商品的数据
     */
    void queryETADocumentDetails();

    /**
     * 查询ETA时间 （C端使用）
     * @return ETA时间
     */
    List<OutboundOrderETAResponseDto> queryETADocumentDetailsToC(List<ToCRequestParamsDto> toCRequestParamsDto);

    List<CommonOrderTraceResponse> queryLogisticsNode(List<ToJDRequestParamsDto> requestParamsDtoList);

    /**
     * 同步零件状态 到 更新采购明细  更新明细中的 是否入库 零件数量
     * @param listPartBuyItemDto 采购入库 原厂弹窗入库
     */
    void syncPartStatus(ListPartBuyItemDto listPartBuyItemDto);

    /**
     * 服务看板缺件查询（企微店端）
     */
    IPage<ShortPartItemWeComDto> queryShortPartWeCom(ShortPartItemVo shortPartItemVo);

    /**
     * 查询未到货，已到货，部分到货数量
     *
     * @return 返回对象（包含数量）
     */
    MissingPartsStatusDto getShortPartStatus(ShortPartItemVo shortPartItemVo);

    /**
     * 根据缺料主键查询 明细
     *
     * @param shortPartItemVo 明细数据
     * @return 明细数据
     */
    ShortPartItemDto queryShortPartItem(ShortPartItemVo shortPartItemVo);

    /**
     * 记录通话记录
     * @param workshopCallRecordDto 通话记录详情
     * @return true false
     */
    Boolean addCallLog(WorkshopCallRecordDto workshopCallRecordDto);

    /**
     * 查询通话记录
     * @param ownerCode 经销商
     * @param roNo 工单号
     * @param serviceAdvisor 服务顾问
     * @return 通话记录
     */
    Page<WorkshopCallRecordDto> queryCallItem(String ownerCode, String roNo, String serviceAdvisor, Integer pageNum, Integer pageSize);

    /**
     * 修改预计交车时间
     * @param roNo 工单号
     * @param endTimeSupposed 预交车时间
     */
    void updateRepairOrderStatus(String roNo, String endTimeSupposed);

    IPage<BookingOrderDto> queryBookingWeCom(BookingOrderVo bookingOrderVo);

    BookingStatusDto queryBookingStatus(BookingOrderVo bookingOrderVo);

    BadgeCountSummaryDto buildPartStatusCount(String ownerCode);
}
