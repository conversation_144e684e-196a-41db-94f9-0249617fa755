package com.volvo.maintain.application.maintainlead.service.Icup;

import cn.hutool.core.date.DateUtil;
import com.volvo.maintain.application.maintainlead.dto.TmVehicleDto;
import com.volvo.maintain.application.maintainlead.dto.icup.DataLakeWarehouseIcupMileageDto;
import com.volvo.maintain.application.maintainlead.dto.icup.IcupMileageDto;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.constants.IcupConstant;
import com.volvo.maintain.infrastructure.gateway.DataLakeWarehouseFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndVehicleCenterFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ImResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class IcupServiceImpl implements IcupService{

    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;
    @Autowired
    private DataLakeWarehouseFeign dataLakeWarehouseFeign;

    @Override
    public IcupMileageDto getIcupMileageByVin(String vin) {
        log.info("getIcupMileageByVin vin:{}",vin);
        IcupMileageDto icupMileageDto=new IcupMileageDto();
        icupMileageDto.setVin(vin);
        DmsResponse<TmVehicleDto> vehicleResponse = midEndVehicleCenterFeign.queryVehicleByVIN(vin);
        log.info("vehicleResponse :{}",vehicleResponse);
        //查询车辆信息
        if(ObjectUtils.isEmpty(vehicleResponse.getData())){
            log.info("getIcupMileageByVin 查询车辆信息异常");
            icupMileageDto.setIsIcup(CommonConstant.DICT_IS_NO);
            return icupMileageDto;
        }
        if (!IcupConstant.ICUP_TYPE_1.equals(vehicleResponse.getData().getIcupType())){
            log.info("getIcupMileageByVin 车辆不是icup车型");
            icupMileageDto.setIsIcup(CommonConstant.DICT_IS_NO);
            return icupMileageDto;
        }
        //调湖仓接口查询车辆信息
        ImResponse<DataLakeWarehouseIcupMileageDto> vehMileage = dataLakeWarehouseFeign.getVehMileage(vin);
        log.info("vehicleResponse vehMileage:{}",vehMileage);
        if(ObjectUtils.isEmpty(vehMileage.getData())){
            log.info("vehMileage 查询车辆里程异常");
            icupMileageDto.setIsIcup(CommonConstant.DICT_IS_NO);
            return icupMileageDto;
        }
        icupMileageDto.setIsIcup(CommonConstant.DICT_IS_YES);
        icupMileageDto.setUpdateDate(Objects.isNull(vehMileage.getData().getSend_time()) ? null: DateUtil.formatDateTime(DateUtil.date(vehMileage.getData().getSend_time().longValue())));
        icupMileageDto.setIcupMileage(vehMileage.getData().getMileage());
        log.info("getIcupMileageByVin end IcupMileageDto:{}",icupMileageDto);
        return icupMileageDto;
    }






}
