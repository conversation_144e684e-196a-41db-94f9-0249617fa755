package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonOrderTrace {
    /**
     * 状态编码，长度1-15
     */
    private String operateCode;

    /**
     * 操作节点描述，长度1-300
     */
    private String operateRemark;

    /**
     * 运单状态节点名称，长度1-50
     */
    private String operateTitle;

    /**
     * 操作时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String operateTime;

    /**
     * 运单号，长度1-50
     */
    private String waybillNo;

    /**
     * 操作人姓名，长度1-50
     */
    private String operateName;

    /**
     * 操作城市，长度1-50
     */
    private String operateAddress;
}
