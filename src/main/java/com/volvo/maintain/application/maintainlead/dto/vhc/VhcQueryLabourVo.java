package com.volvo.maintain.application.maintainlead.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description 根据零件查工时返回参数
 * @Date 2024/11/2 18:33
 */
@Data
@ApiModel("根据零件查工时返回参数")
public class VhcQueryLabourVo {

    @ApiModelProperty("配置代码")
    private String apackageCode;

    @ApiModelProperty("维修工时代码")
    private String appRepairRange;

    @ApiModelProperty("派工工时")
    private String assignLabourHour;

    @ApiModelProperty("收费工时")
    private String claimLabour;

    @ApiModelProperty("数据来源")
    private String dataSources;

    @ApiModelProperty("索赔工时")
    private String defineStdLabour;

    @ApiModelProperty("下发标志")
    private String downTag;

    @ApiModelProperty("是否能修改")
    private String isCanUpdate;

    @ApiModelProperty("是否锁定")
    private String isLockInfo;

    @ApiModelProperty("维修工时")
    private String labourCodef;

    @ApiModelProperty("维修工时名称")
    private String labourName;

    @ApiModelProperty("维修工时分类代码")
    private String mainGroupCode;

    @ApiModelProperty("维修工时分类名称")
    private String mainGroupName;

    @ApiModelProperty("车工时")
    private String modelLabourCode;

    @ApiModelProperty("厂端工时")
    private String oemLabourHour;

    @ApiModelProperty("操作代码")
    private String operationCode;

    @ApiModelProperty("维修工时分类代码")
    private String repairGroupCode;

    @ApiModelProperty("维修工时类型代码")
    private String repairTypeCode;

    @ApiModelProperty("维修工时id")
    private String roLabourId;

    @ApiModelProperty("开始年")
    private String startModelYear;

    @ApiModelProperty("结束年")
    private String stopModelYear;

    @ApiModelProperty("工时")
    private String stdLabourHour;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("工时类型code")
    private String workerTypeCode;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;

}
