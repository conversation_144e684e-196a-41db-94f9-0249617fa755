package com.volvo.maintain.application.maintainlead.vo.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 提醒角色
 * @Date 2024/11/13 15:31
 */
@ApiModel("提醒角色")
@Data
@Builder
public class RemindRoleVo {

    @ApiModelProperty("角色表主键id")
    private String roleId;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色代码")
    private String roleCode;

    @ApiModelProperty("是否勾选")
    private Boolean checked;
}
