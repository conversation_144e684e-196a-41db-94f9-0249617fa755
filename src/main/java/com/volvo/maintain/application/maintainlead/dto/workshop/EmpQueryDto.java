package com.volvo.maintain.application.maintainlead.dto.workshop;

import java.util.List;

public class EmpQueryDto {
    private List<String> empIds;
    private List<String> userIds;

    public EmpQueryDto() {
    }

    public List<String> getEmpIds() {
        return this.empIds;
    }

    public List<String> getUserIds() {
        return this.userIds;
    }

    public void setEmpIds(final List<String> empIds) {
        this.empIds = empIds;
    }

    public void setUserIds(final List<String> userIds) {
        this.userIds = userIds;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof EmpQueryDto)) {
            return false;
        } else {
            EmpQueryDto other = (EmpQueryDto)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$empIds = this.getEmpIds();
                Object other$empIds = other.getEmpIds();
                if (this$empIds == null) {
                    if (other$empIds != null) {
                        return false;
                    }
                } else if (!this$empIds.equals(other$empIds)) {
                    return false;
                }

                Object this$userIds = this.getUserIds();
                Object other$userIds = other.getUserIds();
                if (this$userIds == null) {
                    if (other$userIds != null) {
                        return false;
                    }
                } else if (!this$userIds.equals(other$userIds)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof EmpQueryDto;
    }

    public int hashCode() {
        int result = 1;
        Object $empIds = this.getEmpIds();
        result = result * 59 + ($empIds == null ? 43 : $empIds.hashCode());
        Object $userIds = this.getUserIds();
        result = result * 59 + ($userIds == null ? 43 : $userIds.hashCode());
        return result;
    }

    public String toString() {
        return "EmpQueryDto(empIds=" + this.getEmpIds() + ", userIds=" + this.getUserIds() + ")";
    }
}
