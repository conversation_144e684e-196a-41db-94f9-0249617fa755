package com.volvo.maintain.application.maintainlead.emums;

import lombok.Getter;

@Getter
public enum RightsStrategyEnum {

    // 普通延保产品
    REGULAR_EXTENDED_WARRANTY(81501001, "普通延保产品"),

    // Evcar延保
    EVCAR_EXTENDED_WARRANTY(81501003, "Evcar延保"),

    // 出险无忧
    ACCIDENT_PIECE_OF_MIND(83451001, "出险无忧"),

    // 服务合同
    SERVICE_CONTRACT(83411002, "服务合同");

    private final Integer code;
    private final String description;

    RightsStrategyEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static RightsStrategyEnum getStrategy(Integer code) {
        for (RightsStrategyEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
