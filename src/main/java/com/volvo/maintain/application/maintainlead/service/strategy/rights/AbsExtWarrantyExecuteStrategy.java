package com.volvo.maintain.application.maintainlead.service.strategy.rights;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.rights.*;
import com.volvo.maintain.application.maintainlead.emums.RightsStrategyEnum;
import com.volvo.maintain.application.maintainlead.vo.PurchaseConditionsResultVo;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.DmscusIfserviceFeign;
import com.volvo.maintain.infrastructure.gateway.DmscusRepairFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.ExtendedWarrantyProductVo;
import com.volvo.maintain.interfaces.vo.WarrantyUpdateStateResultVo;
import com.volvo.utils.BeanMapperUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.ERROR_TYPE_35021012;

/**
 * 延保抽象
 */
@Slf4j
public abstract class AbsExtWarrantyExecuteStrategy extends AbsRightsExecuteStrategy {

    @Autowired
    private DmscusRepairFeign dmscusRepairFeign;

    @Autowired
    private DmscusIfserviceFeign dmscusIfserviceFeign;

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    /**
     * 查询可上架商品列表
     *
     * @param dto
     */
    @Override
    public IPage<RightsProductDto> productList(RightsProductDto dto) {
        //调原延保商品查询接口
        Page<RightsProductDto> page = new Page<>();
        //查延保产品列表
        Map<String, String> queryParam = Maps.newHashMap();
        queryParam.put("productNo",dto.getProductNo());
        queryParam.put("productName",dto.getProductName());
        queryParam.put("type",dto.getProductType().toString());
        queryParam.put("isValid","10041001");
        queryParam.put("isMall","10041001");
        DmsResponse<Page<ExtendedWarrantyProductVo>> pageDmsResponse = dmscusRepairFeign.queryExtendedWarrantyProductInfo(queryParam, dto.getPageNum(), dto.getPageSize());
        Page<ExtendedWarrantyProductVo> data = pageDmsResponse.getData();
        List<ExtendedWarrantyProductVo> records = data.getRecords();
        if (pageDmsResponse.isSuccess() && ObjectUtils.isNotEmpty(data) && !CollectionUtils.isEmpty(records)) {
            BeanUtil.copyProperties(data,page);
            page.setRecords(records.stream().map(e -> {
                RightsProductDto productDto = new RightsProductDto();
                productDto.setProductName(e.getProductName());
                productDto.setProductNo(e.getProductNo());
                productDto.setProductType(e.getType());
                return productDto;
            }).collect(Collectors.toList()));
        }
        return page;
    }

    @Override
    public List<GiveStatusSyncDto> giveSync(List<GiveStatusSyncDto> statusSyncDtos) {
        //此处调用原延保状态激活接口
        List<String> orderCodes = statusSyncDtos.stream().map(GiveStatusSyncDto::getOrderCode).collect(Collectors.toList());
        WarrantyUpdateStateDTO warrantyUpdateStateDTO = new WarrantyUpdateStateDTO();
        warrantyUpdateStateDTO.setGiveStatus(statusSyncDtos.get(0).getGiveStatus());
        warrantyUpdateStateDTO.setOrderCodeList(orderCodes);
        DmsResponse<List<WarrantyUpdateStateResultVo>> dmsResponse = dmscusIfserviceFeign.updateWarrantyData(warrantyUpdateStateDTO);
        if(dmsResponse.isFail()){
            throw new ServiceBizException(dmsResponse.getErrMsg());
        }
        return Collections.emptyList();
    }

    @Override
    public List<PurchaseEligibilityCheckResponseDto> buyValid(PurchaseEligibilityCheckRequestDto purchaseEligibilityCheckRequestDto) {
        List<String> productNoList = purchaseEligibilityCheckRequestDto.getProductNoList();
        if(CollectionUtils.isEmpty(productNoList)){
            return Lists.newArrayList();
        }
        List<PurchaseEligibilityCheckResponseDto> dtos = getPurchaseEligibilityCheckResponseDtos(purchaseEligibilityCheckRequestDto);
        setErrorMsg(dtos);
        return dtos;
    }

    protected List<PurchaseEligibilityCheckResponseDto> getPurchaseEligibilityCheckResponseDtos(PurchaseEligibilityCheckRequestDto purchaseEligibilityCheckRequestDto) {
        // 此处调用原延保
      /*  if (purchaseEligibilityCheckRequestDto.getCount()>1){
            return productNoList.stream().map(productNo -> getPurchaseEligibilityCheckResponseDto(purchaseEligibilityCheckRequestDto.getVin(), productNo,ERROR_TYPE_35021012)).collect(Collectors.toList());
        }*/
        /**
         *  35021003：车辆的新旧车类型不否符合产品购买范围
         *  35021005：车辆的车型代码不否符合产品购买范围
         *  35021006：车辆的发动机代码不否符合产品的购买范围
         *
         */
        DmsResponse<List<PurchaseConditionsResultVo>> response = dmscloudServiceFeign.purchaseConditions(BeanUtil.copyProperties(purchaseEligibilityCheckRequestDto, PurchaseConditionsParamDto.class));
        if (response.isFail()) {
            throw new ServiceBizException(response.getErrMsg());
        }
        if(CollectionUtils.isEmpty(response.getData())) {
            return Collections.emptyList();
        }
        DmsResponse<CommonConfigDto> configDmsResponse= dmscloudServiceFeign.getConfigByKey(this.mark(), "mall_sales_product_quantity");
        CommonConfigDto data = configDmsResponse.getData();
        if (configDmsResponse.isFail() || Objects.isNull(data)) {
            throw new ServiceBizException("获取购买数量异常");
        }
        if (org.springframework.util.StringUtils.isEmpty(data.getConfigValue())){
            throw new ServiceBizException("获取购买数量为空");
        }
        List<PurchaseEligibilityCheckResponseDto> dtos = BeanMapperUtil.copyList(response.getData(), PurchaseEligibilityCheckResponseDto.class);
        dtos.forEach(obj -> {
            if (org.springframework.util.StringUtils.isEmpty(obj.getCode())){
                obj.setCount(Integer.parseInt(data.getConfigValue()));
            }
        });
        return dtos;
    }

    private static PurchaseEligibilityCheckResponseDto getPurchaseEligibilityCheckResponseDto(String vin, String productNo,String errorCode) {
        PurchaseEligibilityCheckResponseDto dto = new PurchaseEligibilityCheckResponseDto();
        dto.setCode(errorCode); //购买数量不能大于1
        dto.setVin(vin);
        dto.setProductNo(productNo);
        dto.setCount(NumberUtils.INTEGER_ONE);
        return dto;
    }


    @Override
    public void give(ContractPurchaseGiveDto giveDto){
        if(Objects.isNull(giveDto.getMileage())){
            giveDto.setMileage(1);
        }
        //此处调用延保购买接口
        giveDto.setGiveStatus(35031001);
        DmsResponse<PurchaseGiveParamsDTO> dmsResponse= dmscusIfserviceFeign.addWarrantyPurchaseGive(giveDto);
        if(dmsResponse.isFail()){
            throw new ServiceBizException(dmsResponse.getErrMsg());
        }
    }

    @Override
    public List<OrderActivationDetailsResponseDto> giveActList(OrderActivationDetailsRequestDto orderActivationDetailsRequestDto){
        String productNos = String.join(",", orderActivationDetailsRequestDto.getProductNoList());
        DmsResponse<List<CustExtWarPurGiveVO>> response = dmscusIfserviceFeign.selectExtWarPurGiveV2(orderActivationDetailsRequestDto.getVin(), orderActivationDetailsRequestDto.getOrderCode(), productNos);
        if(response.isFail()){
            throw new ServiceBizException(response.getErrMsg());
        }
        List<CustExtWarPurGiveVO> data = response.getData();
        if(CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        return data.stream().map(e -> {
            OrderActivationDetailsResponseDto dto = new OrderActivationDetailsResponseDto();
            dto.setOrderCode(e.getOrderCode());
            dto.setVin(e.getVin());
            dto.setProductNo(e.getProductNo());
            dto.setGiveStatus(e.getGiveStatus().toString());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<UsageDetailsResponseDto> useList(UsageDetailsRequestDto usageDetailsDto){
        return Lists.newArrayList();
    }

    @Override
    public IPage<GiveRecordResponseDto> givelist(GiveRecordRequestDto giveRecordRequestDto)  {
        Page<GiveRecordResponseDto> page = new Page<>();
        return page;
    }
}
