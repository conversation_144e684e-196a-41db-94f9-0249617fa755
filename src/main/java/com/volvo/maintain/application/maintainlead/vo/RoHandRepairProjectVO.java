package com.volvo.maintain.application.maintainlead.vo;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * <p>
 * 交修项目表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class RoHandRepairProjectVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 项目编号ID
     */
    private Integer itemId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * JOB_NO
     */
    private String jobNo;

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 交修项目代码
     */
    private String handRepairProjectCode;

    /**
     * 交修项目名称
     */
    private String handRepairProjectName;

    /**
     * 维修类型代码
     */
    private String repairTypeCode;

    /**
     * 收费区分代码
     */
    private String chargePartitionCode;

    /**
     * 帐类
     */
    private String accounts;

    /**
     * 折扣率
     */
    private Double discount;

    /**
     * CSC_CODE
     */
    private String cscCode;

    /**
     * CSC_CONTENT
     */
    private String cscContent;

    /**
     * CSC_CLASS4
     */
    private String cscClass4;

    /**
     * QB号码
     */
    private String qbPhone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否有效
     */
    private BigDecimal isValid;

    /**
     * 是否删除
     */
    private Boolean isDeleted;


    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * VIDA单号
     */
    private String vidaNo;

    /**
     * vidaId
     */
    private Long vidaId;

    /**
     * 协同的jobNo
     */
    private String vidaJobNo;

    /**
     * 服务套餐代码
     */
    private String servicePkgCode;

    /**
     * 预约单号
     */
    private String bookingOrderNo;

    /**
     * 适用维修类型
     */
    private String jobType;

    /**
     * 适用账类
     */
    private String accountGroup;

    /**
     * 是否延保
     */
    private Integer isExtendInsurance;

    /**
     * 是否服务合同购买工单
     */
    private Integer isServeContractBuyOrder;

    /**
     * 是否保养活动工单
     */
    private Integer isUpkeepActivityOrder;
    
	/**
	 * 保修部件一级
	 */
    private String repairPartsLevel1;
    /**
     * 保修部件二级
     */
    private String repairPartsLevel2;
    /**
     * 维修现象
     */
    private String faultPhenomenon;
    
    /**
     * 是否维修部件缺失
     */
    private String isRepairPartsMissing;
    
    /**
     * 是否文件缺失
     */
    private String isFileMissing;
    /**
     * 是否不修
     */
    private String isNotRepaired;
    /**
     * 故障类型
     */
    private String faultType;
}
