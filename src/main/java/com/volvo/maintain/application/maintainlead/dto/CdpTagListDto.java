package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2023/11/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Cdp标签信息")
public class CdpTagListDto {
    // vin
    private String vin;
    // 标签名称
    private String tagName;
    // 标签id
    private String tagID;

    // 标签来源
    private String tagValue;

    // 标签类型
    private String tagType;

    // 标签类型
    private String tagDescription;
}
