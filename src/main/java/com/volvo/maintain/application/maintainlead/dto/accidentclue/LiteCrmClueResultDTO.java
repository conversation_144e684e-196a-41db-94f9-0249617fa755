package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * description 线索下发通用接口返回参数
 * <AUTHOR>
 * @date 2023/10/23 10:26
 */
@Data
@ApiModel(description="线索下发通用接口返回参数")
public class LiteCrmClueResultDTO {

    /**
     * 拒收的经销商code
     */
    @ApiModelProperty(value = "拒收的经销商code", required = true, example = "[\"SHJ\",\"SHN\"]")
    private List<String> rejectionCodes;


    public LiteCrmClueResultDTO() {
    }


    public LiteCrmClueResultDTO(List<String> rejectionCodes) {
        this.rejectionCodes = rejectionCodes;
    }
}
