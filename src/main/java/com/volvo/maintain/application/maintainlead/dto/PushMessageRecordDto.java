package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


@ApiModel("消息推送记录")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PushMessageRecordDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private int id;

    /**
     * 场景类型:1.邮件,2.短信,3.站内信,4.PUSH通知,5.蓝条
     */
    @ApiModelProperty(value = "场景类型:1.邮件,2.短信,3.站内信,4.推APPPUSH,5.蓝条,6.推企微push")
    private int sinceType;

    /**
     * 目标类型:1.APP,2.小程序
     */
    @ApiModelProperty(value = "目标类型:1.APP,2.小程序")
    private String targetType;

    /**
     * 业务主键或业务编码
     */
    @ApiModelProperty(value = "业务主键或业务编码")
    private String bizNo;

    /**
     * 子业务编码
     */
    @ApiModelProperty(value = "子业务编码")
    private String subBizNo;

    /**
     * 接受者：邮箱/手机号等
     */
    @ApiModelProperty(value = "接受者：邮箱/手机号等")
    private String receiver;

    /**
     * 	请求参数
     */
    @ApiModelProperty(value = "请求参数")
    private String reqParams;

    /**
     * 重试次数,默认3次
     */
    @ApiModelProperty(value = "重试次数,默认3次")
    private int retryCount;

    /**
     * 最后一次重试时间
     */
    @ApiModelProperty(value = "最后一次重试时间")
    private Date lastRetryTime;

    /**
     * 响应码
     */
    @ApiModelProperty(value = "响应码")
    private String respCode;

    /**
     * 错误消息 截取200长度字符
     */
    @ApiModelProperty(value = "错误消息 截取200长度字符")
    private String respContent;

    /**
     * 任务状态: 0:待处理 1.处理成功 2.处理失败 -1:重试最大次数异常
     */
    @ApiModelProperty(value = "任务状态: 0:待处理 1.处理成功 2.处理失败 -1:重试最大次数异常")
    private Integer taskStatus;

    /**
     * 数据创建时间
     */
    @ApiModelProperty("数据创建时间")
    private Date createdAt;

    /**
     * 数据创建人
     */
    @ApiModelProperty("数据创建人")
    private String createdBy;

    /**
     * 数据修改时间
     */
    @ApiModelProperty("数据修改时间")
    private Date updatedAt;

    /**
     * 数据修改人
     */
    @ApiModelProperty("数据修改人")
    private String updatedBy;

    private String userId;

    private String content;

}
