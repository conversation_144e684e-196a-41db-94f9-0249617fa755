package com.volvo.maintain.application.maintainlead.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description VHC车辆健康检查小类配置信息
 * @Date 2024/10/8 15:35
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("VHC车辆健康检查小类配置信息")
public class VhcItemConfigInfoDto {

    @ApiModelProperty("检查项id")
    private Integer id;

    @ApiModelProperty("检查项名称")
    private String itemName;

    @ApiModelProperty("是否勾选")
    private Boolean checked;

    @ApiModelProperty("检查项故障现象")
    private List<VhcMalfunctionInfoDto> vhcMalfunctionInfoDtoList;
}
