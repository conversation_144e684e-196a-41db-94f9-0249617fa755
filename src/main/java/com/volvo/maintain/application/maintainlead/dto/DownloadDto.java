package com.volvo.maintain.application.maintainlead.dto;

import com.yonyou.cyx.framework.service.excel.ExcelExportColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 功能描述：下载中心导出
 *
 * <AUTHOR>
 * @since 2024/01/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class DownloadDto {

    @ApiModelProperty("回调的url")
    private String serviceUrl;
    @ApiModelProperty("查询条件")
    private Map<String, Object> queryParams;
    @ApiModelProperty("表头信息")
    private List<ExcelExportColumn> excelExportColumnList;
    @ApiModelProperty("excel命名")
    private String excelName;
    @ApiModelProperty("sheet命名")
    private String sheetName;
    @ApiModelProperty("id列属性名")
    private String fieldId;
    @ApiModelProperty("是否post请求")
    public boolean postFlag;
    @ApiModelProperty("是否post请求头")
    public boolean postHeaderFlag = false;
}
