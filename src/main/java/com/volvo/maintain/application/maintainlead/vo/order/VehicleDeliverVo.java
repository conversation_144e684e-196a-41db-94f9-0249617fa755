package com.volvo.maintain.application.maintainlead.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 取送车订单
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "VehicleDeliverVO", description = "VehicleDeliverVO")
public class VehicleDeliverVo {

    @ApiModelProperty(value = "id", required = true)
    private Long id;
    @ApiModelProperty(value = "订单Code")
    private String orderCode;
    @ApiModelProperty(value = "e代驾 订单id")
    private Long orderId;
    @ApiModelProperty(value = "VIN")
    private String vin;
    @ApiModelProperty(value = "经销商code")
    private String dealerCode;
    @ApiModelProperty(value = "经销商名称")
    private String dealerName;
    @ApiModelProperty(value = "经销商联系人")
    private String dealerContacts;
    @ApiModelProperty(value = "经销商手机")
    private String dealerCell;
    @ApiModelProperty(value = "下单人oneid")
    private Integer customerOneId;
    @ApiModelProperty(value = "车主oneid")
    private Integer ownerOneId;
    @ApiModelProperty(value = "养修预约单号")
    private String reservationNo;
    @ApiModelProperty(value = "订单状态：82721001 待确认, 82721002 已下单, 82721003 资金已冻结, 82721004 订单取消, 82721005 等待司机接单, 82721006 司机已接单, 82721007 司机已开启订单, 82721008 司机已就位, 82721009 司机开车中, 82721010 司机到达目的地, 82721011 已收车, 82721012 订单已完成, 82721013 已评价, 82721016 已关闭")
    private Integer orderStatus;
    @ApiModelProperty(value = "渠道号")
    private Integer channel;
    @ApiModelProperty(value = "商户id 最多20个字符")
    private String customerId;
    @ApiModelProperty(value = "取送车订单类型 82711001 取车, 82711002 送车,")
    private Integer type;
    @ApiModelProperty(value = "订单成单模式 0-客服派单 1-司机抢单 3-实时派单")
    private Integer mode;
    @ApiModelProperty(value = "下单人手机号 手机号")
    private String createMobile;
    @ApiModelProperty(value = "车主手机号  手机号")
    private String mobile;
    @ApiModelProperty(value = "车主姓名")
    private String username;
    @ApiModelProperty(value = "取车地址联系人姓名  最多20个字符")
    private String pickupContactName;
    @ApiModelProperty(value = "取车地址联系人手机号 手机号")
    private String pickupContactPhone;
    @ApiModelProperty(value = "取车地址 最多100个字符")
    private String pickupAddress;
    @ApiModelProperty(value = "取车地址经度")
    private Double pickupAddressLng;
    @ApiModelProperty(value = "取车地址纬度")
    private Double pickupAddressLat;
    @ApiModelProperty(value = "还车地址联系人姓名  最多20个字符")
    private String returnContactName;
    @ApiModelProperty(value = "还车地址联系人手机号 手机号")
    private String returnContactPhone;
    @ApiModelProperty(value = "还车地址 最多100个字符")
    private String returnAddress;
    @ApiModelProperty(value = "还车地址经度")
    private Double returnAddressLng;
    @ApiModelProperty(value = "还车地址纬度")
    private Double returnAddressLat;
    @ApiModelProperty(value = "预约时间 必须比当前时间晚至少半个小时，当mode=3预约时间可以不传 (yyyy-MM-dd HH:mm:ss)")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bookingTime;
    @ApiModelProperty(value = "车牌号")
    private String carNo;
    @ApiModelProperty(value = "车辆品牌名称 最多50个字符")
    private String carBrandName;
    @ApiModelProperty(value = "车辆车系名称 最多50个字符")
    private String carSeriesName;
    @ApiModelProperty(value = "订单支付方式，默认为0 0-vip扣款 1-用户付款")
    private Integer payMode;
    @ApiModelProperty(value = "优惠券码 payMode=1的情况下，可以使用优惠券")
    private String couponCode;
    @ApiModelProperty(value = "中转地址 最多100个字符")
    private String midAddress;
    @ApiModelProperty(value = "中转地址经度")
    private Double midAddressLng;
    @ApiModelProperty(value = "中转地址纬度")
    private Double midAddressLat;
    @ApiModelProperty(value = "驻店司机工号")
    private String fixedDriverId;
    @ApiModelProperty(value = "是否发送短信 0-发送短信 1-不发短信 默认是 0 ")
    private Integer pushSms;
    @ApiModelProperty(value = "操作时间 yyyy-MM-dd HH:mm:ss", dataType = "java.lang.String")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;
    @ApiModelProperty(value = "创建时间 yyyy-MM-dd HH:mm:ss", dataType = "java.lang.String")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @ApiModelProperty(value = "更新时间 yyyy-MM-dd HH:mm:ss", dataType = "java.lang.String")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    @ApiModelProperty(value = "工单号")
    private String workOrderNo;
    @ApiModelProperty(value = "工单开单时间")
    private String workOrderIssuingTime;
    @ApiModelProperty(value = "是否添加取送车费用 10041001是 10041002否")
    private Integer addPickUpAndDropOffFee;
    @ApiModelProperty(value = "订单来源")
    private Integer orderSource;
    /**
     * 首次响应时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "首次响应时间 yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstResponseTime;


    /**
     * 订单来源是否是C端创建:10041001是,10041002否
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "订单来源是否是C端创建:10041001是,10041002否")
    private Integer minProgram;

    /**
     * 跟进时间(单位:分钟)
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "跟进时间(单位:分钟)")
    private Integer followUpOnTime;

    /**
     * 司机姓名
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 司机联系方式
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "司机联系方式")
    private String driverContactInfo;

    @ApiModelProperty(value = "司机接单时间")
    private Date takeOrderTime;

    @ApiModelProperty(value = "等待司机接单时间")
    private Date waitDriverTakeOrderTime;

    @ApiModelProperty(value = "司机接单耗时(单位:min)")
    private Integer takeOrderTimeConsuming;

    @ApiModelProperty(value = "按时到达时差(单位:min)")
    private Integer driverArriveTimeDif;

    @ApiModelProperty(value = "取送车订单的类型区分（1：实时单 2：预约单）")
    private Integer orderKind;

    @ApiModelProperty(value = "取消原因")
    private String cancelReason;

    @ApiModelProperty(value = "取消对象")
    private Integer cancelSource;

    @ApiModelProperty(value = "异议反馈")
    private String reviewReason;

    @ApiModelProperty(value = "顾问id")
    private Integer adviserId;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "下单时间")
    private Date placeOrderTime;

    @ApiModelProperty(value = "取车省code")
    private String pickupProvinceCode;

    @ApiModelProperty(value = "取车市code")
    private String pickupCityCode;

    @ApiModelProperty(value = "取车区code")
    private String pickupAreaCode;

    @ApiModelProperty(value = "送车省code")
    private String returnProvinceCode;

    @ApiModelProperty(value = "送车市code")
    private String returnCityCode;

    @ApiModelProperty(value = "送车区code")
    private String returnAreaCode;
    
    @ApiModelProperty(value = "车型code")
    private String modelCode;
}