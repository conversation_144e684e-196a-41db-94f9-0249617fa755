package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName PadVehiclePreviewDto
 * <AUTHOR>
 * @Date 2024/02/22
 */
@Data
@ApiModel("查询车主信息Dto")
public class PadVehiclePreviewDto {

    @ApiModelProperty(value = "车主唯一标识号(车架号)",name = "vin")
    private String vin;

    @ApiModelProperty(value = "车牌号" , name = "license")
    private String license;

    @ApiModelProperty(value = "预约单号" , name = "bookingNo")
    private String bookingNo;

    @ApiModelProperty(value = "经销商" , name = "ownerCode")
    private String ownerCode;
}
