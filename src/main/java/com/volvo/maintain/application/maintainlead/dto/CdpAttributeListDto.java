package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Cdp属性信息")
public class CdpAttributeListDto {
    // 属性id
    private String attID;
    // 属性名称
    private String attName;
    // 属性值
    private String attValue;

}
