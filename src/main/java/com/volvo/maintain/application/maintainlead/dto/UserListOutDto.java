package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "输出用户信息不分页", description = "输出用户信息不分页")
public class UserListOutDto {
	@ApiModelProperty(value = "用户id", position = 1)
	private Long userId;

	@ApiModelProperty(value = "职位id", position = 2)
	private Long userOrgId;
	/**
	 * 登录账号
	 */
	@ApiModelProperty("账号")
	private String userCode;
	/**
	 * 员工ID
	 */
	@ApiModelProperty("EMP_ID")
	private Long empId;
	/**
	 * 本身所在company_id
	 */
	@ApiModelProperty("经销商店id")
	private Long companyId;

	@ApiModelProperty("员工号")
	private String employeeNo;
	/**
	 * 员工姓名
	 */
	@ApiModelProperty("员工姓名")
	private String employeeName;
	/**
	 * 性别
	 */
	@ApiModelProperty("性别（10021001男 10021002女）")
	private Integer gender;
	/**
	 * E_MAIL
	 */
	@ApiModelProperty("E_MAIL")
	private String eMail;
	/**
	 * 邮编
	 */
	@ApiModelProperty("邮编")
	private String zipCode;
	/**
	 * 身份证号
	 */
	@ApiModelProperty("身份证号")
	private String certificateId;
	/**
	 * 手机号
	 */
	@ApiModelProperty("手机号")
	private String mobilePhone;
	/**
	 * 地址
	 */
	@ApiModelProperty("地址")
	private String address;
	/**
	 * 建档日期
	 */
	@ApiModelProperty("建档日期")
	private LocalDateTime foundDate;
	/**
	 * 出生日期
	 */
	@ApiModelProperty("出生日期")
	private LocalDateTime birthday;
	/**
	 * 离职日期
	 */
	@ApiModelProperty("离职日期")
	private LocalDateTime dimissionDate;
	/**
	 * 在职状态
	 */
	@ApiModelProperty("在职状态 10081001:在职  , 10081002:离职")
	private Integer isOnjob;
	/**
	 * 微信昵称
	 */
	@ApiModelProperty("微信昵称")
	private String nicename;
	/**
	 * 国家
	 */
	@ApiModelProperty("国家")
	private String country;
	/**
	 * 省级
	 */
	@ApiModelProperty("省级")
	private String province;

	/**
	 * 市
	 */
	@ApiModelProperty("市")
	private String city;

	/**
	 * 区
	 */
	@ApiModelProperty("区")
	private String area;

	/**
	 * 公会id
	 */
	@ApiModelProperty("公会id")
	private String unionId;

	/**
	 * 用户状态
	 */
	@ApiModelProperty("用户状态")
	private Integer userStatus;
	/**
	 * 数据来源:店面主机厂集团
	 */
	@ApiModelProperty("数据来源:")
	private Integer dataSources;

	/**
	 * 婚姻状况
	 */
	@ApiModelProperty("婚姻状况")
	private Integer marriageStatus;
	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;
	/**
	 * QQ
	 */
	@ApiModelProperty("QQ")
	private String qq;
	/**
	 * 毕业院校
	 */
	@ApiModelProperty("毕业院校")
	private String university;
	/**
	 * 籍贯
	 */
	@ApiModelProperty("籍贯")
	private String nativePlace;
	/**
	 * 户口
	 */
	@ApiModelProperty("户口")
	private String registered;
	/**
	 * 培训认证信息
	 */
	@ApiModelProperty("培训认证信息")
	private String authentication;
	/**
	 * 是否有效
	 */
	@ApiModelProperty("是否有效")
	private Integer validStatus;
	/**
	 * 是否手机号被绑定
	 */
	@ApiModelProperty("是否手机号被绑定")
	private Integer isReplace;
	/**
	 * 微信openId
	 */
	@ApiModelProperty("微信openId")
	private String openId;

	/**
	 * 头像url
	 */
	@ApiModelProperty("头像url")
	private String headPortrait;
	/**
	 * 微信号
	 */
	@ApiModelProperty("微信号")
	private String wechat;
	/**
	 * 民族
	 */
	@ApiModelProperty("民族")
	private String nation;
	/**
	 * 驾驶证级别（A、B、C）
	 */
	@ApiModelProperty("驾驶证级别")
	private String drivingLevel;
	/**
	 * 教育程度
	 */
	@ApiModelProperty("教育程度")
	private Integer degreeEdu;
	/**
	 * 年龄
	 */
	@ApiModelProperty("年龄")
	private Integer age;
	/**
	 * 汽车行业时长（年）
	 */
	@ApiModelProperty("汽车行业市场")
	private Integer autoTime;
	/**
	 * 是否校企合作院校毕业
	 */
	@ApiModelProperty("是否校企合作院校毕业")
	private Integer cooperationSchool;
	/**
	 * 毕业院校
	 */
	@ApiModelProperty("毕业院校")
	private String graduateSchool;
	/**
	 * 姓名（英文）
	 */
	@ApiModelProperty("英文名")
	private String employeeNameEn;

	@ApiModelProperty("组织id")
	private Long orgId;

	@ApiModelProperty("售后使用：用户组织id")
	private Long sellOrgId;


	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public Long getUserOrgId() {
		return userOrgId;
	}

	public void setUserOrgId(Long userOrgId) {
		this.userOrgId = userOrgId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public Long getEmpId() {
		return empId;
	}

	public void setEmpId(Long empId) {
		this.empId = empId;
	}

	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getEmployeeNo() {
		return employeeNo;
	}

	public void setEmployeeNo(String employeeNo) {
		this.employeeNo = employeeNo;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public Integer getGender() {
		return gender;
	}

	public void setGender(Integer gender) {
		this.gender = gender;
	}

	public String geteMail() {
		return eMail;
	}

	public void seteMail(String eMail) {
		this.eMail = eMail;
	}

	public String getZipCode() {
		return zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	public String getCertificateId() {
		return certificateId;
	}

	public void setCertificateId(String certificateId) {
		this.certificateId = certificateId;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public LocalDateTime getFoundDate() {
		return foundDate;
	}

	public void setFoundDate(LocalDateTime foundDate) {
		this.foundDate = foundDate;
	}

	public LocalDateTime getBirthday() {
		return birthday;
	}

	public void setBirthday(LocalDateTime birthday) {
		this.birthday = birthday;
	}

	public LocalDateTime getDimissionDate() {
		return dimissionDate;
	}

	public void setDimissionDate(LocalDateTime dimissionDate) {
		this.dimissionDate = dimissionDate;
	}

	public Integer getIsOnjob() {
		return isOnjob;
	}

	public void setIsOnjob(Integer isOnjob) {
		this.isOnjob = isOnjob;
	}

	public String getNicename() {
		return nicename;
	}

	public void setNicename(String nicename) {
		this.nicename = nicename;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getUnionId() {
		return unionId;
	}

	public void setUnionId(String unionId) {
		this.unionId = unionId;
	}

	public Integer getUserStatus() {
		return userStatus;
	}

	public void setUserStatus(Integer userStatus) {
		this.userStatus = userStatus;
	}

	public Integer getDataSources() {
		return dataSources;
	}

	public void setDataSources(Integer dataSources) {
		this.dataSources = dataSources;
	}

	public Integer getMarriageStatus() {
		return marriageStatus;
	}

	public void setMarriageStatus(Integer marriageStatus) {
		this.marriageStatus = marriageStatus;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}

	public String getUniversity() {
		return university;
	}

	public void setUniversity(String university) {
		this.university = university;
	}

	public String getNativePlace() {
		return nativePlace;
	}

	public void setNativePlace(String nativePlace) {
		this.nativePlace = nativePlace;
	}

	public String getRegistered() {
		return registered;
	}

	public void setRegistered(String registered) {
		this.registered = registered;
	}

	public String getAuthentication() {
		return authentication;
	}

	public void setAuthentication(String authentication) {
		this.authentication = authentication;
	}

	public Integer getValidStatus() {
		return validStatus;
	}

	public void setValidStatus(Integer validStatus) {
		this.validStatus = validStatus;
	}

	public Integer getIsReplace() {
		return isReplace;
	}

	public void setIsReplace(Integer isReplace) {
		this.isReplace = isReplace;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getHeadPortrait() {
		return headPortrait;
	}

	public void setHeadPortrait(String headPortrait) {
		this.headPortrait = headPortrait;
	}

	public String getWechat() {
		return wechat;
	}

	public void setWechat(String wechat) {
		this.wechat = wechat;
	}

	public String getNation() {
		return nation;
	}

	public void setNation(String nation) {
		this.nation = nation;
	}

	public String getDrivingLevel() {
		return drivingLevel;
	}

	public void setDrivingLevel(String drivingLevel) {
		this.drivingLevel = drivingLevel;
	}

	public Integer getDegreeEdu() {
		return degreeEdu;
	}

	public void setDegreeEdu(Integer degreeEdu) {
		this.degreeEdu = degreeEdu;
	}

	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	public Integer getAutoTime() {
		return autoTime;
	}

	public void setAutoTime(Integer autoTime) {
		this.autoTime = autoTime;
	}

	public Integer getCooperationSchool() {
		return cooperationSchool;
	}

	public void setCooperationSchool(Integer cooperationSchool) {
		this.cooperationSchool = cooperationSchool;
	}

	public String getGraduateSchool() {
		return graduateSchool;
	}

	public void setGraduateSchool(String graduateSchool) {
		this.graduateSchool = graduateSchool;
	}

	public String getEmployeeNameEn() {
		return employeeNameEn;
	}

	public void setEmployeeNameEn(String employeeNameEn) {
		this.employeeNameEn = employeeNameEn;
	}

	public Long getSellOrgId() {
		return sellOrgId;
	}

	public void setSellOrgId(Long sellOrgId) {
		this.sellOrgId = sellOrgId;
	}

	@Override
	public String toString() {
		return "UserListOutDTO{" +
				"userId=" + userId +
				", userOrgId=" + userOrgId +
				", userCode='" + userCode + '\'' +
				", empId=" + empId +
				", companyId=" + companyId +
				", employeeNo='" + employeeNo + '\'' +
				", employeeName='" + employeeName + '\'' +
				", gender=" + gender +
				", eMail='" + eMail + '\'' +
				", zipCode='" + zipCode + '\'' +
				", certificateId='" + certificateId + '\'' +
				", mobilePhone='" + mobilePhone + '\'' +
				", address='" + address + '\'' +
				", foundDate=" + foundDate +
				", birthday=" + birthday +
				", dimissionDate=" + dimissionDate +
				", isOnjob=" + isOnjob +
				", nicename='" + nicename + '\'' +
				", country='" + country + '\'' +
				", province='" + province + '\'' +
				", city='" + city + '\'' +
				", area='" + area + '\'' +
				", unionId='" + unionId + '\'' +
				", userStatus=" + userStatus +
				", dataSources=" + dataSources +
				", marriageStatus=" + marriageStatus +
				", remark='" + remark + '\'' +
				", qq='" + qq + '\'' +
				", university='" + university + '\'' +
				", nativePlace='" + nativePlace + '\'' +
				", registered='" + registered + '\'' +
				", authentication='" + authentication + '\'' +
				", validStatus=" + validStatus +
				", isReplace=" + isReplace +
				", openId='" + openId + '\'' +
				", headPortrait='" + headPortrait + '\'' +
				", wechat='" + wechat + '\'' +
				", nation='" + nation + '\'' +
				", drivingLevel='" + drivingLevel + '\'' +
				", degreeEdu=" + degreeEdu +
				", age=" + age +
				", autoTime=" + autoTime +
				", cooperationSchool=" + cooperationSchool +
				", graduateSchool='" + graduateSchool + '\'' +
				", employeeNameEn='" + employeeNameEn + '\'' +
				", orgId=" + orgId +
				", sellOrgId=" + sellOrgId +
				'}';
	}
}
