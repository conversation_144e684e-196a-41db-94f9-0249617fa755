package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("cdp档案")
public class ObjectOneIdV2Dto {
	private String id;
	private String name;
	private String cvr_start_time;
	private String cvr_end_time;
}