package com.volvo.maintain.application.maintainlead.dto;

import com.volvo.maintain.application.maintainlead.dto.workshop.VehicleEntranceEntityDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("环检单tag查询")
public class PrecheckOrderTagParamsDto {

    @ApiModelProperty("车架号车牌号")
    private List<VinLicenseDto> vinLicenseDtos;

    @ApiModelProperty("经销商code")
    private String ownerCode;

    @ApiModelProperty("进场信息")
    private List<VehicleEntranceEntityDto> records;
}
