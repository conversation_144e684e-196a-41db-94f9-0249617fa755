package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("NB定制化标签查询数据")
public class CustomizedLabelDto {

    /** 车架号 */
    private String vin;

    /** 经销商代码 */
    private String ownerCode;

    /** 会员id */
    private Integer MemberId;

    /** 车架号 */
    private String licensePlate;

    /** 调用端类别 0 客户端，1 服务端 */
    private String selectType;

    /** 客户手机号 */
    private String phone;

    /** 客户手机号 */
    private List<String> tagCodes;


}
