package com.volvo.maintain.application.maintainlead.service.techclaim.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.volvo.annotation.DistributedLock;
import com.volvo.maintain.application.maintainlead.dto.CustomerInfoDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderExtDto;
import com.volvo.maintain.application.maintainlead.dto.claimapply.ClaimApplyUseDTO;
import com.volvo.maintain.application.maintainlead.dto.techclaim.EbaotechIssueDto;
import com.volvo.maintain.application.maintainlead.service.techclaim.EbaotechClaimService;
import com.volvo.maintain.infrastructure.constants.CdpConstant;
import com.volvo.maintain.infrastructure.gateway.DmscusIfserviceFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.yonyou.cyx.framework.util.bean.ApplicationContextHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class EbaotechClaimServiceImpl implements EbaotechClaimService {

    @Autowired
    private DmscusIfserviceFeign dmscusIfserviceFeign;

    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;

    @Qualifier("threadEBao")
    @Autowired
    private ThreadPoolTaskExecutor thread;

    @DistributedLock(prefix = "EbaotechClaimService:lock:issueApproval:", key = {"#ebaotechIssueDto.registNo"}, delimiter = "-")
    @Override
    public void     issueApproval(EbaotechIssueDto ebaotechIssueDto) {
        log.info("EbaotechIssueDto: {}", ebaotechIssueDto);
        String caseNo = ebaotechIssueDto.getRegistNo();
        if(StringUtils.isEmpty(caseNo)){
            log.error("EbaotechIssueDto:registNo is empty");
            return;
        }
        //先记录下发日志.
        ClaimApplyUseDTO claimApplyUseDTO = new ClaimApplyUseDTO();
        claimApplyUseDTO.setCaseNo(caseNo);
        DmsResponse<Long> logDmsResponse = dmscusIfserviceFeign.addApprovalLog(claimApplyUseDTO);
        //根据报案号查询报案信息
        DmsResponse<ClaimApplyUseDTO> applyDetailReponse = dmscusIfserviceFeign.getAllClaimApplyDetail(caseNo);
        if(!applyDetailReponse.isSuccess()){
            log.error("issueApproval getAllClaimApplyDetail error:"+applyDetailReponse.getErrMsg());
        }else{
            ClaimApplyUseDTO claimApplyUseDetailDTO = applyDetailReponse.getData();
            if(Objects.isNull(claimApplyUseDetailDTO)){
                log.error("issueApproval getAllClaimApplyDetail is null");
            }else{
                claimApplyUseDetailDTO.setLogId(logDmsResponse.getData());
                log.info("issueApproval getAllClaimApplyDetail success");
                EbaotechClaimServiceImpl proxy = ApplicationContextHelper.getBeanByType(EbaotechClaimServiceImpl.class);
                proxy.issueApprovalSync(caseNo, claimApplyUseDetailDTO);
            }
        }
    }

    @Async("threadEBao")
    public void issueApprovalSync(String caseNo,ClaimApplyUseDTO detailDto) {
        //工单扩展信息
        if(Objects.nonNull(detailDto.getOwnerCode()) && Objects.nonNull(detailDto.getOrderNo())){
            DmsResponse<RepairOrderExtDto> repairOrderExt = domainMaintainOrdersFeign.getOrderExtByOwnerCodeAndRoNo(detailDto.getOwnerCode(), detailDto.getOrderNo());
            log.info("issueApprovalSync repairOrderExt:{}", JSON.toJSONString(repairOrderExt));
            if(!repairOrderExt.isFail() && Objects.nonNull(repairOrderExt.getData())){
                RepairOrderExtDto repairOrderExtDto = repairOrderExt.getData();
                if (Objects.nonNull(repairOrderExtDto.getInsurerChannel())){
                    detailDto.setInsurerChannel(repairOrderExtDto.getInsurerChannel());
                }
            }
        }
        //根据报案号查询是否存在审批记录,同时判定审批记录是否可以进行下发
        DmsResponse<ClaimApplyUseDTO> approvalDetail = dmscusIfserviceFeign.getApprovalDetail(caseNo);

        if(Objects.nonNull(approvalDetail) && ObjectUtil.isNotEmpty(approvalDetail.getData())){
            dmscusIfserviceFeign.updateApproval(detailDto);
        }else{
            dmscusIfserviceFeign.addApproval(detailDto);
        }
    }
}
