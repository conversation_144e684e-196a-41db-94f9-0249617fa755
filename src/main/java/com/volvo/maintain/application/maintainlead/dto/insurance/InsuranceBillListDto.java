package com.volvo.maintain.application.maintainlead.dto.insurance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 保险信息列表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("保险信息")
public class InsuranceBillListDto{
	private static final long serialVersionUID = 1L;

	/**
	 * 系统id
	 */
	private String appId;

	/**
	 * 所有者代码
	 */
	private String ownerCode;

	/**
	 * 所有者的父组织代码
	 */
	private String ownerParCode;

	/**
	 * 组织id
	 */
	private Integer orgId;

	/**
	 * 主键id自增序列
	 */
	private Long id;

	/**
	 * 投保单主键
	 */
	private Long insuranceId;

	/**
	 * 险种名称
	 */
	private String insuranceTypeName;

	/**
	 * 险种类型
	 */
	private Integer insuranceTypeKind;

	/**
	 * 保额
	 */
	private BigDecimal insuranceAmount;

	/**
	 * 应收金额
	 */
	private BigDecimal receivableAmount;

	/**
	 * 保险开始日期
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date insuranceBeginDate;

	/**
	 * 保险结束日期
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date insuranceEndDate;

	/**
	 * 是否赠送
	 */
	private Integer isGiving;

	/**
	 * 数据来源
	 */
	private Integer dataSources;

	/**
	 * 是否删除
	 */
	private Boolean isDeleted;

	/**
	 * 是否有效
	 */
	private Integer isValid;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date createdAt;

	/**
	 * 更新时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date updatedAt;

	/**
	 * 投保单号
	 */
	private String insuranceNo;

	/**
	 * 客户投保类型
	 */
	private Integer insuranceType;

	/**
	 * 保险公司名称
	 */
	private String insuranceName;

	/**
	 * 大区名称
	 */
	private String bigAreaName;

	/**
	 * 小区名称
	 */
	private String smallAreaName;

	/**
	 * 经销商代码
	 */
	private String dealerCode;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 投保人姓名
	 */
	private String policyHolderName;

	/**
	 * 投保人电话
	 */
	private String policyHolderPhone;

	/**
	 * 投保人手机
	 */
	private String policyHolderMobile;

	/**
	 * 投保人邮编
	 */
	private String policyHolderZipcode;

	/**
	 * 投保人地址
	 */
	private String policyHolderAddress;

}
