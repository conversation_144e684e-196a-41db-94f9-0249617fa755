package com.volvo.maintain.application.maintainlead.service;

import com.volvo.maintain.application.maintainlead.dto.AccidentCluesDto;
import com.volvo.maintain.application.maintainlead.dto.AccidentCluesFollowStatusChangeTaskDto;
import com.volvo.maintain.application.maintainlead.dto.CompanyDetailByCodeDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.*;
import com.volvo.maintain.application.maintainlead.vo.AccidentCluesVo;
import com.volvo.maintain.application.maintainlead.vo.DealerVO;

import java.util.List;

public interface AccidentCluesService {

    /**
     * 接受下发线索
     * @param dto liteCrm下发线索
     * @return 布尔成功失败
     */
    LiteCrmClueResultDTO crmToNewbieClueDistribute(LeadOperationResultDto dto);

    InsuranceDealerDto getInsuranceDealer(String insuranceNo, String vin);

    /**
     * 事故线索下发后置操作
     * @param dto
     * @return
     */
    boolean clueDistributePostProcess(LeadOperationResultDto dto);

    /**
     * 	线索池新增超时未跟进状态
     */
    void updateAccidentCluesTimeOutJob(AccidentCluesFollowStatusChangeTaskDto dto);
    /**
     * 	线索池新增关闭状态
     */
    void updateAccidentCluesCloseJob(AccidentCluesFollowStatusChangeTaskDto dto);

    /**
     * 事故线索集合
     */
    List<AccidentCluesVo> list(AccidentCluesDto query);
    /**
     * 根据acId,dealerPhone查询虚拟手机号
     */
    String getVirtualNumberById(Long acId);


    /**
     * 导出数据查询
     * <p>
     * 下载中心回调
     */
    List<AccidentCluesExportDto> exportOss(AccidentCluesExportQueryDto query);

    /**
     *查询全网服务经理
     * @param params
     * @return
     */
    DealerVO getDealerList(DealerDTO params);
    /**
     * 事故线索分配经销商
     */
    void allotDealer(List<AllotDealerDTO> params);
    /**
     * 查询经销商信息
     */
    CompanyDetailByCodeDto getDealerInfo(String dealerCode);
    /**
     * 线索池列表统计数据查询
     */
    AccidentCluesSumInfoDTO getSumAccidentInfo(AccidentCluesExportQueryDto dto);

}
