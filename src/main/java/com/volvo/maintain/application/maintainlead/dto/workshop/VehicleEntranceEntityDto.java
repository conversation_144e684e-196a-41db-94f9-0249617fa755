package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * tt_vehicle_entrance
 * 
 * <AUTHOR>
 * @version 1.0.0 2023-04-17
 */
@Data
public class VehicleEntranceEntityDto implements Serializable {
    /** 版本号 */
    private static final long serialVersionUID = 8085037879461841880L;

    /** 主键 */
    private Integer id;

    /** 进场车辆上报流水id */
    private String entranceId;

    /** 车牌号 */
    private String licensePlate;

    /** 经销商code */
    private String dealerCode;

    /** 进场时间 */
    private String entryTime;

    /** 置顶状态(0-非置顶，1-置顶) */
    private Integer topStatus;
    
    /**
     * 置顶时间
     */
    private Date topTime;   

    /** 接待状态(待接待:0、接待中:1、已建单:2) */
    private Integer receiveStatus;

    /** 接待人 */
    private Integer receiveId;
    
    /** 接待人 */
    private String receiveName;

    /** 接待时间 */
    private String receiveTime;

    /** 预约单号 */
    private String appointmentId;

    /** 预约时间 */
    private String appointmentTime;

    /** 环检单号 */
    private String esrsId;

    /** 工单号 */
    private String workOrderId;
    
    /** 是否存在进行中的订单（不存在-0，存在-1） */
    private Integer isExistOrder;

    /** 特殊车辆标识（非特殊车辆-0，特殊车辆-1） */
    private Integer isSpecialVehicle;

    /** 车架号 */
    private String vin;

    /** 是否有效（0-有效，1-无效） */
    private Integer isEffective;

    /** 删除标识（0-未删除，1-删除） */
    private Integer isDeleted;

    /** 创建人 */
    private Integer createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新人 */
    private Integer updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 版本号 */
    private Integer recordVersion;

    //邀约类型
    @TableField(exist = false)
    private Integer inviteType;

    //待授权
    @TableField(exist = false)
    private String authorization;

    //是否vip
    @TableField(exist = false)
    private boolean vip;

    private String serviceSource;

    private String serviceType;

    private Long inviteTagId;

    private String tagName;

    /**
     * 标签状态 亮不亮
     */
    private String tagState;

    /**
     * 业务类型
     */
    private String businessType;
    
    private List<String> vipGroup;

    private List<String> tagList;

}