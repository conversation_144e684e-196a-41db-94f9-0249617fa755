package com.volvo.maintain.application.maintainlead.service.strategy.clues;

import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadOperationResultDto;
import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueDto;
import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueResDto;
import com.volvo.maintain.application.maintainlead.dto.faultlight.FaultLightClueResponseDto;
import com.volvo.maintain.application.maintainlead.emums.ClueStrategyEnum;
import com.volvo.maintain.application.maintainlead.service.faultLight.FaultLightService;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FaultLampStrategy extends AbsClueExecuteStrategy {

    @Autowired
    private FaultLightService faultLightService;

    @Override
    protected ClueStrategyEnum strategyEnum() {
        return ClueStrategyEnum.FAULT_LAMP;
    }

    @Override
    protected void preProcess(LeadOperationResultDto dto) {
        DistributeClueDto distributeClueDto = dto.getDistributeClueDto();
        faultLightService.subComments(dto);
        faultLightService.setCompanyDetail(dto,distributeClueDto.getDealerCode());
    }

    @Override
    protected DistributeClueResDto distributeClues(LeadOperationResultDto dto) {
        DmsResponse<FaultLightClueResponseDto> response = domainMaintainLeadFeign.addClueCompensate(dto);
        return new DistributeClueResDto(Boolean.TRUE,null,response.getData());
    }

    @Override
    protected boolean postProcess(LeadOperationResultDto dto) {
        DistributeClueDto distributeClueDto = dto.getDistributeClueDto();
        DistributeClueResDto dtoDistributeClueResDto = dto.getDistributeClueResDto();
        FaultLightClueResponseDto responseDto = (FaultLightClueResponseDto)dtoDistributeClueResDto.getData();
        faultLightService.doMess(dto, responseDto.getDate(), responseDto.getTtFaultLightCluePo(), responseDto.isFlag(), responseDto.getComments(), distributeClueDto.getDealerCode());
        return true;
    }
}
