package com.volvo.maintain.application.maintainlead.service;

import java.util.List;

import com.volvo.maintain.application.maintainlead.dto.RepairOrderAndClaimTypeReqDto;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderAndClaimTypeRespVO;

public interface RepairOrderService {

	/**
	 * 
	 * @param repairOrderAndClaimTypeReq
	 * @return
	 */
	List<RepairOrderAndClaimTypeRespVO> queryRepairOrderAndClaimTypeByVin(RepairOrderAndClaimTypeReqDto repairOrderAndClaimTypeReq);
}