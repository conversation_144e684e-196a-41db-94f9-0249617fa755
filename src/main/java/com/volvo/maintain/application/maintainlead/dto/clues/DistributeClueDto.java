package com.volvo.maintain.application.maintainlead.dto.clues;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class DistributeClueDto{
    /**
     * 经销商代码(临时)
     */
    private String dealerCode;

    /**
     * 下发日志ID(临时)
     */
    private Long logId;

    /**
     * 车主姓名
     */
    private String name;

    /**
     * 车主电话
     */
    private String tel;

    /**
     * 有效SA ID集合
     */
    private List<Long> saIds;
}
