package com.volvo.maintain.application.maintainlead.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("服务合同购买dto")
public class ContractPurchaseGiveDto {

    /**
     *
     */
    @NotNull(message = "dealerCode不能为空")
    private Integer dealerCode;

    /**
     *渠道
     */
    @NotNull(message = "渠道不能为空")
    private Integer giveChannel;

    /**
     *车牌号
     */
    private String licensePlateNum;

    /**
     *会员id
     */
    private String memberId;

    /**
     *车辆里程
     */
    private Integer mileage;

    /**
     *订单编号
     */
    @NotNull(message = "订单编号不能为空")
    private String orderCode;

    /**
     *支付时间
     */
    @NotNull(message = "支付时间不能为空")
    private String payTime;

    /**
     *延保产品件号 延保必传
     */
    @NotNull(message = "产品件号不能为空")
    private String productNo;

    /**
     *商品编号
     */
    @NotNull(message = "商品编号不能为空")
    private String skuId;

    /**
     *vin
     */
    @NotNull(message = "vin不能为空")
    private String vin;

    /**
     *购买数量(默认1)
     */
    @NotNull(message = "购买数量不能为空")
    private Integer count;

    /**
     *车辆销售时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date salesDate;

    /**
     *车型
     */
    private String modelCode;

    /**
     *车辆销售价格
     */
    private double invoicePrice;

    /**
     *开票日期
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date invoiceDate;

    /**
     *发动机代码
     */
    private String engineNo;

    private  Integer purchaseCount;

    private  Integer giveStatus;

    private  String ownerCode;
}
