package com.volvo.maintain.application.maintainlead.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RepairOrderReqV2DTO implements Serializable {
	
    private static final long serialVersionUID = 1L;
    /**
     * 所有者代码
     */
	@ApiModelProperty(value = "经销商code", name = "ownerCode", required = true)
    private String ownerCode;

    /**
     * 工单号
     */
	@ApiModelProperty(value = "工单号", name = "roNo", required = true)
    private String roNo;
}