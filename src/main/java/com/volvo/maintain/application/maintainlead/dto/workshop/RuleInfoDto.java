package com.volvo.maintain.application.maintainlead.dto.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 规则配置信息
 * @Date 2024/11/14 14:13
 */
@ApiModel("规则配置信息")
@Data
public class RuleInfoDto {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("提醒类型")
    private String businessType;

    @ApiModelProperty("经销商代码")
    private String ownerCode;

    @ApiModelProperty("是否启用")
    private String isEnabled;

    @ApiModelProperty("值")
    private String remindValue;

    @ApiModelProperty("提醒时间")
    private String remindTime;

    @ApiModelProperty("提醒规则")
    private String remindRule;

    @ApiModelProperty("保存角色信息")
    private List<SaveRoleInfoDto> saveRoleInfoDto;

    @ApiModelProperty("保存自定义人员信息")
    private List<SaveCustomUserInfoDto> saveCustomUserInfoDto;

    @ApiModelProperty("删除自定义人员Id集合")
    private List<Integer> deleteCustomUserIds;

    @ApiModelProperty("特殊车辆信息")
    private List<SaveSpecialVehicleConfigDto> saveSpecialVehicleConfigDto;

    @ApiModelProperty("删除特殊车辆Id集合")
    private List<Integer> deleteSpecialVehicleIds;
}
