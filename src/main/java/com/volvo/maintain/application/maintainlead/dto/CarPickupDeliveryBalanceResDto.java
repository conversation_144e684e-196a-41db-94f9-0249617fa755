package com.volvo.maintain.application.maintainlead.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("上门取送车余额查询")
public class CarPickupDeliveryBalanceResDto implements Serializable {
	
    private static final long serialVersionUID = 1L;

	@ApiModelProperty("商户id")
    private String merchantId;

    @ApiModelProperty("子渠道id")
    private String typeId;

    @ApiModelProperty("供应商")
    private String type;
    
    @ApiModelProperty("金额")
    private String price;
}
