package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@ApiModel("线索分配经销商")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AllotDealerDTO implements Serializable {
    private static final long serialVersionUID = 34352134L;
    @ApiModelProperty("线索id")
    private Long acId;
    @ApiModelProperty("经销商代码")
    private String companyCode;
    @ApiModelProperty("线索来源")
    @NotBlank(message = "线索来源不能为空")
    private String sourceChannel;
    @ApiModelProperty("厂端vcdc")
    private String source;

}
