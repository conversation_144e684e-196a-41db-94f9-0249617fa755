package com.volvo.maintain.application.maintainlead.service.repairAssign;

import java.util.List;

import com.volvo.maintain.application.maintainlead.dto.RepairOrderReqDTO;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderReqV2DTO;
import com.volvo.maintain.application.maintainlead.dto.repairAssign.TtRoAssignDTO;
import com.volvo.maintain.application.maintainlead.vo.FinalInspectionContentVO;
import com.volvo.maintain.application.maintainlead.vo.FinalInspectionTreeVO;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderV2VO;
import com.volvo.maintain.application.maintainlead.vo.RoHandRepairProjectVO;
import com.volvo.maintain.interfaces.vo.CheckFinalInspectionReqVO;
import com.volvo.maintain.interfaces.vo.CheckFinalInspectionRespVO;
import com.volvo.maintain.interfaces.vo.FinalInspectionAttVO;

public interface RepairAssignV2Service {

	/**
	 * 查询工单组根据工单信息
	 * @param repairOrderReq
	 * @return
	 */
	List<RepairOrderV2VO> queryRepairOrders(RepairOrderReqV2DTO repairOrderReq);

	/**
	 * 根据经销商+工单号批量查询工单信息
	 * @param repairOrderListReq
	 * @return
	 */
	List<RoHandRepairProjectVO> qualityInspection(List<RepairOrderReqDTO> repairOrderListReq);

	/**
	 * 根据维修类型 筛选出 维修部位&维修现象下拉选
	 * @param repairTypeCode
	 * @return
	 */
	FinalInspectionTreeVO queryRepairAnalysisByType(String repairTypeCode);

	/**
	 * 新增根据交修项目维修类型集合反查终检内容清单
	 * @param repairTypeCodeList
	 * @return
	 */
	List<FinalInspectionContentVO> queryFinalInspectionContentByType(List<String> repairTypeCodeList);

	/**
	 * 维修质检
	 * @param dtoList
	 */
	void maintainRepairAssignCompleteV2(List<TtRoAssignDTO> dtoList);

	/**
	 * 维修项目校验
	 * @param checkFinalInspectionReqList
	 * @return
	 */
	List<CheckFinalInspectionRespVO> checkFinalInspection(List<CheckFinalInspectionReqVO> checkFinalInspectionReqList);

	/**
	 * 查询附加项目 根据 经销商+工单号
	 * @param ownerCode
	 * @param roNo
	 * @return
	 */
	List<FinalInspectionAttVO> queryFinalInspectionAttByRoNo(String ownerCode, String roNo);
}
