package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("交车校验报告记录表")
public class HandoverInspectionDetailDto {

    /**
     * 提醒表id
     */
    private Long reminderId;

    /**
     * 报告类型
     */
    private String reportType;

    /**
     * 报告单号
     */
    private String reportNo;

    /**
     * 报告状态
     */
    private String reportStatus;

    /**
     * 首次提交时间
     */
    private String initialSubmissionTime;

    /**
     * 进场时间
     */
    private String entryTime;

    /**
     * 交车时间
     */
    private String deliveryTime;

}
