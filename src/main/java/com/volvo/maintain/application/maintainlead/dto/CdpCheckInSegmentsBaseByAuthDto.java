package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;



/**
 * <AUTHOR>
 * @date 2024/02/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("CDP判断属于人群返回")
public class CdpCheckInSegmentsBaseByAuthDto {
    // 是否属于任意一个人群
    private Boolean in_any_segment;

    // 属于的人群ID列表
    private String[] in_segment_list;


}
