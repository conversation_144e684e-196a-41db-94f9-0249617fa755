package com.volvo.maintain.application.maintainlead.dto.inviteClue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 邀约话术
 */
@ApiModel(description = "邀约话术")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InvitationScripDto {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 经销商编号
     */
    @ApiModelProperty(value = "经销商编号")
    private String ownerCode;

    /**
     * 话术标题
     */
    @ApiModelProperty(value = "话术标题")
    private String title;

    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    private Integer typeCodeId;

    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    private List<Integer> typeCodeIds;
    /**
     * 客户标签
     */
    @ApiModelProperty(value = "客户标签")
    private String customerTagIds;

    /**
     * 合并标签
     */
    private String mergeTagIds;

    /**
     * 车辆标签
     */
    @ApiModelProperty(value = "车辆标签")
    private String vehicleTagIds;

    /**
     * 初始话术
     */
    @ApiModelProperty(value = "初始话术")
    private String defTalkskill;

    /**
     * 标签话术
     */
    @ApiModelProperty(value = "标签话术")
    private String tagTalkskill;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Integer isEnable;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    private Integer dataSources;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer weight;

    /**
     * 删除标识（0-未删除，1-已删除）
     */
    @ApiModelProperty(value = "删除标识（0-未删除，1-已删除）")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 创建sql人
     */
    @ApiModelProperty(value = "创建sql人")
    private String createSqlby;

    /**
     * 更新sql人
     */
    @ApiModelProperty(value = "更新sql人")
    private String updateSqlby;

    /**
     * 分组id
     */
    private String tagId;
}