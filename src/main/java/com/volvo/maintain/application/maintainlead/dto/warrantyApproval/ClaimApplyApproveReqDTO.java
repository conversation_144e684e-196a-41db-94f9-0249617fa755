package com.volvo.maintain.application.maintainlead.dto.warrantyApproval;

import com.volvo.maintain.application.maintainlead.dto.claimapply.ClaimApplyUseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "延保理赔申请列表查询DTO", description = "延保理赔申请列表查询DTO")
@Data
public class ClaimApplyApproveReqDTO {

    @ApiModelProperty("延保审批ID")
    private Long warrantyApprovalId;

    @ApiModelProperty("审批状态")
    private Integer approvalStatus;

    @ApiModelProperty("审批备注")
    private String remark;

    @ApiModelProperty("详情")
    private ClaimApplyUseDTO claimApplyUseDTO;
}