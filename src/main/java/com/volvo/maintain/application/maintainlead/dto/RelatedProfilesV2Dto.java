package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("cdp档案")
public class RelatedProfilesV2Dto {
	private String subject_id;
	private String relation_type;
	private String object_profile_name;
	private String cvr_start_time;
	private String cvr_end_time;
	private List<ObjectOneIdV2Dto> object_oneid;
}
