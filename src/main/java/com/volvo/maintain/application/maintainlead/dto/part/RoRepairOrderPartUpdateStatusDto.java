package com.volvo.maintain.application.maintainlead.dto.part;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("维修工单零件埋点标识")
public class RoRepairOrderPartUpdateStatusDto {

	/**
	 * 配件代码
	 */
	@ApiModelProperty(value = "partNo",required = true)
	private String partNo;

	/**
	 * 埋点标识
	 */
	@ApiModelProperty(value = "partUpdateStatus")
	private String partUpdateStatus;
}
