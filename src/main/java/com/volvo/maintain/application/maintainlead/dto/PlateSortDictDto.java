package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "一级板块分类", description = "一级板块分类")
@Data
public class PlateSortDictDto {


    @ApiModelProperty(value = "appId")
    private String appId;

    @ApiModelProperty(value = "分类代码")
    private Integer type;

    @ApiModelProperty(value = "分类名称")
    private String typeName;

    @ApiModelProperty(value = "codeID")
    private Integer codeId;

    @ApiModelProperty(value = "描述(中文)")
    private String codeCnDesc;

    @ApiModelProperty(value = "描述(英文)")
    private String codeEnDesc;

    @ApiModelProperty(value = "序号")
    private Integer num;

    @Override
    public String toString() {
        return "PersonalCtrlDTO [type=" + type + ", typeName=" + typeName + ", codeId=" + codeId + ", codeCnDesc="
                + codeCnDesc + ", codeEnDesc=" + codeEnDesc + "]";
    }
}
