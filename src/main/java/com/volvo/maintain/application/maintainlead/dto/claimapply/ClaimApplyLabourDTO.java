package com.volvo.maintain.application.maintainlead.dto.claimapply;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/09/13
 */
@Data
public class ClaimApplyLabourDTO implements Serializable {

    /**
     * 系统id
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织id
     */
    private Integer orgId;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 理赔申请id 关联tt_claim_apply_use的id
     */
    private Long applyId;

    /**
     * 工时代码
     */
    private String labourCode;

    /**
     * 工时名称
     */
    private String labourName;

    /**
     * 工时数
     */
    private BigDecimal stdLabourHour;

    /**
     * 单价
     */
    private BigDecimal labourPrice;

    /**
     * 总价
     */
    private BigDecimal labourAmount;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 授权份额
     */
    private BigDecimal approvalQuota;

    /**
     * 授权金额
     */
    private BigDecimal approvalAmount;

}
