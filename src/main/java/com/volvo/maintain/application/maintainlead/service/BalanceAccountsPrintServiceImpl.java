package com.volvo.maintain.application.maintainlead.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.BalanceAccountsExportTempDTO;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class BalanceAccountsPrintServiceImpl implements BalanceAccountsPrintService{

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Autowired
    private RedissonClient redissonClient;

    @Override
    public void exportFile(Map<String, String> queryParams) {
        //导出入参
        setPageParams(queryParams);
        log.info("exportFile queryParams:{}", queryParams);
        //经销商code
        String ownerCode = queryParams.get("ownerCode");
        RLock lock = null;
        try {
            //锁key 标识符+经销商code
            lock = redissonClient.getLock(StringUtils.join("BalanceAccountsPrint", ownerCode));
            boolean isLocked = false;
            // 设置锁超时时间 30分钟 超时10分钟
            isLocked = lock.tryLock(30, 10, TimeUnit.MINUTES);
            if (!isLocked) {
                log.info("结算单导出拦截，{}", new Date());
                throw new RuntimeException("上一个导出还在执行中,请耐心等待");
            }
            //生成export_id 经销商code+时间戳
            String exportId = ownerCode + System.currentTimeMillis();
            //根据入参查询导出数据
            while (true) {
                // 分页导出
                DmsResponse<Page<Map<String, String>>> result = dmscloudServiceFeign.findAll(queryParams);
                log.info("dmscloudServiceFeign findAll result :{}", result);
                //查询数据为空 跳出循环
                if (CollectionUtils.isEmpty(result.getData().getRecords())) {
                    log.info("维修业务查询数据为空");
                    break;
                }

                List<BalanceAccountsExportTempDTO> balanceAccountsExportTempDTOS = new ArrayList<>();
                result.getData().getRecords().stream().forEach(e -> {
                    BalanceAccountsExportTempDTO dto = new BalanceAccountsExportTempDTO();
                    dto.setBalanceNo(e.get("BALANCE_NO"));
                    dto.setOwnerCode(e.get("OWNER_CODE"));
                    dto.setRoNo(e.get("RO_NO"));
                    dto.setExportId(exportId);
                    balanceAccountsExportTempDTOS.add(dto);
                });
                //入库打印temp表数据
                log.info("rinseBalanceAccountsTemp 入库打印temp表数据:{}", balanceAccountsExportTempDTOS);
                dmscloudServiceFeign.rinseBalanceAccountsTemp(balanceAccountsExportTempDTOS);
                log.info("rinseBalanceAccountsTemp 结束");
                //页数+1
                queryParams.put("pageNum", Integer.toString(Integer.parseInt(queryParams.get("pageNum")) + 1));
                queryParams.put("currentPage", Integer.toString(Integer.parseInt(queryParams.get("currentPage")) + 1));
                queryParams.put("current", Integer.toString(Integer.parseInt(queryParams.get("current")) + 1));
            }

            //下载中心
            dmscloudServiceFeign.balanceAccountsDownload(exportId, ownerCode);

        } catch (Exception e) {
            log.info("经销商导出异常:{}", e);
            throw new RuntimeException("经销商导出异常", e);
        } finally {
            if (null != lock && lock.isLocked()) {
                lock.unlock();
            }
        }

    }


    //分页入参
    public void setPageParams(Map<String, String> queryParams) {
        log.info("exportFile :{}", queryParams);
        if (Objects.isNull(queryParams.get("current"))) {
            queryParams.put("current", "1");
        }
        if (Objects.isNull(queryParams.get("page"))) {
            queryParams.put("page", "1");
        }
        if (Objects.isNull(queryParams.get("currentPage"))) {
            queryParams.put("currentPage", "1");
        }
        if (Objects.isNull(queryParams.get("pageNum"))) {
            queryParams.put("pageNum", "1");
        }
        queryParams.put("limit", "5000");
        queryParams.put("size", "5000");
        queryParams.put("pageSize", "5000");
        log.info("exportFile end:{}", queryParams);
    }
}
