package com.volvo.maintain.application.maintainlead.dto.warrantyApproval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "延保回款查询列表", description = "延保回款查询列表")
public class warrantyReturnPageDTO  implements Serializable {

    @ApiModelProperty("回款ID")
    private Long id;

    @ApiModelProperty("经销商代码")
    private String ownerCode;

    @ApiModelProperty("经销商名称")
    private String ownerName;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("年")
    private String yearNo;

    @ApiModelProperty("月")
    private String monthNo;

    @ApiModelProperty("不含税金额")
    private BigDecimal noTaxAmt;

    @ApiModelProperty("含税开票金额")
    private BigDecimal amt;

    @ApiModelProperty("VCDC税金")
    private BigDecimal taxAmt;
}
