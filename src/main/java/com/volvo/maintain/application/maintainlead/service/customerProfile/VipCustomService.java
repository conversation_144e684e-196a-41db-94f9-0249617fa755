package com.volvo.maintain.application.maintainlead.service.customerProfile;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.VipCustomPageReqDTO;
import com.volvo.maintain.application.maintainlead.dto.VipGroupImportDTO;
import com.volvo.maintain.application.maintainlead.dto.VipImportDto;
import com.volvo.maintain.interfaces.vo.VehicleEntranceVo;

import java.util.List;

public interface VipCustomService {

    void increasVip(List<VipImportDto> vipImportDto);

    void saveEntranceReceptionVehicle(String message);

    VehicleEntranceVo getVehicleEntranceVO(VehicleEntranceVo vehicleEntranceVO);

    void vipEntranceEmail();

    String importVipGroup(List<VipGroupImportDTO> importDTOList);

    Page<VipGroupImportDTO> vipGroupPage(VipCustomPageReqDTO pageReqDTO);

    void vipGroupExport(VipCustomPageReqDTO pageReqDTO);

    List<VipGroupImportDTO> exportCallback(VipCustomPageReqDTO pageReqDTO);
}
