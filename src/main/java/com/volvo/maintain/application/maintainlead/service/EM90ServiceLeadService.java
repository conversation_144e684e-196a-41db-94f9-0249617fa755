package com.volvo.maintain.application.maintainlead.service;


import com.volvo.maintain.application.maintainlead.dto.EmailBodyDto;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderVO;
import com.volvo.maintain.interfaces.vo.VehicleEntranceVo;

import java.util.List;
import java.util.Map;

/**
 * 功能描述：EM90服务线索接口
 *
 * <AUTHOR>
 * @since 2024/02/22
 */
public interface EM90ServiceLeadService {

    /**
     * 功能描述：根据车牌号查询对应vin
     *
     * @param licensePlate 车牌号
     * @return vin
     */
    String queryOwnerVehicle(VehicleEntranceVo vehicleEntranceVO);
    /**
     * 功能描述：查询当前车辆在本店中是否有未结算的维修工单（非PDS）
     *
     * @param dealerCode 经销商信息
     * @param vin 车架号
     * @return RepairOrderVO 工单信息
     */
    RepairOrderVO queryUnsettledRepairOrder(String dealerCode, String vin);
    /**
     * 功能描述：推送EM90专属管家
     *
     * @param content 推送内容
     */
    void pushQwExclusiveButler(String vin,String content);
    /**
     * 功能描述：新增推送记录
     *
     * @param vin 车架号
     * @param vehicleEntranceVO 车辆进场信息
     */
    Integer insertPushMessageRecord(Integer sinceType,String subBizNo,String bizNo);
    /**
     * 功能描述：修改推送记录
     *
     * @param pushId 主键id
     * @param flag 是否成功
     */
    void updatePushMessageRecord(Integer pushId, boolean flag);
    /**
     * 功能描述：根据车型代码查询本地配置
     *
     * @param modelId 车型代码
     */
    Boolean queryConfig(String modelId,String modelCode);
    /**
     * 功能描述：EM90保养线索提醒
     *
     */
    void EM90MaintenanceClueReminder();

    String replacePlaceholders(Map<String, String> map, String content);
    /**
     * EM90-上工位待分拨异常提醒
     */
    void EM90UpperWorkstationToBeAllocated(Integer beginTimeOut, Integer endTimeOut);

    String queryDealerName(String dealerCode);

    /**
     * 补偿消息推送
     * @param bizNo 业务号
     */
    void compensateMessagePush(List<String> bizNo, String minutes);

    /**
     * 根据消息对象转换为消息内容
     * @param dto 消息对象
     * @return 内容
     */
    String templateContent(EmailBodyDto dto, String bizNo);
}
