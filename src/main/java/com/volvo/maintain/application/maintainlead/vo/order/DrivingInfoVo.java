package com.volvo.maintain.application.maintainlead.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * <p>
 * 商户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@ApiModel(value = "DrivingInfoVO 对象", description = "DrivingInfoVO")
public class DrivingInfoVo {

    @ApiModelProperty(value = "代驾单id ")
    private String daijiaOrderId;
    @ApiModelProperty(value = "代驾费用 单位厘")
    private String daijiaPrice;
    @ApiModelProperty(value = "代驾类型 1-取车")
    private String type;
    @ApiModelProperty(value = "司机工号 ")
    private String driverNo;
    @ApiModelProperty(value = "司机手机号 ")
    private String driverPhone;
    @ApiModelProperty(value = "预约时间 毫秒数 yyyy-MM-dd HH:mm:ss")
    private String bookingTime;
    @ApiModelProperty(value = "取车地址联系人姓名 ")
    private String pickupContactName;
    @ApiModelProperty(value = "取车地址联系人手机号 ")
    private String pickupContactPhone;
    @ApiModelProperty(value = "取车地址 ")
    private String pickupAddress;
    @ApiModelProperty(value = "取车地址经度 ")
    private String pickupAddressLng;
    @ApiModelProperty(value = "取车地址纬度 ")
    private String pickupAddressLat;
    @ApiModelProperty(value = "还车地址联系人姓名 ")
    private String returnContactName;
    @ApiModelProperty(value = "还车地址联系人手机号 ")
    private String returnContactPhone;
    @ApiModelProperty(value = "还车地址 ")
    private String returnAddress;
    @ApiModelProperty(value = "还车地址经度 ")
    private String returnAddressLng;
    @ApiModelProperty(value = "还车地址纬度 ")
    private String returnAddressLat;

    @ApiModelProperty(value = "费用明细 ")
    private List<FeeDetailVo> feeDetail;

}

