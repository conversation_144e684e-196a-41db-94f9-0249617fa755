package com.volvo.maintain.application.maintainlead.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 取送车订单
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "PlaceOrderVO", description = "PlaceOrderVO")
public class PlaceOrderVo {

    @ApiModelProperty(value = "id", required = true)
    private Long id;

    @ApiModelProperty(value = "e代驾订单号")
    private String orderId;
    
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String carNo;
    /**
     * VIN
     */
    @ApiModelProperty(value = "VIN")
    private String vin;
    
    @ApiModelProperty(value = "车型code")
    private String modelCode;

}