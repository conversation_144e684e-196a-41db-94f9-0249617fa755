package com.volvo.maintain.application.maintainlead.dto.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020/10/29 18:59
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("短信发送DTO")
public class MessageSendDto {
    @ApiModelProperty(value = "员工Id列表",name = "employeeIds")
    private String employeeIds;

    @ApiModelProperty(value = "手机号列表",name = "mobiles")
    private String mobiles;

    @ApiModelProperty(value = "OneId列表",name = "oneIds")
    private String oneIds;

    @ApiModelProperty(value = "参数map",name = "paramMap")
    private Map<String,String> paramMap;

    @ApiModelProperty(value = "模板Id",name = "templateId")
    private String templateId;

    private String appId;

    private String event;

}
