package com.volvo.maintain.application.maintainlead.service.warrantyApproval.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderExtDto;
import com.volvo.maintain.application.maintainlead.dto.claimapply.ClaimApplyUseDTO;
import com.volvo.maintain.application.maintainlead.dto.reminder.WarrantyMaintenanceReminderDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.*;
import com.volvo.maintain.application.maintainlead.service.warrantyApproval.WarrantyApprovalService;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.APP_ID;

@Slf4j
@Service
public class WarrantyApprovalServiceImpl implements WarrantyApprovalService {

    @Resource
    private DmscusIfserviceFeign ifFeign;

    @Resource
    private DmscusReportFeign reportFeign;

    @Resource
    private DmscusIfserviceFeign ifServiceFeign;
    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
    /**
     * 延保理赔申请列表查询
     */
    @Override
    public Page<ClaimApplyPageRespDTO> approvalList(ClaimApplyPageReqDTO claimApplyPageReqDTO) {
        DmsResponse<Page<ClaimApplyPageRespDTO>> res = reportFeign.warrantyApprovalList(claimApplyPageReqDTO);
        if (ObjectUtil.isNotEmpty(res) && ObjectUtil.isNotEmpty(res.getData())) {
            return res.getData();
        }
        return new Page<>();
    }

    /**
     * 延保理赔申请列表导出
     */
    @Override
    public void exportList(ClaimApplyPageReqDTO reqDTO) {
        reportFeign.exportWarrantyApprovalList(reqDTO);
    }

    /**
     * 延保理赔申请审批
     */
    @Override
    public void approve(ClaimApplyApproveReqDTO reqDTO) {
        log.info("approve reqDTO:{}", JSON.toJSONString(reqDTO));
        //工单扩展信息  延保渠道切换
        if (Objects.nonNull(reqDTO.getClaimApplyUseDTO().getOwnerCode()) && Objects.nonNull(reqDTO.getClaimApplyUseDTO().getOrderNo()))  {
            String ownerCode = reqDTO.getClaimApplyUseDTO().getOwnerCode();
            String orderNo = reqDTO.getClaimApplyUseDTO().getOrderNo();
            DmsResponse<RepairOrderExtDto> repairOrderExt = domainMaintainOrdersFeign.getOrderExtByOwnerCodeAndRoNo(ownerCode, orderNo);
            log.info("issueApprovalSync repairOrderExt:{}", JSON.toJSONString(repairOrderExt));
            if(!repairOrderExt.isFail() && Objects.nonNull(repairOrderExt.getData())){
                RepairOrderExtDto repairOrderExtDto = repairOrderExt.getData();
                if (Objects.nonNull(repairOrderExtDto.getInsurerChannel())){
                    reqDTO.getClaimApplyUseDTO().setInsurerChannel(repairOrderExtDto.getInsurerChannel());
                }
            }
        }
        DmsResponse<Void> approve = ifFeign.approve(reqDTO);
        if(approve.isFail()){
            throw new ServiceBizException("操作失败："+approve.getErrMsg());
        }
    }

    /**
     * 审批id查询审批记录，易宝来源
     */
    @Override
    public List<ClaimApplyApproveRecordRespDTO> approvalRecordList(Long id) {
        return ifFeign.approvalRecordList(id).getData();
    }

    /**
     * 获取申请信息
     */
    @Override
    public ClaimApplyUseDTO getApprovalDetail(String caseNo) {
        return ifFeign.getApprovalDetail(caseNo).getData();
    }

    @Override
    public void modifyApprovalAmount(ClaimApplyUseDTO dto) {
        log.info("application>modifyApprovalAmount：{}",JSON.toJSONString(dto));
        //工单扩展信息  延保渠道切换
        if (Objects.nonNull(dto.getOwnerCode()) && Objects.nonNull(dto.getOrderNo()))  {
            String ownerCode = dto.getOwnerCode();
            String orderNo = dto.getOrderNo();
            DmsResponse<RepairOrderExtDto> repairOrderExt = domainMaintainOrdersFeign.getOrderExtByOwnerCodeAndRoNo(ownerCode, orderNo);
            log.info("modifyApprovalAmount repairOrderExt:{}", JSON.toJSONString(repairOrderExt));
            if(!repairOrderExt.isFail() && Objects.nonNull(repairOrderExt.getData())){
                RepairOrderExtDto repairOrderExtDto = repairOrderExt.getData();
                if (Objects.nonNull(repairOrderExtDto.getInsurerChannel())){
                    dto.setInsurerChannel(repairOrderExtDto.getInsurerChannel());
                }
            }
        }
        DmsResponse<Void> voidDmsResponse = ifFeign.modifyApprovalAmount(dto);
        if(voidDmsResponse.isFail()){
            throw new ServiceBizException("修改授权金额失败："+voidDmsResponse.getErrMsg());
        }
    }

    /**
     * 延保回款查询列表
     */
    @Override
    public Page<warrantyReturnPageDTO> returnList(warrantyReturnReqDTO dto) {
        DmsResponse<Page<warrantyReturnPageDTO>> res = ifServiceFeign.returnList(dto);
        if (ObjectUtil.isNotEmpty(res) && ObjectUtil.isNotEmpty(res.getData())) {
            return res.getData();
        }
        return new Page<>();
    }

    /**
     * 延保回款查询明细列表
     */
    @Override
    public List<warrantyReturnDetailPageDTO> detailList(Long returnId)  {
        log.info("application>detailList:{}", returnId);
        if (ObjectUtil.isEmpty(returnId)) {
            return new ArrayList<>();
        }
        DmsResponse<List<warrantyReturnDetailPageDTO>> dmsResponse = ifServiceFeign.detailList(returnId);
        log.info("dmsResponse:{}", dmsResponse);
        if (ObjectUtil.isNotEmpty(dmsResponse) && ObjectUtil.isNotEmpty(dmsResponse.getData())) {
            return dmsResponse.getData();
        }
        return new ArrayList<>();
    }

    /**
     * 延保回款列表汇总导出
     */
    @Override
    public List<warrantyReturnPageDTO> reportList(warrantyReturnReqDTO dto) {
        DmsResponse<List<warrantyReturnPageDTO>> res = reportFeign.reportList(dto);
        if (ObjectUtil.isNotEmpty(res) && ObjectUtil.isNotEmpty(res.getData())) {
            return res.getData();
        }
        return new ArrayList<>();
    }

    /**
     * 延保回款查询明细列表导出
     */
    @Override
    public List<warrantyReturnDetailPageDTO> reportDetailList(List<Long> returnIds)  {
        log.info("application>reportDetailList:{}", returnIds);
        if (ObjectUtil.isEmpty(returnIds)) {
            return new ArrayList<>();
        }
        DmsResponse<List<warrantyReturnDetailPageDTO>> dmsResponse = reportFeign.reportDetailList(returnIds);
        log.info("dmsResponse:{}", dmsResponse);
        if (ObjectUtil.isNotEmpty(dmsResponse) && ObjectUtil.isNotEmpty(dmsResponse.getData())) {
            return dmsResponse.getData();
        }
        return new ArrayList<>();
    }

    @Override
    public WarrantyMaintenanceReminderDTO getReminder(String vin, boolean flag, String roNo) {
        log.info("application getReminder:{},{}", vin, flag);
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if(Objects.isNull(currentLoginInfo)){
            throw new ServiceBizException("获取登录信息失败");
        }
        DmsResponse<WarrantyMaintenanceReminderDTO> reminder = dmscloudServiceFeign.getReminder(flag ? currentLoginInfo.getOwnerCode() : null , vin, roNo);
        return reminder.getData();
    }
}
