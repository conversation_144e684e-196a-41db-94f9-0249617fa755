package com.volvo.maintain.application.maintainlead.dto.bookingOrder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("预约单")
public class BookingOrderDto {
    @ApiModelProperty("预约单号")
    private String bookingOrderNo;
    @ApiModelProperty("经销商代码")
    private String ownerCode;
    @ApiModelProperty("预约到店时间")
    private String bookingComeTime;

    /**
     * 姓名
     */
    private String deliverName;

    /**
     * 车主
     */
    private String ownerName;

    private String deliverPhone;

    /**
     * 车牌
     */
    private String license;

    /**
     * 预约类别
     */
    private String bookingTypeCode;
    
    /**
     * 预约类别名称
     */
    private String bookingTypeName;

    /**
     * 服务顾问
     */
    private String serviceAdvisor;

    /**
     * 客户需求
     */
    private String remark;

    /**
     * VHC备注
     */
    private String customerDesc;


    private String tag;

}
