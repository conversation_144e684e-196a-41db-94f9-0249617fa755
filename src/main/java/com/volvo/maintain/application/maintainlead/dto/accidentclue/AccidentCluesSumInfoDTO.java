package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value="AccidentCluesSumInfoDTO",description="事故线索统计数据")
@Data
public class AccidentCluesSumInfoDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty(value = "异常线索总条数",name="abnormalTotal")
  private Integer abnormalTotal;

  @ApiModelProperty(value = "未跟进线索总条数",name="noFollowTotal")
  private Integer noFollowTotal;

  @ApiModelProperty(value = "全部",name="searchCount")
  private Integer searchCount;

  @ApiModelProperty(value = "继续跟进总条数",name="keepFollowTotal")
  private Integer keepFollowTotal;

  @ApiModelProperty(value = "超时未跟进总条数",name="overtimeTotal")
  private Integer overtimeTotal;


}
