package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.TagValueRuleDto.ComposeDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CdpTagInfoService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CustomizedLabelService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.TagInfoService;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.enums.FirstPlateEnum;
import com.volvo.maintain.infrastructure.enums.SecondPlateEnum;
import com.volvo.maintain.infrastructure.enums.TagEnum;
import com.volvo.maintain.infrastructure.enums.ThirdPlateEnum;
import com.volvo.maintain.infrastructure.gateway.CdpFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainLeadFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndDictCenterFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import com.volvo.maintain.infrastructure.util.DateUtil;
import com.volvo.maintain.infrastructure.util.StringUtil;
import com.volvo.maintain.interfaces.vo.*;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.BLOCK_TYPE_ONE;


/**
 * 功能描述：标签管理实现
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Service
@Slf4j
public class TagInfoServiceImpl implements TagInfoService {
    @Autowired
    DomainMaintainLeadFeign domainMaintainLeadFeign;

    @Autowired
    MidEndDictCenterFeign midEndDictCenterFeign;

    @Autowired
    CdpTagInfoService cdpTagInfoService;

    @Autowired
    CustomizedLabelService customizedLabelService;
    @Autowired
    CdpFeign cdpFeign;

    @Qualifier("thread360Pool")
    @Autowired
    private ThreadPoolTaskExecutor thread360Pool;

    @Autowired
    private CommonMethodService commonMethodService;


    private static String pattern = "\\d+(\\.\\d+)?";
    /**
     * 功能描述：查询标签信息列表
     *
     * @param tagInfoDTO 标签信息dto对象
     * @return Page<TagInfoVO> 标签信息列表
     */
    @Override
    public Page<TagInfoVo> queryTagInfo(TagInfoDto tagInfoDTO) {
        log.info("queryTagInfo  request:{}", JSONObject.toJSONString(tagInfoDTO));
        DmsResponse<Page<TagInfoVo>> result = domainMaintainLeadFeign.queryTagInfo(tagInfoDTO);
        log.info("queryTagInfo response:{}", JSONObject.toJSONString(result));
        if (result.isFail()) {
            throw new ServiceBizException("列表信息查询失败:{}", result.getReturnMessage());
        }
        result.getData().getRecords().forEach(i -> {
            if (i.getTagSource() == null) {
                i.setTagSource(CommonConstant.NEWBIE_TAG_SOURCE);
            }
            if (i.getShowLevel() == null) {
                i.setShowLevel(CommonConstant.SHOW_LEVEL_SHOW);
            }
            if (i.getIsGetDetail() ==null) {
                i.setIsGetDetail(CommonConstant.DICT_IS_NO);
            }
            if (i.getShowSort() ==null) {
                i.setShowSort(CommonConstant.SHOW_SORT);
            }
        });
        return result.getData();
    }
    /**
     * 功能描述：查询标签详情
     *
     * @param id 标签id
     * @return TagInfoVO 标签信息
     */
    @Override
    public TagInfoVo queryTagInfoById(Long id) {
        log.info("queryTagInfoById  request:{}", id);
        DmsResponse<TagInfoVo> result = domainMaintainLeadFeign.queryTagInfoById(id);
        log.info("queryTagInfoById response:{}", JSONObject.toJSONString(result));
        return result.getData();
    }

    /**
     * 功能描述：更新标签信息
     *
     * @param changeTagInfoDTO 标签入参
     */
    @Override
    public Integer updateTagInfo(ChangeTagInfoDto changeTagInfoDTO) {
        //获取已存在展示顺序
        DmsResponse<List<Integer>> showSortList = domainMaintainLeadFeign.queryShowSort();
        //获取当前数据信息
        DmsResponse<TagInfoVo> dmsResponse = domainMaintainLeadFeign.queryTagInfoTempById(changeTagInfoDTO.getId());
        // 增加临时表  根据id 查询临时表是否有数据
        DmsResponse<List<TagInfoVo>> dmsResponseTempData = domainMaintainLeadFeign.queryTagTempInfoList();
        log.info("updateTagInfo->dmsResponseTempData:{}", JSONObject.toJSONString(dmsResponseTempData));
        log.info("dmsResponse response:{}", JSONObject.toJSONString(dmsResponse));
        log.info("showSortList response:{}", JSONObject.toJSONString(showSortList));
        if (showSortList.isSuccess() && ObjectUtils.isNotEmpty(showSortList.getData()) && dmsResponse.isSuccess() && ObjectUtils.isNotEmpty(dmsResponse.getData())) {
            showSortList.getData().remove(dmsResponse.getData().getShowSort());
            log.info("showSortList :{}", JSONObject.toJSONString(showSortList));
            if (showSortList.getData().contains(changeTagInfoDTO.getShowSort())) {
                throw new ServiceBizException("该展示顺序已存在！！！");
            }
        }
        if (changeTagInfoDTO.getShowThirdBlock() != null) {
            changeTagInfoDTO.setBlockType(CommonConstant.THIRD_BLOCK_TYPE);
        } else if (changeTagInfoDTO.getShowSecondBlock() != null) {
            changeTagInfoDTO.setBlockType(CommonConstant.SECOND_BLOCK_TYPE);
        } else if (changeTagInfoDTO.getShowFirstBlock() != null) {
            changeTagInfoDTO.setBlockType(CommonConstant.FIRST_BLOCK_TYPE);
        }
        DmsResponse<Integer> result = null;
        if (dmsResponseTempData.isSuccess() && CollectionUtil.isNotEmpty(dmsResponseTempData.getData())) {
            //有数据的话 直接更新
            log.info("updateTagInfo  request:{}", JSONObject.toJSONString(changeTagInfoDTO));
            result = domainMaintainLeadFeign.updateTagInfo(changeTagInfoDTO);
            log.info("updateTagInfo response:{}", JSONObject.toJSONString(result));
        } else if (dmsResponseTempData.isSuccess() && CollectionUtil.isEmpty(dmsResponseTempData.getData())) {
            // 没有数据先同步一条数据在更新。
            log.info("domainMaintainLeadFeign->request:{}", JSONObject.toJSONString(changeTagInfoDTO));
            DmsResponse<Integer> line = domainMaintainLeadFeign.synchronizeDataToTemp();
            log.info("domainMaintainLeadFeign->response:{}", line);
            if (line.getData() > 0) {
                result = domainMaintainLeadFeign.updateTagInfo(changeTagInfoDTO);
            }
        }
        return result.getData();
    }
    /**
     * 功能描述：同步CDP标签信息
     *
     */
    @Override
    public void syncCDPTagTree() {
        log.info("syncCDPTagTree begin");
        //重试次数
        Integer retryCount = 0;
        CdpTagTaskDto cdpTagTaskDto = new CdpTagTaskDto();
        //读取任务池中当天是否有未完成任务或未获取到任务
        DmsResponse<CdpTagTaskDto> exist = domainMaintainLeadFeign.queryExistTask();
        log.info("queryExistTask response:{}", JSONObject.toJSONString(exist));
        if (exist.isFail()) {
            throw new ServiceBizException("获取同步任务异常！！！");
        }
        if (ObjectUtils.isNotEmpty(exist.getData())) {
            if (exist.getData().getRetryCount() >= CommonConstant.RETRY_COUNT_THREE) {
                throw new ServiceBizException("已超最大重试次数！！！");
            }
            retryCount = exist.getData().getRetryCount();
            BeanUtil.copyProperties(exist.getData(),cdpTagTaskDto);
        } else {
            cdpTagTaskDto.setBizNo(DateUtil.queryNow());
            cdpTagTaskDto.setSinceType(3);
            //插入当天任务入任务池
            DmsResponse<Integer> cdpTaskId = domainMaintainLeadFeign.insertCdpTagTask(cdpTagTaskDto);
            cdpTagTaskDto.setId(cdpTaskId.getData());
            cdpTagTaskDto.setTaskStatus(0);
        }
        if (cdpTagTaskDto.getTaskStatus() != CommonConstant.TASK_STATUS_SUCCESS) {
            try {
                //获取CDP标签数据
                List<TagListDto> cdpTagListDtoList = cdpTagInfoService.queryTagList();
                //处理本地标签库数据
                this.handleLocalTagInfo(cdpTagListDtoList);
                //更新任务状态
                cdpTagTaskDto.setTaskStatus(CommonConstant.TASK_STATUS_SUCCESS);
                cdpTagTaskDto.setRetryCount(1 + retryCount);
                domainMaintainLeadFeign.updateCdpTagTask(cdpTagTaskDto);
            } catch (Exception e) {
                log.info("同步CDP标签失败:{}", e.getMessage());
                if (Objects.equals(retryCount, CommonConstant.RETRY_COUNT_TWO)) {
                    cdpTagTaskDto.setRetryCount(CommonConstant.RETRY_COUNT_THREE);
                    cdpTagTaskDto.setTaskStatus(CommonConstant.TASK_STATUS_MAX);
                } else {
                    cdpTagTaskDto.setRetryCount(1 + retryCount);
                    cdpTagTaskDto.setTaskStatus(CommonConstant.TASK_STATUS_CLOSE);
                }
                domainMaintainLeadFeign.updateCdpTagTask(cdpTagTaskDto);
            }
        }
    }

    /**
     * 功能描述：获取CDP标签及本地配置
     *
     * @param vin 车架号
     * @param mobile 手机号
     * @return CompleteTagInfoVO 全量标签信息
     */
    @Override
    public List<CompleteTagInfoVo> queryCdpTagInfoAndConfigure(String vin, String mobile, Integer memberId) {
        log.info("queryCdpTagInfoAndConfigure start...");
        // 获取cdp车辆标签
        CompletableFuture<CustomerTagsDto> cdpTagFuture = CompletableFuture.supplyAsync(() -> queryTagListByVinAndMobile(vin,""), thread360Pool);
        // 获取cdp客户标签
        CompletableFuture<CustomerTagsDto> cdpCustomTagFuture = CompletableFuture.supplyAsync(() -> queryTagListByVinAndMobile("", mobile), thread360Pool);
        // 获取字典中心当前一级标签
        CompletableFuture<List<Integer>> dictFuture = CompletableFuture.supplyAsync(() -> queryCodeDataType(), thread360Pool);
        // 查询本地标签配置
        CompletableFuture<List<TagInfoVo>> localTagFuture = CompletableFuture.supplyAsync(() -> queryCompleteTagInfoByFirstCode(), thread360Pool);
        // 收集Future
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                cdpTagFuture,
                cdpCustomTagFuture,
                dictFuture,
                localTagFuture
        );
        // 从Future中提取结果
        List<TagInfoVo> tagInfoVOS = new ArrayList<>();
        CustomerTagsDto customerTagsDto = new CustomerTagsDto();
        CustomerTagsDto cdpCustomerTagsDto = new CustomerTagsDto();
        List<Integer> codeList = new ArrayList<>();
        log.info("queryCdpTagInfoAndConfigure futures get start...");
        try {
            // 等待所有查询完成
            allFutures.get(60, TimeUnit.SECONDS);

            // 将CompletableFutures的结果添加到最终列表
            if (ObjectUtils.isNotEmpty(localTagFuture)) {
                tagInfoVOS.addAll(localTagFuture.get());
            }
            if (ObjectUtils.isNotEmpty(cdpTagFuture)) {
                customerTagsDto = cdpTagFuture.get();
            }
            if (ObjectUtils.isNotEmpty(cdpCustomTagFuture)){
                cdpCustomerTagsDto = cdpCustomTagFuture.get();
            }
            if (ObjectUtils.isNotEmpty(dictFuture)){
                codeList = dictFuture.get();
            }
        } catch (InterruptedException | ExecutionException | TimeoutException | ServiceBizException e) {
            log.info("queryCdpTagInfoAndConfigure->CompletableFutureError", e);
            e.printStackTrace();
            return new ArrayList<>();
        }
        log.info("queryCdpTagInfoAndConfigure futures get end...");

        List<CdpTagInfoVo> cdpTagInfoVOS = customerTagsDto.getTagInfoVOS();
        List<CdpAttributeListDto> attLisit = customerTagsDto.getAttLisit();
        if (CollectionUtil.isNotEmpty(cdpCustomerTagsDto.getTagInfoVOS())) {
            cdpTagInfoVOS.addAll(cdpCustomerTagsDto.getTagInfoVOS());
        }
        if (CollectionUtil.isNotEmpty(cdpCustomerTagsDto.getAttLisit())) {
            attLisit.addAll(cdpCustomerTagsDto.getAttLisit());
        }

        // 处理值
        log.info("queryCdpTagInfoAndConfigure handleValueByValueRule start...");
        this.handleValueByValueRule(tagInfoVOS, cdpTagInfoVOS, attLisit, vin, mobile, memberId);
        log.info("queryCdpTagInfoAndConfigure handleValueByValueRule end...");

        // 处理详情、值规则、拼接板块数据
        log.info("queryCdpTagInfoAndConfigure handleDetail start...");
        List<CompleteTagInfoVo> completeTagInfoVOS = this.handleDetail(tagInfoVOS, codeList);
        log.info("queryCdpTagInfoAndConfigure handleDetail end...");
        return completeTagInfoVOS;
    }
    /**
     * 功能描述：获取字段转译详情
     *
     * @param id 主键id
     * @return FieldTranslationVO 字段转译信息
     */
    @Override
    public FieldTranslationVo queryFieldTranslationDetail(Long id) {
        log.info("queryFieldTranslationDetail request:{}", id);
        //查询当前标签详情
        DmsResponse<TagInfoVo> result = domainMaintainLeadFeign.queryTagInfoById(id);
        if (result.isSuccess() && ObjectUtils.isNotEmpty(result.getData())) {
            FieldTranslationVo fieldTranslationVO = new FieldTranslationVo();
            BeanUtils.copyProperties(result.getData(), fieldTranslationVO);
            if (StringUtils.isNotEmpty(result.getData().getValueRule())) {
                //获取值规则
                TagValueRuleDto tagValueRuleDto= JSONObject.parseObject(result.getData().getValueRule(), TagValueRuleDto.class);
                if (ObjectUtils.isNotEmpty(tagValueRuleDto.getShowRule()) && ObjectUtils.isNotEmpty(tagValueRuleDto.getShowRule().getConvert())) {
                    fieldTranslationVO.setConvert(tagValueRuleDto.getShowRule().getConvert());
                    List<String> tagCodes = new ArrayList<>();
                    //获取字段集合
                    if (CollectionUtil.isNotEmpty(tagValueRuleDto.getShowRule().getConvert().getOr())) {
                        tagCodes = tagValueRuleDto.getShowRule().getConvert().getOr().stream().map(TagValueRuleDto.andOrDto::getTagId).collect(Collectors.toList());
                    } else if (CollectionUtil.isNotEmpty(tagValueRuleDto.getShowRule().getConvert().getAnd())) {
                        tagCodes = tagValueRuleDto.getShowRule().getConvert().getAnd().stream().map(TagValueRuleDto.andOrDto::getTagId).collect(Collectors.toList());
                    }
                    DmsResponse<List<TagInfoVo>> infoVOList = domainMaintainLeadFeign.queryTagInfoByTagCodes(tagCodes);
                    if (infoVOList.isSuccess()) {
                        fieldTranslationVO.setFieldInfoList(infoVOList.getData());
                    }
                }
            }
            log.info("queryFieldTranslationDetail response:{}", JSONObject.toJSONString(fieldTranslationVO));
            return fieldTranslationVO;
        }
        return new FieldTranslationVo();
    }
    /**
     * 功能描述：查询字段下拉框
     *
     * @param tagInfoDTO dto对象
     * @return List<TagInfoVO> 字段下拉框
     */
    @Override
    public List<TagInfoVo> queryFieldInfo(TagInfoDto tagInfoDTO) {
        log.info("queryFieldInfo request:{}", JSONObject.toJSONString(tagInfoDTO));
        DmsResponse<List<TagInfoVo>> infoVOList = domainMaintainLeadFeign.queryFieldInfo(tagInfoDTO);
        log.info("queryFieldInfo response:{}", infoVOList);
        if (infoVOList.isSuccess()) {
            return infoVOList.getData();
        }
        return new ArrayList<>();
    }

    /**
     * 功能描述：添加/更新字段转译
     *
     * @param fieldTranslationDto 字段更新修改Dto
     */
    @Override
    public void updateFieldInfo(FieldTranslationDto fieldTranslationDto) {
        ChangeTagInfoDto changeTagInfoDTO = new ChangeTagInfoDto();
        BeanUtil.copyProperties(fieldTranslationDto, changeTagInfoDTO);
        TagValueRuleDto tagValueRuleDto = new TagValueRuleDto();
        tagValueRuleDto.setGetType(CommonConstant.CLIENT_SELF_PROCESSING);
        tagValueRuleDto.setShowType(CommonConstant.TEXT_SHOW_TYPE);
        TagValueRuleDto.ShowRuleDto showRuleDto = new TagValueRuleDto.ShowRuleDto();
        if (ObjectUtils.isNotEmpty(fieldTranslationDto.getConvert())) {
            showRuleDto.setConvert(fieldTranslationDto.getConvert());
            tagValueRuleDto.setShowRule(showRuleDto);
            changeTagInfoDTO.setValueRule(JSON.toJSONString(tagValueRuleDto));
        }
        log.info("updateFieldInfo request:{}", JSONObject.toJSONString(changeTagInfoDTO));
        DmsResponse<Void> result;
        if (fieldTranslationDto.getId() != null) {
            changeTagInfoDTO.setTagName(fieldTranslationDto.getShowName());
            result = domainMaintainLeadFeign.updateFieldInfo(changeTagInfoDTO);
        } else {
            changeTagInfoDTO.setTagId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
            changeTagInfoDTO.setTagName(fieldTranslationDto.getShowName());
            changeTagInfoDTO.setShowName(fieldTranslationDto.getShowName());
            changeTagInfoDTO.setTagType(fieldTranslationDto.getTagType());
            changeTagInfoDTO.setIsTag(CommonConstant.DICT_IS_YES);
            changeTagInfoDTO.setIsValid(CommonConstant.DICT_IS_YES);
            changeTagInfoDTO.setIsDetailTag(CommonConstant.DICT_IS_NO);
            changeTagInfoDTO.setIsGetDetail(CommonConstant.DICT_IS_NO);
            changeTagInfoDTO.setTagSource(CommonConstant.NEWBIE_TAG_SOURCE);
            result = domainMaintainLeadFeign.insertTagRuleInfo(changeTagInfoDTO);
            // 增加临时表  根据id 查询临时表是否有数据
            DmsResponse<TagInfoVo> dmsResponseTempData = domainMaintainLeadFeign.queryTagTempInfoById();
            if (dmsResponseTempData.isSuccess() && Objects.nonNull(dmsResponseTempData.getData())) {
                DmsResponse<Void> tempResult = domainMaintainLeadFeign.insertTagConfigTemp(changeTagInfoDTO);
                if (tempResult.isFail()) {
                    throw new ServiceBizException("添加临时表字段转译失败！！！");
                }
            }
        }
        log.info("updateFieldInfo response:{}", JSONObject.toJSONString(result));
        if (result.isFail()) {
            throw new ServiceBizException("添加字段转译失败！！！");
        }
    }

    /**
     * 功能描述：获取字段转译列表信息
     *
     * @param showName    展示名称
     * @param currentPage 当前页
     * @param pageSize    每页条数
     * @return TagInfoVO 字段转译列表信息
     */
    @Override
    public Page<TagInfoVo> queryFieldTranslation(String showName, Integer currentPage, Integer pageSize, String tagType) {
        DmsResponse<Page<TagInfoVo>> result = domainMaintainLeadFeign.queryFieldTranslation(showName, currentPage, pageSize, tagType);
        log.info("queryTagInfo response:{}", JSONObject.toJSONString(result));
        if (result.isFail()) {
            throw new ServiceBizException("列表信息查询失败:{}", result.getReturnMessage());
        }
        result.getData().getRecords().forEach(i -> {
            if (StringUtils.isNotEmpty(i.getValueRule())) {
                //获取值规则
                TagValueRuleDto tagValueRuleDto = new TagValueRuleDto();
                tagValueRuleDto = JSONObject.parseObject(i.getValueRule(), TagValueRuleDto.class);
                if (ObjectUtils.isNotEmpty(tagValueRuleDto.getShowRule()) && ObjectUtils.isNotEmpty(tagValueRuleDto.getShowRule().getConvert())) {
                    List<String> tagCodes = new ArrayList<>();
                    List<String> operator = new ArrayList<>();
                    List<String> oddsRatio = new ArrayList<>();
                    //获取字段集合
                    if (CollectionUtil.isNotEmpty(tagValueRuleDto.getShowRule().getConvert().getOr())) {
                        tagCodes = tagValueRuleDto.getShowRule().getConvert().getOr().stream().map(TagValueRuleDto.andOrDto::getTagId).collect(Collectors.toList());
                        operator = tagValueRuleDto.getShowRule().getConvert().getOr().stream().map(TagValueRuleDto.andOrDto::getOperator).collect(Collectors.toList());
                        oddsRatio = tagValueRuleDto.getShowRule().getConvert().getOr().stream().map(TagValueRuleDto.andOrDto::getValue).collect(Collectors.toList());
                    } else if (CollectionUtil.isNotEmpty(tagValueRuleDto.getShowRule().getConvert().getAnd())) {
                        tagCodes = tagValueRuleDto.getShowRule().getConvert().getAnd().stream().map(TagValueRuleDto.andOrDto::getTagId).collect(Collectors.toList());
                        operator = tagValueRuleDto.getShowRule().getConvert().getAnd().stream().map(TagValueRuleDto.andOrDto::getOperator).collect(Collectors.toList());
                        oddsRatio = tagValueRuleDto.getShowRule().getConvert().getAnd().stream().map(TagValueRuleDto.andOrDto::getValue).collect(Collectors.toList());
                    }
                    i.setOperator(operator);
                    i.setOddsRatio(oddsRatio);
                    DmsResponse<List<TagInfoVo>> infoVOList = domainMaintainLeadFeign.queryTagInfoByTagCodes(tagCodes);
                    if (infoVOList.isSuccess()) {
                        i.setTranslationData(infoVOList.getData());
                    }
                }
            }
        });
        return result.getData();
    }

    /**
     * 功能描述：删除字段转译
     *
     * @param fieldTranslationDto 字段更新修改Dto
     */
    @Override
    public void deleteFieldInfo(FieldTranslationDto fieldTranslationDto) {
        DmsResponse<TagInfoVo> response = domainMaintainLeadFeign.delTagInfo(fieldTranslationDto.getTagId());
        if (response.isFail()) {
            throw new ServiceBizException("删除标签信息失败:{}", response.getReturnMessage());
        }
    }

    private List<TagInfoVo> queryCompleteTagInfoByFirstCode() {
        log.info("queryCompleteTagInfoByFirstCode start...");
        DmsResponse<List<TagInfoVo>> tagInfoVOList = domainMaintainLeadFeign.queryCompleteTagInfoByFirstCode();
        log.info("queryCompleteTagInfoByFirstCode end...");
        return null == tagInfoVOList || tagInfoVOList.isFail() ? new ArrayList<>() : tagInfoVOList.getData();
    }

    private CustomerTagsDto queryTagListByVinAndMobile(String vin, String mobile) {
        if (StringUtils.isBlank(vin) && StringUtils.isBlank(mobile)) {
            return new CustomerTagsDto();
        }
        log.info("queryTagListByVinAndMobile start...");
        CustomerTagsDto customerTagsDto = cdpTagInfoService.queryTagListByVinAndMobile(vin, mobile);
        log.info("queryTagListByVinAndMobile end...");
        return customerTagsDto;
    }

    private List<Integer> queryCodeDataType() {
        log.info("queryCodeDataType start...");
        MidResponse<List<PlateSortDictDto>> response = midEndDictCenterFeign.queryCodeDataType(CommonConstant.FIRST_BLOCK_CODE);
        if (null == response) {
            return new ArrayList<>();
        }
        if (null == response.getData()) {
            return new ArrayList<>();
        }
        log.info("queryCodeDataType end...");
        return response.getData().stream().map(PlateSortDictDto::getCodeId).collect(Collectors.toList());
    }

    private void handleValueByValueRule(List<TagInfoVo> tagInfoVOS, List<CdpTagInfoVo> cdpTagInfoVOS, List<CdpAttributeListDto> attList, String vin, String mobile, Integer memberId) {
        log.info("handleValueByValueRule start...");
        String selectType = "1";
        //获取组合规则、转义规则中的tagId
        List<String> tagCodes = new ArrayList<>();
        tagInfoVOS.forEach(i -> {
            if (i.getShowFirstBlock()!=null) {
                tagCodes.add(i.getTagId());
            }
        });
        //获取Newbie定制化标签
        CustomizedLabelDto customizedLabelDto = new CustomizedLabelDto();
        customizedLabelDto.setTagCodes(tagCodes);
        customizedLabelDto.setVin(vin);
        if (memberId != null) {
            customizedLabelDto.setMemberId(memberId);
        }
        if (StringUtils.isNotEmpty(mobile)) {
            customizedLabelDto.setPhone(mobile);
        }
        customizedLabelDto.setSelectType(selectType);
        log.info("queryCustomizedLabel queryCustomizedLabel request:{}", JSONObject.toJSONString(customizedLabelDto));
        List<NbTagInfoVo> NewBieTagInfoVOList = customizedLabelService.queryCustomizedLabel(customizedLabelDto);
        log.info("queryCustomizedLabel queryCustomizedLabel end...");
        Map<String, NbTagInfoVo> tagInfoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(NewBieTagInfoVOList)) {
            tagInfoMap = NewBieTagInfoVOList.stream().collect(Collectors.toMap(NbTagInfoVo::getTagId, Function.identity()));
        }
        Map<String, CdpTagInfoVo> cdpTagInfoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(cdpTagInfoVOS)) {
            cdpTagInfoMap = cdpTagInfoVOS.stream().collect(Collectors.toMap(CdpTagInfoVo::getTagId, Function.identity()));
        }
        //添加值
        for (TagInfoVo tagInfoVO : tagInfoVOS) {
            if (StringUtils.isNotEmpty(tagInfoVO.getValueRule())) {
                TagValueRuleDto tagValueRuleDto= JSONObject.parseObject(tagInfoVO.getValueRule(), TagValueRuleDto.class);
                    tagInfoVO.setGetType(tagValueRuleDto.getGetType());
                    tagInfoVO.setShowType(tagValueRuleDto.getShowType());
                    if (ObjectUtils.isNotEmpty(tagInfoMap.get(tagInfoVO.getTagId()))) {
                        tagInfoVO.setTagValue(tagInfoMap.get(tagInfoVO.getTagId()).getTagValue());
                    } else if (ObjectUtils.isNotEmpty(cdpTagInfoMap.get(tagInfoVO.getTagId()))) {
                        tagInfoVO.setTagValue(cdpTagInfoMap.get(tagInfoVO.getTagId()).getTagValue());
                    }
                }
        }
        log.info("handleValueByValueRule end...");
    }


    public List<CompleteTagInfoVo> handleDetail(List<TagInfoVo> tagInfoVOList, List<Integer> codeList) {
        log.info("handleDetail handleDetail start...");
        // 排序
        tagInfoVOList = tagInfoVOList.stream().sorted(Comparator.comparing(TagInfoVo::getShowSort)).collect(Collectors.toList());;
        final Map<String, TagInfoVo> tagDataMap = tagInfoVOList.stream().collect(Collectors.toMap(TagInfoVo::getTagId, Function.identity()));
        for (TagInfoVo item : tagInfoVOList) {
            TagValueRuleDto tagValueRuleDto= JSONObject.parseObject(item.getValueRule(), TagValueRuleDto.class);
            if (ObjectUtils.isNotEmpty(tagValueRuleDto)) {
                handleValueRule(tagValueRuleDto, item, tagDataMap);
            } else {
                item.setShowType(CommonConstant.TEXT_SHOW_TYPE);
            }
            //处理详情
            if (item.getIsGetDetail().equals(CommonConstant.DICT_IS_YES) && item.getIsDetailTag().equals(CommonConstant.DICT_IS_NO)
                    && item.getDetailGetType() != null && item.getDetailGetType().equals(TagEnum.OTHER_TAG.getCode())) {
                item.setDetailTagInfoVOS(queryDetailData(item.getTagId(), tagInfoVOList));
            }
        }
        // 处理板块
        log.info("handleDetail assembleData start...");
        List<CompleteTagInfoVo> completeTagInfoVOS = assembleData(codeList, tagInfoVOList);
        log.info("handleDetail assembleData end...");
        return completeTagInfoVOS;
    }

    public List<CompleteTagInfoVo> assembleData(List<Integer> codeList, List<TagInfoVo> tagInfoVOList) {
        log.info("assembleData start...");
        Map<Integer, List<TagInfoVo>> tagInfoMap = tagInfoVOList.stream()
                .collect(Collectors.groupingBy(TagInfoVo::getIsTag));
        //标签数据
        List<TagInfoVo> tagInfoVOS = new ArrayList<>();
        if (tagInfoMap.containsKey(CommonConstant.DICT_IS_YES)) {
            tagInfoVOS = tagInfoMap.get(CommonConstant.DICT_IS_YES);
        }

        //字段数据
        List<TagInfoVo> infoVOList = new ArrayList<>();
        if (tagInfoMap.containsKey(CommonConstant.DICT_IS_NO)) {
            infoVOList = tagInfoMap.get(CommonConstant.DICT_IS_NO);
        }
        List<CompleteTagInfoVo> completeTagInfoVOS = new ArrayList<>();

        List<BlockConfigDto> blockConfig = getBlockConfig();
        Map<Integer, List<BlockConfigDto>> blockMap = blockConfig.stream().collect(Collectors.groupingBy(BlockConfigDto::getBlockType));

        List<BlockConfigDto> firstBlcok = blockMap.get(BLOCK_TYPE_ONE);

        if(CollectionUtils.isNotEmpty(firstBlcok)){
            for (BlockConfigDto blockConfigDto: firstBlcok) {
                CompleteTagInfoVo completeTagInfoVO = new CompleteTagInfoVo();
                //处理展现形式
                Integer code = blockConfigDto.getBlockId();
                completeTagInfoVO.setPresentationForm(CommonConstant.UP_AND_DOWN);
                completeTagInfoVO.setPlateId(code);
                completeTagInfoVO.setPlateName(FirstPlateEnum.getNameByCode(code));
                judgePlate(tagInfoVOS, infoVOList, blockConfigDto, completeTagInfoVO, blockMap);
                completeTagInfoVOS.add(completeTagInfoVO);
            }
        }
        log.info("assembleData end...");
        return completeTagInfoVOS;
    }

    private List<BlockConfigDto> getBlockConfig(){
        DmsResponse<List<BlockConfigDto>> blockConfig = domainMaintainLeadFeign.getBlockConfig();
        List<BlockConfigDto> data = blockConfig.getData();
        return Objects.isNull(data)? Lists.newArrayList() : data;
    }

    private  void judgePlate(List<TagInfoVo> tagInfoVOS, List<TagInfoVo> infoVOList, BlockConfigDto configDto, CompleteTagInfoVo completeTagInfoVO,Map<Integer, List<BlockConfigDto>> blockMap) {
        //第一层
        if (configDto.getBlockType() == 1) {
            List<TagInfoVo> taginfoList = tagInfoVOS.stream().filter(t -> isSameTag(t,configDto)).collect(Collectors.toList());
            completeTagInfoVO.setTagInfoVOS(taginfoList);
            List<TagInfoVo> infoList = infoVOList.stream().filter(t -> isSameTag(t,configDto)).collect(Collectors.toList());
            completeTagInfoVO.setInfoVOList(infoList);
            List<BlockConfigDto> configDtos = blockMap.get(configDto.getBlockType()+1);
            if(CollectionUtils.isNotEmpty(configDtos)){
                configDtos = configDtos.stream().filter(e -> e.getParentBlockId().equals(configDto.getBlockId())).collect(Collectors.toList());
                for(BlockConfigDto secondBlock : configDtos){
                    judgePlate(tagInfoVOS, infoVOList, secondBlock, completeTagInfoVO, blockMap);
                }
            }
        } else {

            String ownerCode = commonMethodService.getOwnerCode();
            if (ThirdPlateEnum.THIRD_PARTY_COUPON.getCode().equals(configDto.getBlockId()) && StringUtils.isNotEmpty(ownerCode) && !commonMethodService.checkWhitelist(ownerCode, 91111043, 0, "", "")){
                return;
            }

            CompleteTagInfoVo nextCompleteTagInfoVO = new CompleteTagInfoVo();
            List<TagInfoVo> secondTaginfoList = tagInfoVOS.stream().filter(t -> isSameTag(t,configDto)).collect(Collectors.toList());
            List<TagInfoVo> secondInfoList = infoVOList.stream().filter(t -> isSameTag(t,configDto)).collect(Collectors.toList());
            //处理展现形式
            nextCompleteTagInfoVO.setPresentationForm(CommonConstant.UP_AND_DOWN);
            nextCompleteTagInfoVO.setPlateId(configDto.getBlockId());
            nextCompleteTagInfoVO.setPlateName(configDto.getBlockName());
            nextCompleteTagInfoVO.setTagInfoVOS(secondTaginfoList);
            nextCompleteTagInfoVO.setInfoVOList(secondInfoList);
            List<CompleteTagInfoVo> completeTagInfoVOS = completeTagInfoVO.getCompleteTagInfoVOS();
            if(CollectionUtils.isEmpty(completeTagInfoVOS)){
                completeTagInfoVOS = Lists.newArrayList();
            }
            completeTagInfoVOS.add(nextCompleteTagInfoVO);
            completeTagInfoVO.setCompleteTagInfoVOS(completeTagInfoVOS);
            List<BlockConfigDto> nextDtos = blockMap.get(configDto.getBlockType()+1);
            if(CollectionUtils.isNotEmpty(nextDtos)){
                nextDtos = nextDtos.stream().filter(e -> e.getParentBlockId().equals(configDto.getBlockId())).collect(Collectors.toList());
                for(BlockConfigDto next : nextDtos){
                    judgePlate(tagInfoVOS, infoVOList, next, nextCompleteTagInfoVO, blockMap);
                }
            }
        }
    }

    private boolean isSameTag(TagInfoVo tagInfoVo,BlockConfigDto configDto){
        switch (configDto.getBlockType()){
            case 1:
                return Objects.equals(tagInfoVo.getBlockType(),configDto.getBlockType()) && Objects.equals(configDto.getParentBlockId(),-1) && Objects.equals(tagInfoVo.getShowFirstBlock(),configDto.getBlockId());
            case 2:
                return Objects.equals(tagInfoVo.getBlockType(),configDto.getBlockType()) && Objects.equals(configDto.getParentBlockId(),tagInfoVo.getShowFirstBlock()) && Objects.equals(tagInfoVo.getShowSecondBlock(),configDto.getBlockId());
            case 3:
                return Objects.equals(tagInfoVo.getBlockType(),configDto.getBlockType()) && Objects.equals(configDto.getParentBlockId(),tagInfoVo.getShowSecondBlock()) && Objects.equals(tagInfoVo.getShowThirdBlock(),configDto.getBlockId());
            case 4:
                return Objects.equals(tagInfoVo.getBlockType(),configDto.getBlockType()) && Objects.equals(configDto.getParentBlockId(),tagInfoVo.getShowThirdBlock()) && Objects.equals(tagInfoVo.getShowFourthBlock(),configDto.getBlockId());
            case 5:
                return Objects.equals(tagInfoVo.getBlockType(),configDto.getBlockType()) && Objects.equals(configDto.getParentBlockId(),tagInfoVo.getShowFourthBlock()) && Objects.equals(tagInfoVo.getShowFifthBlock(),configDto.getBlockId());
            case 6:
                return Objects.equals(tagInfoVo.getBlockType(),configDto.getBlockType()) && Objects.equals(configDto.getParentBlockId(),tagInfoVo.getShowFifthBlock()) && Objects.equals(tagInfoVo.getShowSixthBlock(),configDto.getBlockId());
            default:
                return false;
        }
    }


    public void handleValueRule(TagValueRuleDto tagValueRuleDto, TagInfoVo item, Map<String, TagInfoVo> tagDataMap) {
        if (ObjectUtils.isNotEmpty(tagValueRuleDto.getShowRule())) {
            //处理范围规则
            if (ObjectUtils.isNotEmpty(tagValueRuleDto.getShowRule().getRange())) {
                List<TagValueRuleDto.RangeDto> rangeDtos = tagValueRuleDto.getShowRule().getRange();
                for (TagValueRuleDto.RangeDto rangeDto : rangeDtos) {
                    if (StringUtils.isNotEmpty(item.getTagValue()) && item.getTagValue().matches(pattern)
                            && Integer.valueOf(item.getTagValue()) >= rangeDto.getMin()
                            && Integer.valueOf(item.getTagValue()) < rangeDto.getMax()) {
                        item.setTagValue(rangeDto.getValue());
                    }
                }
            }
            //处理脱敏规则
            if (ObjectUtils.isNotEmpty(tagValueRuleDto.getShowRule().getSensitive())) {
                TagValueRuleDto.SensitiveDto sensitiveDto = tagValueRuleDto.getShowRule().getSensitive();
                if (StringUtils.isNotEmpty(item.getTagValue())) {
                    item.setTagValue(StringUtil.replaceSubstring(item.getTagValue(), Integer.valueOf(sensitiveDto.getStart()), Integer.valueOf(sensitiveDto.getLength()), sensitiveDto.getSymbol()));
                }
            }
            //处理组合规则
            if (CollectionUtil.isNotEmpty(tagValueRuleDto.getShowRule().getCompose())) {
                List<ComposeDto> composeDtos = tagValueRuleDto.getShowRule().getCompose();
                String tagValue = "";
                for (ComposeDto compose : composeDtos) {
                    TagInfoVo tagInfoVO = tagDataMap.get(compose.getTagId());
                    if (ObjectUtils.isNotEmpty(tagInfoVO) && StringUtils.isNotEmpty(tagInfoVO.getTagValue())) {
                        tagValue = tagValue + tagInfoVO.getTagValue() + compose.getSymbol();
                    }
                }
                item.setTagValue(tagValue);
            }
            //处理转义规则
            if (ObjectUtils.isNotEmpty(tagValueRuleDto.getShowRule().getConvert())) {
                TagValueRuleDto.ConvertDto convertDtos = tagValueRuleDto.getShowRule().getConvert();
                if (CollectionUtil.isNotEmpty(convertDtos.getOr())) {
                    List<Boolean> list = judgeConvert(convertDtos.getOr(), tagDataMap);
                    if (list.contains(true)){
                        item.setShowLevel(CommonConstant.SHOW_LEVEL_DONT_SHOW);
                        item.setRuleFlag(CommonConstant.DICT_IS_NO);
                    }
                }
                if (CollectionUtil.isNotEmpty(convertDtos.getAnd())) {
                    List<Boolean> list = judgeConvert(convertDtos.getAnd(), tagDataMap);
                    if (list.contains(false)) {
                        item.setShowLevel(CommonConstant.SHOW_LEVEL_DONT_SHOW);
                        item.setRuleFlag(CommonConstant.DICT_IS_NO);
                    }
                }
            }
        }
    }


    private  List<Boolean> judgeConvert(List<TagValueRuleDto.andOrDto> andOrDto, Map<String, TagInfoVo> tagDataMap) {
        List<Boolean> list = new ArrayList<>();
        for (TagValueRuleDto.andOrDto convert : andOrDto) {
            TagInfoVo tagInfoVO = tagDataMap.get(convert.getTagId());
            boolean result = false;
            if (ObjectUtils.isNotEmpty(tagInfoVO) && StringUtils.isNotEmpty(tagInfoVO.getTagValue()) && StringUtils.isNotEmpty(convert.getValue())) {
                if (convert.getOperator().equals("EQ") && convert.getValue().equals(tagInfoVO.getTagValue())) {
                    result = true;
                } else if (convert.getOperator().equals("NE") && !convert.getValue().equals(tagInfoVO.getTagValue())) {
                    result = true;
                } else if (convert.getOperator().equals("GT") && convert.getValue().matches(pattern) && tagInfoVO.getTagValue().matches(pattern) && Double.valueOf(tagInfoVO.getTagValue()) > Double.valueOf(convert.getValue())) {
                    result = true;
                } else if (convert.getOperator().equals("LT") && convert.getValue().matches(pattern) && tagInfoVO.getTagValue().matches(pattern) && Double.valueOf(tagInfoVO.getTagValue()) < Double.valueOf(convert.getValue())) {
                    result = true;
                } else if (convert.getOperator().equals("GE") && convert.getValue().matches(pattern) && tagInfoVO.getTagValue().matches(pattern) && Double.valueOf(tagInfoVO.getTagValue()) >= Double.valueOf(convert.getValue())) {
                    result = true;
                } else if (convert.getOperator().equals("LE") && convert.getValue().matches(pattern) && tagInfoVO.getTagValue().matches(pattern) && Double.valueOf(tagInfoVO.getTagValue()) <= Double.valueOf(convert.getValue())) {
                    result = true;
                }
            }
            list.add(result);
        }
        return list;
    }

    public List<TagInfoVo> queryDetailData(String tagId, List<TagInfoVo> tagInfoVOList) {
        List<TagInfoVo> detailTagInfo = new ArrayList<>();
        for (TagInfoVo item : tagInfoVOList) {
            if (StringUtils.isNotEmpty(item.getDetailTagPid()) && item.getDetailTagPid().equals(tagId)) {
                if (item.getIsDetailTag().equals(CommonConstant.DICT_IS_YES) && item.getIsGetDetail().equals(CommonConstant.DICT_IS_NO) && item.getDetailGetType() != null && item.getDetailGetType().equals(TagEnum.OTHER_TAG)) {
                    detailTagInfo.add(item);
                } else {
                    item.setDetailTagInfoVOS(queryDetailData(item.getTagId(), tagInfoVOList));
                    detailTagInfo.add(item);
                }
            }
        }
        return detailTagInfo;
    }


    private void handleLocalTagInfo(List<TagListDto> cdpTagListDtoList) {
        List<TagMetadataLogDto> tagMetadataLogDtos = new ArrayList<>();
        List<String> delTagInfo = new ArrayList<>();
        List<TagListDto> updateTagInfo = new ArrayList<>();
        List<TagListDto> insertTagInfo = new ArrayList<>();
        //查询全量标签字段
        DmsResponse<List<TagInfoVo>> tagInfoVOs = domainMaintainLeadFeign.queryCompleteData();
        if (tagInfoVOs.isFail()){
            throw new ServiceBizException("查询本地标签字段失败"+tagInfoVOs.getErrMsg());
        }
        Map<String,TagInfoVo> tagInfoVoMap = tagInfoVOs.getData().stream().collect(Collectors.toMap(TagInfoVo::getTagId, Function.identity()));

        cdpTagListDtoList.forEach(i -> {
            TagInfoVo  tagInfoVo  = tagInfoVoMap.get(i.getTagId());
            if (ObjectUtils.isNotEmpty(tagInfoVo)) {
                if (i.getRemoved().equals("1")) {
                    delTagInfo.add(i.getTagId());
                } else {
                    updateTagInfo.add(i);
                }
            } else {
                if (!i.getRemoved().equals("1")) {
                    insertTagInfo.add(i);
                }
            }
        });
        //批量处理本地标签数据
        tagMetadataLogDtos = extractedBatchHandle(delTagInfo, updateTagInfo, insertTagInfo,tagInfoVoMap);
        domainMaintainLeadFeign.insertTagMetadataLogDto(tagMetadataLogDtos);
    }
    /**
     * 批量处理标签、字段数据、组合同步日志
     */
    private List<TagMetadataLogDto> extractedBatchHandle(List<String> delTagInfo, List<TagListDto> updateTagInfo, List<TagListDto> insertTagInfo, Map<String,TagInfoVo> tagInfoVos) {
        List<TagMetadataLogDto> tagMetadataLogDtos = new ArrayList<>();
        String insertRemark = "insert";
        String updateRemark = "update";
        String delRemark = "delete";
        DmsResponse<Void> insertResult = domainMaintainLeadFeign.insertTagInfo(insertTagInfo);
        if (insertResult.isFail()){
            throw new ServiceBizException("插入标签、字段配置失败！！"+insertResult.getErrMsg());
        }
        DmsResponse<Void> updateResult = domainMaintainLeadFeign.updateTagInfoByCdp(updateTagInfo);
        if (updateResult.isFail()){
            throw new ServiceBizException("更新标签、字段配置失败！！"+updateResult.getErrMsg());
        }
        DmsResponse<Void> delResult = domainMaintainLeadFeign.batchDelTagInfo(delTagInfo);
        if (delResult.isFail()){
            throw new ServiceBizException("删除标签、字段配置失败！！"+delResult.getErrMsg());
        }
        insertTagInfo.forEach(i->{
            TagMetadataLogDto tagMetadataLogDto = new TagMetadataLogDto();
            tagMetadataLogDto.setTagId(i.getTagId());
            tagMetadataLogDto.setAfterLog(JSONObject.toJSONString(i));
            tagMetadataLogDto.setRemarks(insertRemark);
            tagMetadataLogDtos.add(tagMetadataLogDto);
        });
        updateTagInfo.forEach(i->{
            TagMetadataLogDto tagMetadataLogDto = new TagMetadataLogDto();
            tagMetadataLogDto.setTagId(i.getTagId());
            tagMetadataLogDto.setAfterLog(JSONObject.toJSONString(i));
            tagMetadataLogDto.setBeforeLog(JSONObject.toJSONString(tagInfoVos.get(i.getTagId())));
            tagMetadataLogDto.setRemarks(updateRemark);
            tagMetadataLogDtos.add(tagMetadataLogDto);
        });
        delTagInfo.forEach(i->{
            TagInfoVo tagInfoVo = tagInfoVos.get(i);
            TagMetadataLogDto tagMetadataLogDto = new TagMetadataLogDto();
            tagMetadataLogDto.setTagId(i);
            tagMetadataLogDto.setBeforeLog(JSONObject.toJSONString(tagInfoVo));
            tagInfoVo.setIsDeleted(1);
            tagMetadataLogDto.setAfterLog(JSONObject.toJSONString(tagInfoVo));
            tagMetadataLogDto.setRemarks(delRemark);
            tagMetadataLogDtos.add(tagMetadataLogDto);
        });
        return tagMetadataLogDtos;
    }


}
