package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;



@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("新增超时未跟进状态条件参数")
public class AccidentCluesFollowStatusChangeTaskDto implements Serializable{
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "查询范围,例100分钟(天)内",required = true,name = "rangeTime",example = "100",dataType = "Integer")
    private Integer rangeTime;
    @ApiModelProperty(value = "查询条件,超过30分钟(天)的数据",required = true,name = "conditionTime",example = "30",dataType = "Integer")
    private Integer conditionTime;
}
