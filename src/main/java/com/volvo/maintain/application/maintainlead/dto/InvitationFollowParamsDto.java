/*
 * Copyright (c) Volvo CAR Distribution (SHANGHAI) Co., Ltd. 2023. All rights reserved.
 */

package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述：线索查询参数
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@ApiModel(value = "InvitationFollowParamsDto", description = "线索查询参数")
public class InvitationFollowParamsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "邀约类型", name = "inviteType")
    private List<Integer> inviteType;

    @ApiModelProperty(value = "划跟进开始时间", name = "planFollowDateStart")
    private String planFollowDateStart;

    @ApiModelProperty(value = "划跟进结束时间", name = "planFollowDateEnd")
    private String planFollowDateEnd;

    @ApiModelProperty(value = "车牌号", name = "licensePlateNum")
    private String licensePlateNum;

    @ApiModelProperty(value = "车架号", name = "vin")
    private String vin;

    @ApiModelProperty(value = "实际跟进开始时间", name = "actualFollowDateStart")
    private String actualFollowDateStart;

    @ApiModelProperty(value = "实际跟进结束时间", name = "actualFollowDateEnd")
    private String actualFollowDateEnd;

    @ApiModelProperty(value = "名称", name = "name")
    private String name;

    @ApiModelProperty(value = "是否是预约客户", name = "isBook")
    private Integer isBook;

    @ApiModelProperty(value = "建议开始时间", name = "adviseInDateStart")
    private String adviseInDateStart;

    @ApiModelProperty(value = "建议结束时间", name = "adviseInDateEnd")
    private String adviseInDateEnd;

    @ApiModelProperty(value = "跟进状态", name = "followStatus")
    private List<Integer> followStatus;

    @ApiModelProperty(value = "级别id集合", name = "leaveIds")
    private List<Integer> leaveIds;

    @ApiModelProperty(value = "工单状态", name = "orderStatus")
    private Integer orderStatus;

    @ApiModelProperty(value = "工单状态集合参数", name = "orderStatusParam")
    private List<Integer> orderStatusParam;

    @ApiModelProperty(value = "顾问名称", name = "saName")
    private String saName;

    @ApiModelProperty(value = "顾问id", name = "saId")
    private String saId;

    @ApiModelProperty(value = "线索创建时间开始", name = "createdAtStart")
    private String createdAtStart;

    @ApiModelProperty(value = "线索创建时间结束", name = "createdAtEnd")
    private String createdAtEnd;

    @ApiModelProperty(value = "isself", name = "isself")
    private Integer isself;

    @ApiModelProperty(value = "当前页", name = "currentPage")
    private Long currentPage;

    @ApiModelProperty(value = "多少页", name = "pageSize")
    private Long pageSize;

    /**
     * 大区 查询条件
     */
    @ApiModelProperty(value = "大区", name = "largeAreaId")
    private String largeAreaId;

    /**
     * 小区 查询条件
     */
    @ApiModelProperty(value = "小区", name = "areaId")
    private String areaId;

    /**
     * 经销商代码
     */
    @ApiModelProperty(value = "经销商代码", name = "dealerCode")
    private String dealerCode;

    /**
     * 二次跟进月份
     */
    @ApiModelProperty(value = "二次跟进月份 ", name = "monthTwice")
    private String monthTwice;

    /**
     * 是否二次跟进
     */
    @ApiModelProperty(value = "线索类型:0 VOC线索,1普通线索 ", name = "线索类型")
    private Integer recordType;

    /**
     * 线索类型:0 ：非VOC类型,1 ：18个月未保养，2：非4S店保养流失,3:流失客户保养灯亮
     */
    @ApiModelProperty(value = "线索类型:0 ：非VOC类型,1 ：18个月未保养，2：非4S店保养流失,3:流失客户保养灯亮", name = "lossType")
    private Integer lossType;

    /**
     * 流失预警类型
     */
    @ApiModelProperty(value = "流失预警类型")
    private Integer lossWarningType;

    @ApiModelProperty(value = "卡劵code", name = "couponCode")
    private String couponCode;

    @ApiModelProperty(value = "卡劵名称", name = "couponName")
    private String couponName;

    /**
     * 线索类型 查询条件
     */
    @ApiModelProperty(value = "线索类型", name = "recordTypeParam", example = "[87891001,87891002,87891003]")
    private List<Integer> recordTypeParam;

    /**
     * 是否主要线索:1主要线索、0附属线索
     */
    @ApiModelProperty(value = "是否主要线索:1主要线索、0附属线索", name = "isMain")
    private Integer isMain;

    /**
     * 返厂意向等级
     */
    @ApiModelProperty(value = "返厂意向等级", name = "returnIntentionLevel")
    private Integer returnIntentionLevel;

    @ApiModelProperty(value = "是否bev",name = "bevFlag")
    private Integer bevFlag;

    @ApiModelProperty(value = "邀约名称",name = "inviteName")
    private List<String> inviteName;
}
