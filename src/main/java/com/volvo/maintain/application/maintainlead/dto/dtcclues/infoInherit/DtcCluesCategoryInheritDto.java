package com.volvo.maintain.application.maintainlead.dto.dtcclues.infoInherit;

import com.volvo.maintain.application.maintainlead.dto.dtcclues.DtcCluesCategoryDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("DTC线索类别")
public class DtcCluesCategoryInheritDto extends DtcCluesCategoryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "车型")
    private String vehicleModels;
}
