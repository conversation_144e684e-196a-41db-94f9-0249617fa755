package com.volvo.maintain.application.maintainlead.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.FullLeadQueryDto;
import com.volvo.maintain.application.maintainlead.dto.FullLeadsCallDto;
import com.volvo.maintain.application.maintainlead.dto.FullLeadsFollowDto;
import com.volvo.maintain.application.maintainlead.dto.clues.RenewalLeadStaticDto;
import com.volvo.maintain.application.maintainlead.vo.CallDetailVo;
import com.volvo.maintain.application.maintainlead.vo.FullLeadVo;
import com.volvo.maintain.application.maintainlead.vo.FullLeadsTagVo;

import java.util.List;
import java.util.Map;

/**
 * 功能描述：全量线索接口
 *
 * <AUTHOR>
 * @since 2023/12/19
 */
public interface FullLeadsService {

    /**
     * 功能描述：查询全量线索列表
     *
     * @param params 全量线索查询dto对象
     * @return IPage<FullLeadVo> 全量线索查询结果
     */
    Page<FullLeadVo> queryFullLeadsList(FullLeadQueryDto params);

    /**
     * 功能描述：查询全量线索列表(厂端)
     *
     * @param params 全量线索查询dto对象
     * @return IPage<FullLeadVo> 全量线索查询结果
     */
    Page<FullLeadVo> queryOemFullLeadsList(FullLeadQueryDto params);

    /**
     * 功能描述：查询全量线索列表
     *
     * @param params 全量线索查询dto对象
     */
    void exportList(FullLeadQueryDto params);

    /**
     * 功能描述：全量线索导出回调
     *
     * @param params 全量线索查询dto对象
     * @return List<FullLeadVo> 全量线索查询结果
     */
    List<FullLeadVo> exportCallback(FullLeadQueryDto params);

    /**
     * 全量线索tag
     *
     * @param id         线索id
     * @param inviteType 线索类型
     * @param vin        vin
     * @param dealerCode 经销商代码
     * @return FullLeadsTagVo
     */
    FullLeadsTagVo queryTagList(Long id, Integer inviteType, String vin, String dealerCode);

    /**
     * 功能描述：全量线索通话记录保存
     *
     * @param params 参数
     * @return List<FullLeadsCallDto> 工作号信息
     */
    List<FullLeadsCallDto> saveCallRecordList(List<FullLeadsCallDto> params);

    /**
     * 全量线索跟进
     *
     * @param followList 跟进列表
     * @return 跟进结果
     */
    Boolean saveFollowRecords(List<FullLeadsFollowDto> followList);

    /**
     * 邀约通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    List<CallDetailVo> inviteCallList(Long detailId);


    /**
     * 续保通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    List<CallDetailVo> insuranceCallList(Long detailId);

    /**
     * 故障灯通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    List<CallDetailVo> faultLightCallList(Long detailId);

    List<Integer> checkFollowAI(List<FullLeadsFollowDto> followList);

    void deleteSelfCreateInvitation(Map<String, Object> param);

    /**
     * 续保线索静态信息
     * @param leadsId
     * @return
     */
    void buildRenewalLeadStaticDto(List<Long> leadsId);
}
