package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2023/11/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("同步CDP标签信息")
public class SyncTagInfoDTo {
    // 标签id
    private String tagId;
    // 标签名称
    private String displayName;

    // 标签描述
    private String description;

    // 是否删除（0:未删除 1:已删除）
    private String removed;
}
