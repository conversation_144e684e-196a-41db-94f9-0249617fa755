package com.volvo.maintain.application.maintainlead.vo.healthcheck;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("检查单详情")
public class VehicleHealthRecordInfoVo implements Serializable {

	@ApiModelProperty(value = "检查单信息")
	private VehicleHealthInfoVo vehicleHealthInfo;
	@ApiModelProperty(value = "检查单项目list")
	private Map<Integer, List<VehicleHealthItemVo>> vehicleHealthItmeLists;
	@ApiModelProperty(value = "客户备注")
	private String remark;
	@ApiModelProperty(name = "里程")
	private Double inMileage;
	@ApiModelProperty(name = "服务顾问")
	private String serviceAdvisor;
}
