package com.volvo.maintain.application.maintainlead.dto.icup;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IcupMileageDto {

    //是否Icup车型  是 10041001 否 10041002
    private int isIcup;

    //icup里程
    private int icupMileage;

    //更新时间
    private String updateDate;

    private String vin;

}
