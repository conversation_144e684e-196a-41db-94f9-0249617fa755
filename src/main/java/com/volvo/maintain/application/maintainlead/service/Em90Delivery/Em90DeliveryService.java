package com.volvo.maintain.application.maintainlead.service.Em90Delivery;

import com.volvo.maintain.application.maintainlead.dto.ReportReasonDto;

import java.util.List;

public interface Em90DeliveryService {

    /**
     * 查询em90工单交车弹框
     * @param ownerCode
     * @param roNo
     * @param vin
     * @return
     */
    boolean selectEm90DeliveryFrame(String ownerCode,String roNo,String vin);

    /**
     * 提交未提交报告原因
     */
    void submitReportReason(ReportReasonDto reportReasonDto);

    /**
     * 查询重点客户弹框
     */
    List<String> queryEmphasisClientRemind(String ownerCode, String vin, String license, String roNo);
}
