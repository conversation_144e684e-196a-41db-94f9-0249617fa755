package com.volvo.maintain.application.maintainlead.vo.healthcheck;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
    * 车辆健康检查项目表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleHealthItemVo implements Serializable {

    private static final long serialVersionUID=1L;


    /**
    * 主键id
    */
    private Integer id;

    /**
    * 车辆健康检查编号
    */
    private String healthNo;

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 经销商code
     */
    private String ownerCode;

    /**
    * 明细名称
    */
    private String itemName;

    /**
    * 备注
    */
    private String remark;

    /**
     * 明细内容
     */
    private String itemContent;

    /**
     * 排序
     */
    private Integer seq;


    /**
     * app项目类型
     */
    private Integer appProjectType;


    /**
     * app排序
     */
    private Integer appSeq;

    /**
    * 明细状态
    */
    private Integer state;

    /**
     * 默认明细状态
     */
    private Integer defaultState;

    /**
    * 分值规则
    */
    private String itemRule;

    /**
    * 分值
    */
    private Integer score;


    /**
     * 终检明细状态（红 黄 绿）
     */
    private Integer endState;

    /**
     * 终检分值
     */
    private Integer endScore;

    /**
    * 检查标准
    */
    private String checkStandard;

    /**
     * 初始化状态选项
     */
    private Integer stateOption;

    /**
     * 检查类型名称
     */
    private String checkType;

    /**
    * 是否删除
    */
    private Integer isDeleted;

    /**
    * 创建人
    */
    private String createdBy;

    /**
    * 创建时间
    */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
    * 修改人
    */
    private String updatedBy;

    /**
    * 修改时间
    */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 是否需要输入框（0不需要 1需要）
     */
    private String isImport;

    /**
     * 输入框值
     */
    private String importVal;

    /**
     * 红灯提示
     */
    private String redRemark;

    /**
     * 黄灯提示
     */
    private String yellowRemark;

    /**
     * 终检备注
     */
    private String endRemark;


    /**
     * 报价状态
     */
    private String pricesheetState;

    /**
     * 报价备注
     */
    private String pricesheetRemark;

    /**
     * 报价阶段名称
     */
    @ApiModelProperty(name="报价阶段名称")
    @TableField(exist = false)
    private String pricesheetStage;

    /**
     * 是否无需零件
     */
    private Integer isNotPart;


    /**
     * 是否无需工时
     */
    private Integer isNotLabour;


    /**
     * 检查项code
     */
    private String itemCode;

    /**
     * 检查项FG
     */
    private String itemFg;

    /**
     * 是否已在工单中
     */
    @ApiModelProperty(name="是否已在工单中")
    @TableField(exist = false)
    private String pricesheetOrede;
}