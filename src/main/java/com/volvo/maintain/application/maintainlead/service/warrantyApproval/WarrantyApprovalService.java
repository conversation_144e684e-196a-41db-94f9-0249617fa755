package com.volvo.maintain.application.maintainlead.service.warrantyApproval;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.claimapply.ClaimApplyUseDTO;
import com.volvo.maintain.application.maintainlead.dto.reminder.WarrantyMaintenanceReminderDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.*;

import java.util.List;

/**
 * 延保审批接口
 */
public interface WarrantyApprovalService {

    /**
     * 延保理赔申请列表查询
     */
    Page<ClaimApplyPageRespDTO> approvalList(ClaimApplyPageReqDTO claimApplyPageReqDTO);

    /**
     * 延保理赔申请列表导出
     */
    void exportList(ClaimApplyPageReqDTO reqDTO);

    /**
     * 延保理赔申请审批
     */
    void approve(ClaimApplyApproveReqDTO reqDTO);

    /**
     * 审批id查询审批记录，易宝来源
     */
    List<ClaimApplyApproveRecordRespDTO> approvalRecordList(Long id);

    /**
     * 获取申请信息
     */
    ClaimApplyUseDTO getApprovalDetail(String caseNo);
    /**
     * 修改授权份额金额
     */
    void modifyApprovalAmount(ClaimApplyUseDTO dto);

    /**
     * 延保回款查询列表
     * @param dto
     * @return
     */
    Page<warrantyReturnPageDTO> returnList(warrantyReturnReqDTO dto);

    /**
     * 延保回款查询明细列表
     * @param returnId
     * @return
     */
    List<warrantyReturnDetailPageDTO> detailList(Long returnId);

    /**
     * 延保回款列表导出
     * @param dto
     * @return
     */
    List<warrantyReturnPageDTO> reportList(warrantyReturnReqDTO dto);

    /**
     * 延保回款查询明细列表导出
     * @param returnIds
     * @return
     */
    List<warrantyReturnDetailPageDTO> reportDetailList(List<Long> returnIds);
    /**
     *查询延保消息提醒
     */
    WarrantyMaintenanceReminderDTO getReminder(String vin, boolean flag, String roNo);
}
