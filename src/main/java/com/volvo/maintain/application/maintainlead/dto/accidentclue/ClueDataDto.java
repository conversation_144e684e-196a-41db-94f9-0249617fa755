package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import com.alibaba.fastjson.JSONArray;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ClueDataDto {
    
    /**
     * 故障灯信息
     */
    private WarningInfo warningInfo;

    /**
     * 客户联系信息集合
     */
    private List<ContactInfo> contactInfo;

    /**
     * 保养灯信息
     */
    private MaintenanceLightInfo maintenanceLightInfo;

    /**
     * 经销商信息集合
     */
    private List<DealerInfo> dealerInfo;

    /**
     * 车辆信息
     */
    private CarInfo carInfo;

    /**
     * 地址信息
     */
    private AddressInfo addressInfo;

    /**
     * 零附件信息
     */
    private CallInfo callInfo;

    /**
     * 零附件信息
     */
    private ZeroAttInfo zeroAttInfo;

    /**
     * 事故线索信息
     */
    private AccidentInfo accidentInfo;

    /**
     * 续保线索信息
     */
    private RenewalInsuranceInfo renewalInsuranceInfo;

    /**
     * 保单信息
     */
    private InsuranceInfo insuranceInfo;
    /**
     * 异常原因:10301-非沃尔沃车;10302-无经销商
     */
    private List<String> dataStatusRemarkInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class WarningInfo {
        /**
         * 故障id
         */
        private String warningId;
        /**
         * 故障名
         */
        private String warningName;
        /**
         * 故障描述（英文）
         */
        private String warningEN;
        /**
         * 故障描述（中文）
         */
        private String warningCN;
        /**
         * 故障等级
         */
        private String warningPriority;

        /**
         * 报警分类
         */
        private String warningCategoryCN;
        /**
         * 故障发生时间
         */
        private String warningTime;
        /**
         * 故障发生省份（中文）
         */
        private String warningProvinceCN;
        /**
         * 故障发生省份（province_id）
         */
        private String warningProvinceId;
        /**
         * 故障发生城市（中文）
         */
        private String warningCityCN;
        /**
         * 故障发生城市（city_id）
         */
        private String warningCityId;

        /**
         * 线索来源：非必须字段。
         */
        private String sourceType;

        /**
         * 是否推荐经销商
         */
        private boolean recommendDealerFlag;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ContactInfo {
        /**
         * 类型名称
         */
        private String typeName;

        /**
         * 类型编号
         */
        private String typeCode;

        /**
         * 客户姓名
         */
        private String customerName;

        /**
         * 客户手机号
         */
        private String customerMobile;

        /**
         * 性别
         */
        private String gender;

        /**
         * 优先级
         */
        private String priority;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MaintenanceLightInfo {
        /**
         * 线索来源：非必须字段。
         */
        private String sourceType;

        /**
         * 邀约类型：非必须字段。
         */
        private String invitationType;

        /**
         * 建议进厂时间：非必须字段。
         */
        private String adviceEnterFactory;

        /**
         * 流失类型：非必须字段。
         */
        private String lossType;

        /**
         * 返厂评级：非必须字段。
         */
        private String rtnIntentionRating;

        /**
         * 动力类型：非必须字段。
         */
        private String powerType;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DealerInfo {

        /**
         * 经销商
         */
        private String dealerCode;

        /**
         * 经销商类型
         */
        private String dealerType;

        /**
         * 经销商名称
         */
        private String dealerName;

        /**
         * 经销商类型，来自经纬度计算，原始数据，最近一次保养记录等
         */
        private String dealerCodeSource;

        /**
         * 是否推荐经销商
         */
        private Boolean recommendDealerFlag;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CarInfo {

        /**
         *  牌照。客户车辆的牌照号码。非必须字段。
         */
        private String licencePlate;

        /**
         *  日均里程。表示客户车辆的平均每日行驶里程。非必须字段。
         */
        private String dailyMile;

        /**
         * 当前里程。客户车辆的当前里程数。非必须字段。
         */
        private String currentMile;

        /**
         * 车型ID。表示客户车辆的具体车型的唯一标识符。非必须字段。
         */
        private String carModelId;

        /**
         * 车型名称。客户车辆的具体车型名称。非必须字段。
         */
        private String carModelName;

        /**
         * 车辆类型。描述客户车辆的类型。对于易保系统，这是一个必填项。非易保系统中可视为非必须字段。
         */
        private String thirdType;

        /**
         * 年款。表示客户车辆的制造年份。非必须字段。
         */
        private String modelYear;

        /**
         * 品牌
         */
        private String carBrand;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AddressInfo {

        /**
         * 省份id
         */
        private String provinceId;

        /**
         * 省份名称
         */
        private String provinceName;

        /**
         * 城市id
         */
        private String cityId;

        /**
         * 城市名称
         */
        private String cityName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CallInfo {

        /**
         * 联络完成时间
         */
        private String callEndTime;

        /**
         * 坐席姓名
         */
        private String firstCallSeat;

        /**
         * 拨打状态
         */
        private String firstCallStatus;

        /**
         * 外呼结果
         */
        private String CallResult;

        /**
         * 备注
         */
        private String comments;

        /**
         * 中台预约单ID
         */
        private String appointmentId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ZeroAttInfo {

        /**
         * 会员id
         */
        private String vipId;

        /**
         * 券码
         */
        private String ticketCode;

        /**
         * 卡券模板id
         */
        private String ticketTemplateId;

        /**
         * 卡券名称
         */
        private String ticketName;

        /**
         * 订单id
         */
        private String orderId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AccidentInfo {

        /**
         * 线索来源。描述了案件信息来源的渠道，非必须字段。
         */
        private String sourceType;

        /**
         * 报案时间。记录了报案具体时间，必须字段，格式：年-月-日 时:分:秒。
         */
        private String reportTime;

        /**
         * 事故类型。描述了事故分类信息，非必须字段。
         */
        private String accidentType;

        /**
         * 人员伤亡标志。标记是否有人员伤亡，true表示有，false表示无。非必须字段。
         */
        private Boolean casualtiesFlag;

        /**
         * 保险公司名称。涉案的保险公司，非必须字段。
         */
        private String insuranceCompany;

        private String insuranceCompanyCode;

        /**
         * 出险时间。记录发生事故的具体时间，必须字段，格式：年-月-日 时:分:秒。在易保系统中为必填项。
         */
        private String accidentDate;

        /**
         * 出险地点经度。描述事故地点的经度坐标，非必须字段。
         */
        private String damageAddrLng;

        /**
         * 出险地点纬度。描述事故地点的纬度坐标，非必须字段。
         */
        private String damageAddrLat;

        /**
         * 来源渠道。描述信息来源的渠道，如易保、400、NewBie，必须字段。
         */
        private String sourceChannel;

        /**
         * 报案号。唯一标识一个事故的编号，在易保系统中为必填项，非必须字段。
         */
        private String reportNo;

        /**
         * 送返修标识。0表示返修，1表示送修，在易保系统中为必填项，非必须字段。
         */
        private Integer clientType;

        /**
         * 出险原因。描述事故发生的原因，非必须字段。
         */
        private String accidentReason;

        /**
         * 是否报警。标记事故现场是否已通知警方，非必须字段。
         */
        private Boolean callPoliceFlag;

        /**
         * 出险地点描述。详细的事故发生地点，在易保系统中为必填项，非必须字段。
         */
        private String accidentAddress;

        /**
         * 伤亡详情。枚举类型，提供更详细的伤亡情况，非必须字段。
         */
        private String casualtiesDetail;

        /**
         * 是否重复。标志案件信息是否重复，默认为否，非必须字段。
         */
        private Integer repeatFlag;

        /**
         * 进线类型。描述来电的类型，必须字段。
         */
        private String callType;

        /**
         * 渠道标签。描述信息来源的具体渠道组合，如400&易保，必须字段。
         */
        private String channelTag;

        /**
         * 重复线索ID集合。存储同一事故的其他报告的ID集合，非必须字段。
         */
        private List<Long> repeatIdList;

        /**
         * 是否是虚拟号 true:是，false:否
         */
        private Boolean virtualPhoneFlag;
        /**
         * 设备id
         */
        private String deviceId;
        /**
         * G-sensor曲线
         */
        private JSONArray gsensor;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RenewalInsuranceInfo {

        /**
         * 续保客户类型
         */
        private String	renewalCustomerType;

        /**
         * 厂端保险到期日期
        */
        private String	factorySideExpireDate;

        /**
         * 店端保险到期日期
          */
        private String	storeSideExipreDate;

        /**
         * 提前出单日期（yyyy-MM-dd）
          */
        private String	advanceIssueDate;

        /**
         * 建议关怀日期（yyyy-MM-dd）
          */
        private String	recommendedCareDate;

        /**
         * 线索数据源
          */
        private String	sourceType;

        /**
         * 线索下发日期（yyyy-MM-dd）
        */
        private String	generateTime;

        /**
         * 销售日期（yyyy-MM-dd）
         */
        private String	saleTime;

        /**
         * 线索判断来源依据
          */
        private String	judgeSourceTypeCriteria;

        /**
         * 是否开启单店保护
          */
        private Boolean	singleStoreProtectionEnabled;

        /**
         * 提前出单周期
          */
        private Integer	advanceIssuePeriod;

        /**
         * 保单经销商
          */
        private String	insuranceDelaerCode;

        /**
         * 最近一次非停业工单经销商代码
          */
        private String	lrnwcDealerCode;

        /**
         * 销售门店代码
          */
        private String	saleStoreDealerCode;

        /**
         * 经销商细分类型
         **/
        private String	dealerCategory;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class InsuranceInfo {

        /**
         * 保单号
         */
        private String	insuranceNo;

        /**
         * 保险公司编号
          */
        private String	insuranceCompanyCode;

        /**
         * 保险公司名称
          */
        private String	insuranceCompanyName;

        /**
         * 保单创建日期
          */
        private String	insuranceCreateTime;

        /**
         * 保单来源
          */
        private String	insuranceSourceType;

        /**
         * 保单生效日期(yyyy-MM-dd)
          */
        private String	effectiveDate;

        /**
         * 保单失效日期(yyyy-MM-dd)
          */
        private String	expireDate;

        /**
         * 商业险产品类型
          */
        private String	commercialProductType;
    }
}
