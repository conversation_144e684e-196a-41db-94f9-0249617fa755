package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;

import com.volvo.maintain.application.maintainlead.dto.coupon.QueryCouponDetailInfo1Dto;
import com.volvo.maintain.application.maintainlead.dto.coupon.QueryCouponDetailInfoDto;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.coupon.QueryCouponDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CouponDetailService;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.MidEndCouponCenterFeign;
import com.volvo.maintain.interfaces.vo.coupon.CouponDetailInfoVO;
import com.volvo.maintain.interfaces.vo.coupon.CouponDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * 功能描述：标签管理实现
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Service
@Slf4j
public class CouponDetailServiceImpl implements CouponDetailService {

    @Autowired
    private MidEndCouponCenterFeign midEndCouponCenterFeign;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public List<CouponDetailVO> queryCouponInfo(QueryCouponDetailInfoDto queryCouponDetailInfoDTO) {

        QueryCouponDetailInfo1Dto queryCouponDetailInfo1DTO = new QueryCouponDetailInfo1Dto();
        queryCouponDetailInfo1DTO.setData(queryCouponDetailInfoDTO);
        ResponseDto<List<CouponDetailVO>> listResponseDto = midEndCouponCenterFeign.allEx(queryCouponDetailInfo1DTO);
        logger.info("NB定制化标签 卡券 listResponseDto：{}",listResponseDto);
        if (listResponseDto == null || !CommonConstant.SUCCESS_CODE.equals(listResponseDto.getReturnCode())) {
            return null;
        }

        if (CollectionUtils.isEmpty(listResponseDto.getData())) {
            return null;
        }
        processCoupon(listResponseDto.getData());
        return listResponseDto.getData();
    }

    @Override
    public List<CouponDetailVO> allCouponAfter(QueryCouponDto queryCouponDto) {
        logger.info("NB定制化标签 经销商卡券 queryCouponDto：{}",queryCouponDto);
        ResponseDto<List<CouponDetailVO>> listResponseDto = midEndCouponCenterFeign.allCouponAfter(queryCouponDto);
        logger.info("NB定制化标签 经销商卡券 listResponseDto：{}",listResponseDto);
        if (listResponseDto == null || !CommonConstant.SUCCESS_CODE.equals(listResponseDto.getReturnCode())) {
            return null;
        }

        if (CollectionUtils.isEmpty(listResponseDto.getData())) {
            return null;
        }
        processCoupon(listResponseDto.getData());
        return listResponseDto.getData();
    }

    public static void processCoupon(List<CouponDetailVO> couponDetailVOs) {
        //判断卡券类型
        couponDetailVOs.forEach(e -> {
            //卡券类型(31081004 储值卡 31081005 优惠券 31081006 兑换券
            switch (e.getCouponType()){
                case "31081004":
                    if(Objects.nonNull(e.getLeftValue())){
                        e.setCouponValueStr(BigDecimal.valueOf(e.getLeftValue()).divide(new BigDecimal("100"),2, RoundingMode.HALF_UP).toPlainString()+"元");
                    }
                    break;
                case "31081006":
                    if (Objects.isNull(e.getCouponValue())){
                        e.setCouponValue(CommonConstant.ZERO);
                    }
                    e.setCouponValueStr(BigDecimal.valueOf(e.getCouponValue()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP).toPlainString()+"元");
                    break;
                case "31081005":
                    //打折
                    if(Objects.equals(83311002,e.getOfferType()) && Objects.nonNull(e.getCouponDiscount())){
                        e.setCouponValueStr(BigDecimal.valueOf(e.getCouponDiscount()).divide(new BigDecimal(10),2,BigDecimal.ROUND_FLOOR).toPlainString()+"折");
                    }
                    //抵用
                    if(Objects.equals(83311001,e.getOfferType()) && Objects.nonNull(e.getCouponValue())){
                        e.setCouponValueStr(BigDecimal.valueOf(e.getCouponValue()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP).toPlainString()+"元");
                    }
                    break;
            }
        });
    }

}
