package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 邮件详情dto
 * 
 * <AUTHOR>
 */
@Data
public class EmailInfoDto {
	public static final int MAX_LENGTH = 1900;
	public static final String MARKER = "...";
	@ApiModelProperty(value = "发件人邮箱")
	private String from;

	@ApiModelProperty(value = "收件人OneId")
	private String[] toOneIds;

	@ApiModelProperty(value = "收件人OneId")
	private Long[] toEmployeeIds;

	@ApiModelProperty(value = "收件人OneId")
	private String[] to;

	@ApiModelProperty(value = "抄送")
	private String[] ccOneIds;

	@ApiModelProperty(value = "抄送")
	private Long[] ccEmployeeIds;

	@ApiModelProperty(value = "抄送")
	private String[] cc;

	@ApiModelProperty(value = "密送")
	private String[] bccOneIds;

	@ApiModelProperty(value = "密送")
	private Long[] bccEmployeeIds;

	@ApiModelProperty(value = "密送")
	private String[] bcc;

	@ApiModelProperty(value = "邮件主题")
	private String subject;

	@ApiModelProperty(value = "邮件内容")
	private String text;

	@ApiModelProperty(value = "模板ID")
	private String templateId;
}
