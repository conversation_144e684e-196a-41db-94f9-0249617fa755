package com.volvo.maintain.application.maintainlead.dto.coupon;

import com.yonyou.cyx.framework.bean.dto.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * UseRuleGroupItem
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-30
 */
@Data
@ApiModel(value = "UseRuleGroupItem", description = "UseRuleGroupItem")
public class UseRuleGroupItemDto extends BaseDTO {

    @ApiModelProperty(value = "分组")
    private int groupNo;
    @ApiModelProperty(value = "授权限")
    private String authorizedItem;
    @ApiModelProperty(value = "比较值")
    private String compareValue;
    @ApiModelProperty(value = "值")
    private String value;
    @ApiModelProperty(value = "数量下限")
    private int minNum;
    @ApiModelProperty(value = "金额下限")
    private double minamount;
    @ApiModelProperty(value = "工单类型")
    private List<String> orderType;

}