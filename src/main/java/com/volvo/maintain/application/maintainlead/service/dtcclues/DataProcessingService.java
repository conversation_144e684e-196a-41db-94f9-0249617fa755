package com.volvo.maintain.application.maintainlead.service.dtcclues;

import com.volvo.maintain.application.maintainlead.dto.dtcclues.DtcCluesCategoryDto;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.ImportDtcCluesCategoryDto;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.ImportResultInfoDto;
import com.yonyou.cyx.framework.service.excel.ExcelExportColumn;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface DataProcessingService {
    List<ImportDtcCluesCategoryDto> parseImportDtcCluesCategoryData(MultipartFile importFile);

    List<ImportResultInfoDto> validateAndConvertImportData(List<ImportDtcCluesCategoryDto> categoryDtoList, Integer category);

    void validateInsertParams(DtcCluesCategoryDto dtcCluesCategoryDto);

    List<ExcelExportColumn> buildExcelColumn(Integer category);

    String getLoginUserId();
}
