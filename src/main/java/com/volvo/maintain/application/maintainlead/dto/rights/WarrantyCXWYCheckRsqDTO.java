package com.volvo.maintain.application.maintainlead.dto.rights;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WarrantyCXWYCheckRsqDTO {
	/**
	 * vin
	 */
	@ApiModelProperty(value = "车架号", required = true)
	private String vin;

	/**
	 * 产品件号
	 */
	@ApiModelProperty(value = "产品件号", required = true)
	private List<String> productNoList;
}