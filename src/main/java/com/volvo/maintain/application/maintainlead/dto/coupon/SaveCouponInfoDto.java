package com.volvo.maintain.application.maintainlead.dto.coupon;

import com.volvo.maintain.application.maintainlead.dto.CollectRangeDto;
import com.yonyou.cyx.framework.bean.dto.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 卡券表
 *
 * <AUTHOR>
 * @since 2020-05-21
 */
@Data
@ApiModel(value = "SaveCouponInfoDTO ", description = "SaveCouponInfoDTO")
public class SaveCouponInfoDto extends BaseDTO {
    @ApiModelProperty(value = "卡券代码")
    private String couponCode;
    @ApiModelProperty(value = "卡券名称")
    private String couponName;
    @ApiModelProperty(value = "卡券类型(储值卡 优惠券 兑换券)")
    private Integer couponType;
    @ApiModelProperty(value = "优惠券类型(抵用 折扣)")
    private Integer offerType;
    @ApiModelProperty(value = "使用场景（线上上城 线下门店 通用）")
    private Integer useScenes;
    @ApiModelProperty(value = "卡券面额")
    private Double couponValue;
    @ApiModelProperty(value = "卡券折扣")
    private Integer couponDiscount;
    @ApiModelProperty(value = "卡券满减(满)")
    private Integer couponFull;
    @ApiModelProperty(value = "卡券描述")
    private String denomination;
    @ApiModelProperty(value = "期限类型（字典）有效期类型【82851001 期限 82851002 时间】")
    private Integer termType;
    @ApiModelProperty(value = "使用期限（单位月）自领取日起")
    private Integer term;
    @ApiModelProperty(value = "开始时间（yyyy-MM-dd HH:mm:ss）")
    private String startDate;
    @ApiModelProperty(value = "结束时间（yyyy-MM-dd HH:mm:ss）")
    private String endDate;
    @ApiModelProperty(value = "是否是商品")
    private Integer asGoods;
    @ApiModelProperty(value = "是否上架(未上架:31091001 已上架:31091002)")
    private Integer asList;
    @ApiModelProperty(value = "可领取总数")
    private Integer totalGet;
    @ApiModelProperty(value = "已领取数")
    private Integer tokenCount;
    @ApiModelProperty(value = "是否可无限领取(否:31101001 是:31101002)")
    private Integer existLimit;
    @ApiModelProperty(value = "每人最大可领取数")
    private Integer maxLimit;
    @ApiModelProperty(value = "用途描述")
    private String couponExplain;
    @ApiModelProperty(value = "卡券图片")
    private String couponImg;
    @ApiModelProperty(value = "卡券创建日期 yyyy-MM-dd HH:mm:ss")
    private String couponCreateDate;
    @ApiModelProperty(value = "卡券业务类型(MKT:31111001 售后券、:31111002 销售券:31111003)")
    private Integer serviceType;
    @ApiModelProperty(value = "是否需要激活（否:31121001 是:31121002）")
    private Integer activationRequired;
    @ApiModelProperty(value = "是否被占用(0:否 1:是)")
    private Integer isOccupied;
    @ApiModelProperty(value = "是否亲善(0:否 1:是)")
    private Integer kindness;
    @ApiModelProperty(value = "占用编号")
    private Integer occupyNumber;
    @ApiModelProperty(value = "占用名称")
    private String occupyName;
    @ApiModelProperty(value = "占用来源(33041001:活动管理 33041002:内容管理 33041003:抽奖小游戏 33041004:首页 33041005:会员任务 33041006:会员权益 33041007:会员成就)")
    private Integer occupySource;
    @ApiModelProperty(value = "卡券状态（82841001 未提交 ,82841002 提交待确认 ,82841003 已确认 ,82841004 已过期 ,82841005 已停用,）")
    private Integer publishState;
    @ApiModelProperty(value = "创建人姓名")
    private String creator;
    @ApiModelProperty(value = "是否被作废(0:否 1:是)")
    private Integer isVolid;

    @ApiModelProperty(value = "激活条件（字典）")
    private Integer activationConditions;

    @ApiModelProperty(value = "创建时间(开始)")
    private String createTimeBegin;
    @ApiModelProperty(value = "创建时间(结束)")
    private String createTimeEnd;
    @ApiModelProperty(value = "卡券编号/卡券名/创建人")
    private String couponCodeNameCreator;
    @ApiModelProperty(value = "被其他渠道绑定的卡券（1：是 0：否）")
    private Integer isBind;
    @ApiModelProperty(value = "创建人")
    private Long createBy;
    @ApiModelProperty(value = "领用对象描述")
    private String receivingObjectDesc;

    @ApiModelProperty(value = "领取范围")
    private CollectRangeDto collectRangeDto;
    @ApiModelProperty(value = "使用规则")
    private UseRuleDto useRule;
    /*@ApiModelProperty(value = "适用项目")
    private ApplayItems applayItems;*/
}