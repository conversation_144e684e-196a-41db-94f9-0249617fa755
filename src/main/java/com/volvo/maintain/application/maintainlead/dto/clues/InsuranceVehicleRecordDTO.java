package com.volvo.maintain.application.maintainlead.dto.clues;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 功能描述：续保线索表
 *
 * <AUTHOR>
 * @since 2024/01/03
 */
@Data
public class InsuranceVehicleRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 邀约ID
     */
    private Long id;

    /**
     * 邀约父类ID
     */
    private Long parentId;

    /**
     * 是否主要线索:1主要线索、0附属线索
     */
    private Integer isMain;

    /**
     * 邀约来源类型：1VCDC下发邀约、2经销商自建邀约、3VOC事故邀约
     */
    private Integer sourceType;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 车主姓名
     */
    private String name;

    /**
     * 车主电话
     */
    private String tel;

    /**
     * 邀约类型
     */
    private Integer inviteType;

    /**
     * 商业险（交强险）到期日期
     */
    private Date adviseInDate;

    /**
     * 新建议进厂日期
     */
    private Date newAdviseInDate;

    /**
     * 最新建议进厂日期
     */
    private Date newestAdviseInDate;

    /**
     * 计划跟进日期
     */
    private Date planFollowDate;

    /**
     * 实际跟进日期
     */
    private Date actualFollowDate;

    /**
     * 计划提醒日期
     */
    private Date planRemindDate;

    /**
     * 实际提醒日期
     */
    private Date actualRemindDate;

    /**
     * 首次跟进时间
     */
    private Date firstFollowDate;

    /**
     * 线索完成时间
     */
    private Date orderFinishDate;

    /**
     * 跟进服务顾问ID
     */
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    private String saName;

    /**
     * 上次跟进服务顾问ID
     */
    private String lastSaId;

    /**
     * 上次跟进服务顾问姓名
     */
    private String lastSaName;

    /**
     * 跟进状态
     */
    private Integer followStatus;

    /**
     * 是否预约单：1 是，0 否
     */
    private Integer isBook;

    /**
     * 预约单号
     */
    private String bookNo;

    /**
     * 线索完成状态
     */
    private Integer orderStatus;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 车主年龄
     */
    private String age;

    /**
     * 车主性别
     */
    private String sex;

    /**
     * 车型
     */
    private String model;

    /**
     * 客户唯一id
     */
    private Long oneId;

    /**
     * 邀约类型 查询条件
     */
    private List<Integer> inviteTypeParam;

    /**
     * 计划跟进日期开始 查询条件
     */
    private String planFollowDateStart;

    /**
     * 计划跟进日期结束 查询条件
     */
    private String planFollowDateEnd;

    /**
     * 实际跟进日期开始 查询条件
     */
    private String actualFollowDateStart;

    /**
     * 实际跟进日期结束 查询条件
     */
    private String actualFollowDateEnd;

    /**
     * 建议进厂日期开始 查询条件
     */
    private String adviseInDateStart;

    /**
     * 建议进厂日期结束 查询条件
     */
    private String adviseInDateEnd;

    /**
     * 邀约状态 查询条件
     */
    private List<Integer> followStatusParam;

    /**
     * 工单状态 线索完成状态
     */
    private List<Integer> orderStatusParam;

    /**
     * 离职用户 id 查询条件
     */
    private List<Integer> leaveIds;

    /**
     * 邀约创建日期开始 查询条件
     */
    private String createdAtStart;

    /**
     * 邀约创建日期结束 查询条件
     */
    private String createdAtEnd;

    /**
     * 是否逾期未跟进：1 是，0 否 查询条件
     */
    private Integer overdue;

    /**
     * 是否查询未分配 查询条件
     */
    private Integer isNoDistribute;

    /**
     * 是否查询待分配 查询条件
     */
    private Integer isWaitDistribute;

    /**
     * 易损件规则id
     */
    private Long partItemRuleId;

    /**
     * 易损件上次更换时间
     */
    private Date lastChangeDate;

    /**
     * 易损件code
     */
    private String itemCode;

    /**
     * 易损件名称
     */
    private String itemName;

    /**
     * 易损件类型
     */
    private Integer itemType;

    /**
     * 续保客户类型(81761001 新保客户  81761002 新转续  81761003  续转续   81761004 在修不在保)
     */
    private Integer insuranceType;

    /**
     * 保险投保单(tt_insurance_bill)表主键id'
     */
    private Long insuranceBillId;

    /**
     * 线索类型：1:交强险   2:商业险'
     */
    private Integer clueType;

    /**
     * 原线索记录新生的投保单号'
     */
    private String newInsureNo;

    private String insuranceName;

    /**
     * 最新失败原因
     */
    private Integer loseReason;

    /**
     * 最新跟进内容
     */
    private String content;
    /**
     * 线索下发类型 96171001 厂端下发 96171002 自店导入
     */
    private Integer clueIssuanceType;
    /**
     * 商业险（交强险）到期日期 修改次数
     */
    private Integer adviseInDateModifyCount;
    /**
     * 厂端保险到期时间
     */
    private Date factoryInsuranceExpiryDate;
}
