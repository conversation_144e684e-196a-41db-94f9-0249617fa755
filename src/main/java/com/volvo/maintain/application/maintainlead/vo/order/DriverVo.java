package com.volvo.maintain.application.maintainlead.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 商户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "DriverVO 对象", description = "DriverVO")
public class DriverVo  {

    @ApiModelProperty(value = "司机工号 ")
    private String driverNo;
    @ApiModelProperty(value = "服务城市 ")
    private String city;
    @ApiModelProperty(value = "司机姓名 ")
    private String name;
    @ApiModelProperty(value = "商家名称 ")
    private String businessName;
    @ApiModelProperty(value = "商家id ")
    private String businessId;

}