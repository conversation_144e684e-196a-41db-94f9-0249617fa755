package com.volvo.maintain.application.maintainlead.dto.insurance;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yonyou.cyx.framework.service.excel.ExcelColumnDefine;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "新续保线索Excel导入实体")
public class InsuranceRecordImportDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * VIN
	 */
	private String vin;
	/**
	 * 车主名称
	 */
	private String name;
	/**
	 * 车主电话
	 */
	private String tel;

	/**
	 * 店端续保到期日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date storeInsuranceExpiryDate;

	/**
	 * 续保客户类型(81761001 新保客户  81761002 新转续  81761003  续转续   81761004 在修不在保)
	 */
	private Integer insuranceType;

	/**
	 * 导入保险公司名称
	 */
	private String insuranceName;

	/**
	 * 跟进人员
	 */
	private String saId;
	/**
	 * 跟进服务顾问姓名
	 */
	private String saName;

	/**
	 * 上次跟进服务顾问ID
	 */
	private String lastSaId;

	/**
	 * 上次跟进服务顾问姓名
	 */
	private String lastSaName;
	/**
	 * 错误信息
	 */
	private String errorMsg;
	/**
	 * 是否导入数据有错误, 1有0否
	 *
	 */
	private Integer isError;
	/**
	 * 行数
	 */
	private Integer lineNumber;
	/**
	 * 经销商代码
	 */
	private String dealerCode;
	/**
	 * 数据来源：1:易保线索  2:投保线索  3:导入线索  4:迁移线索
	 */
	private Integer dataSources;
	/**
	 * 车牌号
	 */
	private String licensePlateNum;
	/**
	 * 车型
	 */
	private String model;

}
