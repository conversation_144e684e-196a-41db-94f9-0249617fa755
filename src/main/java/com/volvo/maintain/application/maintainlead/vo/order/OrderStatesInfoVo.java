package com.volvo.maintain.application.maintainlead.vo.order;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <p>
 * 商户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "OrderStatesInfoVO 对象", description = "OrderStatesInfoVO")
public class OrderStatesInfoVo {

    @ApiModelProperty(value = "就位位置 ")
    private JSONObject arrivePos;
    @ApiModelProperty(value = "接单位置 ")
    private JSONObject acceptPos;
    @ApiModelProperty(value = "开车位置 ")
    private JSONObject drivePos;
    @ApiModelProperty(value = "完成位置 ")
    private JSONObject finishPos;
    @ApiModelProperty(value = "当前位置 ")
    private JSONObject currentPos;

}