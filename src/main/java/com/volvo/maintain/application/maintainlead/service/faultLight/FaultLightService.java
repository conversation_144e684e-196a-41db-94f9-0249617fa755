package com.volvo.maintain.application.maintainlead.service.faultLight;

import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadOperationResultDto;
import com.volvo.maintain.application.maintainlead.dto.faultlight.TtFaultLightCluePo;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

public interface FaultLightService {

    void subComments(LeadOperationResultDto dto);

    boolean doClueDataSynchro(LeadOperationResultDto dto, String dealer);

    @Transactional(rollbackFor = Exception.class)
    int addClueCompensate(LeadOperationResultDto dto, String dealer);

    void setCompanyDetail(LeadOperationResultDto dto, String dealer);

    void doMess(LeadOperationResultDto dto, Date date, TtFaultLightCluePo cluePO, boolean flag, String comments, String dealer);

    void pushMessage(Long id, String followUpStatus, String bizStatus, Date updateTime);
}
