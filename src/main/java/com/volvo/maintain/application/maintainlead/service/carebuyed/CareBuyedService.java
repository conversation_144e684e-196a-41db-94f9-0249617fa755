package com.volvo.maintain.application.maintainlead.service.carebuyed;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CareBoughtQueryDTO;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CheckedCareBuyeDUseScopeVo;
import com.volvo.maintain.application.maintainlead.dto.carebuy.MaintenancePackageDto;
import com.volvo.maintain.application.maintainlead.vo.CareBoughtVO;

public interface CareBuyedService {

    CheckedCareBuyeDUseScopeVo checkUseScope(Long id);

    Page<CareBoughtVO> queryEquityDetail(CareBoughtQueryDTO queryCareBoughtDTO);

    void exportCareBoughtData(CareBoughtQueryDTO queryCareBoughtDTO);
}
