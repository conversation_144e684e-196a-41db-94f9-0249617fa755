package com.volvo.maintain.application.maintainlead.dto.repairAssign;
import java.util.List;

import lombok.Data;

@Data
public class RepairInfoDTO {
	/**
	 * 保修部件一级
	 */
    private String repairPartsLevel1;
    /**
     * 保修部件二级
     */
    private String repairPartsLevel2;
    /**
     * 维修类型
     */
    private String repairTypeCode;
    /**
     * 是否维修部件缺失
     */
    private String isRepairPartsMissing;
    
    /**
     * 是否文件缺失
     */
    private String isFileMissing;
    /**
     * 是否不修
     */
    private String isNotRepaired;
    /**
     * 维修现象
     */
    private String faultPhenomenon;
    /**
     * 交修项目id
     */
    private String repairProjectId;
    /**
     * 附件
     */
    private List<AttachmentDTO> fileList;
 }
