package com.volvo.maintain.application.maintainlead.service;

import java.util.List;

import com.volvo.maintain.application.maintainlead.dto.CarPickupCarPhotosResDto;
import com.volvo.maintain.application.maintainlead.dto.CarPickupDeliveryBalanceResDto;
import com.volvo.maintain.application.maintainlead.dto.CarPickupDeliveryCheckBalanceResDto;
import com.volvo.maintain.application.maintainlead.dto.order.PriceDto;

public interface CarPickupDeliveryService {
	
	/**
	 * 校验余额
	 * @param ownerCode
	 * @return
	 */
	CarPickupDeliveryCheckBalanceResDto checkBalance(PriceDto priceDto);
	
	/**
	 * 查询余额
	 * @param ownerCode
	 * @return
	 */
	List<CarPickupDeliveryBalanceResDto> queryBalance(String ownerCode);

	/**
	 * 查询验车图片
	 * @param orderId
	 * @return
	 */
	CarPickupCarPhotosResDto getCarPhotos(String orderId);
}