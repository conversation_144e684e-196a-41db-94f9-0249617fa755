package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.volvo.exception.ServiceBizException;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.service.EM90ServiceLeadService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.VipCustomService;
import com.volvo.maintain.application.maintainlead.vo.PadVehiclePreviewResultVo;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.enums.BizGroupEnum;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import com.volvo.maintain.infrastructure.util.DateUtil;
import com.volvo.maintain.infrastructure.util.ToStringUtils;
import com.volvo.maintain.interfaces.vo.MemberInfoVo;
import com.volvo.maintain.interfaces.vo.ModelVO;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;
import com.volvo.maintain.interfaces.vo.VehicleEntranceVo;
import com.yonyou.cyx.framework.service.excel.ExcelExportColumn;
import com.yonyou.cyx.function.utils.bean.BeanMapperUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import java.util.*;

@Service
public class VipCustomServiceImpl implements VipCustomService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DomainMaintainLeadFeign maintainLeadsFegin;

    /**
     * fegin
     */
    @Autowired
    private DmscloudServiceFeign dmscloudServiceFegin;

    /**
     * fegin
     */
    @Autowired
    private MidEndOrgCenterFeign midEndOrgCenterFegin;

    @Autowired
    private MIdEndMemberCenterFeign mIdEndMemberCenterFegin;

    @Autowired
    private VehicleOwnershipServiceFeign vehicleOwnershipServiceFegin;

    @Autowired
    private MidEndMessageCenterFeign midEndMessageCenterFeign;

    @Autowired
    private EM90ServiceLeadService em90ServiceLeadService;

    @Autowired
    private DmscusReportFeign reportFeign;

    @Value("${vip-entrance-reception.mail}")
    private String mail;

    @Value("${volvo.message.appId}")
    private String appId1;

    private static String appId;

    public static final String STATUS_CODE = "200";

    /**
     * 重点维修组
     */
    public static final String SINCE_TYPE = "360_FACE_CUSTOMER_REMINDER";

    /**
     * lb : 重点关照组  t: 重点维修组 90:重点服务组
     * 重点关照组 - lb
     */
    public static final String CUSTOMER_CARE = "360_FACE_CUSTOMER_CARE";

    /**
     * 重点服务组 - t
     */
    public static final String CUSTOMER_ATTENTION = "360_FACE_CUSTOMER_ATTENTION";

    public static final String VIP_EMAIL_SUBJECT = "VIP客户到店提醒【%s】";

    public static final String OLD_VIP_EMAIL_SUBJECT = "VIP客户到店提醒";

    @Autowired
    DownloadServiceFeign downloadServiceFeign;

    @PostConstruct
    void init(){
        appId=appId1;
    }

    @Override
    public void saveEntranceReceptionVehicle(String message) {

        if (StringUtils.isEmpty(message)) return;

        VehicleEntranceVo vehicleEntranceVO = JSON.parseObject(message, VehicleEntranceVo.class);
        logger.info("MQ车辆进厂信息，VehicleEntranceVO:{}", vehicleEntranceVO);

        String licensePlate = vehicleEntranceVO.getLicensePlate();
        String dealerCode = vehicleEntranceVO.getDealerCode();
        if (ObjectUtils.isEmpty(vehicleEntranceVO) && StringUtils.isEmpty(licensePlate) && StringUtils.isEmpty(dealerCode)) {
            return;
        }
        logger.info("vip进厂邮件推送开始处理 处理Redis锁信息，licensePlate:{}", licensePlate);
        RLock lock = redissonClient.getLock(StringUtils.join("vipEntranceReceptionVehicle", licensePlate));
        try {

            //根据车牌号查询vin
            String queryVin  = em90ServiceLeadService.queryOwnerVehicle(vehicleEntranceVO);
            logger.info("queryOwnerVehicle根据车牌号查询车架号:{}",queryVin);

            // 获取锁
            if (!lock.tryLock()) {
                logger.info("执行被拦截，{}", new Date());
                throw new RuntimeException("同车牌同时执行被拦截");
            }
            logger.info("vip进厂邮件推送开始处理，licensePlate:{}", licensePlate);

            //车架号或车牌号查询vip群组（不会返回bx，也无需邮件通知）
            DmsResponse<List<String>> vipGroupRes = maintainLeadsFegin.queryVipGroupByVinOrLicense(vehicleEntranceVO.getVin(), licensePlate);
            logger.info("车辆VIP群组信息，vipGroupRes:{}", JSON.toJSONString(vipGroupRes));
            if (ObjectUtils.isEmpty(vipGroupRes) || ObjectUtils.isEmpty(vipGroupRes.getData())) {
                logger.info("该车不是VIP不符合推送条件：licensePlate:{}", licensePlate);
                return;
            }

            getVehicleEntranceVO(vehicleEntranceVO);
            String vin = vehicleEntranceVO.getVin();

            //1. 循环vip群组
            List<String> vipGroup = vipGroupRes.getData();
            vipGroup.forEach(vipCode -> {
                BizGroupEnum bizGroupEnum = BizGroupEnum.resolve(vipCode);
                logger.info("循环vip群组，当前vipCode:{}，bizGroupEnum:{}", vipCode, JSON.toJSONString(bizGroupEnum));
                if (ObjectUtil.isEmpty(bizGroupEnum) || ObjectUtil.isEmpty(bizGroupEnum.getMailSinceType())) {
                    return;
                }
                EmailBodyDto emailBodyDto = initEmailBodyDTO(vin, licensePlate, vehicleEntranceVO, bizGroupEnum);
                //2. 判断是否推送过消息，推送并记录
                recordVipEmailAndSend(emailBodyDto.getMailSinceType(), emailBodyDto, queryVin, vin, vehicleEntranceVO);
            });
        } catch (Exception e) {
            logger.error("发送邮件失败 ",e);
        }finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 初始化邮件内容DTO
     */
    private EmailBodyDto initEmailBodyDTO(String vin, String licensePlate, VehicleEntranceVo vehicleEntranceVO, BizGroupEnum bizGroupEnum) {
        EmailBodyDto emailBodyDto = new EmailBodyDto();
        emailBodyDto.setVin(vin);
        emailBodyDto.setLicense(licensePlate);
        emailBodyDto.setDealerCode(vehicleEntranceVO.getDealerCode());
        emailBodyDto.setEntryTime(vehicleEntranceVO.getEntryTime());

        emailBodyDto.setOwnerName(vehicleEntranceVO.getOwnerName());
        emailBodyDto.setPhone(vehicleEntranceVO.getPhone());
        emailBodyDto.setModelCode(vehicleEntranceVO.getModelName());
        //邮件场景
        emailBodyDto.setMailSinceType(bizGroupEnum.getMailSinceType());
        //标题
        emailBodyDto.setMailSubject(String.format(VIP_EMAIL_SUBJECT, bizGroupEnum.getMailTitleFillInfo()));
        return emailBodyDto;
    }

    /**
     * 每个vip群组的处理，判断是否推送过消息，需要推送就推，并记录
     * @param mailSinceType
     * @param emailBodyDto
     * @param queryVin
     * @param vin
     * @param vehicleEntranceVO
     */
    private void recordVipEmailAndSend(String mailSinceType, EmailBodyDto emailBodyDto, String queryVin, String vin, VehicleEntranceVo vehicleEntranceVO) {
        DmsResponse<PushMessageRecordVo> pushMessageRecord = maintainLeadsFegin.queryPushMessageRecord(mailSinceType, emailBodyDto.getDealerCode()+ queryVin);
        logger.info("消息推送记录，pushMessageRecord:{}", pushMessageRecord);

        PushMessageRecordVo pushMessageRecordVo = pushMessageRecord.getData();
        logger.info("消息推送记录，pushMessageRecordVo:{}", pushMessageRecordVo);

        if (ObjectUtils.isNotEmpty(pushMessageRecordVo)) {
            logger.info("当天有发送记录：vin{}", vin);
            return;
        }

        pushMessageRecordVo = new PushMessageRecordVo();
        pushMessageRecordVo.setTaskStatus(0);
        pushMessageRecordVo.setBizNo(mailSinceType);
        pushMessageRecordVo.setSubBizNo(emailBodyDto.getDealerCode()+ queryVin);
        pushMessageRecordVo.setSinceType(1);
        logger.info("新增消息推送记录:{}",pushMessageRecordVo);
        DmsResponse<Integer> integerDmsResponse = maintainLeadsFegin.insertPushMessageRecord(pushMessageRecordVo);

        if(integerDmsResponse == null || !STATUS_CODE.equals(integerDmsResponse.getResultCode())){
            return;
        }

        Integer data1 = integerDmsResponse.getData();
        if(ObjectUtils.isEmpty(data1)){
            return;
        }

        pushMessageRecordVo.setId(data1);

        IsExistByCodeDto isExistByCodeDTO = new IsExistByCodeDto();
        isExistByCodeDTO.setCompanyCode(vehicleEntranceVO.getDealerCode());
        ResponseDto<List<CompanyDetailByCodeDto>> listResponseDto = midEndOrgCenterFegin.selectByCompanyCode(isExistByCodeDTO);
        logger.info("根据经销商code查询经销商信息，listResponseDto:{}", listResponseDto);

        List<CompanyDetailByCodeDto> listResponseDtoData = listResponseDto.getData();
        logger.info("根据经销商code查询经销商信息，listResponseDtoData:{}", listResponseDtoData);

        CompanyDetailByCodeDto companyDetailByCodeDTO = listResponseDtoData.get(0);
        logger.info("根据经销商code查询经销商信息，companyDetailByCodeDTO:{}", companyDetailByCodeDTO);

        emailBodyDto.setDealerName(companyDetailByCodeDTO.getCompanyNameCn());

        MidResponse<String> bindInfobyVin = vehicleOwnershipServiceFegin.getBindInfoByVin(vin);
        logger.info("根据经销商code查询经销商信息，companyDetailByCodeDTO:{}", companyDetailByCodeDTO);

        List<String> memberid = Collections.singletonList(bindInfobyVin.getData());
        logger.info("根据经销商code查询经销商信息，companyDetailByCodeDTO:{}", companyDetailByCodeDTO);

        //调用会员接口查询会员信息
        ResponseDto<List<MemberInfoVo>> memberInfoVOResult = mIdEndMemberCenterFegin.getMemberInfoByIdList(memberid);
        logger.info("getMemberInfoByIdList调用会员接口查询会员信息返回:{}", memberInfoVOResult);
        if (!memberInfoVOResult.getReturnCode().equals(CommonConstant.SUCCESS_CODE)) {
            logger.info("getMemberInfoByIdList调用会员接口查询会员信息返回:{}", memberInfoVOResult);
        }
        if (!CollectionUtils.isEmpty(memberInfoVOResult.getData())){
            logger.info("getMemberInfoByIdList调用会员接口查询会员信息返回:{}", memberInfoVOResult);
            List<MemberInfoVo> memberInfoVoList = memberInfoVOResult.getData();
            MemberInfoVo memberInfoVo = memberInfoVoList.get(0);
            emailBodyDto.setOwnerName(memberInfoVo.getRealName());
            emailBodyDto.setPhone(memberInfoVo.getMemberPhone());
        }
        sendEmailEW(emailBodyDto,pushMessageRecordVo);
    }

    @Override
    public VehicleEntranceVo getVehicleEntranceVO(VehicleEntranceVo vehicleEntranceVO) {
        logger.info("MQ车辆进厂信息，VehicleEntranceVO:{}", vehicleEntranceVO);
        Objects.requireNonNull(vehicleEntranceVO, "getVehicleEntranceVO vehicleEntranceVO isNull");
        PadVehiclePreviewDto padVehiclePreviewDto = new PadVehiclePreviewDto();
        padVehiclePreviewDto.setOwnerCode(vehicleEntranceVO.getDealerCode());
        padVehiclePreviewDto.setLicense(vehicleEntranceVO.getLicensePlate());
        DmsResponse<PadVehiclePreviewResultVo> pageDtoDmsResponse = dmscloudServiceFegin.queryOwnerVehicle(padVehiclePreviewDto);
        logger.info("车辆feigon信息，pageDtoDmsResponse:{}", pageDtoDmsResponse);
        if(pageDtoDmsResponse == null || !STATUS_CODE.equals(pageDtoDmsResponse.getResultCode())){
            return vehicleEntranceVO;
        }
        PadVehiclePreviewResultVo data = pageDtoDmsResponse.getData();
        logger.info("车辆主信息，data:{}", data);
        if(null == data){
            return null;
        }

        vehicleEntranceVO.setVin(Objects.isNull(data.getVin()) ? "" : data.getVin());
        vehicleEntranceVO.setOwnerName(Objects.isNull(data.getOwnerName()) ? "" : data.getOwnerName());
        vehicleEntranceVO.setPhone(Objects.isNull(data.getMobile()) ? "" : data.getMobile());
        String modelCode = Objects.isNull(data.getModelCode()) ? "" : data.getModelCode();
        DmsResponse<List<ModelVO>> modelDmsResponse = dmscloudServiceFegin.model();
        logger.info("车辆信息，modelDmsResponse:{}", modelDmsResponse);
        if(modelDmsResponse == null){
            logger.info("getVehicleEntranceVO modelDmsResponse isnull ");
            return vehicleEntranceVO;
        }
        if(!STATUS_CODE.equals(modelDmsResponse.getResultCode())){
            logger.info("getVehicleEntranceVO !STATUS_CODE ResultCode");
            return vehicleEntranceVO;
        }
        List<ModelVO> modelVOList = modelDmsResponse.getData();
        if(CollectionUtils.isEmpty(modelVOList)){
            logger.info("车辆信息，modelDmsResponse:{}", modelDmsResponse);
            return vehicleEntranceVO;
        }

        // 在 modelVOList 中查找 modelCode 等于 targetModelCode 的模型对应的 modelName
        String modelName = modelVOList.stream()
                .filter(m -> modelCode.equals(m.getModelCode()))
                .map(ModelVO::getModelName)
                .findFirst() // 因为 modelName 可能为空，因此需要使用 findFirst 方法获取 Optional<String>，以避免 NPE
                .orElse(null); // 如果没有找到对应的模型，返回 null

        vehicleEntranceVO.setModelName(modelName);
        return vehicleEntranceVO;
    }

    public void sendEmailEW(EmailBodyDto emailBodyDto, PushMessageRecordVo pushMessageRecordVo) {
        logger.info("消息推送记录:{}",pushMessageRecordVo);
        EmailInfoDto emailInfoDto = getEmailInfoDto(emailBodyDto);
        boolean flag;
        try {
            flag = pushEmail(emailInfoDto);
        } catch (Exception e) {
            flag = true;
        }

        if (flag){
            pushMessageRecordVo.setTaskStatus(1);
        }else {
            pushMessageRecordVo.setTaskStatus(2);
        }
        pushMessageRecordVo.setReqParams(emailBodyDto.toString());
        pushMessageRecordVo.setReceiver(emailBodyDto.getPhone());
        logger.info("更新消息推送记录:{}",pushMessageRecordVo);
        maintainLeadsFegin.updatePushMessageRecord(pushMessageRecordVo);

        logger.info("VIP客户到店提醒发送邮件结束");
    }

    private EmailInfoDto getEmailInfoDto(EmailBodyDto emailBodyDto) {
        logger.info("VIP客户到店提醒发送邮件:{}",emailBodyDto);
        EmailInfoDto emailInfoDto = new EmailInfoDto();
        emailInfoDto.setFrom(CommonConstant.VOLVO_EMAIL);
        emailInfoDto.setTo(mail.split(","));
        emailInfoDto.setSubject(ObjectUtil.isEmpty(emailBodyDto.getMailSubject()) ? OLD_VIP_EMAIL_SUBJECT : emailBodyDto.getMailSubject());
        StringBuilder context = new StringBuilder("");
        context.append("<div style=\"width: 500px; margin: auto;\">");
        context.append("<p>");
        context.append("【VIP客户到店提醒】有VIP 客户进厂养修，客户信息如下：");
        context.append("</p >");
        context.append("<p>");
        context.append("经销商代码："+ (StringUtils.isEmpty(emailBodyDto.getDealerCode()) ? "" : emailBodyDto.getDealerCode()));
        context.append("</p >");
        context.append("<p>");
        context.append("经销商名称:"+ (StringUtils.isEmpty(emailBodyDto.getDealerName()) ? "" : emailBodyDto.getDealerName()));
        context.append("</p >");
        context.append("<p>");
        context.append("进厂时间:"+DateUtil.formatDefaultDateTimes(emailBodyDto.getEntryTime()));
        context.append("</p >");
        context.append("<p>");
        context.append("车型:"+ (StringUtils.isEmpty(emailBodyDto.getModelCode()) ? "" : emailBodyDto.getModelCode()));
        context.append("</p >");
        context.append("<p>");
        context.append("车牌号:"+ (StringUtils.isEmpty(emailBodyDto.getLicense()) ? "" : emailBodyDto.getLicense()));
        context.append("</p >");
        context.append("<p>");
        context.append("VIN:"+ (StringUtils.isEmpty(emailBodyDto.getVin()) ? "" : emailBodyDto.getVin()));
        context.append("</p >");
        context.append("<p>");
        context.append("车主姓名:"+ (StringUtils.isEmpty(emailBodyDto.getOwnerName()) ? "" : emailBodyDto.getOwnerName()));
        context.append("</p >");
        context.append("<p>");
        context.append("联系电话:"+ (StringUtils.isEmpty(emailBodyDto.getPhone()) ? "" : emailBodyDto.getPhone()));
        context.append("</p >");
        context.append("</div>");
        emailInfoDto.setText(context.toString());
        logger.info("发送邮件dto:{}",emailInfoDto);
        return emailInfoDto;
    }

    @Retryable(value = {ServiceBizException.class}, maxAttempts = 3, backoff = @Backoff(delay = 500))
    private boolean pushEmail(EmailInfoDto emailInfoDto) {
        DmsResponse dmsResponse = midEndMessageCenterFeign.pushEmails(appId, emailInfoDto);
        logger.info("VIP客户到店提醒发送邮件中 dto:{}",dmsResponse);
        if (!"0".equals(dmsResponse.getReturnCode())) {
            logger.error("发送邮件失败信息 dto:{}",dmsResponse.getErrMsg());
            throw new ServiceBizException("发送邮件失败！");
        }
        return true;
    }

    @Override
    public void increasVip(List<VipImportDto> vipImportDto) {
        if (CollectionUtils.isEmpty(vipImportDto) && vipImportDto.size() > CommonConstant.VIP_CUSTOM_INCREAS_MAX_SIZE) {
            throw new ServiceBizException("导入VIP客户总数需小于10000");
        }
        //bizGroup必填校验
        boolean emptyBizGroup = vipImportDto.stream().anyMatch(dto ->
                ObjectUtil.isEmpty(dto.getBizGroup())
        );
        if (emptyBizGroup) {
            throw new ServiceBizException("bizGroup必填");
        }
        //vip数据去重
        vipImportDto = vipImportDto.stream().distinct().collect(Collectors.toList());
        List<List<VipImportDto>> partition = Lists.partition(vipImportDto, CommonConstant.VIP_CUSTOM_INCREAS_SIZE);
        partition.forEach(i -> {
            logger.info("increasVip Data:{}", JSONObject.toJSONString(i));
            maintainLeadsFegin.increasVip(i);
        });

    }

    @Override
    public void vipEntranceEmail() {
        DmsResponse<List<PushMessageRecordVo>> listDmsResponse = maintainLeadsFegin.selectPushMessageRecordList();
        if (!STATUS_CODE.equals(listDmsResponse.getResultCode())) {
            logger.info("发送邮件失败信息 dto:{}",listDmsResponse.getErrMsg());
            return;
        }
        List<PushMessageRecordVo> pushMessageRecordVos = listDmsResponse.getData();
        if (ObjectUtils.isEmpty(pushMessageRecordVos)) {
            logger.info("邮件补偿推送数据无");
            return;
        }
        logger.info("邮件补偿推送数据开始");
        for (PushMessageRecordVo pushMessageRecordVo : pushMessageRecordVos) {
            String reqParams = pushMessageRecordVo.getReqParams();
            logger.info("邮件补偿推送数据，reqParams:{}", reqParams);
            try {
                EmailBodyDto emailBodyDto = JSONObject.parseObject(ToStringUtils.toJSONString(reqParams), EmailBodyDto.class);
                sendEmailEW(emailBodyDto, pushMessageRecordVo);
            } catch (Exception e) {
                logger.info("邮件补偿推送数据，推送失败:{}", e.getMessage());
            }
        }
    }

    /**
     * 页面导入VIP客户群组
     */
    @Override
    public String importVipGroup(List<VipGroupImportDTO> importDTOList) {
        DmsResponse<Void> resultResponse = maintainLeadsFegin.importVipGroup(importDTOList);
        if (resultResponse == null || !resultResponse.isSuccess()) {
            logger.error("页面导入VIP客户群组失败:{}", resultResponse == null ? "" : resultResponse.getErrMsg());
            return resultResponse == null ? "页面导入VIP客户群组失败" : resultResponse.getErrMsg();
        }
        return null;
    }

    @Override
    public Page<VipGroupImportDTO> vipGroupPage(VipCustomPageReqDTO pageReqDTO) {
        logger.info("页面查询VIP客户群组，pageReqDTO:{}", pageReqDTO);
        DmsResponse<Page<VipGroupImportDTO>> pageDmsResponse = maintainLeadsFegin.vipCustomPage(pageReqDTO);
        if (pageDmsResponse == null || !pageDmsResponse.isSuccess()) {
            logger.error("页面查询VIP客户群组:{}", pageDmsResponse == null ? "" : pageDmsResponse.getErrMsg());
            throw new ServiceBizException(pageDmsResponse == null ? "页面查询VIP客户群组失败" : pageDmsResponse.getErrMsg());
        }
        return pageDmsResponse.getData();
    }

    @Override
    public void vipGroupExport(VipCustomPageReqDTO pageReqDTO) {
        if (ObjectUtil.isEmpty(pageReqDTO)) {
            pageReqDTO = new VipCustomPageReqDTO();
        }
        pageReqDTO.setPageSize(ObjectUtil.isEmpty(pageReqDTO.getPageSize()) ? 99999L : pageReqDTO.getPageSize());
        pageReqDTO.setCurrentPage(ObjectUtil.isEmpty(pageReqDTO.getCurrentPage()) ? 1L : pageReqDTO.getCurrentPage());
        // 获取导出字段
        List<ExcelExportColumn> exportColumnList = buildExcelColumn();
        // 组装下载中心 DTO
        DownloadDto dto = new DownloadDto();
        dto.setQueryParams(BeanMapperUtil.toMap(pageReqDTO));
        dto.setExcelName("重点客户组别维护.xlsx");
        dto.setSheetName("重点客户组别维护列表");
        dto.setServiceUrl("http://application-maintain-management/vipCustom/vipGroup/export/callback");
        dto.setExcelExportColumnList(exportColumnList);
        logger.info("重点客户组别维护导出:{}", JSON.toJSONString(dto));
        // 导出
        downloadServiceFeign.downloadExportExcel(dto);
    }

    /**
     * 延保理赔申请列表导出回调
     */
    @Override
    public List<VipGroupImportDTO> exportCallback(VipCustomPageReqDTO reqDTO) {
        logger.info("重点客户组别维护列表导出回调参数:{}", JSON.toJSONString(reqDTO));
        if (ObjectUtil.isEmpty(reqDTO)) {
            reqDTO = new VipCustomPageReqDTO();
        }
        reqDTO.setPageSize(ObjectUtil.isEmpty(reqDTO.getPageSize()) ? 99999L : reqDTO.getPageSize());
        reqDTO.setCurrentPage(ObjectUtil.isEmpty(reqDTO.getCurrentPage()) ? 1L : reqDTO.getCurrentPage());
        IPage<VipGroupImportDTO> page = this.vipGroupPage(reqDTO);
        logger.info("重点客户组别维护列表导出回调结果数量:{}", page.getRecords().size());
        return page.getRecords();
    }

    /**
     * 导出字段
     */
    private List<ExcelExportColumn> buildExcelColumn() {

        List<ExcelExportColumn> exportColumnList = new ArrayList<>();

        exportColumnList.add(new ExcelExportColumn("vin", "车架号"));
        exportColumnList.add(new ExcelExportColumn("licensePlate", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("bizGroup", "重点类别"));

        return exportColumnList;
    }


}
