package com.volvo.maintain.application.maintainlead.dto;

import com.volvo.maintain.interfaces.vo.CdpTagInfoVo;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/11/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("属性及标签信息")
public class CustomerTagsDto {
    // idValue
    private String idValue;

    // vin
    private String vin;

    // 属性集合
    private List<CdpAttributeListDto> attLisit;

    // 属性集合 （cdp 未声明原因，所以增加backUp 属性集合）
    private List<CdpAttributeListDto> attList;


    // 标签集合
    private List<CdpTagListDto> tagList;


    // 解析标签结构
    private List<CdpTagInfoVo> tagInfoVOS;


}
