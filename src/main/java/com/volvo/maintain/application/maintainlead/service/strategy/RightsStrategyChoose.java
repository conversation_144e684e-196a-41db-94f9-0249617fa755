package com.volvo.maintain.application.maintainlead.service.strategy;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.volvo.design.strategy.AbstractStrategyChoose;
import com.volvo.event.ApplicationInitializingEvent;
import com.volvo.exception.ServiceBizException;
import com.volvo.exception.ServiceException;
import com.volvo.maintain.application.maintainlead.dto.ContractPurchaseGiveDto;
import com.volvo.maintain.application.maintainlead.dto.CustomerInfoDto;
import com.volvo.maintain.application.maintainlead.dto.ExtWarPurgiveDataVO;
import com.volvo.maintain.application.maintainlead.dto.GroupDetailsConfigDTO;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.TmVehicleDto;
import com.volvo.maintain.application.maintainlead.dto.rights.GiveRecordRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.GiveRecordResponseDto;
import com.volvo.maintain.application.maintainlead.dto.rights.GiveStatusSyncDto;
import com.volvo.maintain.application.maintainlead.dto.rights.OrderActivationDetailsRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.OrderActivationDetailsResponseDto;
import com.volvo.maintain.application.maintainlead.dto.rights.PurchaseEligibilityCheckRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.PurchaseEligibilityCheckResponseDto;
import com.volvo.maintain.application.maintainlead.dto.rights.RightsProductDto;
import com.volvo.maintain.application.maintainlead.dto.rights.UsageDetailsRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.UsageDetailsResponseDto;
import com.volvo.maintain.application.maintainlead.emums.PurchaseConditionsTypeEnum;
import com.volvo.maintain.application.maintainlead.emums.RightsStrategyEnum;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.DmscusRepairFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainAuthFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndBasicdataCenterFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndInvoiceCenterFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndVehicleCenterFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.ContentsPartVo;
import com.volvo.maintain.interfaces.vo.InvoiceOwnerVO;
import com.volvo.maintain.interfaces.vo.TmConfigVO;
import com.volvo.utils.ApplicationContextUtils;

import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class RightsStrategyChoose extends AbstractStrategyChoose implements RightsExecuteStrategy {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Autowired
    private DmscusRepairFeign dmscusRepairFeign;

    @Autowired
    private MidEndInvoiceCenterFeign invoiceCenterFeign;

    @Autowired
    private MidEndBasicdataCenterFeign basicdataCenterFeign;

    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;
    
    @Resource
    private DomainMaintainAuthFeign domainMaintainAuthFeign;

    @Override
    public void onApplicationEvent(ApplicationInitializingEvent event) {
        Map<String, RightsExecuteStrategy> actual = ApplicationContextUtils.getBeansOfType(RightsExecuteStrategy.class);
        actual.forEach((beanName, bean) -> {
            if(StringUtils.isEmpty(bean.mark())){
                return;
            }
            AbstractStrategyChoose beanExist = (AbstractStrategyChoose)this.abstractExecuteStrategyMap.get(bean.mark());
            if (beanExist != null) {
                throw new ServiceException(String.format("[%s] Duplicate execution policy", bean.mark()));
            } else {
                this.abstractExecuteStrategyMap.put(bean.mark(), bean);
            }
        });
    }


    @Override
    public String mark() {
        return StringUtils.EMPTY;
    }


    /**
     * 查询可上架商品列表
     *
     * @param dto
     */
    @Override
    public IPage<RightsProductDto> productList(RightsProductDto dto) {
        Integer queryProductType = dto.getProductType();
        String productType = this.getDirValue(queryProductType);
        log.info("productList productNo is :{} ,productType is :{} ",dto.getProductNo(),productType);
        RightsExecuteStrategy strategy = (RightsExecuteStrategy)this.choose(productType);
        dto.setProductType(Integer.valueOf(productType));
        IPage<RightsProductDto> page = strategy.productList(dto);
        if(CollectionUtils.isNotEmpty(page.getRecords())){
            page.getRecords().forEach(e -> e.setProductType(queryProductType));
        }
        return page;
    }

    @Transactional
    @Override
    public List<GiveStatusSyncDto> giveSync(List<GiveStatusSyncDto> statusSyncDtos) {
        List<String> partNos = statusSyncDtos.stream().map(GiveStatusSyncDto::getProductNo).collect(Collectors.toList());
        Map<String, Integer> productTypeByPartNos = this.getProductTypeByPartNos(partNos);
        Map<String, List<GiveStatusSyncDto>> statusSyncDtosMap = statusSyncDtos.stream().collect(Collectors.groupingBy(GiveStatusSyncDto::getProductNo));
        statusSyncDtosMap.forEach((productNo,giveStatusSyncDtos) -> {
            Integer productType = Optional.ofNullable(productTypeByPartNos.get(productNo)).orElseThrow(() -> new ServiceBizException("零件类型不支持"));
            log.info("giveSync productNo is :{} ,productType is :{} ",productNo,productType);
            RightsExecuteStrategy strategy = (RightsExecuteStrategy)this.choose(productType.toString());
            strategy.giveSync(giveStatusSyncDtos);
        });
        return statusSyncDtos;
    }


    private Map<String,Integer> getProductTypeByPartNos(List<String> partNos){
        if(CollectionUtils.isEmpty(partNos)){
            return Maps.newHashMap();
        }
        DmsResponse<List<ContentsPartVo>> dmsResponse = dmscusRepairFeign.selectByPartNos(partNos);
        List<ContentsPartVo> contentsPartVos = dmsResponse.getData();
        if(dmsResponse.isSuccess() && CollectionUtils.isNotEmpty(contentsPartVos)){
            return contentsPartVos.stream().collect(Collectors.toMap(ContentsPartVo::getPartNo,ContentsPartVo::getBizType));
        }
        return Maps.newHashMap();
    }


    private String getDirValue(Integer key){
        if(Objects.isNull(key)){
            return StringUtils.EMPTY;
        }
        DmsResponse<CustomerInfoDto> dmsResponse = dmscloudServiceFeign.queryDmsDefaultParam(key.toString());
        CustomerInfoDto data = dmsResponse.getData();
        if(dmsResponse.isSuccess() && Objects.nonNull(data)){
            return data.getDmsDefault();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 产品购买
     *
     * @param giveDto
     */
    public void give(ContractPurchaseGiveDto giveDto){
        log.info("产品购买 giveDto is :{}",giveDto);
        PurchaseEligibilityCheckRequestDto checkRequestDto = new PurchaseEligibilityCheckRequestDto();
        checkRequestDto.setVin(giveDto.getVin());
        checkRequestDto.setEngineNo(giveDto.getEngineNo());
        checkRequestDto.setModelCode(giveDto.getModelCode());
        checkRequestDto.setProductNoList(Lists.newArrayList(giveDto.getProductNo()));
        //购买资格校验失败
        List<PurchaseEligibilityCheckResponseDto> PurchaseEligibilityCheckResponseDtos=  buyValid(checkRequestDto);
        log.info("购买资格校验:{}" , PurchaseEligibilityCheckResponseDtos);
        PurchaseEligibilityCheckResponseDto purchaseEligibilityCheckDto = PurchaseEligibilityCheckResponseDtos.get(0);
        if (!StringUtils.isEmpty(purchaseEligibilityCheckDto.getCode()))  {
            throw new ServiceException(String.format("[%s] 购买资格校验失败", purchaseEligibilityCheckDto.getCode()));
        }
        //获取车辆信息
        DmsResponse<TmVehicleDto> vehicleDtoResponse = midEndVehicleCenterFeign.getVehicleByVIN(giveDto.getVin());
        log.info("获取车辆信息:{}" , vehicleDtoResponse);
        if (vehicleDtoResponse.isFail() || Objects.isNull(vehicleDtoResponse.getData())) {
            throw new ServiceException(String.format("[%s] vin不存在", giveDto.getVin()));
        }
        String dirValue = this.getDirValue(giveDto.getDealerCode());
        if (StringUtils.isEmpty(dirValue)) {
            throw new ServiceException(String.format("[%s] dealerCode不存在", giveDto.getDealerCode()));
        }
        fillGiveDto(giveDto, dirValue, vehicleDtoResponse);
        Map<String, Integer> productTypeByPartNos = this.getProductTypeByPartNos(Lists.newArrayList(giveDto.getProductNo()));
        Integer productType = Optional.ofNullable(productTypeByPartNos.get(giveDto.getProductNo())).orElseThrow(() -> new ServiceBizException("零件类型不支持"));
        log.info("give productNo is :{} ,productType is :{} ",giveDto.getProductNo(),productType);
        RightsExecuteStrategy strategy = (RightsExecuteStrategy)this.choose(productType.toString());
        strategy.give(giveDto);
    }

    private void fillGiveDto(ContractPurchaseGiveDto giveDto, String dirValue, DmsResponse<TmVehicleDto> vehicleDtoResponse) {
        giveDto.setOwnerCode(dirValue);
        giveDto.setPurchaseCount(giveDto.getCount());
        Long configId=null;
        TmVehicleDto vehicleDto = vehicleDtoResponse.getData();
        if (ObjectUtils.isNotEmpty(vehicleDto)) {
            String engineNO= StringUtils.isBlank(vehicleDto.getConfigCode()) || vehicleDto.getConfigCode().length() < 6 ?null:vehicleDto.getConfigCode().substring(3,5);
            giveDto.setEngineNo(engineNO);
            giveDto.setInvoiceDate(vehicleDto.getInvoiceDate());
            giveDto.setModelCode(vehicleDto.getModelCode());
            giveDto.setSalesDate(vehicleDto.getOwnerReportedSalesDate());
            if(StringUtils.isNotEmpty(vehicleDto.getConfigId())){
                configId = Long.valueOf(vehicleDto.getConfigId());
            }
        }
        ResponseDto<InvoiceOwnerVO> responseDTO= invoiceCenterFeign.selectInvoiceByVin(giveDto.getVin());
        String invoicePrice = "";
        log.info("查询发票信息：{}",JSON.toJSONString(responseDTO));
        if(responseDTO !=null && responseDTO.getData() !=null){
            invoicePrice= getInvociePrice(responseDTO);
            log.info("get invoice price result: {}", invoicePrice);
            //如果取不到发票价格，取 配置表中的msrp
            if(StringUtil.isNullOrEmpty(invoicePrice)){
                invoicePrice= getVehicleMsrp(configId);
            }
        }else{
            invoicePrice= getVehicleMsrp(configId);
        }
        if(StringUtils.isNotEmpty(invoicePrice)){
            log.info("发票价格：{}",invoicePrice);
            giveDto.setInvoicePrice(Double.valueOf(invoicePrice));
        }
    }

    /**
     * C端购买上架产品限制查询接口(新增 通用接口支持延保/保养套餐)
     * @param purchaseEligibilityCheckRequestDto 校验购买限制入参
     * @return 根据入参返回结果
     */
    @Override
    public List<PurchaseEligibilityCheckResponseDto> buyValid(PurchaseEligibilityCheckRequestDto purchaseEligibilityCheckRequestDto) {
        List<String> productNos = purchaseEligibilityCheckRequestDto.getProductNoList();
        List<PurchaseEligibilityCheckResponseDto> responses = new ArrayList<>();
        Map<String, Integer> productTypeByPartNos = getProductTypeByPartNos(productNos);
        // 根据产品类型分组策略枚举
        Map<Integer, List<String>> productsGroupedByType = new HashMap<>();
        productNos.forEach(productNo -> {
            Integer productType = productTypeByPartNos.get(productNo);
            if(Objects.isNull(productType)){
                PurchaseEligibilityCheckResponseDto dto = new PurchaseEligibilityCheckResponseDto();
                dto.setProductNo(productNo);
                dto.setCode(PurchaseConditionsTypeEnum.NOT_EXIST_CONTENTS_PART.getErrorCode());
                dto.setMsg(PurchaseConditionsTypeEnum.NOT_EXIST_CONTENTS_PART.getMessage());
                responses.add(dto);
            }else{
                productsGroupedByType.computeIfAbsent(productType, k -> new ArrayList<>()).add(productNo);
            }
        });
        TmVehicleDto vehicleDetail = queryVehicleDetail(purchaseEligibilityCheckRequestDto.getVin());
        productsGroupedByType.forEach((productType, groupedProductNos) -> {
            log.info("buyValid Executing strategy for productType :{}, productNos :{}", productType, groupedProductNos);
            RightsExecuteStrategy strategy = (RightsExecuteStrategy) this.choose(productType.toString());
            List<PurchaseEligibilityCheckResponseDto> strategyResponses = strategy.buyValid(buildPurchaseEligibilityCheckRequestDto(vehicleDetail, purchaseEligibilityCheckRequestDto.getVin(), purchaseEligibilityCheckRequestDto.getMileage(), groupedProductNos));
            responses.addAll(strategyResponses);
        });
        return responses;
    }

    /**
     * 构建 PurchaseEligibilityCheckRequestDto 对象
     * @param vehicleDetail 车辆信息
     * @param vin 车架号
     * @param mileage 里程
     * @param groupedProductNos 产品list
     * @return 完整请求对象
     */
    private PurchaseEligibilityCheckRequestDto buildPurchaseEligibilityCheckRequestDto(TmVehicleDto vehicleDetail, String vin, Integer mileage, List<String> groupedProductNos) {
        PurchaseEligibilityCheckRequestDto groupedRequestDto = new PurchaseEligibilityCheckRequestDto();
        groupedRequestDto.setVin(vin);
        groupedRequestDto.setMileage(mileage);
        groupedRequestDto.setProductNoList(groupedProductNos);
        Long configId=null;
        //groupedRequestDto.setCount(count);
        // 复制车辆详细信息中的字段
        if (ObjectUtils.isNotEmpty(vehicleDetail)) {
            String engineNO= StringUtils.isBlank(vehicleDetail.getConfigCode()) || vehicleDetail.getConfigCode().length() < 6 ?null:vehicleDetail.getConfigCode().substring(3,5);
            groupedRequestDto.setEngineNo(engineNO);
            groupedRequestDto.setAge(vehicleDetail.getVehicleAge());
            groupedRequestDto.setInvoiceDate(vehicleDetail.getInvoiceDate());
            groupedRequestDto.setInvoicePrice(vehicleDetail.getDirectivePrice());
            groupedRequestDto.setModelCode(vehicleDetail.getModelCode());
            if(StringUtils.isNotEmpty(vehicleDetail.getConfigId())){
                configId = Long.valueOf(vehicleDetail.getConfigId());
            }
        }

        ResponseDto<InvoiceOwnerVO> responseDTO= invoiceCenterFeign.selectInvoiceByVin(vin);

        String invoicePrice = "";
        log.info("查询发票信息：{}",JSON.toJSONString(responseDTO));
        if(responseDTO !=null && responseDTO.getData() !=null){
            invoicePrice= getInvociePrice(responseDTO);
            log.info("get invoice price result: {}", invoicePrice);
            //如果取不到发票价格，取 配置表中的msrp
            if(StringUtil.isNullOrEmpty(invoicePrice)){
                invoicePrice= getVehicleMsrp(configId);
            }
        }else{
            invoicePrice= getVehicleMsrp(configId);
        }
        if(StringUtils.isNotEmpty(invoicePrice)){
            log.info("发票价格：{}",invoicePrice);
            groupedRequestDto.setInvoicePrice(Double.valueOf(invoicePrice));
        }
        return groupedRequestDto;
    }

    private String getInvociePrice(ResponseDto<InvoiceOwnerVO> responseDTO){
        //延保购买校验 发票金额改造— 获取taxPriceTotal价税合计，发票金额
        return responseDTO.getData().getTaxAmountTotal();
    }

    private  String getVehicleMsrp(Long configId){
        if(configId != null) {
            ResponseDto<TmConfigVO> responseConfig = basicdataCenterFeign.selectConfigById(configId);
            log.info("查询msrp:{}",JSON.toJSONString(responseConfig));
            Double vehicleMSRP = responseConfig != null && responseConfig.getData() != null ? responseConfig.getData().getMsrp() : null;
            return Objects.isNull(vehicleMSRP) ? "":vehicleMSRP+"";
        }else{
            log.info("configid 为空");
        }
        return null;
    }

    /**
     * 根据 vin 查询 车辆信息
     * @param vin 车架号
     * @return 车辆信息
     */
    private TmVehicleDto queryVehicleDetail(String vin) {
        if (vin.length() >= 18) {
            return null;
        }
        DmsResponse<TmVehicleDto> vehicleDtoResponse = midEndVehicleCenterFeign.getVehicleByVIN(vin);
        log.info("queryVehicleDetail give vin result Response:{}", vehicleDtoResponse);
        if (vehicleDtoResponse.isFail() || Objects.isNull(vehicleDtoResponse.getData())) {
            return null;
        }
        return vehicleDtoResponse.getData();
    }

    /**
     * 提供C端退款查询激活明细接口(退款用)
     * orderActivationDetailsRequestDto 查询激活明细入参
     * @return 根据入参返回结果
     */
    @Override
    public List<OrderActivationDetailsResponseDto> giveActList(OrderActivationDetailsRequestDto orderActivationDetailsRequestDto) {
        log.info("giveActList give orderActivationDetailsRequestDto result:{}", orderActivationDetailsRequestDto);
        List<String> productNos = orderActivationDetailsRequestDto.getProductNoList();
        Map<String, Integer> productTypeByPartNos = getProductTypeByPartNos(productNos);
        // 根据产品类型分组策略枚举
        Map<Integer, List<String>> productsGroupedByType = new HashMap<>();
        productNos.forEach(productNo -> {
            Integer productType = Optional.ofNullable(productTypeByPartNos.get(productNo))
                    .orElseThrow(() -> new ServiceBizException("零件类型不支持"));
            productsGroupedByType.computeIfAbsent(productType, k -> new ArrayList<>()).add(productNo);
        });
        List<OrderActivationDetailsResponseDto> responses = new ArrayList<>();
        productsGroupedByType.forEach((productType, groupedProductNos) -> {
            log.info("giveActList Executing strategy for productType :{}, productNos :{}", productType, groupedProductNos);
            RightsExecuteStrategy strategy = (RightsExecuteStrategy) this.choose(productType.toString());
            List<OrderActivationDetailsResponseDto> strategyResponses = strategy.giveActList(buildOrderActivationDetailsRequestDto(orderActivationDetailsRequestDto.getOrderCode(),orderActivationDetailsRequestDto.getVin(),groupedProductNos));
            responses.addAll(strategyResponses);
        });
        return responses;
    }

    private OrderActivationDetailsRequestDto buildOrderActivationDetailsRequestDto(String orderCode, String vin, List<String> productNos) {
        OrderActivationDetailsRequestDto orderActivationDetailsRequestDto = new OrderActivationDetailsRequestDto();
        orderActivationDetailsRequestDto.setOrderCode(orderCode);
        orderActivationDetailsRequestDto.setVin(vin);
        orderActivationDetailsRequestDto.setProductNoList(productNos);
        return orderActivationDetailsRequestDto;
    }


    /**
     * 保养套餐使用明细
     * usageDetailsDto 保养套餐使用明细入参
     * @return 根据入参返回结果
     */
    @Override
    public List<UsageDetailsResponseDto> useList(UsageDetailsRequestDto usageDetailsDto) {
        log.info("useList for usageDetailsDto :{}", usageDetailsDto);
        usageDetailsDto.setCareBuyedId(usageDetailsDto.getId());
        RightsExecuteStrategy strategy = (RightsExecuteStrategy) this.choose(RightsStrategyEnum.SERVICE_CONTRACT.getCode().toString());
        return  strategy.useList(usageDetailsDto);
    }

    /**
     * 保养套餐购买记录
     * usageDetailsDto 保养套餐使用明细入参
     * @return 根据入参返回结果
     */
    @Override
    public IPage<GiveRecordResponseDto> givelist(GiveRecordRequestDto giveRecordRequestDto)  {
        log.info("giveList for giveRecordRequestDto :{}", giveRecordRequestDto);
        RightsExecuteStrategy strategy = (RightsExecuteStrategy) this.choose(RightsStrategyEnum.SERVICE_CONTRACT.getCode().toString());
        return  strategy.givelist(giveRecordRequestDto);
    }

	public List<ExtWarPurgiveDataVO> queryGiveList(String vin) {
    	log.info("RightsStrategyChoose-queryGiveList: {}", vin);
    	DmsResponse<List<ExtWarPurgiveDataVO>> findExtrusiveData = dmscloudServiceFeign.findExtrusiveData(vin);
    	if(Objects.isNull(findExtrusiveData) || findExtrusiveData.isFail()) {
    		return new ArrayList<>();
    	}
    	List<ExtWarPurgiveDataVO> data = findExtrusiveData.getData();
    	
    	Map<String, GroupDetailsConfigDTO> sortMap = new HashMap<>();
    	try {
    		// 查询配置化数据
    		List<String> businessType = new ArrayList<>();
    		businessType.add("81501001");
    		DmsResponse<List<GroupDetailsConfigDTO>> queryGroupDetailsConfigByBusinessType = domainMaintainAuthFeign.queryGroupDetailsConfigByBusinessType(businessType);
    		List<GroupDetailsConfigDTO> groupDetailsConfigList = queryGroupDetailsConfigByBusinessType.getData();
    		log.info("groupDetailsConfigList: {}", JSON.toJSONString(groupDetailsConfigList));
    		groupDetailsConfigList.stream().filter(Objects::nonNull).forEach(obj->{
    			String warrantyComponents  = obj.getComponentsType();
    			String source  = obj.getSource();
    			String key = String.join("-",String.valueOf(warrantyComponents), String.valueOf(source));
    			sortMap.put(key, obj);
    		});
    		log.info("sortMap:{}", JSON.toJSONString(sortMap));
		} catch (Exception e) {
			log.info("获取 配置失败：", e);
		}
    	List<ExtWarPurgiveDataVO> sortList = data.stream().filter(Objects::nonNull).map(obj->{
    		String key = String.join("-",obj.getWarrantyComponents(), String.valueOf(obj.getSource()));
    		GroupDetailsConfigDTO groupDetailsConfig = sortMap.get(key);
			obj.setSort(999);
    		if(Objects.nonNull(groupDetailsConfig)) {
    			if(Objects.nonNull(groupDetailsConfig.getSort())) {
    				obj.setSort(groupDetailsConfig.getSort());
    			}
    			String allowSelection = groupDetailsConfig.getAllowSelection();
    			if(StringUtils.isBlank(allowSelection)) {
    				allowSelection = "10041001";
    			}
    			obj.setAllowSelection(allowSelection);
    		}
    		return obj;
    	}).sorted(Comparator.comparing(ExtWarPurgiveDataVO::getSort)).collect(Collectors.toList());
    	
    	log.info("sortList:{}", JSON.toJSONString(sortList));
		return sortList;
	}
}
