package com.volvo.maintain.application.maintainlead.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SettlementEventDto {

    private String roNo;

    private String vin;

    private String ownerCode;

    private Long userId;

    private Double sumPrice;

    private String balanceNo;

    private Integer payModel;

    private Boolean onlinePayFlag;
}
