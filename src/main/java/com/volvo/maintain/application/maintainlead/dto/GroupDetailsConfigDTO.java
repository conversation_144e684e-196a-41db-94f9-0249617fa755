package com.volvo.maintain.application.maintainlead.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GroupDetailsConfigDTO {
    
	@ApiModelProperty(value = "工单组id")
    private Long groupId;
    
	@ApiModelProperty(value = "保修部件")
    private String componentsType;
    
	@ApiModelProperty(value = "来源")
    private String source;
    
	@ApiModelProperty(value = "业务类型")
    private String businessType;
    
	@ApiModelProperty(value = "限制数量")
    private String limitQty;
	
	@ApiModelProperty(name = "allowSelection", value = "默认选中")
	private String allowSelection;
	
	@ApiModelProperty(name = "sort", value = "排序", hidden = true)
	private Integer sort;
}
