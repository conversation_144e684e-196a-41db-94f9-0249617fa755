package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class LeadsLogRecordDto {

    private Long id;

    /**
     * 场景类型 1、事故线索
     */
    private Integer sinceType;

    /**
     * 子场景类型 1.litecrm下发
     */
    private Integer subSinceType;

    /**
     * 业务主键或业务编码
     */
    private String bizNo;

    /**
     * 子业务编码
     */
    private String subBizNo;

    /**
     * 请求参数
     */
    private String reqParams;

    /**
     * 错误消息 截取200长度字符
     */
    private String respContent;

    /**
     * 任务状态 0.待处理 1.处理成功 2.处理失败
     */
    private Integer taskStatus;

    /**
     * 删除标识（0-未删除，1-已删除）
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建sql人
     */
    private String createSqlby;

    /**
     * 更新sql人
     */
    private String updateSqlby;
}
