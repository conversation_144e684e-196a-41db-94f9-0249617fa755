package com.volvo.maintain.application.maintainlead.service.customerProfile;

import com.volvo.maintain.application.maintainlead.dto.PrecheckOrderTagDto;
import com.volvo.maintain.application.maintainlead.dto.VinLicenseDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.VehicleEntranceEntityDto;

import java.util.List;

/**
 * 环检单标签
 */
public interface PrecheckOrderTagService {

    /**
     *查询环检单标签
     * @return
     */
    List<PrecheckOrderTagDto> queryPrecheckOrderTag(String ownerCode, List<VinLicenseDto> vinLicenseDtos, List<VehicleEntranceEntityDto> records);

}
