package com.volvo.maintain.application.maintainlead.service.carebuyed.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CareBoughtQueryDTO;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CareBuyedExtDto;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CheckedCareBuyeDUseScopeVo;
import com.volvo.maintain.application.maintainlead.service.carebuyed.CareBuyedService;
import com.volvo.maintain.application.maintainlead.vo.CareBoughtVO;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.DmscusReportFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CareBuyedServiceImpl implements CareBuyedService {

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Autowired
    private DmscusReportFeign dmscusReportFeign;

    @Override
    public CheckedCareBuyeDUseScopeVo checkUseScope(Long id){
        DmsResponse<CheckedCareBuyeDUseScopeVo> dmsResponse = dmscloudServiceFeign.checkUseScope(id);
        if (dmsResponse.isFail()) {
            throw new ServiceBizException("检查使用范围异常:"+ dmsResponse.getReturnMessage());
        }
        return dmsResponse.getData();
    }

    @Override
    public Page<CareBoughtVO> queryEquityDetail(CareBoughtQueryDTO queryCareBoughtDTO){
        DmsResponse<Page<CareBoughtVO>> pageDmsResponse = dmscusReportFeign.queryEquityDetail(queryCareBoughtDTO);
        log.info("查询权益使用记录成功.{}",JSONUtil.toJsonStr(pageDmsResponse));
        if (pageDmsResponse.isFail()) {
            log.error("查询权益使用记录失败.");
        }
        List<CareBoughtVO> records = pageDmsResponse.getData().getRecords();
        setUseScope(records);
        return pageDmsResponse.getData();
    }

    private void setUseScope(List<CareBoughtVO> records) {
        if(CollectionUtils.isNotEmpty(records)){
            List<CareBuyedExtDto> useScopeList = records.stream().filter(e -> Objects.nonNull(e.getCareBuyedId())).map(e -> {
                CareBuyedExtDto extDto = new CareBuyedExtDto();
                extDto.setCareBuyedId(e.getCareBuyedId());
                extDto.setSaleDealer(e.getSaleDealer());
                return extDto;
            }).collect(Collectors.toList());
            DmsResponse<List<CareBuyedExtDto>> useScopeRes = dmscloudServiceFeign.careBuyedUseScopeList(useScopeList);
            List<CareBuyedExtDto> careBuyedUseScopeList = useScopeRes.getData();
            if(!org.springframework.util.CollectionUtils.isEmpty(careBuyedUseScopeList)){
                Map<String, CareBuyedExtDto> useScopeMap = careBuyedUseScopeList.stream().collect(Collectors.toMap(e -> e.getCareBuyedId().toString(), Function.identity(), (e1, e2) -> e1));
                records.forEach(careBuyedVo -> {
                    if(Objects.nonNull(careBuyedVo.getId())){
                        Optional.ofNullable(useScopeMap.get(careBuyedVo.getId().toString())).ifPresent(extDto -> {
                            careBuyedVo.setUseScope(extDto.getUseScope().toString());
                            careBuyedVo.setUseScopeName(extDto.getUseScopeName());
                            careBuyedVo.setSaleDealer(extDto.getSaleDealer());
                            careBuyedVo.setSaleDealerName(extDto.getSaleDealerName());
                        });
                    }
                });
            }
        }
    }

    @Override
    public void exportCareBoughtData(CareBoughtQueryDTO queryCareBoughtDTO){
        dmscusReportFeign.exportCareBoughtData(queryCareBoughtDTO);
    }
}
