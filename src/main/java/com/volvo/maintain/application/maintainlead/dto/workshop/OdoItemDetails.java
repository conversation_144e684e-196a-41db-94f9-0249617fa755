package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OdoItemDetails {

    /**
     * 行号
     */
    private String lineNo;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商家商品编码
     */
    private String spGoodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 原商品编码
     */
    private String oldSpGoodsNo;

    /**
     * 是否替代链
     */
    private Integer isReplace;

    /**
     * oscc item 中添加采购明细主键id 用来更新
     */
    private Long id;
}
