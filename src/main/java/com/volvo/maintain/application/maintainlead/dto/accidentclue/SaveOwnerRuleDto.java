package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 事故线索-分配规则配置表
 */
@ApiModel(description = "事故线索-分配规则配置新增")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaveOwnerRuleDto {
	/**
	 * 分配规则集合
	 */
	private List<OwnerRuleDto> allotList;

	/**
	 * 消息配置集合
	 */
	private List<OwnerRuleDto> messageList;
}