package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BadgeCountSummaryDto {

    private MissingPartsStatusDto missingPartsStatus;

    private BookingStatusDto bookingStatus;

    private VehicleEntranceCountDto vehicleEntranceCount;
}
