package com.volvo.maintain.application.maintainlead.dto.coupon;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 卡券列表
 * 张善龙
 * 2024.1.11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("卡券查询")
public class QueryCouponDetailInfo1Dto implements Serializable {
    private static final long serialVersionUID = 1L;

    private QueryCouponDetailInfoDto data;

}
