package com.volvo.maintain.application.maintainlead.dto.warrantyApproval;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@ApiModel(value = "延保理赔申请列表查询DTO", description = "延保理赔申请列表查询DTO")
@Data
public class ClaimApplyPageReqDTO {

    @ApiModelProperty("当前页")
    private Long currentPage;

    @ApiModelProperty("每页显示条数（每次最大不能超过5000条）")
    private Long pageSize;

    @ApiModelProperty("经销商代码")
    private String ownerCode;

    @ApiModelProperty("工单号")
    private String roNo;

    @ApiModelProperty("报案号")
    private String caseNo;

    @ApiModelProperty("vin")
    private String vin;

    @ApiModelProperty("审批状态多选")
    private List<Integer> approvalStatusList;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("终审通过时间开始，格式：yyyy-MM-dd HH:mm:ss")
    private Date finalApprovalDateBegin;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("终审通过时间截止，格式：yyyy-MM-dd HH:mm:ss")
    private Date finalApprovalDateEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("出险日期开始，格式：yyyy-MM-dd HH:mm:ss")
    private Date faultTimeBegin;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("出险日期截止，格式：yyyy-MM-dd HH:mm:ss")
    private Date faultTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("出险日期开始，格式：yyyy-MM-dd HH:mm:ss")
    private Date receivedStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("出险日期截止，格式：yyyy-MM-dd HH:mm:ss")
    private Date receivedEndtime;

    @ApiModelProperty("承保渠道")
    private  Integer underwritingChannel;
}