package com.volvo.maintain.application.maintainlead.service.whitelistManagement.impl;

import com.volvo.maintain.application.maintainlead.dto.white.CheckWhiteListDto;
import com.volvo.maintain.application.maintainlead.service.whitelistManagement.WhitelistCheckService;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainAuthFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.white.CheckWhiteListVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import static com.volvo.maintain.infrastructure.constants.CommonConstant.WECOM_ACCIDENT_LEAD_CODE;
import static com.volvo.maintain.infrastructure.constants.CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE;

@Service
@Slf4j
public class WhitelistCheckServiceImpl implements WhitelistCheckService {

    @Autowired
    private DomainMaintainAuthFeign domainMaintainAuthFeign;

    @Override
    public List<CheckWhiteListDto> checkWhitelist(CheckWhiteListVo checkWhiteListVo) {

        validateCheckWhiteListVo(checkWhiteListVo);

        Integer rosterType = Optional.ofNullable(checkWhiteListVo.getRosterType())
                .orElse(WECOM_ACCIDENT_ROSTER_TYPE_WHITE);

        validateRosterType(rosterType);

        DmsResponse<Boolean> enabledResponse = domainMaintainAuthFeign.checkIfWhitelistEnabled(checkWhiteListVo.getModType());
        if (Objects.isNull(enabledResponse) || enabledResponse.isFail()) {
            throw new ServiceBizException("查询白名单开关失败");
        }

        Boolean enable = enabledResponse.getData();
        return determineWhiteListStatus(checkWhiteListVo, enable, rosterType);
    }

    private void validateCheckWhiteListVo(CheckWhiteListVo checkWhiteListVo) {
        if (Objects.isNull(checkWhiteListVo)) {
            throw new ServiceBizException("查询白名单参数异常");
        }
        if (Objects.isNull(checkWhiteListVo.getModType())) {
            throw new ServiceBizException("查询白名单类型异常");
        }
        if (CollectionUtils.isEmpty(checkWhiteListVo.getOwnerCodes())) {
            throw new ServiceBizException("查询白名单经销商参数异常");
        }
    }

    private void validateRosterType(Integer rosterType) {
        if (!WECOM_ACCIDENT_ROSTER_TYPE_WHITE.equals(rosterType) && !WECOM_ACCIDENT_LEAD_CODE.equals(rosterType)) {
            throw new ServiceBizException("查询白/黑名单经销商参数异常");
        }
    }

    private List<CheckWhiteListDto> determineWhiteListStatus(CheckWhiteListVo checkWhiteListVo, Boolean enable, Integer rosterType) {
        if (Objects.isNull(enable)) {
            // 未配置白名单
            return checkWhiteListVo.getOwnerCodes()
                    .stream()
                    .map(ownerCode -> new CheckWhiteListDto(ownerCode, Boolean.FALSE))
                    .collect(Collectors.toList());
        } else if (enable) {
            // 启用白名单
            return getWhiteListedDealers(checkWhiteListVo, rosterType);
        } else {
            // 全网开放
            return checkWhiteListVo.getOwnerCodes()
                    .stream()
                    .map(ownerCode -> new CheckWhiteListDto(ownerCode, Boolean.TRUE))
                    .collect(Collectors.toList());
        }
    }

    private List<CheckWhiteListDto> getWhiteListedDealers(CheckWhiteListVo checkWhiteListVo, Integer rosterType) {
        DmsResponse<List<String>> dealerResponse = domainMaintainAuthFeign.getWhitelistedDealers(checkWhiteListVo.getModType(), rosterType);
        if (Objects.isNull(dealerResponse) || dealerResponse.isFail()) {
            throw new ServiceBizException("查询白名单列表失败");
        }

        List<String> dealers = Optional.ofNullable(dealerResponse.getData()).orElse(Lists.newArrayList());
        return checkWhiteListVo.getOwnerCodes()
                .stream()
                .map(ownerCode -> new CheckWhiteListDto(ownerCode, dealers.contains(ownerCode)))
                .collect(Collectors.toList());
    }
}
