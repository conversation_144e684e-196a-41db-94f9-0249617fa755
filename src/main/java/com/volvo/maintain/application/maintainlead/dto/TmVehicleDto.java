package com.volvo.maintain.application.maintainlead.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <p>
 * 车辆表 tm_vehicle
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "TmVehicleDTO对象", description = "车辆表 tm_vehicle")
public class TmVehicleDto {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id", required = true)
    private Long id;

    @ApiModelProperty(value = "客户信息表主键")
    private Long customerId;

    @ApiModelProperty(value = "公司代码")
    private String companyCode;

    @ApiModelProperty(value = "所有者的父组织代码（用于二网使用）")
    private String ownerParCode;

    @ApiModelProperty(value = "经销商代码   原产品OWNER_CODE")
    private String dealerCode;

    @ApiModelProperty(value = "VIN")
    private String vin;

    @ApiModelProperty(value = "发动机号")
    private String engineNo;

    @ApiModelProperty(value = "制造日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productDate;

    @ApiModelProperty(value = "出厂日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date factoryDate;

    @ApiModelProperty(value = "品牌代码")
    private String brandId;

    @ApiModelProperty(value = "车系代码")
    private String seriesId;

    @ApiModelProperty(value = "车型代码id")
    private String modelId;

    @ApiModelProperty(value = "车型代码")
    private String modelCode;
    @ApiModelProperty(value = "配置代码")
    private String configId;

    @ApiModelProperty(value = "颜色")
    private String colorId;

    @ApiModelProperty(value = "内饰ID")
    private String trimId;

    private String productId;

    @ApiModelProperty(value = "里程")
    private Integer mileage;

    @ApiModelProperty(value = "开票时间（保修起始日期）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date invoiceDate;

    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    @ApiModelProperty(value = "钥匙编号")
    private String keyNo;

    @ApiModelProperty(value = "产地")
    private String productingArea;

    @ApiModelProperty(value = "国Ⅲ、国Ⅳ、国Ⅲ+OBD、欧Ⅲ、欧Ⅳ、欧Ⅳ+OBD、国Ⅲ欧Ⅳ、国V")
    private Integer dischargeStandard;

    @ApiModelProperty(value = "排气量")
    private String exhaustQuantity;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否有合格证")
    private Integer hasCertificate;

    @ApiModelProperty(value = "合格证号")
    private String certificateNumber;

    @ApiModelProperty(value = "合格证存放地")
    private String certificateLocus;

    @ApiModelProperty(value = "是否")
    private Integer oemTag;

    @ApiModelProperty(value = "是，否")
    private Integer isDirect;

    @ApiModelProperty(value = "代收经销商姓名")
    private String collectingDealer;

    @ApiModelProperty(value = "仓库代码")
    private String storageId;

    @ApiModelProperty(value = "库位代码")
    private String storagePositionCode;

    @ApiModelProperty(value = "库存状态(1413)")
    private Integer ownStockStatus;

    @ApiModelProperty(value = "配车状态(1414)")
    private Integer dispatchedStatus;

    @ApiModelProperty(value = "是否锁定")
    private Integer isLock;

    @ApiModelProperty(value = "运损状态")
    private Integer trafficMarStatus;

    @ApiModelProperty(value = "是，否试驾车")
    private Integer isTestDrive;

    @ApiModelProperty(value = "入库类型(1317)")
    private Integer entryType;

    @ApiModelProperty(value = "首次入库日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstStockInDate;

    @ApiModelProperty(value = "最后入库日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date latestStockInDate;

    @ApiModelProperty(value = "最后入库人")
    private String lastStockInBy;

    @ApiModelProperty(value = "出库类型(1318)")
    private Integer deliveryType;

    @ApiModelProperty(value = "首次出库日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstStockOutDate;

    @ApiModelProperty(value = "最后出库日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date latestStockOutDate;

    @ApiModelProperty(value = "最后出库人")
    private String lastStockOutBy;

    @ApiModelProperty(value = "车辆配置代码")
    private String vsn;

    @ApiModelProperty(value = "含税采购价")
    private Double purchasePrice;

    @ApiModelProperty(value = "车厂销售指导价")
    private Double oemDirectivePrice;

    @ApiModelProperty(value = "销售指导价")
    private Double directivePrice;

    @ApiModelProperty(value = "批售指导价格")
    private Double wholesaleDirectivePrice;

    @ApiModelProperty(value = "采购日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date purchaseDate;

    @ApiModelProperty(value = "是否外借")
    private Integer isBorrowed;

    @ApiModelProperty(value = "保养手册号")
    private String warrantyManualNo;

    @ApiModelProperty(value = "数据来源的系统代码")
    private String sourceSystem;

    @ApiModelProperty(value = "APP_ID")
    private String appId;

    @ApiModelProperty(value = "保险开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuranceStartDate;

    @ApiModelProperty(value = "首选 经销商")
    private String firstDealer;

    @ApiModelProperty(value = "保险开始日期来源")
    private String insuranceStartDateSource;

    @ApiModelProperty(value = "保险开始日期页面是否可编辑")
    private Integer insuranceStartDatePageCanBeEdited;

    @ApiModelProperty(value = "车主上报销售日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ownerReportedSalesDate;

    @ApiModelProperty(value = "品牌代码")
    private String brandCode;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "品牌英文名称")
    private String brandNameEn;

    @ApiModelProperty(value = "配置代码 车款代码")
    private String configCode;

    @ApiModelProperty(value = "配置名称 车款名称")
    private String configName;

    @ApiModelProperty(value = "配置名称英文名 车款英文名")
    private String configNameEn;

    @ApiModelProperty(value = "配置年份 年款")
    private String configYear;

    @ApiModelProperty(value = "变速箱")
    private String transMission;

    @ApiModelProperty(value = "水货车(0:否 1:是)")
    private Integer wwCar;

    @ApiModelProperty(value = "行驶证号")
    private String licenseNo;

    @ApiModelProperty(value = "上牌日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date licenseDate;

    @ApiModelProperty(value = "保修结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date invoiceEndDate;
    @ApiModelProperty(value = "车系名称")
    private String seriesName;
    @ApiModelProperty(value = "车型名称")
    private String modelName;
    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "老DMS-普通销售")
    private String commonTypeOfSale;

    private  String name;


    /**
     * 日平均行驶里程
     */
    private BigDecimal dailyAverageMileage;

    private String modelYear;

    private Integer vehicleAge;

    @ApiModelProperty(value = "車色圖片(縮略圖)")
    private String colorSmallImage;

    @ApiModelProperty(value = "車色圖片(完整圖片)")
    private String colorBigImage;

    @ApiModelProperty(value = "PC圖片")
    private String colorPcImage;

    @ApiModelProperty(value = "直售PC端圖片")
    private String colorDsPcImage;

    @ApiModelProperty(value = "icup车型0 其他， 1 为 ICUP，2为 TSP")
    private Integer icupType;

}
