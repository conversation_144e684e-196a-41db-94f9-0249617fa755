package com.volvo.maintain.application.maintainlead.dto.warrantyApproval;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "延保理赔申请审批历史DTO", description = "延保理赔申请审批历史DTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClaimApplyApproveRecordRespDTO {

    @ApiModelProperty("审批状态中文")
    private String approvalStatusStr;

    @ApiModelProperty("审批时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalDate;

    @ApiModelProperty("审批备注")
    private String remark;

    @ApiModelProperty("授权金额")
    private BigDecimal approvalAmount;

}