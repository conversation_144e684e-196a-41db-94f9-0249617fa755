package com.volvo.maintain.application.maintainlead.dto.faultlight;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class FaultLightTopicDto implements Serializable {

    private static final long serialVersionUID = -2323526899508074845L;
    /**修改时间*/
    private Date updateTime;
    /**crmId*/
    private Long id;
    /**线索跟进状态*/
    private String followUpStatus;
    /**线索完成状态*/
    private String bizStatus;
    /**线索验证状态*/
    private String verifyStatus;
    /**线索类型*/
    private String leadsType;
    /**经销商*/
    private String dealerCode;
}
