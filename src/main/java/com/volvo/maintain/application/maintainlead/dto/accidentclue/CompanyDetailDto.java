package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyDetailDto {

    @ApiModelProperty(value = "售后大区ID")
    private Long    afterBigAreaId;

    @ApiModelProperty(value = "售后大区名称")
    private String  afterBigAreaName;

    @ApiModelProperty(value = "售后小区ID")
    private Long    afterSmallAreaId;

    @ApiModelProperty(value = "售后小区名称")
    private String  afterSmallAreaName;

    private Long  oneId;

    private String ownerName;

    private String ownerMobile;

    /**
     * 跟进人名称
     */
    private String followPeopleName;

    /**
     *   跟进人员id
     */
    private Long followPeople;

    /**
     * 规则id
     */
    private Long ownerRuleId;

    private String modelId;

    private String modelName;

    private String modelYear;
}
