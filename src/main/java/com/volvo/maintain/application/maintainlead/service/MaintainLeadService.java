package com.volvo.maintain.application.maintainlead.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.InvitationFollowParamsDto;
import com.volvo.maintain.application.maintainlead.dto.InviteVehicleRecordDetailDto;
import com.volvo.maintain.application.maintainlead.dto.maintainLead.MaintainLeadParamsDto;

/**
 * 功能描述：养修线索接口
 *
 * <AUTHOR>
 * @since 2023-11-03
 */
public interface MaintainLeadService {
    /**
     * 功能描述：查询邀约线索列表
     *
     * @param paramDto 养修线索dto对象
     * @return IPage<MaintainLeadDto> 养修线索列表
     */
    Page<MaintainLeadParamsDto> getInviteVehicleRecord(InvitationFollowParamsDto paramDto);

    /**
     * 功能描述：保存跟进记录
     *
     * @param param 查询邀约跟进记录查询dto
     * @return int  返回保存结果
     */
    int saveInviteVehicleRecord(InviteVehicleRecordDetailDto param);
}
