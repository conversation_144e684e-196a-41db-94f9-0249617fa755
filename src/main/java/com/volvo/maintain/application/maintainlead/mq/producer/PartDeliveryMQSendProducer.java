package com.volvo.maintain.application.maintainlead.mq.producer;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.volvo.maintain.application.maintainlead.dto.partdelivery.PartDeliveryQuantityDTO;
import com.volvo.maintain.application.maintainlead.dto.partdelivery.QueryBuyPartInfoRepDTO;
import com.volvo.maintain.application.maintainlead.dto.workshop.PartBuyItemDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.SynPurchaseDetailsDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 消息发送
 */
@Component
@Slf4j
public class PartDeliveryMQSendProducer {

    @Autowired
    @Qualifier("tocTransparent")
    private RocketMQTemplate rocketMQTemplate;
    
    public void asycSendPartDeliveryMQMsg(PartDeliveryQuantityDTO partDeliveryQuantity){
        try {
            log.info("asycSendPartDeliveryMQMsg:{}", JSON.toJSONString(partDeliveryQuantity));
            asycSendPartDeliveryMQMsg(JSON.toJSONString(partDeliveryQuantity), "PART_DELIVERY_QUANTITY", null);
            log.info("asycSendPartDeliveryMQMsg end ");
        }catch (Exception e){
            log.error("asycSendPartDeliveryMQMsg 异常",e);
        }
    }
    
    //异步发送消息
    public void asycSendPartDeliveryMQMsg(String msg,String topic, String tag){
        log.info("asycSendPartDeliveryMQMsg :{}",msg);
    	try {
            rocketMQTemplate.asyncSend(org.apache.commons.lang3.StringUtils.join(Lists.newArrayList(topic, tag),":"), new Message<String>() {
                @Override
                public String getPayload() {
                    return msg;
                }
                @Override
                public MessageHeaders getHeaders() {
                    HashMap<String, Object> headers = Maps.newHashMap();
                    return new MessageHeaders(headers);
                }
            }, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("pushMessage,消息投递成功,result:{}", JSON.toJSONString(sendResult));
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("sendLucencyWorkshopMsg,消息投递失败:{}", throwable);
                }
            });
        } catch (Exception e) {
            log.error("sendLucencyWorkshopMsg,推送消息失败:{}", e);
        }
    }

	public void sendOsccPartDeliveryMQMsg(List<String> val, String ownerCode, String purchaseNo) {
		log.info("OSCC : {}, ownerCode: {}, partNos: {}", purchaseNo, ownerCode, JSON.toJSONString(val));
		try {			
			List<QueryBuyPartInfoRepDTO> collect = val.stream().filter(Objects::nonNull).map(obj -> {
				QueryBuyPartInfoRepDTO queryBuyPartInfoRepDTO = new QueryBuyPartInfoRepDTO();
				queryBuyPartInfoRepDTO.setPartNo(obj);
				queryBuyPartInfoRepDTO.setStorageCode("OEMK");
				return queryBuyPartInfoRepDTO;
			}).collect(Collectors.toList());
			
			PartDeliveryQuantityDTO partDeliveryQuantity = new PartDeliveryQuantityDTO();
			partDeliveryQuantity.setType("84271002");
			partDeliveryQuantity.setOwnerCode(ownerCode);
			partDeliveryQuantity.setPurchaseNo(purchaseNo);
			partDeliveryQuantity.setPartDetails(collect);
			asycSendPartDeliveryMQMsg(partDeliveryQuantity);
		} catch (Exception e) {
			log.info("发送OSCC 在途异常", e);
		}
	}

	public void sendEnterRecordPartDeliveryMQMsg(DmsResponse<List<SynPurchaseDetailsDto>> response, String ownerCode,
			String stockInNo, List<PartBuyItemDto> dms_table, String stockInType) {
		try {
			log.info("sendEnterRecordPartDeliveryMQMsg : {}, ownerCode: {}, partNos: {} , stockInType: {}", JSON.toJSONString(response), ownerCode, JSON.toJSONString(dms_table), stockInType);
			
			List<QueryBuyPartInfoRepDTO> collect = dms_table.stream().filter(Objects::nonNull).map(obj->{
				QueryBuyPartInfoRepDTO queryBuyPartInfoRepDTO = new QueryBuyPartInfoRepDTO();
				queryBuyPartInfoRepDTO.setPartNo(obj.getPartNo());
				queryBuyPartInfoRepDTO.setStorageCode(obj.getStorageCode());
				return queryBuyPartInfoRepDTO;
			}).collect(Collectors.toList());
			String purchaseNo = null;
			if(Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getData())) {
				List<SynPurchaseDetailsDto> data = response.getData();
				Optional<SynPurchaseDetailsDto> findFirst = data.stream().filter(Objects::nonNull).filter(obj->StringUtils.isNotBlank(obj.getPurchaseNo())).findFirst();
				if(findFirst.isPresent()) {
					SynPurchaseDetailsDto synPurchaseDetails = findFirst.get();
					if(StringUtils.isNotBlank(synPurchaseDetails.getPurchaseNo())) {
						purchaseNo = synPurchaseDetails.getPurchaseNo();
					}
				}
				
			}
			PartDeliveryQuantityDTO partDeliveryQuantity = new PartDeliveryQuantityDTO();
			partDeliveryQuantity.setType("84271002");
			if(Objects.equals(stockInType, "80881010") || Objects.equals(stockInType, "80881011")) {
				partDeliveryQuantity.setType("84271001");				
			}
			partDeliveryQuantity.setOwnerCode(ownerCode);
			partDeliveryQuantity.setPurchaseNo(purchaseNo);
			partDeliveryQuantity.setStockInNo(stockInNo);
			partDeliveryQuantity.setPartDetails(collect);
			asycSendPartDeliveryMQMsg(partDeliveryQuantity);
		} catch (Exception e) {
			log.info("发送签收单入参 在途异常", e);
		}
	}
}
