package com.volvo.maintain.application.maintainlead.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 延保购买记录信息数据VO
 *
 * @Author:Zhou<PERSON><PERSON>
 * @version $id:ExtWarPurgiveDataVO.java,v0.1 2020年8月18日 下午4:07:35 ZhouJie Exp$
 */
@ApiModel(value = "延保购买记录信息数据VO")
@Data
public class ExtWarPurgiveDataVO implements Serializable {

	private static final long	serialVersionUID	= 3660027820154603521L;

	@ApiModelProperty(name = "dealerCode", value = "经销商编号", required = true)
	private String				dealerCode;

	@ApiModelProperty(name = "effectiveDate", value = "延保生效日期", required = true)
	private String				effectiveDate;

	@ApiModelProperty(name = "expireDate", value = "延保失效日期", required = true)
	private String				expireDate;

	@ApiModelProperty(name = "insuranceOrderNo", value = "保单号", required = true)
	private String				insuranceOrderNo;

	@ApiModelProperty(name = "licensePlateNum", value = "车牌号", required = true)
	private String				licensePlateNum;

	@ApiModelProperty(name = "productName", value = "延保零件名称(产品名称)", required = true)
	private String				productName;

	@ApiModelProperty(name = "productNo", value = "延保零件号(产品件号)", required = true)
	private String				productNo;
	
	@ApiModelProperty(name = "warrantyComponents", value = "保修部件", hidden = true)
	private String warrantyComponents;

	@ApiModelProperty(name = "extensionType", value = "延保类型（81501001：普通延保产品 81501002：新二手车延保 81501003：Evcar延保）", required = true)
	private String				extensionType;

	@ApiModelProperty(name = "provider", value = "延保提供方（81541001：PICC 81541002：易保）", required = true)
	private String				provider;

	@ApiModelProperty(name = "numberOfYears", value = "延保年数(库里存放年限数字，不存枚举)", required = true)
	private String				numberOfYears;

	@ApiModelProperty(name = "purchaseDate", value = "延保购买时间", required = true)
	private String				purchaseDate;

	@ApiModelProperty(name = "sellDate", value = "车辆销售日期", required = true)
	private String				sellDate;

	@ApiModelProperty(name = "updatedTime", value = "延保单更新日期", required = true)
	private String				updatedTime;

	@ApiModelProperty(name = "vin", value = "车架号", required = true)
	private String				vin;

	@ApiModelProperty(value = "产品大类(业务类型): 83441001:车险 83441002:非车险", name = "bizType")
	private Integer		bizType;
	/**
	 * 扩展表信息 add by wlj 20230724
	 */
	@ApiModelProperty(value = "订单编号")
	private String orderCode;
	@ApiModelProperty(value = "支付时间")
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date payTime;
	@ApiModelProperty(value = "状态")
	private int giveStatus;
	@ApiModelProperty(value = "渠道")
	private int giveChannel;
	@ApiModelProperty(value = "扩展字段,用于记录车辆里程、车型、开票日期等等")
	private String tocExtend;
	@ApiModelProperty(value = "店端名称")
	private String dealerName;
	@ApiModelProperty(value = "经销商代码")
	private String ownerCode;

	/**
	 * 延保购买记录来源
	 */
	@ApiModelProperty(value = "延保购买记录来源(81561001：店端自购    81561002：厂端赠送)")
	private Integer source;

	private List<TireInfoVo> tireInfoVos;

	private Long id;

	/**
	 * '类型 1:左前轮;2:右前轮;3:左后轮;4:右后轮;'
	 */
	private Integer itemType;

	/**
	 * 品牌
	 */
	private String  brand;

	/**
	 * 规格
	 */
	private String specs;

	/**
	 * 序列号
	 */
	private String sequence;

	/**
	 * 文件地址
	 */
	private String fileId;


	private String orderNo;
	
	@ApiModelProperty(name = "allowSelection", value = "排序")
	private String allowSelection;
	
	@ApiModelProperty(name = "sort", value = "排序", hidden = true)
	private Integer sort;
}
