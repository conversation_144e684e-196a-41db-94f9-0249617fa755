package com.volvo.maintain.application.maintainlead.dto.part;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "查询是否缺料dto", description = "PartDto")
public class PartDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "零件号", hidden = true)
    private String partNo;// 零件号
    @ApiModelProperty(value = "配件名称", hidden = true)
    private String partName;// 零件名称
    @ApiModelProperty(value = "仓库代码", hidden = true)
    private String storageCode;// 仓库代码
    @ApiModelProperty(value = "工单号", hidden = true)
    private String roNo;// 工单号
    @ApiModelProperty(value = "所有者名称", hidden = true)
    private String ownerName;// 所有者名称
    @ApiModelProperty(value = "车牌号", hidden = true)
    private String license;// 车牌号
    @ApiModelProperty(value = "车主手机号", hidden = true)
    private String phone; // 车主手机号
    @ApiModelProperty(value = "A新增 U修改或 D删除 S未操作", hidden = true)
    private String itemUpdateStatus;//A新增 U修改或 D删除 S未操作
    @ApiModelProperty(value = "零件明细id", hidden = true)
    private Long itemId;
    /*
     * app传入，总数量
     * */
    @ApiModelProperty(value = "总数量", hidden = true)
    private BigDecimal practicalQuantity;
    @ApiModelProperty(value = "锁定用户", hidden = true)
    private String lockUser; // 锁定用户
}