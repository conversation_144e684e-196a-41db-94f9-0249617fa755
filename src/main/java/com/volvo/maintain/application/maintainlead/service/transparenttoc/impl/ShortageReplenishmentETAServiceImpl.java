package com.volvo.maintain.application.maintainlead.service.transparenttoc.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.volvo.annotation.DistributedLock;
import com.volvo.maintain.application.maintainlead.service.transparenttoc.ShortageReplenishmentETAService;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.DomainPurchaseFeign;
import com.volvo.maintain.infrastructure.gateway.request.ETAOperationParamsDTO;
import com.volvo.maintain.infrastructure.gateway.request.UpdateETAMappingDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ETAMappingReturnDTO;
import com.volvo.maintain.infrastructure.gateway.response.PartPurchaseOrderDetaileDTO;
import com.volvo.maintain.infrastructure.gateway.response.ShortPartDto;

import lombok.extern.slf4j.Slf4j;


@Service
@Slf4j
public class ShortageReplenishmentETAServiceImpl implements ShortageReplenishmentETAService {
	
	@Resource
	private DomainPurchaseFeign domainPurchaseClient;
	
	@Resource
	private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
	
	@Override
	@DistributedLock(prefix = "updateETAMapping:lock", key = {"#etaOperationParams.ownerCode","#etaOperationParams.partNo"}, delimiter = "-", timeout = 1, timeUnit = TimeUnit.MINUTES)
	public boolean updateETAMapping(ETAOperationParamsDTO etaOperationParams) {
		log.info("updateETAMapping: etaOperationParams: {}", JSON.toJSONString(etaOperationParams));
		purchaseLinkedDataHandle(etaOperationParams);
		
		// 场景变更
		DmsResponse<Boolean> etaSceneHandle = domainPurchaseClient.etaSceneHandle(etaOperationParams);
		if(Objects.isNull(etaSceneHandle) || etaSceneHandle.isFail()) {
			return false;
		}
		
		// 查询采购单
		DmsResponse<List<ETAMappingReturnDTO>> result = domainPurchaseClient.queryEtaByOnwerCodeAndPartNo(etaOperationParams.getOwnerCode(), etaOperationParams.getPartNo());
		if(Objects.isNull(result) || result.isFail()) {
			return etaSceneHandle.getData();
		}
		List<ETAMappingReturnDTO> queryEtaByOnwerCodeAndPartNo = result.getData();
		// 过滤采购单数据为空的数据
		List<ETAMappingReturnDTO> collect = queryEtaByOnwerCodeAndPartNo.stream().filter(Objects::nonNull).filter(obj-> Objects.nonNull(obj.getPurchaseOrderDetailId())).map(obj->{
			obj.setIsLinked(10041002);
			return obj;
		}).collect(Collectors.toList());
		log.info("动态匹配数据关联：{}", JSON.toJSONString(collect));
		if (CollectionUtils.isEmpty(collect)) {
			return true;
		}
		UpdateETAMappingDTO updateETAMappingDTO = new UpdateETAMappingDTO();
		updateETAMappingDTO.setEtaMappingReturnList(collect);
		// 更新缺料表数据
		domainMaintainOrdersFeign.updateShortETAMappingLinked(updateETAMappingDTO);
		return true;
	}

	/**
	 * 处理采购单关联数据
	 * @param etaOperationParams
	 */
	private void purchaseLinkedDataHandle(ETAOperationParamsDTO etaOperationParams) {
		List<String> list = Arrays.asList("4");
		if(!list.contains(etaOperationParams.getSceneCode())) {
			return;
		}
		if("10041001".equals(etaOperationParams.getIsLinked()) && Objects.isNull(etaOperationParams.getShortId())) {			
			// 查询采购单零件数据
			BigDecimal partQuantity = etaOperationParams.getPartQuantity();
			// 查询缺料单数据
			DmsResponse<List<ShortPartDto>> queryShortInfoBySheetNo = domainMaintainOrdersFeign.queryShortInfoBySheetNo(etaOperationParams.getOwnerCode(), etaOperationParams.getSheetNo(), etaOperationParams.getPartNo(), 0);
			if(Objects.nonNull(queryShortInfoBySheetNo) && queryShortInfoBySheetNo.isSuccess()) {
				List<ShortPartDto> data = queryShortInfoBySheetNo.getData();
				// 更新关联数据
				List<ShortPartDto> collect = data.stream().filter(Objects::nonNull).filter(obj->Objects.isNull(obj.getIsLinked()) || obj.getIsLinked()==10041002).collect(Collectors.toList());
				Optional<ShortPartDto> findFirst = collect.stream().filter(obj->partQuantity.compareTo(obj.getShortQuantity())==0).findFirst();
				if(!findFirst.isPresent()) {
					log.info("进入大于区域");
					findFirst = collect.stream().filter(obj->partQuantity.compareTo(obj.getShortQuantity())>0).findFirst();
				}
				if(!findFirst.isPresent()) {
					log.info("进入小于区域");
					findFirst = collect.stream().findFirst();
				}
				if(findFirst.isPresent()) {
					ShortPartDto shortPartDto = findFirst.get();
					Long purchaseOrderDetailId = shortPartDto.getPurchaseOrderDetailId();
					if(Objects.nonNull(purchaseOrderDetailId)) {
						try {
							Set<Long> set = new HashSet<>();
							set.add(purchaseOrderDetailId);
							DmsResponse<List<PartPurchaseOrderDetaileDTO>> queryPurchaseOrderById = domainPurchaseClient.queryPurchaseOrderById(set);
							PartPurchaseOrderDetaileDTO partPurchaseOrderDetaile = queryPurchaseOrderById.getData().get(0);
							Long id = partPurchaseOrderDetaile.getId();
							Long newPurchaseOrderDetailId = shortPartDto.getPurchaseOrderDetailId();
							// 说明此数据处于异常关联状态 不做更新反向关联处理
							if(Objects.nonNull(partPurchaseOrderDetaile) && Objects.nonNull(partPurchaseOrderDetaile.getPartsStatus()) && Objects.equals(newPurchaseOrderDetailId, id)) {
								return;
							}
						} catch (Exception e) {
							log.info("校验数据异常：", e);
						}
					}
					Long shortId = shortPartDto.getShortId();
					// 更新缺料明细数据
					List<ETAMappingReturnDTO> shortPartLinked = new ArrayList<>();
					ETAMappingReturnDTO etaMappingReturn = new ETAMappingReturnDTO();
					etaMappingReturn.setShortId(shortId);
					etaMappingReturn.setIsLinked(10041001);
					etaMappingReturn.setPurchaseOrderDetailId(etaOperationParams.getPurchaseOrderDetailId());
					shortPartLinked.add(etaMappingReturn);
					if(Objects.isNull(etaOperationParams.getPurchaseOrderDetailId())) {
						return;
					}
					log.info("请求更新数据反向关联入参：{}", JSON.toJSONString(shortPartLinked));
					UpdateETAMappingDTO updateETAMappingDTO = new UpdateETAMappingDTO();
					updateETAMappingDTO.setEtaMappingReturnList(shortPartLinked);
					DmsResponse<Boolean> updateShortETAMappingLinked = domainMaintainOrdersFeign.updateShortETAMappingLinked(updateETAMappingDTO);
					if(Objects.nonNull(queryShortInfoBySheetNo) && updateShortETAMappingLinked.isSuccess()) {						
						BigDecimal shortQuantity = shortPartDto.getShortQuantity();
						BigDecimal setScale = shortQuantity.setScale(0, RoundingMode.CEILING);
						BigDecimal subtract = partQuantity.subtract(setScale);
						etaOperationParams.setPartQuantity(subtract.compareTo(BigDecimal.ZERO)<=0?BigDecimal.ZERO:subtract);
						etaOperationParams.setLinkedPartQuantity(subtract.compareTo(BigDecimal.ZERO)<=0?partQuantity:setScale);
						etaOperationParams.setShortId(shortId);
					}					
				}
			}
		}
	}
	
}
