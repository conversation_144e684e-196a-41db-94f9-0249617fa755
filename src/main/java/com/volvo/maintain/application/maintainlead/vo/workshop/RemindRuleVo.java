package com.volvo.maintain.application.maintainlead.vo.workshop;

import com.volvo.maintain.infrastructure.annotation.ExcelExportColumnAnno;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 提醒规则返回数据
 * @Date 2024/11/13 15:31
 */
@ApiModel("提醒规则返回数据")
@Data
public class RemindRuleVo {

    @ApiModelProperty("规则id")
    private Integer id;

    @ApiModelProperty("提醒类型")
    @ExcelExportColumnAnno(columName = "businessType", columDesc = "提醒类型")
    private String businessType;

    @ApiModelProperty("是否启用")
    @ExcelExportColumnAnno(columName = "isEnabled", columDesc = "是否启用")
    private String isEnabled;

    @ApiModelProperty("提醒规则")
    @ExcelExportColumnAnno(columName = "remindRule", columDesc = "提醒规则")
    private String remindRule;

    @ApiModelProperty("运算符号")
    @ExcelExportColumnAnno(columName = "operationSymbol", columDesc = "运算符号")
    private String operationSymbol;

    @ApiModelProperty("值")
    @ExcelExportColumnAnno(columName = "remindValue", columDesc = "值")
    private String remindValue;

    @ApiModelProperty("提醒时间")
    @ExcelExportColumnAnno(columName = "remindTime", columDesc = "提醒时间")
    private String remindTime;

    @ApiModelProperty("提醒角色")
    @ExcelExportColumnAnno(columName = "remindRole", columDesc = "提醒角色")
    private String remindRole;

    @ApiModelProperty("提醒人员")
    @ExcelExportColumnAnno(columName = "remindUserName", columDesc = "提醒人员")
    private String remindUserName;

}
