package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/03/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("地区全量数据")
public class RegionAllDto {

    // id
    private Integer id;
    // 地区id
    private Integer regionId;
    // 地区code
    private String regionCode;
    // 地区名称
    private String regionName;
    // 地区英文名称
    private String regionEname;
    // 父地区id
    private String parentRegionId;
    // 子节点
    private List<RegionAllDto> tcRegionList;

}
