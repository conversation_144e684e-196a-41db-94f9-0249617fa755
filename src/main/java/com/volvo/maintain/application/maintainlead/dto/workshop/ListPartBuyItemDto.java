package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 此dto用于同步零件状态 同步到
 */
@Data
public class ListPartBuyItemDto {
    private String stockInNo;	//入库单号
    private String customerCode;	//业务客户编号
    private String customerName;	//业务客户名称
    private Integer stockInType;	//入库单类型
    private String vvInNo;	//VV单号
    private String orderDate;	//开单日期
    private String deliveryNo;	//货运单号
    private Integer stockInStatus;	//单据状态
    private BigDecimal beforeTaxAmount;	//入库金额
    private Integer stockInPronum;	//入库项数
    private String approveUser;	//审批人
    private String approveTime;	//审批时间
    private String approveMsg;	//审批意见
    private String allocateReason;	//调拨原因
    private String delItems;  //删除记录itemId;以,隔开
    private String selectItems; //勾选需入账记录ITEMID
    private String reserveItems; //勾选需做预留记录ITEMID

    private List<PartBuyItemDto> dms_table;  //表格数据

    private String reasonType; //原因选择

    private String reason; //原因

    private String inOutSource; //出入库类型（APP/手工）

    private String ownerCode;
}

