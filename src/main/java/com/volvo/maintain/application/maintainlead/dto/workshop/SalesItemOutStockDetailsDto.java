package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SalesItemOutStockDetailsDto {

    /**
     * 销售订单编号
     */
    private String salesNo;

    /**
     * 行号
     */
    private String lineNo;

    /**
     * 企业商品编码
     */
    private String goodsNo;

    /**
     * 货主商品编码
     */
    private String ownerGoodsNo;

    /**
     * 商家商品编码
     */
    private String spGoodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 缺货明细单号
     */
    private String outstockNo;

    /**
     * 到总仓时间
     */
    private String cdcTime;

    /**
     * ETA时间
     */
    private String etaTime;

    private String etaTimeStr;

    /**
     * 采购订单明细主键ID;
     */
    private Long id;
}
