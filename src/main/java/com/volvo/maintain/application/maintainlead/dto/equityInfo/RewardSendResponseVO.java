package com.volvo.maintain.application.maintainlead.dto.equityInfo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;

/**
 * 用户清单列表出参
 *
 * <AUTHOR>
 * @email
 * @date 2023-08-14
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class RewardSendResponseVO {

    /**
     *用户是否有终身保养权益信息
     */
    private List<RewardSendVO> list;

}
