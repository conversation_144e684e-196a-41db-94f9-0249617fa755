package com.volvo.maintain.application.maintainlead.service.whitelistManagement;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.company.DataInputVo;
import com.volvo.maintain.application.maintainlead.dto.white.WhiteListDto;
import com.volvo.maintain.interfaces.vo.white.VehicleHealthCheckWhiteListVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface WhitelistManagementService {


    Page<VehicleHealthCheckWhiteListVo> selectWhitelist(String modType, String itemCode, String isDeleted, int currentPage, int pageSize);

    List<VehicleHealthCheckWhiteListVo> selectWhitelistDetail(String itemCode, String rosterType);

    int insertWhitelist(WhiteListDto whiteListDto);

    int toggleWhitelistActivation(String itemCode, String isDeleted, String rosterType);

    List<DataInputVo> vehicleOrPhoneImport(String modType, String vehicleOrPhone, MultipartFile importFile);

    /**
     * 根据类型查询存在的经销商白名单
     * @param vo 参数白名单类型，白名单 0 roseType
     * @return 返回该名单单类型的经销商
     */
    List<String> queryWhiteListByParams(VehicleHealthCheckWhiteListVo vo);

    /**
     * 检验白名单是否存在
     * @param modType 白名单类型
     * @return true / false
     */
    Boolean checkIfWhitelistEnabled(Integer modType);
}
