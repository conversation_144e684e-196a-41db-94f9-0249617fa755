package com.volvo.maintain.application.maintainlead.vo.healthcheck;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@ApiModel("检查单详情")
@Data
public class VehicleHealthVo {

    @ApiModelProperty(value = "检查单信息")
    private VehicleHealthInfoVo vehicleHealthInfo;

    @ApiModelProperty(value = "检查项目list")
    private List<VehicleHealthItemVo> itemPOList;


}
