package com.volvo.maintain.application.maintainlead.dto.clues;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class InsuranceLeadsTransparencyQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 当前页
     */
    private Long currentPage;

    /**
     * 多少页
     */
    private Long pageSize;

    /**
     * 大区ID 逗号隔开
     */
    private String afterBigareaId;

    /**
     * 小区ID 逗号隔开
     */
    private String afterSmallareaId;

    /**
     * 经销商代码
     */
    private List<String>  ownerCode;

    /**
     * vin
     */
    private String  vin;

    /**
     * 车牌号
     */
    private String  licensePlate;

    /**
     * 保险到期日期起
     */
    private String  insuranceExpiryDateStart;

    /**
     * 保险到期日期止
     */
    private String  insuranceExpiryDateEnd;

    /**
     * 跟进时间起
     */
    private String  followUpTimeStart;

    /**
     * 跟进时间止
     */
    private String  followUpTimeEnd;

    /**
     * 续保客户类型
     */
    private String  renewCustomerType;

    /**
     * c  - 厂  d  - 店
     */
    private String  type;
}
