package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2024/03/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("取送车订单信息")
public class VehicleOrderResponseDtoDto {
    // id
    private Integer id;
    // 取送车订单code
    private String orderCode;
    // e代驾订单id
    private String orderId;

    //经销商code
    private String dealerCode;

    // 经销商名称
    private String dealerName;

    // 车架号
    private String vin;

    // 车牌号
    private String carNo;
}
