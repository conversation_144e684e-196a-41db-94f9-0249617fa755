package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(description = "请求")
public class RequestDto<T> implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "请求数据")
	private T data;

	@ApiModelProperty(value = "当前页")
	private Long				page;

	@ApiModelProperty(value = "每页条数")
	private Long				pageSize;

}
