package com.volvo.maintain.application.maintainlead.service.strategy.clues;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.ClueDataDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadOperationResultDto;
import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueResDto;
import com.volvo.maintain.application.maintainlead.emums.ClueStrategyEnum;
import com.volvo.maintain.application.maintainlead.service.AccidentCluesService;
import com.volvo.maintain.application.maintainlead.service.FullLeadsService;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class AccidentClueStrategy extends AbsClueExecuteStrategy {

    @Autowired
    private AccidentCluesService accidentCluesService;

    @Autowired
    private FullLeadsService fullLeadsService;

    @Override
    protected ClueStrategyEnum strategyEnum() {
        return ClueStrategyEnum.ACCIDENT_CLUE;
    }

    @Override
    protected boolean validate(LeadOperationResultDto dto) {
        if(Objects.isNull(dto.getDataStatus())){
            log.info("validate dto.getDataStatus() is null");
            return false;
        }
        boolean validated = Objects.nonNull(dto) && StringUtils.isNotBlank(dto.getVehicleVin()) &&
                StringUtils.isNotBlank(dto.getSourceClueId()) &&
                StringUtils.isNotBlank(dto.getLeadsType()) &&
                StringUtils.isNotBlank(dto.getLeadsReceiveTime()) &&
                Objects.nonNull(dto.getData()) && dto.getId() != null &&
                Objects.nonNull(dto.getData().getCarInfo());
        String insuranceCompanyCode = dto.getData().getAccidentInfo().getInsuranceCompanyCode();
        if (!(StringUtils.isNotBlank(insuranceCompanyCode) && ClueStrategyEnum.ACCIDENT_CLUE_TB.getCode().equals(insuranceCompanyCode))) {
            validated = validated && CollectionUtils.isNotEmpty(dto.getData().getContactInfo());
        }
        return validated;
    }

    @Override
    protected boolean handleIdempotency(Long id) {
        // 查询是否存在线索
        DmsResponse<AccidentCluesDto> cluesResult = domainMaintainLeadFeign.queryCluesExistsByCrmId(id);
        if (cluesResult.isFail()) {
            throw new ServiceBizException("调用领域线索失败！");
        }
        return Objects.nonNull(cluesResult.getData());
    }

    @Override
    protected boolean checkWhitelist(LeadOperationResultDto dto,ClueStrategyEnum strategyEnum, String dealer) {
        //原检查逻辑
        boolean checked = super.checkWhitelist(strategyEnum, dealer);
        //追加渠道白名单检查逻辑
        if(checked){
            String insuranceCompanyCode = dto.getData().getAccidentInfo().getInsuranceCompanyCode();
            if (StringUtils.isNotBlank(insuranceCompanyCode)) {
                ClueStrategyEnum clueChannelStrategyEnum = Optional.ofNullable(ClueStrategyEnum.getStrategy(insuranceCompanyCode)).orElse(ClueStrategyEnum.ACCIDENT_CLUE_PA);
                checked = super.checkWhitelist(clueChannelStrategyEnum, dealer);
            }
        }
        return checked;
    }
    @Override
    protected DistributeClueResDto distributeClues(LeadOperationResultDto dto) {
        log.info("accidentClue distributeClues:");
        String dealer = this.getDealer(dto);
        CompanyDetailByCodeDto dealerInfo = accidentCluesService.getDealerInfo(dealer);
        if(Objects.nonNull(dealerInfo)){
            log.info("distributeClues dealerInfo:{}", JSON.toJSONString(dealerInfo));
            ClueDataDto.AddressInfo addressInfo = dto.getData().getAddressInfo();
            if(Objects.isNull(addressInfo)){
                addressInfo = new ClueDataDto.AddressInfo();
                dto.getData().setAddressInfo(addressInfo);
            }
            addressInfo.setProvinceId(String.valueOf(dealerInfo.getProvinceId()));
            addressInfo.setProvinceName(dealerInfo.getProvinceName());
            addressInfo.setCityId(String.valueOf(dealerInfo.getCityId()));
            addressInfo.setCityName(dealerInfo.getCityName());
        }
        DmsResponse<String> domainResponse = domainMaintainLeadFeign.insertAccidentClues(dto);
        log.info("事故线索下发响应:{}", JSON.toJSONString(domainResponse));
        if (domainResponse.isFail() || StringUtils.isBlank(domainResponse.getData())) {
            log.error("调用领域线索失败");
            return new DistributeClueResDto(Boolean.FALSE,null,null);
        }
        //续保线索跟进报表记录
        /*try{
            fullLeadsService.buildRenewalLeadStaticDto(Collections.singletonList(dto.getId()));
        }catch (Exception e){
            log.error("续保线索跟进报表记录失败",e);
        }*/
        return new DistributeClueResDto(Boolean.TRUE,null,domainResponse.getData());
    }

    @Override
    protected boolean postProcess(LeadOperationResultDto dto) {
        return accidentCluesService.clueDistributePostProcess(dto);
    }

}
