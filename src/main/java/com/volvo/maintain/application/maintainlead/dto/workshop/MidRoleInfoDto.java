package com.volvo.maintain.application.maintainlead.dto.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 中台角色集合信息
 * @Date 2024/11/15 10:50
 */

@Data
@ApiModel("查询自定义信息入参")
@Builder
@AllArgsConstructor
public class MidRoleInfoDto {

    @ApiModelProperty(value = "数据来源（厂端：10451002，店面：10451001，自定义：10451003）")
    private Integer dataSource;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色id")
    private String roleId;

    @ApiModelProperty("角色代码")
    private String roleCode;
}
