package com.volvo.maintain.application.maintainlead.service.protectingCustomersMarket.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.customermarketing.OwnerInfoDTO;
import com.volvo.maintain.application.maintainlead.dto.inviteClue.InvitationScripDto;
import com.volvo.maintain.application.maintainlead.dto.order.ShopVehicleDeliverDto;
import com.volvo.maintain.application.maintainlead.service.OrderService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CdpTagInfoService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CustomerService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.InvitationService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.TagInfoService;
import com.volvo.maintain.application.maintainlead.service.protectingCustomersMarket.ProtectingCustomersMarketService;
import com.volvo.maintain.application.maintainlead.vo.order.ShopVehicleDeliverVo;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.enums.PickDeliverCarOrderStatusEnum;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndVehicleCenterFeign;
import com.volvo.maintain.infrastructure.gateway.response.CdpResponse;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.util.DateUtil;
import com.volvo.maintain.interfaces.vo.PurchaseIntentionVo;
import com.volvo.maintain.interfaces.vo.TagInfoVo;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.HandlerInterceptor;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ProtectingCustomersMarketServiceImpl implements ProtectingCustomersMarketService {
    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Autowired
    private CdpTagInfoService cdpTagInfoService;

    @Autowired
    private TagInfoService tagInfoService;

    @Autowired
    private InvitationService invitationService;

    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
    @Autowired
    private HandlerInterceptor getUserRestInterceptor;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CustomerService customerService;
    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;

    /**
     * 查询是否存在换购意向
     *
     * @param vin             车架号
     * @param delivererMobile 送修人手机号
     * @param roNo            工单号
     */
    @Override
    public List<PurchaseIntentionVo> queryPurchaseIntention(String vin, String delivererMobile, String roNo) {
        // 是否主工单且没有意向 也包含工单号为空也是true
        if (!isMasterAndExchangePurchaseValid(vin, delivererMobile, roNo)) {
            return Collections.emptyList();
        }

        //判断vin是否有取车订单，如果有则直接返回空（取送车不弹窗）
        if (whetherHavePickDeliverCarOrder(vin)) {
            return Collections.emptyList();
        }

        //获取系统配置 修改为 查询标签数据，标签数据是提前写好的。
        List<TagInfoVo> tagInfoList = queTagInfo();
        if (CollectionUtils.isEmpty(tagInfoList)) {
            log.info("Tag info list is null or empty");
            return Collections.emptyList();
        }
        Map<Integer, String> tagIdMap = groupTagIdsByIsTag(tagInfoList);
        CdpCheckInSegmentsBaseByAuthDto customerTagsDto = cdpTagInfoService.checkInSegmentsBaseByAuth(delivererMobile, tagIdMap.getOrDefault(CommonConstant.DICT_IS_NO, ""));
        if (ObjectUtils.isEmpty(customerTagsDto) || !customerTagsDto.getIn_any_segment()) {
            log.info("customerTagsDto tags info is null or empty");
            return Collections.emptyList();
        }
        // 用来获取话术逻辑配置的，
        CommonConfigDto talkSkillWeightConfigDto = queryCommonConfigDto(CommonConstant.TALKSKILL_WEIGHT_KEY, CommonConstant.CUSTOMERMARKETING);
        if (ObjectUtils.isEmpty(talkSkillWeightConfigDto) || StringUtils.isBlank(talkSkillWeightConfigDto.getDmsDefault())) {
            log.info("talkSkillWeightConfigDto tags info is null or empty");
            return Collections.emptyList();
        }
        //cdp返回的群组 和10041001的匹配上，（10041001是valueRule 中的tagId） 拿到tagIdList 去查询话术
        List<PurchaseIntentionVo> purchaseIntentionVoList = createPurchaseIntentionVo(tagIdMap.getOrDefault(CommonConstant.DICT_IS_YES, ""), customerTagsDto.getIn_segment_list());
        List<String> tagIds = purchaseIntentionVoList.stream().map(PurchaseIntentionVo::getTagId).collect(Collectors.toList());
        log.info("purchaseIntentionVoList size: {}", JSONObject.toJSONString(tagIds));
        Map<String, List<String>> tagMap = tagInfoList.stream().filter(e -> e.getIsTag() == CommonConstant.DICT_IS_YES).filter(e -> {
            TagValueRuleDto tagValueRuleDto = JSONObject.parseObject(e.getValueRule(), TagValueRuleDto.class);
            List<String> innerTagIdSet = tagValueRuleDto.getShowRule().getConvert().getAnd().stream().map(TagValueRuleDto.andOrDto::getTagId).filter(tagIds::contains).collect(Collectors.toList());
            if (!innerTagIdSet.isEmpty()) {
                e.setInnerTagId(innerTagIdSet.get(0));
                return true;
            }
            return false;
        }).collect(Collectors.groupingBy(TagInfoVo::getInnerTagId,Collectors.mapping(TagInfoVo::getTagId,Collectors.toList())));
        List<String> outterTagIds = tagMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
        log.info("filterTagIdList size: {}", JSONObject.toJSONString(outterTagIds));
        //查询话术标签集合  用tagIds + 类型等于1000 （保客营销） 查到匹配的保客营销数据。
        List<InvitationScripDto> scripList = invitationService.scriptManageQuery(outterTagIds, CommonConstant.BUSINESSTYPE);
        //将  e.setInnerTagId 赋值给 List<InvitationScripDto> scripList  里面的 tagId；
        // 遍历scripList，查找每个customerTagId对应的tagMap中的key
        for (InvitationScripDto scrip : scripList) {
            String customerTagId = scrip.getCustomerTagIds();
            for (Map.Entry<String, List<String>> entry : tagMap.entrySet()) {
                if (entry.getValue().contains(customerTagId)) {
                    scrip.setTagId(entry.getKey());
                }
            }
        }
        log.info("scripList size: {}", JSONObject.toJSONString(scripList));
        // 这一步的作用是，cdp返回的群组 和10041001的匹配上，（10041001是valueRule 中的tagId） 所以用purchaseIntentionVoList去反匹配标签找到tagId。
        List<TagInfoVo> filteredPurchaseIntention = filterDataObjects(Arrays.stream(customerTagsDto.getIn_segment_list()).collect(Collectors.toList()), tagInfoList);
        if (CollectionUtils.isEmpty(filteredPurchaseIntention)) {
            // 车型群组都未命中则没有购车意向 无需弹窗  (得到的最终理由)
            log.info("filteredPurchaseIntention is null or empty No intention");
            return Collections.emptyList();
        }
        // 如果 cdp 返回 EM90和三90 车型群组 默认拿TAG100和TAG101去命中转译标签规则
        tagTranslation(filteredPurchaseIntention, tagInfoList, outterTagIds);
        List<OrderTagSnapshotDto> orderTagSnapshotRecord = queryOrderTagSnapshotList(delivererMobile, null);
        // 把话术构建进去 返回客户端
        return createPurchaseIntentionVos(filteredPurchaseIntention, scripList, orderTagSnapshotRecord);
    }

    /**
     * 判断vin是否有取车订单,如果有则返回空（取送车不弹窗）
     *
     * 查询参数：
     * vin 车架号
     * dealerCode 经销商
     * createTimeStart 创建时间
     * createTimeEnd 创建时间
     * type 82711001 取送车订单类型 - 取车
     * orderStatusList 订单状态(不等于订单取消)
     */
    private boolean whetherHavePickDeliverCarOrder(String vin) {
        //1. 参数封装
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime startDateTime = currentDateTime.minusDays(15).with(LocalTime.MIN);
        List<Integer> orderStatusList = PickDeliverCarOrderStatusEnum.getAllCodes()
                .stream()
                //过滤已取消、已关闭
                .filter(code -> ObjectUtil.notEqual(code, PickDeliverCarOrderStatusEnum.ORDER_CANCELLED.getCode())
                        && ObjectUtil.notEqual(code, PickDeliverCarOrderStatusEnum.ORDER_CLOSED.getCode()))
                .collect(Collectors.toList());

        ShopVehicleDeliverDto shopVehicleDeliverDto = ShopVehicleDeliverDto.builder()
                .vin(vin)
                // 开始日期:当前日期减去15天的0点
                .createTimeStart(DateUtil.convertToDate(startDateTime))
                // 结束日期：当前日期
                .createTimeEnd(DateUtil.convertToDate(currentDateTime))
                .companyCode(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode())
                .orderStatusList(orderStatusList)
                // 取送车订单类型 82711001 取车
                .type(CommonConstant.PICK_UP_CAR_CODE)
                .build();

        RequestDto<ShopVehicleDeliverDto> requestDto = new RequestDto<>();
        requestDto.setData(shopVehicleDeliverDto);
        requestDto.setPage(CommonConstant.VEHICLE_ORDER_PAGE.longValue());
        requestDto.setPageSize(CommonConstant.VEHICLE_ORDER_PAGE.longValue());
        log.info("取送车不弹窗 requestDto: {}", JSONObject.toJSONString(requestDto));

        //2. 查询
        Page<ShopVehicleDeliverVo> page = orderService.shopSearchVehiicleDeliverPage(requestDto);

        //3. 有记录，不弹窗
        return ObjectUtil.isNotEmpty(page) && ObjectUtil.isNotEmpty(page.getRecords());
    }

    /**
     * 匹配车型和理由的关系，用tt_tag_config.detail_tag_pid进行维护
     *
     * @param result 最终结果
     * @return 对最终结果 匹配关系返给客户端
     */
    private List<PurchaseIntentionVo> matchCarAndReason(List<PurchaseIntentionVo> result, List<OrderTagSnapshotDto> orderTagSnapshotRecord) {
        if (CollectionUtils.isEmpty(result)) {
            log.info("final result matchCarAndReason requestParams is null or empty return default data！");
            return Collections.emptyList();
        }
        return mergeTags(result, orderTagSnapshotRecord);
    }

    public List<PurchaseIntentionVo> mergeTags(List<PurchaseIntentionVo> result, List<OrderTagSnapshotDto> orderTagSnapshotRecord) {
        Map<String, PurchaseIntentionVo> tagMap = result.stream()
                .collect(Collectors.toMap(
                        PurchaseIntentionVo::getTagId,
                        vo -> {
                            // 防止修改 原对象
                            PurchaseIntentionVo newVo = new PurchaseIntentionVo();
                            newVo.setTagId(vo.getTagId());
                            newVo.setTagName(vo.getTagName());
                            newVo.setTagValue(vo.getTagValue());
                            newVo.setShowType(vo.getShowType());
                            if (CollectionUtils.isNotEmpty(vo.getRecommendedCarModels())) {
                                newVo.setRecommendedCarModels(vo.getRecommendedCarModels());
                            }
                            newVo.setReasonsForRecommendation(vo.getReasonsForRecommendation());
                            if (CollectionUtils.isNotEmpty(vo.getFollowHistory())) {
                                newVo.setFollowHistory(vo.getFollowHistory());
                            }
                            newVo.setRecommendedScript(vo.getRecommendedScript());
                            return newVo;
                        },
                        (vo1, vo2) -> {
                            // 合并逻辑
                            if (CollectionUtils.isNotEmpty(vo2.getReasonsForRecommendation())) {
                                vo1.setReasonsForRecommendation(vo2.getReasonsForRecommendation());
                            }
                            if (CollectionUtils.isNotEmpty(vo2.getRecommendedCarModels())) {
                                vo1.setRecommendedCarModels(vo2.getRecommendedCarModels());
                            }
                            if (CollectionUtils.isNotEmpty(vo2.getRecommendedScript())) {
                                vo1.setRecommendedScript(vo2.getRecommendedScript());
                            }
                            return vo1;
                        }
                ));
        log.info("final result mergeTags tagMap: {}", JSONObject.toJSONString(tagMap));
        return optimizeMethod(orderTagSnapshotRecord, tagMap);
    }

    private List<PurchaseIntentionVo> optimizeMethod(List<OrderTagSnapshotDto> orderTagSnapshotRecord, Map<String, PurchaseIntentionVo> tagMap) {
        List<PurchaseIntentionVo> finalResult = tagMap.values().stream()
                .filter(vo -> CollectionUtils.isNotEmpty(vo.getReasonsForRecommendation()) || CollectionUtils.isNotEmpty(vo.getRecommendedCarModels()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(finalResult)) {
            log.info("finalResult is null or empty return default data");
            return Collections.emptyList();
        }
        // 车辆群组都满足空 直接返回 不弹窗
        boolean modelGroupFlag = finalResult.stream()
                .allMatch(vo -> CollectionUtils.isEmpty(vo.getRecommendedCarModels()) && (Objects.equals(35291001,vo.getShowType())));
        if (modelGroupFlag) {
            log.info("finalResult modelGroupFlag is null or empty return default data");
            return Collections.emptyList();
        }
        finalResult.get(0).setFollowHistory(orderTagSnapshotRecord);
        return finalResult.stream()
                .filter(v -> CollectionUtils.isNotEmpty(v.getReasonsForRecommendation()) || CollectionUtils.isNotEmpty(v.getRecommendedCarModels()))
                .sorted(Comparator.comparingInt((PurchaseIntentionVo p) -> p.getReasonsForRecommendation().size()).reversed())
                .collect(Collectors.toList());
    }

    private void tagTranslation(List<TagInfoVo> filteredPurchaseIntention, List<TagInfoVo> tagInfoList, List<String> outterTagIds) {
        List<TagInfoVo> matchingTags = tagInfoList.stream()
                .filter(tag -> tag.getTagId().contains("TAG100") || tag.getTagId().contains("TAG101"))
                .collect(Collectors.toList());
        filteredPurchaseIntention.addAll(matchingTags);

        List<TagInfoVo> escapeShowName = tagInfoList.stream()
                .filter(tagInfo -> CommonConstant.DICT_IS_YES == tagInfo.getIsTag())
                .filter(tagInfo -> outterTagIds.contains(tagInfo.getTagId()))
                .collect(Collectors.toList());
        Map<String, TagInfoVo> tagDataMap = escapeShowName.stream().collect(Collectors.toMap(TagInfoVo::getTagId, Function.identity(), (k1, k2) -> k2));
        // 转译规则。
        for (TagInfoVo item : escapeShowName) {
            TagValueRuleDto tagValueRuleDto = JSONObject.parseObject(item.getValueRule(), TagValueRuleDto.class);
            if (ObjectUtils.isNotEmpty(tagValueRuleDto)) {
                tagInfoService.handleValueRule(tagValueRuleDto, item, tagDataMap);
            } else {
                item.setShowType(CommonConstant.TEXT_SHOW_TYPE);
            }
        }
        log.info("final tagTranslation escapeShowName: {}", JSONObject.toJSONString(escapeShowName));
        ObjectMapper mapper = new ObjectMapper();
        //key valueRule中的tagId  赋值转义后的showName
        Map<String, TagInfoVo> innerTagMap  = escapeShowName.stream().collect(Collectors.toMap(e -> extractTagIds(e, mapper), Function.identity(), (e1, e2) -> e1));
        filteredPurchaseIntention.forEach(e -> Optional.ofNullable(innerTagMap.get(e.getTagId())).ifPresent(e1 -> e.setShowName(e1.getShowName())));
        log.info("final tagTranslation innerTagMap: {}", JSONObject.toJSONString(innerTagMap));
        filteredPurchaseIntention.addAll(escapeShowName);
        log.info("final tagTranslation filteredPurchaseIntention: {}", JSONObject.toJSONString(filteredPurchaseIntention));
    }

    /**
     * build filteredPurchaseIntention
     *
     * @param filteredPurchaseIntention 反查所有的标签匹配中的结果
     * @return 返回构建的对象，
     */
    public List<PurchaseIntentionVo> createPurchaseIntentionVos(List<TagInfoVo> filteredPurchaseIntention, List<InvitationScripDto> matchedScripts, List<OrderTagSnapshotDto> orderTagSnapshotRecord) {
        if (CollectionUtils.isEmpty(filteredPurchaseIntention)) {
            //一个车型群组（tagType = marketing_group and detail_tag_pid = 0）都未命中则没有购车意向，此时无需弹框
            log.info("filteredPurchaseIntention is null or empty");
            return null;
        }
        Map<String, List<String>> tagIdToTalkSkillsMap = matchedScripts.stream()
                .collect(Collectors.groupingBy(InvitationScripDto::getTagId,
                        Collectors.mapping(InvitationScripDto::getTagTalkskill, Collectors.toList())));
        List<PurchaseIntentionVo> result = new ArrayList<>();
        for (TagInfoVo tagInfo : filteredPurchaseIntention) {
            PurchaseIntentionVo purchaseIntentionVo = new PurchaseIntentionVo();
            purchaseIntentionVo.setTagId(tagInfo.getTagId());
            purchaseIntentionVo.setTagName(CommonConstant.PROTECTING_CUSTOMER_MARKET_TAG_NAME);
            purchaseIntentionVo.setTagValue("true");
            purchaseIntentionVo.setDetailTagPid(tagInfo.getDetailTagPid());
            if (StringUtils.isNotEmpty(tagInfo.getValueRule())) {
                //获取值规则
                TagValueRuleDto tagValueRuleDto = JSONObject.parseObject(tagInfo.getValueRule(), TagValueRuleDto.class);
                purchaseIntentionVo.setShowType(tagValueRuleDto.getShowType());
            }

            if ("0".equals(tagInfo.getDetailTagPid())) {
                purchaseIntentionVo.setRecommendedCarModels(Collections.singletonList(tagInfo.getShowName()));
            } else {
                purchaseIntentionVo.setReasonsForRecommendation(Collections.singletonList(tagInfo.getShowName()));
            }
            List<String> recommendedScripts = tagIdToTalkSkillsMap.getOrDefault(tagInfo.getTagId(), Collections.emptyList());
            purchaseIntentionVo.setRecommendedScript(recommendedScripts);
            result.add(purchaseIntentionVo);
        }
        log.info("createPurchaseIntentionVos result is " + JSONObject.toJSONString(result));

        result.forEach(intention -> {
            List<String> reasons = filteredPurchaseIntention.stream()
                    .filter(tagInfo -> intention.getTagId().equals(tagInfo.getDetailTagPid()))
                    .map(TagInfoVo::getShowName)
                    .collect(Collectors.toList());
            intention.setReasonsForRecommendation(reasons);
        });
        log.info("matching carModel and reason result is " + JSONObject.toJSONString(result));
        result.forEach(script -> {
            if (CollectionUtils.isEmpty(script.getRecommendedScript())) {
                CommonConfigDto commonConfigDto = queryCommonConfigDto(script.getTagId(), CommonConstant.CUSTOMERMARKETING_MODEL);
                String dmsDefault = Optional.ofNullable(commonConfigDto)
                        .map(CommonConfigDto::getDmsDefault)
                        .orElse("");

                if (!dmsDefault.isEmpty()) {
                    script.setRecommendedScript(Collections.singletonList(dmsDefault));
                }
            }
        });
        log.info("matching Script and modelCar result is " + JSONObject.toJSONString(result));
        return matchCarAndReason(result, orderTagSnapshotRecord);
    }


    /**
     * 按照 is_tag 分组。key 为tagId。
     *
     * @param tagInfoList 标签数据
     * @return 返回map  value为tagId拼接的数据。
     */
    private Map<Integer, String> groupTagIdsByIsTag(List<TagInfoVo> tagInfoList) {
        if (CollectionUtils.isEmpty(tagInfoList)) {
            return Collections.emptyMap();
        }
        Map<Integer, List<TagInfoVo>> tagsMap = tagInfoList.stream()
                .collect(Collectors.groupingBy(TagInfoVo::getIsTag));
        Map<Integer, String> tagIdMap = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();
        // 处理 isTag 为 10041002 的情况
        tagIdMap.put(CommonConstant.DICT_IS_NO, tagsMap.getOrDefault(CommonConstant.DICT_IS_NO, Collections.emptyList())
                .stream()
                .map(TagInfoVo::getTagId)
                .collect(Collectors.joining(",")));

        // 处理 isTag 为 10041001 的情况
        List<TagInfoVo> tagsTwo = tagsMap.getOrDefault(CommonConstant.DICT_IS_YES, Collections.emptyList());
        String tagIds = tagsTwo.stream()
                .map(tag -> extractTagIds(tag, mapper))
                .filter(tagId -> !tagId.isEmpty())
                .collect(Collectors.joining(","));
        tagIdMap.put(CommonConstant.DICT_IS_YES, tagIds);
        log.info("groupTagIdsByIsTag tagIdMap is {}", JSONObject.toJSONString(tagIdMap));
        return tagIdMap;
    }


    /**
     * 注意 规则定制，convert节点下 只有一个元素。所以不做其他逻辑处理。
     *
     * @param tag    标签对象
     * @param mapper json转换，
     * @return 返回10041001 规则中的tagId 拼接，。
     */
    private String extractTagIds(TagInfoVo tag, ObjectMapper mapper) {
        try {
            JsonNode root = mapper.readTree(tag.getValueRule());
            JsonNode convertNode = root.path("showRule").path("convert");
            String condition = convertNode.fieldNames().next();
            List<String> tagIdList = new ArrayList<>();
            convertNode.path(condition).elements().forEachRemaining(element ->
                    tagIdList.add(element.path("tagId").asText()));
            return String.join(",", tagIdList);
        } catch (JsonProcessingException e) {
            log.error("extractTagIds", e);
            return "";
        }
    }

    /**
     * query all tagInfo
     *
     * @return 所有的标签信息
     */
    private List<TagInfoVo> queTagInfo() {
        TagInfoDto tagInfoDto = new TagInfoDto();
        tagInfoDto.setTagType("marketing_group");
        tagInfoDto.setCurrentPage(1);
        tagInfoDto.setPageSize(50);
        Page<TagInfoVo> tagInfoVoPage = tagInfoService.queryTagInfo(tagInfoDto);
        return CollectionUtils.isNotEmpty(tagInfoVoPage.getRecords()) ? tagInfoVoPage.getRecords() : Lists.newArrayList();
    }

    /**
     * Determine if it is the main repairOrder And there is no opinion data
     * （确定是否为主工单 且 不存在意向数据）
     *
     * @param vin             the VIN (Vehicle Identification Number)
     * @param delivererMobile the deliverer's mobile number
     * @param roNo            the RO (Repair Order) number
     * @return true if the main repairOrder and no opinion data is valid, false otherwise
     */
    private Boolean isMasterAndExchangePurchaseValid(String vin, String delivererMobile, String roNo) {
        DmsResponse<Boolean> response = dmscloudServiceFeign.judgeIsMasterAndExchangePurchase(vin, delivererMobile, roNo);
        log.info("checkMasterAndExchangePurchaseResponse: {}", JSONObject.toJSONString(response));
        if (response.isFail()) {
            return false;
        }
        return Boolean.TRUE.equals(response.getData());
    }

    /**
     * Creates a list of PurchaseIntentionVo objects based on the provided tagInfoVoList and segmentList.
     * (基于is_tag == 10041001 的数据 与is_tag == 10041002 的数据（02的数据是查询cdp返回的结果）的交集 匹配成功的数据构建对象返回客户端)
     *
     * @param tagInfoVoList The string representation of tagInfoVoList, separated by commas.
     * @param segmentList   An array of strings representing segments.
     * @return A list of PurchaseIntentionVo objects that match both tagInfoVoList and segmentList.
     */
    private List<PurchaseIntentionVo> createPurchaseIntentionVo(String tagInfoVoList, String[] segmentList) {
        if (segmentList == null || segmentList.length == 0 || tagInfoVoList == null || tagInfoVoList.isEmpty()) {
            return Lists.newArrayList();
        }
        // 将 tagInfoVoList 拆分成一个 Set 集合
        Set<String> tagSet = new HashSet<>(Arrays.asList(tagInfoVoList.split(",")));
        // 将 segmentList 转换为一个 Set 集合
        Set<String> segmentSet = new HashSet<>(Arrays.asList(segmentList));
        // 取两个集合的交集（即相同的元素）
        Set<String> commonTags = Sets.intersection(tagSet, segmentSet);
        return commonTags.stream()
                .map(tagId -> {
                    PurchaseIntentionVo vo = new PurchaseIntentionVo();
                    vo.setTagId(tagId);
                    vo.setTagName(CommonConstant.PROTECTING_CUSTOMER_MARKET_TAG_NAME);
                    vo.setTagValue(String.valueOf(true));
                    return vo;
                }).collect(Collectors.toList());
    }


    public List<TagInfoVo> filterDataObjects(List<String> cdpGroupList, List<TagInfoVo> dataObjects) {
        return dataObjects.stream()
                .filter(dataObject -> cdpGroupList.contains(dataObject.getTagId()))
                .collect(Collectors.toList());
    }

    public List<InvitationScripDto> matchScriptByRule(String configKey, List<InvitationScripDto> script, List<String> cdpGroup) {
        if (CollectionUtils.isNotEmpty(script)) {
            List<String> tagInfoList = script.stream().map(InvitationScripDto::getCustomerTagIds).collect(Collectors.toList());
            Set<String> tags = new HashSet<>(tagInfoList);
            Set<String> group = new HashSet<>(cdpGroup);
            switch (configKey) {
                case "A_equals_B":
                    List<String> tagIdRecord = matchAEqualsB(tags, group);
                    log.info("matchScriptByRule is A_equals_B ");
                    return script.stream().filter(s -> tagIdRecord.contains(s.getCustomerTagIds())).collect(Collectors.toList());
                case "A_includes_B":
                    matchAIncludesB(tags, group);
                    return null;
                case "B_includes_A":
                    matchBIncludesA(tags, group);
                    return null;
                default:
                    getDefaultScript();
                    return null;
            }
        }
        return null;
    }

    /**
     * A_equals_B
     *
     * @param tags  标签集合
     * @param group 群组
     * @return 结果话术。
     */
    private List<String> matchAEqualsB(Set<String> tags, Set<String> group) {
        if (tags.equals(group)) {
            return getMatchingScript(tags);
        }
        return getDefaultScript();
    }

    /**
     * A_includes_B
     *
     * @param tags  标签集合
     * @param group 群组
     * @return 结果话术。
     */
    private List<String> matchAIncludesB(Set<String> tags, Set<String> group) {
        if (group.containsAll(tags)) {
            return getMatchingScript(group);
        }
        return getDefaultScript();
    }

    /**
     * B_includes_A
     *
     * @param tags  标签集合
     * @param group 群组
     * @return 结果话术。
     */
    private List<String> matchBIncludesA(Set<String> tags, Set<String> group) {
        if (tags.containsAll(group)) {
            return getMatchingScript(group);
        }
        return getDefaultScript();
    }


    private List<String> getMatchingScript(Set<String> tags) {
        return new ArrayList<>(tags);
    }

    /**
     * 默认方法
     *
     * @return 默认话术
     */
    private List<String> getDefaultScript() {
        List<String> defaultScript = new ArrayList<>();
        defaultScript.add("Default Script");
        return defaultScript;
    }

    /**
     * 获取配置逻辑（A_equals_B，A_includes_B，B_includes_A）
     *
     * @param configKey 配置的key
     * @param groupType 配置的组
     * @return 返回具体的配置逻辑
     */
    public CommonConfigDto queryCommonConfigDto(String configKey, String groupType) {
        if (StringUtils.isEmpty(configKey) || StringUtils.isEmpty(groupType)) {
            return new CommonConfigDto();
        }
        DmsResponse<CommonConfigDto> commonConfigDto = domainMaintainOrdersFeign.queryDmsDefaultParam2(configKey, groupType);
        log.info("queryCommonConfigDtoRequestParams: {}", commonConfigDto);
        if (commonConfigDto.isFail()) {
            throw new ServiceBizException("调用领域工单失败！");
        }
        if (ObjectUtils.isEmpty(commonConfigDto.getData())) {
            return new CommonConfigDto();
        }
        return commonConfigDto.getData();
    }

    /**
     * 根据手机号查询 意向记录
     *
     * @param mobile 手机号
     * @return 意向结果集
     */
    @Override
    public List<OrderTagSnapshotDto> queryOrderTagSnapshotList(String mobile, String vin) {
        DmsResponse<List<OrderTagSnapshotDto>> orderTagSnapshotDmsResponse = domainMaintainOrdersFeign.queryOrderTagSnapshotList(mobile, vin);
        log.info("queryOrderTagSnapshotListRequestParams: {}", JSONObject.toJSONString(orderTagSnapshotDmsResponse));
        if (orderTagSnapshotDmsResponse.isFail()) {
            throw new ServiceBizException("调用领域工单失败！");
        }
        return orderTagSnapshotDmsResponse.getData();
    }

    @Override
    public OwnerInfoDTO queryOwnerInfo(String vin) {
        log.info("queryOwnerInfo start vin:{}", vin);
        if (StringUtils.isBlank(vin)) {
            return null;
        }
        String ownerCode = LoginInfoUtil.getCurrentLoginInfo().getOwnerCode();
        log.info("queryOwnerInfo start ownerCode:{},vin:{}", ownerCode, vin);
        // 根据vin查询最近工单送修人
        DmsResponse<OwnerInfoDTO> orderrsp = domainMaintainOrdersFeign.queryOwnerInfo(ownerCode, vin);
        log.info("queryOwnerInfo orderrsp:{}", orderrsp);
        if (orderrsp.isFail()) {
            log.info("queryOwnerInfo orderrsp isFail");
            return null;
        }
        // 如果存在最近工单联系人则直接进行返回
        OwnerInfoDTO ownerInfo = orderrsp.getData();
        if (null == ownerInfo) {
            log.info("queryOwnerInfo ownerInfo isNull");
            return null;
        }
        // 如果没有查询到车牌号则尝试去车辆中心进行二次查询
        if (StringUtils.isBlank(ownerInfo.getLicense())) {
            log.info("queryOwnerInfo license isBlank");
            ownerInfo.setLicense(getPlateNumber(vin));
        }
        return ownerInfo;
    }

    /**
     * 获取 cdp 车主信息，已废弃
     */
    @Deprecated
    private OwnerInfoDTO getOwnerInfoDTO(String vin) {
        // 如果查询不到尝试查询cdp车主
        CustomerInfoDto customer = customerService.queryCdpCustomer(vin, "v2");
        log.info("queryOwnerInfo customer:{}", customer);
        if (null == customer) {
            log.info("queryOwnerInfo customer isNull");
            return null;
        }
        String mobile = customer.getMobile();
        log.info("queryOwnerInfo mobile:{}", mobile);
        if (StringUtils.isBlank(mobile)) {
            log.info("queryOwnerInfo mobile isBlank");
            return null;
        }
        OwnerInfoDTO ownerInfo = new OwnerInfoDTO();
        ownerInfo.setDelivererMobile(mobile);
        ownerInfo.setDeliverer(customer.getCustomerName());
        ownerInfo.setLicense(getPlateNumber(vin));
        // 尝试查询性别和里程
        CdpResponse<List<CustomerTagsDto>> tagrsp = customerService.queryCdpCustomAndTag(vin, Arrays.asList(mobile));
        if (tagrsp.isFail()) {
            log.info("queryOwnerInfo tagrsp isFail");
            return ownerInfo;
        }
        List<CustomerTagsDto> data = tagrsp.getData();
        if (CollectionUtils.isEmpty(data)) {
            log.info("queryOwnerInfo data isEmpty");
            return ownerInfo;
        }
        CustomerTagsDto customerTags = data.get(0);
        if (null == customerTags) {
            log.info("queryOwnerInfo customerTags isNull");
            return ownerInfo;
        }
        List<CdpAttributeListDto> attList = customerTags.getAttList();
        log.info("queryOwnerInfo attList:{}", attList);
        if (CollectionUtils.isEmpty(attList)) {
            log.info("queryOwnerInfo attLisit isEmpty");
            return ownerInfo;
        }
        Map<String, CdpAttributeListDto> attMap = attList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(CdpAttributeListDto::getAttID, Function.identity(), (k1, k2) -> k1));
        if (MapUtils.isEmpty(attMap)) {
            log.info("queryOwnerInfo attMap isEmpty");
            return ownerInfo;
        }
        // 获取用户名称
        CdpAttributeListDto fa = attMap.get("fullname_aftersale");
        if (null != fa) {
            if (StringUtils.isBlank(ownerInfo.getDeliverer())) {
                ownerInfo.setDeliverer(fa.getAttValue());
            }
        }
        // 获取用户性别
        CdpAttributeListDto ga = attMap.get("gender_aftersale");
        if (null != ga) {
            Integer gender = 80121003;
            String attValue = ga.getAttValue();
            if (StringUtils.isNotBlank(attValue)) {
                if (Objects.equals(attValue, "先生")) {
                    gender = 80121001;
                } else {
                    gender = 80121002;
                }
            }
            log.info("queryOwnerInfo attValue:{},gender:{}", attValue, gender);
            ownerInfo.setDelivererGender(gender);
        }
        return ownerInfo;
    }

    private String getPlateNumber(String vin) {
        // 尝试查询车牌号
        DmsResponse<TmVehicleDto> response = midEndVehicleCenterFeign.getVehicleByVIN(vin);
        if (response.isSuccess()) {
            TmVehicleDto vehicle = response.getData();
            if (null != vehicle) {
                return vehicle.getPlateNumber();
            }
        }
        return null;
    }
}
