package com.volvo.maintain.application.maintainlead.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 事故线索vo
 * <AUTHOR>
 * @since 2020-11-18
 */
@Data
@ApiModel("事故线索vo")
public class AccidentCluesOrderVo {

    @ApiModelProperty(name = "进厂经销商", value = "intoDealerCode")
    private String intoDealerCode;

    @ApiModelProperty(name = "进厂时间", value = "intoDealerDate")
    private String intoDealerDate;

    @ApiModelProperty(name = "是否作废(绑定：10041002，作废：10041001)", value = "isDelete")
    private Integer isDelete;

    @ApiModelProperty(name = "是否反结算", value = "isReverse")
    private Integer isReverse;

    @ApiModelProperty(name = "车牌号", value = "license")
    private String license;

    @ApiModelProperty(name = "原维修类型", value = "originalRepairTypeCode")
    private String originalRepairTypeCode;

    @ApiModelProperty(name = "维修类型", value = "repairTypeCode")
    private String repairTypeCode;

    @ApiModelProperty(name = "工单号", value = "roNo")
    private String roNo;

    @ApiModelProperty(name = "工单状态", value = "roStatus")
    private Integer roStatus;

    @ApiModelProperty(name = "vin", value = "vin")
    private String vin;

    @ApiModelProperty(name = "最大时间", value = "maxDay")
    private String maxDay;
}
