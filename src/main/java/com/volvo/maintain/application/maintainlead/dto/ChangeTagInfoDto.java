package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2023/12/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("标签信息入参")
public class ChangeTagInfoDto {
    /**
     * 主键ID
     */
    private Long id;



    /**
     * 标签ID
     */
    private String tagId;


    /**
     * 标签名称
     */
    private String tagName;


    /**
     * 显示名称
     */
    private String showName;
    /**
     * 标签类型
     */
    private String tagType;

    /**
     * 标签来源
     */
    private Integer tagSource;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 是否支持查看详情
     */
    private Integer isGetDetail;

    /**
     * 详情获取类型
     */
    private Integer detailGetType;

    /**
     * 是否是详情标签
     */
    private Integer isDetailTag;

    /**
     * 详情标签父tag_id
     */
    private String detailTagPid;

    /**
     * 展示优先级
     */
    private Integer showLevel;

    /**
     * 板块类型：1/2/3
     */
    private Integer blockType;

    /**
     * 展示一级板块
     */
    private Integer  showFirstBlock;

    /**
     * 展示二级板块
     */
    private Integer  showSecondBlock;


    /**
     * 展示三级板块
     */
    private Integer  showThirdBlock;

    /**
     * 值规则
     */
    private String  valueRule;

    /**
     * 排序
     */
    private Integer showSort;

    /**
     * 排序
     */
    private Integer isTag;

    /**
     * 是否有效
     */
    private Integer isValid;

}
