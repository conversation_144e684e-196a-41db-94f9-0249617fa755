package com.volvo.maintain.application.maintainlead.service.strategy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.volvo.design.strategy.AbstractExecuteStrategy;
import com.volvo.maintain.application.maintainlead.dto.ContractPurchaseGiveDto;
import com.volvo.maintain.application.maintainlead.dto.ExtWarPurgiveDataVO;
import com.volvo.maintain.application.maintainlead.dto.rights.*;

import java.util.List;

public interface RightsExecuteStrategy extends AbstractExecuteStrategy<Object, Object> {


    /**
     * 查询可上架商品列表
     */
    IPage<RightsProductDto> productList(RightsProductDto dto);

    List<GiveStatusSyncDto> giveSync(List<GiveStatusSyncDto> statusSyncDtos);

    /**
     * 产品购买
     */
    void give(ContractPurchaseGiveDto contractPurchaseGiveDto);


    /**
     * C端购买上架产品限制查询接口(新增 通用接口支持延保/保养套餐)
     * @return 根据入参返回结果
     */
    List<PurchaseEligibilityCheckResponseDto> buyValid(PurchaseEligibilityCheckRequestDto purchaseEligibilityCheckRequestDto);

    /**
     * 提供C端退款查询激活明细接口(退款用)
     * @return 根据入参返回结果
     */
    List<OrderActivationDetailsResponseDto> giveActList(OrderActivationDetailsRequestDto orderActivationDetailsRequestDto);


    /**
     * 保养套餐使用明细
     * @return 根据入参返回结果
     */
     List<UsageDetailsResponseDto> useList(UsageDetailsRequestDto usageDetailsDto);

    /**
     * 保养套餐购买记录
     * @return 根据入参返回结果
     */
    IPage<GiveRecordResponseDto> givelist(GiveRecordRequestDto giveRecordRequestDto);
}
