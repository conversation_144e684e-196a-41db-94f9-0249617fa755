package com.volvo.maintain.application.maintainlead.dto.equityInfo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 用户清单列表出参
 *
 * <AUTHOR>
 * @email
 * @date 2023-08-14
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class RewardSendVO {

    /**
     *数据id
     */
    private Long id;
    /**
     * 规则id
     */
    private Long ruleId;
    /**
     *车架号
     */
    private String vin;
    /**
     * 车型代码
     */
    private String modelCode;

    /**
     * 车型名称
     */
    private String modelName;

    /**
     * 年款
     */
    private String configYear;

    /**
     * 配置code
     */
    private String configCode;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 车辆类型(93051001-大客户，93051002-销售部)
     */
    private Integer vehicleType;

    /**
     * 清单状态(1-启用；0-禁用)
     */
    private String rewardStatus;

    /**
     * 发放有效数，无发放记录数据默认0
     */
    private Integer sendNum;

    /**
     *创建时间
     */
    private String createTime;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RewardSendVO that = (RewardSendVO) o;
        return Objects.equals(id, that.id) && Objects.equals(vin, that.vin) && Objects.equals(modelCode, that.modelCode) && Objects.equals(modelName, that.modelName)
                && Objects.equals(configYear, that.configYear) && Objects.equals(configCode, that.configCode) && Objects.equals(configName, that.configName) && Objects.equals(vehicleType,that.vehicleType)
                && Objects.equals(rewardStatus, that.rewardStatus)  && Objects.equals(createTime, that.createTime) && Objects.equals(sendNum, that.sendNum);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, vin, modelCode, modelName, configYear, configCode, configName, vehicleType, rewardStatus, createTime, sendNum);
    }
}
