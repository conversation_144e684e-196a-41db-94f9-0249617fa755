package com.volvo.maintain.application.maintainlead.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.yonyou.cyx.function.utils.jsonserializer.date.JsonSimpleDateTimeDeserializer;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("根据vin获取所有绑定列表")
public class CarOwnerRelationVo {
    
    private Integer id;
    
    private String uid;

    /**
     * 会员id
     */
    private String memberId;

    /**
     * vin码
     */
    private String vinCode;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 会员one_id
     */
    private String oneId;

    /**
     * 操作类型 解绑-0  绑定-1
     */
    private String eventType;

    /**
     * 操作时间	@datetime("yyyy-MM-dd HH:mm:ss")
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateDate;

    /**
     * 车辆每类型首次操作  0-否 1-是
     */
    private boolean isFirst;

    /**
     * 是否车主 0-亲友 1-车主 2-代理人
     */
    private Integer isOwner;

    /**
     * 是否授权卡劵 0-否 1-是
     */
    private Integer isCouponAuth;

    /**
     * 记录创建时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateCreate;

    /**
     * 车型
     */
    private String seriesCode;

    /**
     * 经销商
     */
    private String dealerCode;

    /**
     * 认证类型:1-发票,2-身份证,3-行驶证，4-组织机构代码+vin码
     */
    private Integer authType;

    /**
     * 绑定方式 1后台绑定，2主动绑定
     */
    private Integer bindType;

    /**
     * 删除标识
     */
    private boolean deleted;

    /**
     * 购车方式
     */
    private Integer bindAuthType;

    /**
     * 是否强绑0-否，1-是
     */
    private Integer isForceBinding;

    /**
     * 工单编号
     */
    private String forceBindingNo;

    /**
     * vocId
     */
    private String vocId;

    /**
     * 证件类型
     */
    private String certificateType;
    
    private boolean hasOwner;
}
