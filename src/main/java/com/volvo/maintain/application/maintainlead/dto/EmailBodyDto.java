package com.volvo.maintain.application.maintainlead.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("邮件发送内容")
public class EmailBodyDto {

    @ApiModelProperty(value = "经销商名称", name = "dealerName")
    private String dealerName;
    @ApiModelProperty(value = "经销商代码", name = "dealerCode")
    private String dealerCode;
    @ApiModelProperty(value = "进场时间", name = "entryTime")
    @JsonFormat(pattern="yyyy/MM/dd HH:mm:ss",timezone="GMT+8")
    private Date entryTime;
    @ApiModelProperty(value = "车架号", name = "vin")
    private String vin;
    @ApiModelProperty(value = "车牌号", name = "license")
    private String license;
    @ApiModelProperty(value = "车型",name = "modelCode")
    private String modelCode;
    @ApiModelProperty(value = "车主姓名",name = "ownerName")
    private String ownerName;
    @ApiModelProperty(value = "联系电话",name = "phone")
    private String phone;

    private List<String> dealerList;

    private String adviseDate;

    /**
     * 邮件类型
     */
    private String mailSinceType;

    /**
     * 邮件主题
     */
    private String mailSubject;
}
