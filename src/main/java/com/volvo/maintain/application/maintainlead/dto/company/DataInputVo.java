package com.volvo.maintain.application.maintainlead.dto.company;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * 张善龙
 * 2024.1.11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("导入返回信息")
public class DataInputVo {

    private String vehicleOrPhone;

    /**
     * remark(失败原因)
     */
    private String remark;

    /**
     * 表格行号
     */
    private Integer rowNum;
}
