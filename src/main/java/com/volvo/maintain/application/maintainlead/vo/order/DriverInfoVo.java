package com.volvo.maintain.application.maintainlead.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 商户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "DriverInfoVO 对象", description = "DriverInfoVO")
public class DriverInfoVo{

    @ApiModelProperty(value = "司机工号 ")
    private String driverId;
    @ApiModelProperty(value = "司机手机号 ")
    private String driverPhone;
    @ApiModelProperty(value = "司机姓名 ")
    private String name;
    @ApiModelProperty(value = "司机星级 ")
    private String newLevel;
    @ApiModelProperty(value = "司机图像（小） ")
    private String pictureSmall;
    @ApiModelProperty(value = "司机图像（中） ")
    private String pictureMiddle;
    @ApiModelProperty(value = "司机图像（大） ")
    private String pictureLarge;
    @ApiModelProperty(value = "司机驾龄 ")
    private String year;
    @ApiModelProperty(value = "司机身份证号码 ")
    private String idCard;

}