package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OSCCRequestParamsDto implements Serializable {

    /**
     * 经销商  (eta使用)
     */
    private String outCustomerNo;

    /**
     * 采购单  (eta使用)
     */
    private String nbCode;

    /**
     * 经销商
     */
    private String ownerCode;

    /**
     * 零件号
     */
    private String partNo;

    private Long id;
}
