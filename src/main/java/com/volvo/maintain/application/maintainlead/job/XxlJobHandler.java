package com.volvo.maintain.application.maintainlead.job;


import com.volvo.maintain.application.maintainlead.dto.SettlementDocConfirmCompensateDto;
import com.volvo.maintain.application.maintainlead.service.OrderService;
import com.volvo.maintain.application.maintainlead.service.clues.RenewalOfInsuranceService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.TagInfoService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.application.maintainlead.dto.AccidentCluesFollowStatusChangeTaskDto;
import com.volvo.maintain.application.maintainlead.service.AccidentCluesService;
import com.volvo.maintain.application.maintainlead.service.EM90ServiceLeadService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.BookingRegisterService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.VipCustomService;
import com.volvo.maintain.application.maintainlead.service.workshop.TransparentWorkshopManageService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class XxlJobHandler {

    @Autowired
    private VipCustomService vipCustomService;
    @Autowired
    private EM90ServiceLeadService em90ServiceLeadService;


    @Autowired
    private BookingRegisterService bookingRegisterService;

    @Autowired
    private TransparentWorkshopManageService transparentWorkshopManageService;

    @Qualifier("thread360Pool")
    @Autowired
    private ThreadPoolTaskExecutor thread360Pool;

    public static final String BEGIN_DATE = "beginDate";
    public static final String END_DATE = "endDate";
    public static final String MINUTES = "minutes";
    public static final String BIZNO = "bizNo";

    @Autowired
    private TagInfoService tagInfoService;
    @Autowired
    private AccidentCluesService accidentCluesService;
    @Autowired
    private RenewalOfInsuranceService renewalOfInsuranceService;
    @Autowired
    private OrderService orderService;

    /**
     * VIP进场邮件补推
     */
    @XxlJob("vipEntranceEmail")
    public void vipEntranceEmail() {
        log.info("-------------vipEntranceEmail start------------");
        XxlJobHelper.log("-------------vipEntranceEmail start------------");
        vipCustomService.vipEntranceEmail();
        XxlJobHelper.log("-------------vipEntranceEmail end------------");
        log.info("-------------vipEntranceEmail end------------");
    }

    /**
     * Newbie消息通知：发送短信或推送企微任务给预约专员、服务经理
     */
    @XxlJob("em90BookingOrderPush")
    public void em90BookingOrderPush() {
        log.info("-------------em90BookingOrderPush start------------");
        XxlJobHelper.log("-------------em90BookingOrderPush start------------");
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject= JSON.parseObject(param);
        int beginDate = Objects.nonNull(jsonObject) ? jsonObject.getInteger(BEGIN_DATE) : 50;
        int endDate = Objects.nonNull(jsonObject) ? jsonObject.getInteger(END_DATE) : 15;
        log.info("em90BookingOrderPush beginDate:{},endDate:{}",beginDate,endDate);

        // 代下单
        CompletableFuture.supplyAsync(() -> em90BookingOrderPush(beginDate,endDate), thread360Pool);
        // EM90-上工位待分拨异常提醒
        CompletableFuture.supplyAsync(() -> EM90UpperWorkstationToBeAllocated(beginDate,endDate), thread360Pool);

        XxlJobHelper.log("-------------em90BookingOrderPush end------------");
        log.info("-------------em90BookingOrderPush end------------");
    }

    private String getParamStr(JSONObject jsonObject,String key){
        return Objects.nonNull(jsonObject) ? jsonObject.getString(key) : null;
    }

    private Integer getParamInt(JSONObject jsonObject, String key){
        return Objects.nonNull(jsonObject) ? jsonObject.getInteger(key) : null;
    }


    private Boolean em90BookingOrderPush(int beginDate,int endDate) {
        try {
            bookingRegisterService.em90BookingOrderPush(beginDate,endDate);
        }catch (Exception e){
            log.info("EM90代下单异常",e);
            return false;
        }
        // 返回结果
        return true;
    }

    private Boolean EM90UpperWorkstationToBeAllocated(int beginDate,int endDate) {
        try {
            em90ServiceLeadService.EM90UpperWorkstationToBeAllocated(beginDate,endDate);
        }catch (Exception e){
            log.info("EM90-上工位待分拨异常提醒异常",e);
            return false;
        }
        // 返回结果
        return true;
    }

    /**
     * EM90保养线索提醒
     */
    @XxlJob("EM90MaintenanceClueReminder")
    public void EM90MaintenanceClueReminder() {
        log.info("-------------EM90MaintenanceClueReminder start------------");
        XxlJobHelper.log("-------------EM90MaintenanceClueReminder start------------");
        em90ServiceLeadService.EM90MaintenanceClueReminder();
        XxlJobHelper.log("-------------EM90MaintenanceClueReminder end------------");
        log.info("-------------EM90MaintenanceClueReminder end------------");
    }




    @XxlJob("compensatePush")
    public void compensatePush() {
        log.info("compensateEM90Push start");
        XxlJobHelper.log("compensateEM90Push start");
        String param = XxlJobHelper.getJobParam();
        JSONObject jsonObject= JSON.parseObject(param);
        String minutes = this.getParamStr(jsonObject,MINUTES);
        List<String> bizNo = getDateParam(jsonObject, BIZNO);
        log.info("compensateEM90Push->bizNo:{}:{}",bizNo,minutes);
        em90ServiceLeadService.compensateMessagePush(bizNo,minutes);
        XxlJobHelper.log("compensateEM90Push end");
        log.info("compensateEM90Push end");
    }

    private List<String> getDateParam(JSONObject jsonObject,String key) {
        JSONArray jsonArray = jsonObject.getJSONArray(key);
        //转换成List<String>
        return jsonArray.toJavaList(String.class);
    }




    /**
     * 同步 cdp 标签树
     */
    @XxlJob("syncCDPTagTree")
    public void syncCDPTagTree() {
        log.info("-------------syncCDPTagTree start------------");
        XxlJobHelper.log("-------------syncCDPTagTree start------------");
        tagInfoService.syncCDPTagTree();
        XxlJobHelper.log("-------------syncCDPTagTree end------------");
        log.info("-------------syncCDPTagTree end------------");
    }
    /**
     *  更新线索池 超时未跟进状态
     */
    @XxlJob("syncAccidentCluesTimeOut")
    public void syncAccidentCluesTimeOut() {
    	String jobParam = XxlJobHelper.getJobParam();
    	log.info("applictionSyncAccidentCluesTimeOut--start:{}", jobParam);
    	AccidentCluesFollowStatusChangeTaskDto parseObject = JSON.parseObject(jobParam, AccidentCluesFollowStatusChangeTaskDto.class);
    	if(null != parseObject && parseObject.getRangeTime() != null && parseObject.getConditionTime() != null) {
    		accidentCluesService.updateAccidentCluesTimeOutJob(parseObject);
    	}
    }
    /**
     *  更新线索池 关闭状态
     */
    @XxlJob("syncAccidentCluesClose")
    public void syncAccidentCluesClose() {
    	String jobParam = XxlJobHelper.getJobParam();
    	log.info("applictionSyncAccidentCluesClose--start:{}", jobParam);
    	AccidentCluesFollowStatusChangeTaskDto parseObject = JSON.parseObject(jobParam, AccidentCluesFollowStatusChangeTaskDto.class);
    	if(null != parseObject && parseObject.getRangeTime() != null && parseObject.getConditionTime() != null) {
    		accidentCluesService.updateAccidentCluesCloseJob(parseObject);
    	}
    }
    /**
     *  续保线索优化-经销商规则初始化
     */
    @XxlJob("syncInsuranceRuleInit")
    public void syncInsuranceRuleInit() {
        log.info("syncInsuranceRuleInit--start:");
        renewalOfInsuranceService.initConfigJob();
    }


    /**
     *  续保线索逾期关闭
     */
    @XxlJob("closeRenewalLead")
    public void closeRenewalLead() {
        log.info("closeRenewalLead--start:");
        renewalOfInsuranceService.closeRenewalLead(null);
        log.info("closeRenewalLead--end:");
    }

    /**
     *  续保线索逾期关闭
     */
    @XxlJob("assignClue")
    public void assignClue() {
        log.info("assignClue--start:");
        renewalOfInsuranceService.assignClue();
        log.info("assignClue--end:");
    }


    @XxlJob("syncOSCCData")
    public void syncOSCCData() {
        log.info("syncOSCCData--start:");
        transparentWorkshopManageService.queryETADocumentDetails();
        log.info("syncOSCCData--end:");
    }

    /**
     * 结算单确认补偿
     */
    @XxlJob("settlementDocConfirmCompensate")
    public void settlementDocConfirmCompensate() {
        String jobParam = XxlJobHelper.getJobParam();
        SettlementDocConfirmCompensateDto parseObject = JSON.parseObject(jobParam, SettlementDocConfirmCompensateDto.class);
        log.info("settlementDocConfirmCompensate--start:{}", JSON.toJSONString(parseObject));
        orderService.settlementDocConfirmCompensate(Objects.nonNull(parseObject) ? parseObject : new SettlementDocConfirmCompensateDto());
        log.info("settlementDocConfirmCompensate--end:");
    }

}
