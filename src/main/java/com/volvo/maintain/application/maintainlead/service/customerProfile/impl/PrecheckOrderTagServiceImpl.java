package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.workshop.VehicleEntranceEntityDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CdpTagInfoService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.PrecheckOrderTagService;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.enums.BizGroupEnum;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainLeadFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.util.DateUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PrecheckOrderTagServiceImpl implements PrecheckOrderTagService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    DmscloudServiceFeign dmscloudServiceFegin;
    
    @Autowired
    DomainMaintainLeadFeign maintainLeadsFegin;

    @Autowired
    CdpTagInfoService cdpTagInfoService;

    @Override
    public List<PrecheckOrderTagDto> queryPrecheckOrderTag(String ownerCode, List<VinLicenseDto> vinLicenseDtos, List<VehicleEntranceEntityDto> records) {
        List<String> vins =vinLicenseDtos.stream().map(VinLicenseDto::getVin).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        logger.info("queryPrecheckOrderTag查询环检单标签:{},vin:{},vinLicenseDtos:{}",ownerCode,vins,vinLicenseDtos);
        if (CollectionUtils.isEmpty(vins)){
            logger.info("queryPrecheckOrderTag isEmpty(vins)");
            return Arrays.asList();
        }
        DmsResponse<Boolean> booleanDmsResponse = dmscloudServiceFegin.checkWhitelist(ownerCode, CommonConstant.CLIENT_PORTRAIT, 0, "");
        logger.info("booleanDmsResponse :{}",booleanDmsResponse);
        //根据经销商code查询是否在360客户画像白名单内
        if (ObjectUtil.isEmpty(booleanDmsResponse) || !booleanDmsResponse.getData()){
            logger.info("查询标签，经销商不在白名单内:{}",ownerCode);
            return Collections.emptyList();
        }

        //剔除掉不存在数据的车架号（查询条件：vins+时间） 如果全部存在直接返回
        TagLogParamsDto tagLogParamsDto=new TagLogParamsDto();
        tagLogParamsDto.setVins(vins);
        tagLogParamsDto.setCreatedAt(DateUtil.formatDateByFormat(new Date(), DateUtil.SIMPLE_DATE_FORMAT));
        tagLogParamsDto.setSinceType(CommonConstant.TAG_PRECHECK);
        tagLogParamsDto.setSubBizNo(ownerCode);
        logger.info("tagLogParamsDto :{}",tagLogParamsDto);
        List<TagLogDto> tagLogDtoList = maintainLeadsFegin.queryTagLog(tagLogParamsDto).getData();
        //如果都存在则直接返回 并且经销商为白名单则直接返回
        if (CollectionUtils.isNotEmpty(tagLogDtoList) && vins.size() == tagLogDtoList.size()) {
            if (booleanDmsResponse.getData()){
                return assemblePrecheckOrderTagDtoList(ownerCode,tagLogDtoList);
            }else {
                logger.info("经销商不在白名单内:{}",ownerCode);
                return Arrays.asList();
            }
        }
        //去交集(过滤掉车架号已存在数据的)
        if (CollectionUtils.isNotEmpty(tagLogDtoList)){
        List<String> existVins = tagLogDtoList.stream().map(TagLogDto::getBizNo).collect(Collectors.toList());
        vins = vins.stream().filter(e -> !existVins.contains(e)).collect(Collectors.toList());
        }

        //不走日志逻辑，假设日志返回为空
        //List<TagLogDto> tagLogDtoList = Collections.emptyList();
        List<PrecheckOrderTagDto> result =vinLicenseDtos.stream().map(vinLicenseDto->new PrecheckOrderTagDto(vinLicenseDto.getVin(),vinLicenseDto.getLicense())).collect(Collectors.toList());
        //根据vins返回邀约信息
        InviteTypeParamsDto inviteTypeParamsDto=new InviteTypeParamsDto();
        inviteTypeParamsDto.setVin(vins);
        inviteTypeParamsDto.setOwnerCode(ownerCode);
        logger.info("inviteTypeParamsDto :{}",inviteTypeParamsDto);
        DmsResponse<List<InviteTypeVinDto>> inviteTypeVinResponse = maintainLeadsFegin.queryInviteTypeByVins(inviteTypeParamsDto);
        logger.info("inviteTypeVinResponse {}",inviteTypeVinResponse);
        if (inviteTypeVinResponse.isFail()){
            logger.error("viplistResponse 根据vins返回邀约信息异常:{}",inviteTypeVinResponse);
        }
        //根据vins查询是否vip
        List<String> bizNos =vinLicenseDtos.stream().map(VinLicenseDto::getLicense).distinct().collect(Collectors.toList());
        List<String> bxQueryVins =vinLicenseDtos.stream().map(VinLicenseDto::getVin).distinct().collect(Collectors.toList());
//        DmsResponse<List<VipVinDto>> viplistResponse = maintainLeadsFegin.batchQueryVipByVin(bizNos);
        DmsResponse<List<VipVinDto>> viplistResponse = maintainLeadsFegin.batchQueryVipGroupByVinOrLicense(
                VipGroupBatchQueryReqDTO.builder()
                        .vinList(bxQueryVins)
                        .licensePlateList(bizNos)
                        .build()
        );
        logger.info("viplistResponse {}", JSON.toJSONString(viplistResponse));
        if (viplistResponse.isFail()){
            logger.error("viplistResponse 根据vins查询是否vip异常:{}",viplistResponse);
        }
        DmsResponse<List<String>> bxQueryVinsResponse = maintainLeadsFegin.batchQueryBxInfoByVins(bxQueryVins);
        logger.info("bxQueryVinsResponse {}", JSON.toJSONString(bxQueryVinsResponse));
        if (bxQueryVinsResponse.isFail()){
            logger.error("bxQueryVinsResponse 根据vin批量查询保修召回组的vin异常:{}", bxQueryVinsResponse);
        }

        //组装数据
        assembleTag(result,viplistResponse.getData(),inviteTypeVinResponse.getData(),null, bxQueryVinsResponse.getData());

        //获取产值标签数据并组装数据
        bleTag(ownerCode, inviteTypeParamsDto, result, records);

        //落库标签数据
        List<TagLogDto> tagLogDtos = assembleTagLogDto(vins, result,ownerCode);
        logger.info("add insertTagLogs :{}",tagLogDtos);
        if(!org.springframework.util.CollectionUtils.isEmpty(records)) {        	
        	DmsResponse insertTagLogsResponse = maintainLeadsFegin.insertTagLogs(tagLogDtos);
        	logger.info("insertTagLogs 落库标签数据:{}",insertTagLogsResponse);
        }
        //将查询到的数据和标签库里的数据拼接返回给前端
        result.addAll(assemblePrecheckOrderTagDtoList(ownerCode,tagLogDtoList));

        //根据经销商code查询是否在360客户画像白名单内
        if (!booleanDmsResponse.getData()){
            logger.info("经销商不在白名单内:{}",ownerCode);
            return Collections.emptyList();
        }
        logger.info("queryPrecheckOrderTag result:{}",result);
        return result;
    }

    private void bleTag(String ownerCode, InviteTypeParamsDto inviteTypeParamsDto, List<PrecheckOrderTagDto> result, List<VehicleEntranceEntityDto> records) {
        //获取产值标签数据并组装数据
        try {
            Map<String, VehicleEntranceEntityDto> tagDtoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(records)) {
                tagDtoMap = records.stream()
                        .filter(dto -> ObjectUtil.isNotEmpty(dto.getVin())) // 过滤VIN为null或空的记录
                        .collect(Collectors.toMap(VehicleEntranceEntityDto::getVin, e -> e, (k1, k2) -> k2));
            }
            Map<String, List<String>> resultMap = new HashMap<>();
            //根据经销商code查询是否在360客户画像白名单内
            if (checkWhitelist(ownerCode, CommonConstant.PRODUCTION_LABEL)) {
                logger.info("查询产值标签，经销商在白名单内:{}", ownerCode);
                List<InviteTypeVinDto> data = null;
                DmsResponse<List<InviteTypeVinDto>> inviteTypeVinResponse2 = maintainLeadsFegin.selectInviteTypeByVins(inviteTypeParamsDto);
                logger.info("inviteTypeVinResponse2 {}", inviteTypeVinResponse2);
                if (inviteTypeVinResponse2.isSuccess()) {
                    data = inviteTypeVinResponse2.getData();
                }

                //线索信息
                if (CollectionUtils.isNotEmpty(data)) {
                    resultMap = data.stream()
                            .collect(Collectors.groupingBy(
                                    InviteTypeVinDto::getVin,
                                    Collectors.mapping(
                                            inviteTypeDto -> inviteTypeDto.getInviteTypeName() != null ? inviteTypeDto.getInviteTypeName() : "null",
                                            Collectors.toList()
                                    )
                            ));
                    logger.info("resultMap :{}", resultMap);
                }
            }

            Map<String, VehicleEntranceEntityDto> finalTagDtoMap = tagDtoMap;
            Map<String, List<String>> finalResultMap = resultMap;
            result.forEach(e -> {
                e.setInviteTypeGroup(finalResultMap.get(e.getVin()));
                VehicleEntranceEntityDto vehicleEntranceEntityDto = finalTagDtoMap.get(e.getVin());
                VehicleTags(ownerCode,vehicleEntranceEntityDto, e);
            });


        } catch (
                Exception e) {
            logger.error("获取产值标签数据并组装数据报错", e);
        }
    }

    /**
     * 标签数据组合并排序
     * @return 返回标签结果
     */
    private void  VehicleTags(String ownerCode, VehicleEntranceEntityDto entity , PrecheckOrderTagDto dto) {
        logger.info("标签数据组合并排序,entity:{},dto:{}", entity,dto);
        if (Objects.isNull(dto)){
            logger.info("标签数据为空");
            return;
        }

        List<String> tagList = new ArrayList<>();
        List<String> vipGroup = dto.getVipGroup();
        List<String> inviteTypeGroup = dto.getInviteTypeGroup();
        if (!CollectionUtils.isEmpty(vipGroup)){
            tagList.addAll(vipGroup);
        }
        if (!CollectionUtils.isEmpty(inviteTypeGroup)){
            tagList.addAll(inviteTypeGroup);
        }

        logger.info("处理预约标签");
        String tag = null;
        if (Objects.nonNull(entity)){
            tag = getTag(entity.getEntryTime(), entity.getAppointmentTime());
        }
        logger.info("处理预约标签,tag:{}", tag);
        if (StringUtils.isNotBlank(tag)){
            tagList.add(tag);
            dto.setBookingTag(tag);
        }
        dto.setTagList(getTagList(ownerCode,tagList));
    }

    private List<String> checkTagList(String ownerCode, List<String> inviteTypeGroup) {
        logger.info("处理小标签白名单");
        if (!checkWhitelist(ownerCode, CommonConstant.PRODUCTION_LABEL_FAULT)){
            logger.info("故障灯标签白名单为关闭 剔除 故障灯标签");
            inviteTypeGroup.remove("故障灯");
        }
        if (!checkWhitelist(ownerCode, CommonConstant.PRODUCTION_LABEL_INSURANCE)){
            logger.info("保险到期标签白名单为关闭 剔除 保险到期标签");
            inviteTypeGroup.remove("保险到期");
        }
        if (!checkWhitelist(ownerCode, CommonConstant.PRODUCTION_LABEL_ACCIDENT)){
            logger.info("事故车标签白名单为关闭 剔除 事故车标签");
            inviteTypeGroup.remove("事故车");
        }

        return inviteTypeGroup;

    }

    private List<String> getTagList(String ownerCode, List<String> inviteTypeGroup) {
        logger.info("处理标签排序规则");

        if (CollectionUtils.isEmpty(inviteTypeGroup)){
            return Collections.emptyList();
        }

        checkTagList(ownerCode, inviteTypeGroup);

        //获取系统配置
        DmsResponse<CommonConfigDto> configResult = dmscloudServiceFegin.getConfigByKey(CommonConstant.PRODUCTION_LABEL_TAG,CommonConstant.PRODUCTION_LABEL_TAG);
        logger.info("configResult response:{}",JSONObject.toJSONString(configResult));
        if (configResult.isFail()){
            logger.info("获取标签排序规则为空");
        }

        String configValue = null;
        if (ObjectUtils.isNotEmpty(configResult.getData())){
            configValue = configResult.getData().getConfigValue();

        }

        List<List<String>> rules;
        if (StringUtils.isBlank(configValue)) {
            rules = Arrays.asList(
                    Arrays.asList("预约", "预约准时"),
                    Collections.singletonList("QB"),
                    Arrays.asList("首保", "定保", "流失预警", "流失客户", "事故车", "保险到期", "故障灯"),
                    Arrays.asList("012客户", "CEM升级", "联系技术"),
                    Collections.singletonList("定保"),
                    Collections.singletonList("流失预警"),
                    Collections.singletonList("流失客户"),
                    Collections.singletonList("事故车"),
                    Collections.singletonList("保险到期"),
                    Collections.singletonList("故障灯"),
                    Collections.singletonList("CEM升级"),
                    Collections.singletonList("联系技术")
            );
        }else {
            Gson gson = new Gson();
            Type type = new TypeToken<List<List<String>>>() {}.getType();
            rules = gson.fromJson(configValue, type);
        }

        logger.info("标签排序");
        List<String> tagList = new ArrayList<>();
        for (List<String> rule : rules) {
            for (String s : rule) {
                if(inviteTypeGroup.contains(s) && !tagList.contains(s)){
                    tagList.add(s);
                    break;
                }
            }
        }
        return tagList;

    }

    private String getTag(String entryTimeStr, String appointmentTimeStr) {
        try {
            if (StringUtils.isBlank(entryTimeStr) || StringUtils.isBlank(appointmentTimeStr)){
                return null;
            }
            logger.info("处理预约标签，entryTimeStr:{}，appointmentTimeStr:{}" , entryTimeStr, appointmentTimeStr);
            // 定义日期时间格式，假设输入格式为 "yyyy/MM/dd HH:mm:ss"
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

            // 解析输入的日期时间字符串
            LocalDateTime entryTime = LocalDateTime.parse(entryTimeStr, formatter);
            LocalDateTime appointmentTime = LocalDateTime.parse(appointmentTimeStr, formatter);

            // 计算 appointmentTime 的前 15 分钟和后 15 分钟
            LocalDateTime startTime = appointmentTime.minusMinutes(15);
            LocalDateTime endTime = appointmentTime.plusMinutes(15);

            // 判断 entryTime 是否在 [startTime, endTime] 范围内
            if (entryTime.isAfter(startTime) && entryTime.isBefore(endTime)) {
                return "预约准时";
            } else {
                return "预约";
            }
        } catch (Exception e) {
            logger.error("日期时间解析异常", e);
            return null;
        }
    }


    /**
     * 查询经销商是否存在白名单
     */
    public Boolean checkWhitelist(String ownerCode, Integer modType) {
        // 查询白名单
        logger.info("查询产值标签，ownerCode :{},modType :{}", ownerCode , modType);
        DmsResponse<Boolean> booleanWhite = dmscloudServiceFegin.checkWhitelist(ownerCode, modType, 0, "");
        logger.info("查询产值标签，ownerCode :{},modType :{}，booleanWhite :{}", ownerCode, modType , booleanWhite);
        if (ObjectUtil.isNotEmpty(booleanWhite) && booleanWhite.getData()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    private List<PrecheckOrderTagDto> assemblePrecheckOrderTagDtoList(String ownerCode, List<TagLogDto> tagLogDtoList){
        logger.info("assemblePrecheckOrderTagDtoList :{}",tagLogDtoList);
        List<PrecheckOrderTagDto> list =new ArrayList<>();
        tagLogDtoList.stream().forEach(e->{
            PrecheckOrderTagDto precheckOrderTagDto = JSONObject.toJavaObject(JSON.parseObject(e.getTagData()), PrecheckOrderTagDto.class);
            precheckOrderTagDto.setVin(e.getBizNo());
            precheckOrderTagDto.setTagList(getTagList(ownerCode,precheckOrderTagDto.getTagList()));
            list.add(precheckOrderTagDto);
        });
        return list;
    }

    /**
     * 组装数据
     */
    public void assembleTag(List<PrecheckOrderTagDto> result, List<VipVinDto> vipVinDtoList, List<InviteTypeVinDto> inviteTypeVinDtoList, List<CdpTagListDto> cdpTagListDTOS, List<String> bxVins){
        logger.info("assembleTag result:{}, vipVinDtoList:{}, inviteTypeVinDtoList:{}, cdpTagListDTOS:{}, bxVins:{}", JSON.toJSONString(result), JSON.toJSONString(vipVinDtoList), JSON.toJSONString(inviteTypeVinDtoList), JSON.toJSONString(cdpTagListDTOS), bxVins);

        //1. vip信息设置
        result.forEach(returnDTO -> {
            List<String> vipGroup = new ArrayList<>();
            returnDTO.setVipGroup(vipGroup);

            //1.1 保修召回组（bx）
            boolean isBx = ObjectUtil.isNotEmpty(bxVins) && bxVins.contains(returnDTO.getVin());
            if (isBx) {
                vipGroup.add(BizGroupEnum.BIZ_GROUP_BX.getCode());
                returnDTO.setVip(true);
            }

            //1.2 重点维修组（t）,新增重点关照组（lb）,重点服务组（90）
            List<String> vipGroupListByQuery = ObjectUtil.isEmpty(vipVinDtoList) ? Collections.emptyList()
                    : vipVinDtoList.stream()
                    //筛选出符合vin或车牌的vip群组
                    .filter(vipVinDTO -> ObjectUtil.isNotEmpty(vipVinDTO.getVipGroup())
                            && ((ObjectUtil.isNotEmpty(vipVinDTO.getVin()) && ObjectUtil.equal(returnDTO.getVin(), vipVinDTO.getVin()))
                            || (ObjectUtil.isNotEmpty(vipVinDTO.getBizNo()) && ObjectUtil.equal(returnDTO.getLicense(), vipVinDTO.getBizNo()))))
                    .flatMap(vipVinDTO -> vipVinDTO.getVipGroup().stream())
                    .distinct()
                    .collect(Collectors.toList());
            returnDTO.setVip(returnDTO.isVip() || ObjectUtil.isNotEmpty(vipGroupListByQuery));
            vipGroup.addAll(vipGroupListByQuery);

            //1.3 直接转成文案，无需前端查配置
            logger.info("组装vip数据，vipGroup:{}", vipGroup);
            List<String> vipGroupTagList = returnDTO.getVipGroup().stream().map(groupCode -> {
                BizGroupEnum resolve = BizGroupEnum.resolve(groupCode);
                return resolve == null ? null : resolve.getMailTitleFillInfo();
            }).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
            returnDTO.setVipGroup(vipGroupTagList);
        });

        //邀约信息
        if (CollectionUtils.isNotEmpty(inviteTypeVinDtoList)){
            Map<String, Integer> inviteTypeMap = inviteTypeVinDtoList.stream().collect(Collectors.toMap(InviteTypeVinDto::getVin, InviteTypeVinDto::getInviteType,((k1,k2)->k1)));
            logger.info("inviteTypeMap :{}",inviteTypeMap);
            result.stream().forEach(e->{
                e.setInviteType(inviteTypeMap.get(e.getVin()));
            });
        }
    }

    /**
     * 组装数据
     */
    private List<TagLogDto> assembleTagLogDto(List<String> vins,List<PrecheckOrderTagDto> precheckOrderTagDtos,String ownerCode){
        logger.info("assembleTagLogDto:{},{}",vins,precheckOrderTagDtos);
        List<TagLogDto> result=new ArrayList<>();
        Map<String, PrecheckOrderTagDto> PrecheckOrderTagDtoMap = precheckOrderTagDtos.stream()
                .filter(dto -> ObjectUtil.isNotEmpty(dto.getVin())) // 过滤VIN为null或空的记录
                .collect(Collectors.toMap(PrecheckOrderTagDto::getVin, e -> e));
        vins.forEach(e -> {
            TagLogDto dto=new TagLogDto();
            dto.setBizNo(e);
            dto.setSinceType(CommonConstant.TAG_PRECHECK);
            dto.setTagData(JSON.toJSONString(PrecheckOrderTagDtoMap.get(e)));
            dto.setSubBizNo(ownerCode);
            result.add(dto);
        });
        logger.info("assembleTagLogDto result:{}",result);
        return result;
    }

}
