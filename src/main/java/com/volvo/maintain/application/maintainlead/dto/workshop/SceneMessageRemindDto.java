package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SceneMessageRemindDto {

    private String ownerCode;

    private String sceneType;

    // 同一条消息默认相同 待发送消息可根据相同数据结果更新消息发送时间
    private String businessId;

    private String message;

    private TemplateParameterDto templateParameter;

    private BusinessParameterDto extend;

    private JSONObject businessParameter;

}
