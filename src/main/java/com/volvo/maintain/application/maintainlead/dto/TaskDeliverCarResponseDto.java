package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 功能描述：取送车下单
 *
 * <AUTHOR>
 * @since 2024/03/08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("取送车下单")
public class TaskDeliverCarResponseDto {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "养修预约单号")
    private String reservationNo;

    @ApiModelProperty(value = "预约时间")
    private String bookingTime;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

}
