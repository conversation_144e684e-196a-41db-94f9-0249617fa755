package com.volvo.maintain.application.maintainlead.dto.vhc;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.volvo.po.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * 车辆健康检查主档表
 * </p>
 *
 * @since 2024-09-24
 */
@Data
public class VhcInfoPoDTO extends BasePO{

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 经销商code
     */
    private String ownerCode;

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 车辆健康检查编号
     */
    private String vhcNo;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String license;

    /**
     * 检查时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date vhcTime;

    /**
     * 报价时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date vhcPriceTime;

    /**
     * 反馈时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date vhcFeedbackTime;

    /**
     * 报价转入工单时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date vhcPriceOrderTime;

    /**
     * 检查状态
     */
    private String vhcState;

    /**
     * 报价是否推送用户
     */
    private String pushUser;
    /**
     * 推送用户时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pushUserTime;

    /**
     * 检查人
     */
    private Integer vhcPeople;

    /**
     * 车辆类型{电车：84701001 油车：84701002 }
     */
    private String vhcType;

    /**
     * 是否删除
     */
    private Integer isDeleted;


}
