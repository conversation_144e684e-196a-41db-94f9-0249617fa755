package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("车架号vin")
public class VipVinDto {

    @ApiModelProperty(value = "车架号",name = "vin")
    private String bizNo;

    @ApiModelProperty(value = "是否VIP ",name = "whetherVip")
    private boolean whetherVip;

    @ApiModelProperty(value = "vip群组",name = "vipGroup")
    private List<String> vipGroup;

    @ApiModelProperty(value = "vin")
    private String vin;
}
