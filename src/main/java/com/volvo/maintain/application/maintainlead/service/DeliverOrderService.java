package com.volvo.maintain.application.maintainlead.service;

import com.volvo.maintain.application.maintainlead.dto.RepairOrderDto;
import com.volvo.maintain.application.maintainlead.vo.CarOwnerRelationVo;
import com.volvo.maintain.interfaces.vo.MemberInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 送修人订单处理
 */
public interface DeliverOrderService {


    /**
     * 记录异常送修人工单
     *
     * @param roNo      工单号
     * @param traceTime 回访时间类型
     * @return map 返回结果
     */
    Map<String, Object> deliveryOrderExceptionRecord(String roNo, String traceTime);

    /**
     * 查询人车关系
     * @param vin 车架号
     * @return 人车关系集合
     */
    List<CarOwnerRelationVo> queryRelationList(String vin);

    /**
     * 查询会员信息根据 集合Id
     * @param memberIdList 会员idList
     * @return 会员信息List
     */
    List<MemberInfoVo> queryMemberInfoByList(List<String> memberIdList);

    /**
     * 记录异常送修人工单 (取消交车)
     * @param roNo      工单号
     * @return map 返回结果
     */
    Map<String, Object> cancelDeliverExceptionRecord(String roNo);

    /**
     * 匹配手机号结果
     * @param memberInfoList 会员中心的会员手机号集合
     * @param delivererMobile 工单表中的送修人手机号
     * @return boolean 结果
     */
    boolean isContainsDelivererMobile(List<MemberInfoVo> memberInfoList, String delivererMobile);

    /**
     * 查询送修人手机号是否异常
     */
    String queryDeliverMobileException(String mobile,String ownerCode,String vin);

    /**
     *
     * @param roNo 工单号
     * @param repairOrderDto 工单对象
     * @param repairOrderInfo Map
     * @param flag 标记
     */
    void structureDeliver(String roNo, RepairOrderDto repairOrderDto, Map<String, Object> repairOrderInfo, boolean flag);

}
