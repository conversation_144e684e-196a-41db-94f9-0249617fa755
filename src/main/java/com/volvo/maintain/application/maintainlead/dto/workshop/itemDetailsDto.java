package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class itemDetailsDto {

    /**
     * 行内码
     */
    private String lineNo;

    /**
     * 企业商品编码
     */
    private String goodsNo;

    /**
     * 货主商品编码
     */
    private String ownerGoodsNo;

    /**
     * 商家商品编码
     */
    private String spGoodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 计划商品数量
     */
    private Integer applyQty;

    /**
     * 实际商品数量
     */
    private Integer realQty;

    /**
     * 销售订单号
     */
    private String salesNo;








}
