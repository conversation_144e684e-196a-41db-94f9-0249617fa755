package com.volvo.maintain.application.maintainlead.service.customerProfile;


import com.volvo.maintain.application.maintainlead.dto.coupon.QueryCouponDetailInfoDto;
import com.volvo.maintain.application.maintainlead.dto.coupon.QueryCouponDto;
import com.volvo.maintain.interfaces.vo.coupon.CouponDetailInfoVO;
import com.volvo.maintain.interfaces.vo.coupon.CouponDetailVO;

import java.util.List;

/**
 * 功能描述：卡券管理接口
 *
 * <AUTHOR>
 * @since 2023-12-15
 */
public interface CouponDetailService {

    List<CouponDetailVO> queryCouponInfo(QueryCouponDetailInfoDto queryCouponDetailInfoDTO);

    List<CouponDetailVO> allCouponAfter(QueryCouponDto queryCouponDto);
}
