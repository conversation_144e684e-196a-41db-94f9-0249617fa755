package com.volvo.maintain.application.maintainlead.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.StatusChangePushDto;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsConfigDto;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceVehicleRecordDTO;
import com.volvo.maintain.application.maintainlead.dto.clues.RenewalLeadStaticDto;
import com.volvo.maintain.application.maintainlead.dto.company.CompanyNewSelectDto;
import com.volvo.maintain.application.maintainlead.vo.CallDetailVo;
import com.volvo.maintain.application.maintainlead.vo.FullLeadVo;
import com.volvo.maintain.application.maintainlead.vo.FullLeadsTagVo;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.constants.RedisConstants;
import com.volvo.maintain.infrastructure.enums.*;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.request.CompanySelectDTO;
import com.volvo.maintain.infrastructure.gateway.request.InviteInsuranceVehicleCustomerNumberDTO;
import com.volvo.maintain.infrastructure.gateway.request.PrintParamVo;
import com.volvo.maintain.infrastructure.gateway.request.SaCustomerNumberDTO;
import com.volvo.maintain.infrastructure.gateway.response.CompanyDetailDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.PrintDataVo;
import com.volvo.maintain.infrastructure.gateway.response.VehicleOwnerVO;
import com.volvo.maintain.infrastructure.util.DateUtil;
import com.volvo.utils.BeanMapperUtil;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.framework.service.excel.ExcelExportColumn;
import com.yonyou.cyx.function.exception.ServiceBizException;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 功能描述：全量线索实现
 *
 * <AUTHOR>
 * @since 2023/12/19
 */
@Service
@Slf4j
public class FullLeadsServiceImpl implements FullLeadsService {

    @Resource
    private DomainMaintainLeadFeign domainMaintainLeadFeign;
    @Resource
    private MidEndOrgCenterFeign midEndOrgCenterFeign;
    @Resource
    private DmscusCustomerFeign dmscusCustomerFeign;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Resource
    private DownloadServiceFeign downloadServiceFeign;
    @Resource
    private DmscusReportFeign dmscusReportFeign;
    @Autowired
    private DomainMaintainAuthFeign domainMaintainAuthFeign;

    @Autowired
    @Qualifier("tocTransparent")
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.workshopRemind.topic}")
    private String renewalLeadTopic;

    @Value("${rocketmq.workshopRemind.renewalTag}")
    private String renewalLeadTag;

    private static final String PAYMENT_OBJECT_CODE = "999999999999";
    private static final String exportUrl = "http://application-maintain-management/fullLeads/exportCallback";
    private static final String excelName = "全量线索.xlsx";
    private static final String sheetName = "全量线索";


    /**
     * 功能描述：查询全量线索列表
     *
     * @param params 全量线索查询dto对象
     * @return IPage<FullLeadVo> 全量线索查询结果
     */
    @Override
    public Page<FullLeadVo> queryFullLeadsList(FullLeadQueryDto params) {

        this.checkAdviseInDate(params);
        params.setDealerCode(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode());
        RestResultResponse<Page<FullLeadVo>> resp = domainMaintainLeadFeign.queryFullLeadsList(params);
        log.info("查询全量线索返回参数：{}", JSONObject.toJSONString(resp));
        if (ObjectUtils.isEmpty(resp) || !ObjectUtils.nullSafeEquals(resp.getResultCode(), HttpStatus.OK.value())) {
            throw new ServiceBizException(resp.getErrMsg());
        }

        return resp.getData();
    }

    /**
     * 功能描述：查询全量线索列表(厂端)
     *
     * @param params 全量线索查询dto对象
     * @return IPage<FullLeadVo> 全量线索查询结果
     */
    @Override
    public Page<FullLeadVo> queryOemFullLeadsList(FullLeadQueryDto params) {

        this.checkAdviseInDate(params);
        this.fillDealerCodeList(params);
        //区域信息查询结果为空返回空线索列表
        if (ObjectUtils.isEmpty(params.getDealerCode()) && CollectionUtils.isEmpty(params.getDealerCodeList())) {
            return new Page<>();
        }
        RestResultResponse<Page<FullLeadVo>> response = dmscusReportFeign.queryFullLeadsList(params);
        log.info("查询厂端全量线索返回参数：{}", JSONObject.toJSONString(response));
        if (ObjectUtils.isEmpty(response) || !ObjectUtils.nullSafeEquals(response.getResultCode(), HttpStatus.OK.value())) {
            throw new ServiceBizException(response.getErrMsg());
        }

        return response.getData();
    }

    /**
     * 功能描述：查询全量线索列表
     *
     * @param params 全量线索查询dto对象
     */
    @Override
    public void exportList(FullLeadQueryDto params) {

        // 获取导出字段
        List<ExcelExportColumn> exportColumnList = buildExcelColumn();
        // 组装下载中心 DTO
        DownloadDto dto = new DownloadDto();
        dto.setQueryParams(BeanMapperUtil.toMap(params));
        dto.setExcelName(excelName);
        dto.setSheetName(sheetName);
        dto.setServiceUrl(exportUrl);
        dto.setExcelExportColumnList(exportColumnList);
        log.info("export full leads params:{}", dto);
        // 导出
        downloadServiceFeign.downloadExportExcel(dto);
    }

    /**
     * 功能描述：全量线索导出回调
     *
     * @param params 全量线索查询dto对象
     * @return List<FullLeadVo> 全量线索查询结果
     */
    @Override
    public List<FullLeadVo> exportCallback(FullLeadQueryDto params) {

        Page<FullLeadVo> fullLeadVoPage = this.queryFullLeadsList(params);
        List<FullLeadVo> records = fullLeadVoPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return records;
        }

        Map<Integer, String> enumMap = Arrays.stream(FullLeadExportDictEnum.values())
                .collect(Collectors.toMap(FullLeadExportDictEnum::getCode, FullLeadExportDictEnum::getDesc));
        records.forEach(row -> {
            row.setInviteTypeDesc(enumMap.getOrDefault(row.getInviteType(), ""));
            row.setInsuranceTypeDesc(enumMap.getOrDefault(row.getInsuranceType(), ""));
            row.setFollowStatusDesc(enumMap.getOrDefault(row.getFollowStatus(), ""));
            row.setItemCodeDesc(enumMap.getOrDefault(row.getItemCode(), ""));
            row.setItemTypeDesc(enumMap.getOrDefault(row.getItemType(), ""));
            row.setLossTypeDesc(enumMap.getOrDefault(row.getLossType(), ""));
            row.setLoseReasonDesc(enumMap.getOrDefault(row.getLoseReason(), ""));
        });

        return records;
    }

    /**
     * 全量线索tag
     *
     * @param id         线索id
     * @param inviteType 线索类型
     * @param vin        vin
     * @param dealerCode 经销商代码
     * @return FullLeadsTagVo
     */
    @Override
    public FullLeadsTagVo queryTagList(Long id, Integer inviteType, String vin, String dealerCode) {

        RestResultResponse<FullLeadsTagVo> resp = domainMaintainLeadFeign.queryTagList(id, inviteType, vin, dealerCode);
        log.info("查询全量线索tag返回参数：{}", JSONObject.toJSONString(resp));
        if (ObjectUtils.isEmpty(resp) || !ObjectUtils.nullSafeEquals(resp.getResultCode(), HttpStatus.OK.value())) {
            throw new ServiceBizException(resp.getErrMsg());
        }
        FullLeadsTagVo data = resp.getData();
        List<FullLeadVo> tagList = data.getTagList();
        if(CollectionUtils.isEmpty(tagList)){
            return data;
        }
        List<FullLeadVo> collect = tagList.stream().filter(vo -> InvitationTypeEnum.INVITE_TYPE_INSURANCE.getCode().equals(vo.getInviteType())).collect(Collectors.toList());
        Optional.ofNullable(collect).ifPresent(items -> items.forEach(item -> {
            // 续保线索,则取处理是否可修改续保到期日期逻辑
            RestResultResponse<List<InsuranceVehicleRecordDTO>> listRestResultResponse = domainMaintainLeadFeign.queryRenewalInsurance(Arrays.asList(item.getId()));
            log.info("queryTagList>listRestResultResponse:{}", JSON.toJSONString(listRestResultResponse));
            if (ObjectUtils.isEmpty(listRestResultResponse)) {
                throw new ServiceBizException("未查询到线索信息");
            }
            if(CollectionUtils.isEmpty(listRestResultResponse.getData())){
                log.info("queryTagList>listRestResultResponse is null");
                return;
            }
            InsuranceVehicleRecordDTO insuranceVehicleRecordDTO = listRestResultResponse.getData().get(0);
            if(Objects.nonNull(insuranceVehicleRecordDTO.getFactoryInsuranceExpiryDate())){
                item.setFactoryInsuranceExpiryDate(DateUtil.formatDateByFormat(insuranceVehicleRecordDTO.getFactoryInsuranceExpiryDate(), DateUtil.FULL_DATE_TIME_FORMAT));
            }
            Integer day = 0; // 经销商提前出单天数
            Integer count = 0; // 修改次数校验
            DmsResponse<CommonConfigDto> configByKey = dmscloudServiceFeign.getConfigByKey(CommonConstant.RENEWAL_INSURANCE_KEY, CommonConstant.RENEWAL_INSURANCE_GROUP);
            log.info("queryTagList>configByKey:{}",JSON.toJSONString(configByKey));
            if (!ObjectUtils.isEmpty(configByKey) && Objects.nonNull(configByKey.getData())) {
                count = Integer.valueOf(configByKey.getData().getConfigValue());
            }
            // 校验经销商是否是白名单
            if (!isWhite(insuranceVehicleRecordDTO.getDealerCode())) {
                // 非白名单，不走下面逻辑
                log.info("queryTagList is not white");
                return;
            }
            if(insuranceVehicleRecordDTO.getOrderStatus().equals(CommonConstant.INVITEINSURANCE_ORDER_STATUS_83681002)
            || insuranceVehicleRecordDTO.getOrderStatus().equals(CommonConstant.INVITEINSURANCE_ORDER_STATUS_83681007)
            || insuranceVehicleRecordDTO.getOrderStatus().equals(CommonConstant.INVITEINSURANCE_ORDER_STATUS_83681004)){
                item.setAdviseInDateDisable(true);
                return;
            }
            // 校验续保日期是否合规
            if(day == 0){
                // 理论上在全量线索上跟进时，即便有多条续保线索，但经销商应该时同一个，
                DmsResponse<Page<InsuranceLeadsConfigDto>> pageDmsResponse = domainMaintainAuthFeign.queryCompanyConfig("", "", insuranceVehicleRecordDTO.getDealerCode(), 1, 10);
                log.info("queryTagList>>>pageDmsResponse:{}", JSON.toJSONString(pageDmsResponse));
                if (ObjectUtils.isEmpty(pageDmsResponse)) {
                    throw new ServiceBizException("查询经销商是否白名单失败!");
                }
                if(CollectionUtils.isEmpty(pageDmsResponse.getData().getRecords())){
                    log.info("未查询到经销商提前出单日期");
                    item.setAdviseInDateDisable(true);
                    return;
                }
                day = pageDmsResponse.getData().getRecords().get(0).getAdvanceDays();
            }
            switch (insuranceVehicleRecordDTO.getClueIssuanceType()) {
                case 96171002:
                    // 店端下发
                    Date date6 = new Date();
                    String date6Str = DateUtil.formatDateByFormat(date6, DateUtil.SIMPLE_DATE_FORMAT);
                    Date date57 = DateUtil.parseDate(date6Str, DateUtil.SIMPLE_DATE_FORMAT);
                    Date date8 = DateUtil.addDay(date57, day);
                    // 店端保险到期时间和当前时间比 0 等于 -1小于 1大于
                    int j = insuranceVehicleRecordDTO.getAdviseInDate().compareTo(date57);
                    if(j < 0){
                        log.info("保险到期时间小于当前时间!");
                        item.setAdviseInDateDisable(true);
                    }
                    // 店端保险到期时间和最大续保到期日期比较 0 等于 -1小于 1大于
                    int k = insuranceVehicleRecordDTO.getAdviseInDate().compareTo(date8);
                    if(k > 0){
                        log.info("保险到期时间大于大续保到期日期!");
                        item.setAdviseInDateDisable(true);
                    }
                    break;
                default:
                    // 厂端下发线索
                    RAtomicLong atomicLong = redissonClient.getAtomicLong(RedisConstants.RENEWAL_INSURANCE_REDIS_KEY + insuranceVehicleRecordDTO.getId());
                    long l = atomicLong.get();
                    log.info("RAtomicLong>>>:{}", l);
                    l = ++l;
                    if(l > count){
                        log.info("厂端线索修改续保到期日期超出次数限制");
                        item.setAdviseInDateDisable(true);
                        break;
                    }
                    Date date1 = new Date();
                    String date1Str = DateUtil.formatDateByFormat(date1, DateUtil.SIMPLE_DATE_FORMAT);
                    Date date5 = DateUtil.parseDate(date1Str, DateUtil.SIMPLE_DATE_FORMAT);

                    Date date2 = insuranceVehicleRecordDTO.getAdviseInDate();
                    Date date3 = DateUtil.getLastDayOfLastMonth(date2);
                    // 0 等于 -1小于 1大于
                    int i2 = date5.compareTo(date3);
                    if(i2 > 0){
                        log.info("厂端线索修改续保到期日期必须在N-1月及以前");
                        item.setAdviseInDateDisable(true);
                    }
                    break;
            }
        }));
        return data;
    }

    /**
     * 功能描述：全量线索通话记录保存
     *
     * @param params 参数
     * @return String 工作号
     */
    @Override
    public List<FullLeadsCallDto> saveCallRecordList(List<FullLeadsCallDto> params) {

        if (CollectionUtils.isEmpty(params)) {
            throw new ServiceBizException(FullLeadsErrorEnum.INVALID_PARAM.getDesc());
        }
        String lowerCase = UUID.randomUUID().toString().replace("-", "").toLowerCase();
        params.stream().filter(Objects::nonNull).map(obj->obj.setCallId(lowerCase)).forEach(this::saveCallRecord);
        return params;
    }

    /**
     * 全量线索跟进
     *
     * @param followList 跟进列表
     * @return 跟进结果
     */
    @Override
    public Boolean saveFollowRecords(List<FullLeadsFollowDto> followList) {

        if (CollectionUtils.isEmpty(followList)) {
            return Boolean.FALSE;
        }
        String batchNo = followList.get(0).getBatchNo();
        log.info("全量线索跟进批次号：{}", batchNo);
        if (ObjectUtils.isEmpty(batchNo)) {
            throw new ServiceBizException(FullLeadsErrorEnum.INVALID_BATCH_NO.getDesc());
        }

        RLock lock = redissonClient.getLock(MessageFormat.format(RedisConstants.FULL_LEADS_FOLLOW_KEY, batchNo));
        try {
            if (!lock.tryLock()) {
                log.info("全量线索跟进,线程：{}, 批次号：{} 未获取到锁", Thread.currentThread().getName(), batchNo);
                return Boolean.FALSE;
            }

            //补完通话记录
            dmscusCustomerFeign.fixSaCustomerNumber(followList);

            // 屏蔽未勾选线索
            followList = followList.stream().filter(i -> String.valueOf(CommonConstant.DICT_IS_YES).equals(i.getSelectStatus() + "")).collect(Collectors.toList());
            // 故障灯handler
            followList = this.handlerFaultLightLeadFollow(followList);
            // 续保线索检查
            this.renewalInsuranceCheck(followList);

            RestResultResponse<List<StatusChangePushDto>> resp = domainMaintainLeadFeign.saveFollowRecords(followList);
            log.info("全量线索跟进返回结果：{}", JSONObject.toJSONString(resp));
            if (ObjectUtils.isEmpty(resp) || !ObjectUtils.nullSafeEquals(resp.getResultCode(), HttpStatus.OK.value())) {
                throw new ServiceBizException(resp.getErrMsg());
            }
            List<StatusChangePushDto> data = resp.getData();
            log.info("续保线索状态变更待推送数据：{}", JSONObject.toJSONString(data));
            if(!CollectionUtils.isEmpty(data)){
                Iterator<StatusChangePushDto> iterator = data.iterator();
                while(iterator.hasNext()){
                    StatusChangePushDto next = iterator.next();
                    if (Objects.isNull(next.getDealerCode()) || !isWhite(next.getDealerCode())) {
                        // 非白名单，删除元素
                        iterator.remove();
                    }
                }
                if(!CollectionUtils.isEmpty(data)){
                    // 推送状态到litecrm
                    dmscusCustomerFeign.acPushLiteCrmClueStatus(data);
                }
            }
            //获取线索类型是续保的线索id
            try {
                List<Long> inviteId=followList.stream().filter(i->InvitationTypeEnum.INVITE_TYPE_INSURANCE.getCode().equals(i.getInviteType())).map(i->i.getId()).collect(Collectors.toList());
                buildRenewalLeadStaticDto(inviteId);
            }catch (Exception e){
                log.error("续保线索跟进报表记录失败",e);
            }

            log.info("全量线索跟进,线程：{}, 批次号:{}, 执行结束", Thread.currentThread().getName(), batchNo);
        } catch (Exception e) {
            log.error("跟进失败", e);
            throw new ServiceBizException(e.getMessage());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return Boolean.TRUE;
    }
    public void buildRenewalLeadStaticDto(List<Long> inviteIds){
        if (CollectionUtils.isEmpty(inviteIds)) {
            log.info("queryTagList>leadsId is null");
        }
        log.info("queryTagList>leadsId:{}", JSON.toJSONString(inviteIds));
        RestResultResponse<List<InsuranceVehicleRecordDTO>> insuranceVehicleRecordList = domainMaintainLeadFeign.queryRenewalInsurance(inviteIds);
        log.info("queryTagList>listRestResultResponse:{}", JSON.toJSONString(insuranceVehicleRecordList));
        if (ObjectUtils.isEmpty(insuranceVehicleRecordList)||ObjectUtils.isEmpty(insuranceVehicleRecordList.getData())) {
            log.info("未查询到线索信息");
        }
        List<String>ownerCodes = insuranceVehicleRecordList.getData().stream().map(InsuranceVehicleRecordDTO::getDealerCode).collect(Collectors.toList());
        //获取经销商信息
        CompanyNewSelectDto companySelectDTO = new CompanyNewSelectDto();
        companySelectDTO.setCompanyCodes(ownerCodes);
        RestResultResponse<List<CompanyDetailDTO>> companyResponse =midEndOrgCenterFeign.companyInfo(companySelectDTO);
        log.info("经销商信息获取结果：{}", JSONObject.toJSONString(companyResponse));
        if (ObjectUtils.isEmpty(companyResponse)||ObjectUtils.isEmpty(companyResponse.getData())) {
            log.info("未查询到经销商信息");
        }
        List<RenewalLeadStaticDto> renewalLeadStaticDtoList = new ArrayList<>();
        for (Long id : inviteIds) {
            InsuranceVehicleRecordDTO insuranceVehicleRecordDTO= insuranceVehicleRecordList.getData().stream().filter(item -> item.getId().equals(id)).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(insuranceVehicleRecordDTO)) {
                log.info("未查询到线索信息");
                continue;
            }
            RenewalLeadStaticDto renewalLeadStaticDto = new RenewalLeadStaticDto();
            CompanyDetailDTO companyDetailDTO = companyResponse.getData().stream().filter(item -> item.getCompanyCode().equals(insuranceVehicleRecordDTO.getOwnerCode())).findFirst().orElse(null);
            if (!ObjectUtils.isEmpty(companyDetailDTO)) {
                renewalLeadStaticDto.setProvince(companyDetailDTO.getProvinceName());
                renewalLeadStaticDto.setCity(companyDetailDTO.getCityName());
            }
            renewalLeadStaticDto.setInviteId(id);
            renewalLeadStaticDto.setVin(insuranceVehicleRecordDTO.getVin());
            renewalLeadStaticDto.setOwnerCode(insuranceVehicleRecordDTO.getDealerCode());
            renewalLeadStaticDto.setLicensePlate(insuranceVehicleRecordDTO.getLicensePlateNum());
            renewalLeadStaticDto.setInsuranceExpiryDate(insuranceVehicleRecordDTO.getFactoryInsuranceExpiryDate());
            if (!ObjectUtils.isEmpty(insuranceVehicleRecordDTO.getInsuranceType())){
                renewalLeadStaticDto.setRenewalCustomerType(insuranceVehicleRecordDTO.getInsuranceType().toString());
            }
            renewalLeadStaticDtoList.add(renewalLeadStaticDto);
        }
        push(renewalLeadStaticDtoList);
    }

    /**
     * 查询是否是白名单店
     * @param dealer
     * @return
     */
    private boolean isWhite(String dealer) {
        log.info("RENEWAL_INSURANCE_WHITE isWhite dealer:{}", dealer);
        DmsResponse<Object> response = domainMaintainAuthFeign.checkWhitelist(dealer, CommonConstant.RENEWAL_INSURANCE_WHITE, CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE, "");
        log.info("RENEWAL_INSURANCE_WHITE isWhite response:{}",response);
        if (Objects.isNull(response) || response.isFail()){
            log.info("RENEWAL_INSURANCE_WHITE isWhite error");
            return false;
        }
        Object data = response.getData();
        if (null == data) {
            log.info("RENEWAL_INSURANCE_WHITE isWhite data isnull");
            return false;
        }
        try {
            return Boolean.parseBoolean(data.toString());
        } catch (Exception e) {
            log.info("RENEWAL_INSURANCE_WHITE isWhite e:{}", e);
            return false;
        }
    }
    @Override
    public List<Integer> checkFollowAI(List<FullLeadsFollowDto> followList) {
        if (CollUtil.isEmpty(followList)) {
            return null;
        }
        List<FullLeadsFollowDto> checkedList = followList.stream().filter(a -> Objects.equals(a.getSelectStatus(), 10041001)).collect(Collectors.toList());
        if (CollUtil.isEmpty(checkedList)) {
            return null;
        }
        RestResultResponse<List<Integer>> resp = domainMaintainLeadFeign.checkFollowAI(checkedList);
        log.info("check follow AI res:{}", resp);
        if (ObjectUtils.isEmpty(resp) || !ObjectUtils.nullSafeEquals(resp.getResultCode(), HttpStatus.OK.value())) {
            log.info("check follow ai failed");
            throw new ServiceBizException(resp.getErrMsg());
        }
        return resp.getData();
    }

    @Override
    public void deleteSelfCreateInvitation(Map<String, Object> param) {
        DmsResponse<Void> response = dmscusCustomerFeign.deleteSelfCreateInvitation(param);
        if (ObjectUtils.isEmpty(response) || response.isFail()) {
            throw new ServiceBizException("删除自建邀约线索失败");
        }
    }

    /**
     * 邀约通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    @Override
    public List<CallDetailVo> inviteCallList(Long detailId) {

        RestResultResponse<List<CallDetailVo>> resp = domainMaintainLeadFeign.inviteCallList(detailId);
        log.info("邀约通话记录查询返回结果：{}", JSONObject.toJSONString(resp));
        if (ObjectUtils.isEmpty(resp) || !ObjectUtils.nullSafeEquals(resp.getResultCode(), HttpStatus.OK.value())) {
            throw new ServiceBizException(resp.getErrMsg());
        }
        return resp.getData();
    }

    /**
     * 续保通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    @Override
    public List<CallDetailVo> insuranceCallList(Long detailId) {

        RestResultResponse<List<CallDetailVo>> resp = domainMaintainLeadFeign.insuranceCallList(detailId);
        log.info("续保通话记录查询返回结果：{}", JSONObject.toJSONString(resp));
        if (ObjectUtils.isEmpty(resp) || !ObjectUtils.nullSafeEquals(resp.getResultCode(), HttpStatus.OK.value())) {
            throw new ServiceBizException(resp.getErrMsg());
        }
        return resp.getData();
    }

    /**
     * 故障灯通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    @Override
    public List<CallDetailVo> faultLightCallList(Long detailId) {

        RestResultResponse<List<CallDetailVo>> resp = domainMaintainLeadFeign.faultLightCallList(detailId);
        log.info("故障灯通话记录查询返回结果：{}", JSONObject.toJSONString(resp));
        if (ObjectUtils.isEmpty(resp) || !ObjectUtils.nullSafeEquals(resp.getResultCode(), HttpStatus.OK.value())) {
            throw new ServiceBizException(resp.getErrMsg());
        }
        return resp.getData();
    }



    /**
     * 处理故障灯跟进信息
     *
     * @param followList 跟进信息
     */
    private List<FullLeadsFollowDto> handlerFaultLightLeadFollow(List<FullLeadsFollowDto> followList) {

        List<FullLeadsFollowDto> faultLightLeads = followList.stream()
                .filter(followDto -> ObjectUtils.nullSafeEquals(followDto.getInviteType(), InvitationTypeEnum.INVITE_TYPE_FAULT_LIGHT.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(faultLightLeads) || ObjectUtils.isEmpty(faultLightLeads.get(0).getVin())
                || ObjectUtils.isEmpty(faultLightLeads.get(0).getRoNo())) {
            log.info("故障灯跟进工单信息不存在，故障灯信息：{}", faultLightLeads);
            return followList;
        }
        List<FullLeadsFollowDto> otherLeads = Optional.of(followList.stream()
                .filter(followDto -> !ObjectUtils.nullSafeEquals(followDto.getInviteType(), InvitationTypeEnum.INVITE_TYPE_FAULT_LIGHT.getCode()))
                .collect(Collectors.toList())).orElse(new ArrayList<>());
        CurrentLoginInfoDto loginInfoDto = LoginInfoUtil.getCurrentLoginInfo();

        //故障灯工单信息查询
        for (FullLeadsFollowDto faultLeadFollowDto : faultLightLeads) {
            List<VehicleOwnerVO> orderList = this.fillFaultLightOrderInfo(faultLeadFollowDto, loginInfoDto);
            if (FaultFolOpeStateEnum.ALREADY_COMPLETED.getCode().equals(faultLeadFollowDto.getStatus())) {
                this.fillFaultLightFollowAmount(faultLeadFollowDto, orderList, loginInfoDto.getOwnerCode());
            }
        }
        otherLeads.addAll(faultLightLeads);
        return otherLeads;
    }
    /**
     * 续保线索保险跟进处理
     * @param followList 跟进信息
     */
    private void renewalInsuranceCheck(List<FullLeadsFollowDto> followList) {
        boolean flag = isWhite(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode());
        followList.stream().forEach(item -> item.setIsWhite(flag));
        // 校验经销商是否是白名单
        if (!flag) {
            // 非白名单，删除元素
            log.info("renewalInsuranceCheck is not white:{}", LoginInfoUtil.getCurrentLoginInfo().getOwnerCode());
            return;
        }
        log.info("handlerRenewalInsurance:");
        List<FullLeadsFollowDto> renewalInsurance = followList.stream()
                .filter(followDto -> ObjectUtils.nullSafeEquals(followDto.getInviteType(), InvitationTypeEnum.INVITE_TYPE_INSURANCE.getCode())
                        && !ObjectUtils.isEmpty(followDto.getAdviseInDate()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(renewalInsurance)) {
            log.info("非续保线索：");
            return;
        }

        List<Long> collect = renewalInsurance.stream().map(FullLeadsFollowDto::getId).collect(Collectors.toList());
        RestResultResponse<List<InsuranceVehicleRecordDTO>> listRestResultResponse = domainMaintainLeadFeign.queryRenewalInsurance(collect);
        log.info("handlerRenewalInsurance>listRestResultResponse:{}", JSON.toJSONString(listRestResultResponse));
        if (ObjectUtils.isEmpty(listRestResultResponse) || !ObjectUtils.nullSafeEquals(listRestResultResponse.getResultCode(), HttpStatus.OK.value())) {
            throw new ServiceBizException(listRestResultResponse.getErrMsg());
        }
        Map<Long, InsuranceVehicleRecordDTO> collect1 = listRestResultResponse.getData().stream().collect(Collectors.toMap(InsuranceVehicleRecordDTO::getId, Function.identity()));
        Integer day = 0; // 经销商提前出单天数
        Integer count = 1; // 修改次数校验
        DmsResponse<CommonConfigDto> configByKey = dmscloudServiceFeign.getConfigByKey(CommonConstant.RENEWAL_INSURANCE_KEY, CommonConstant.RENEWAL_INSURANCE_GROUP);
        log.info("handlerRenewalInsurance>configByKey:{}",JSON.toJSONString(configByKey));
        if (!ObjectUtils.isEmpty(configByKey) && Objects.nonNull(configByKey.getData())) {
            count = Integer.valueOf(configByKey.getData().getConfigValue());
        }
        for (FullLeadsFollowDto f: renewalInsurance) {
            if(!collect1.containsKey(f.getId())){
                log.info("未查询到对应的线索");
                continue;
            }
            InsuranceVehicleRecordDTO insuranceVehicleRecordDTO = collect1.get(f.getId());
            // 校验续保日期是否发生变更
            String fs = DateUtil.formatDateByFormat(f.getAdviseInDate(), DateUtil.SIMPLE_DATE_FORMAT);
            String ins = DateUtil.formatDateByFormat(insuranceVehicleRecordDTO.getAdviseInDate(), DateUtil.SIMPLE_DATE_FORMAT);
            if(fs.equals(ins)){
                log.info("保险到期日期未发生变化");
                continue;
            }

            // 校验续保日期是否合规
            if(day == 0){
                // 理论上在全量线索上跟进时，即便有多条续保线索，但经销商应该时同一个，
                DmsResponse<Page<InsuranceLeadsConfigDto>> pageDmsResponse = domainMaintainAuthFeign.queryCompanyConfig("", "", insuranceVehicleRecordDTO.getDealerCode(), 1, 10);
                log.info("renewalInsuranceCheck>>>:{}", JSON.toJSONString(pageDmsResponse));
                if (ObjectUtils.isEmpty(pageDmsResponse)) {
                    throw new ServiceBizException("查询经销商是否白名单失败!");
                }
                if(CollectionUtils.isEmpty(pageDmsResponse.getData().getRecords())){
                    throw new ServiceBizException(insuranceVehicleRecordDTO.getDealerCode()+"未查询到提前出单日期,不允许修改保险到期日期");
                }
                day = pageDmsResponse.getData().getRecords().get(0).getAdvanceDays();
            }
            switch (insuranceVehicleRecordDTO.getClueIssuanceType()) {
                case 96171002:
                    // 店端下发
                    Date date6 = new Date();
                    String date6Str = DateUtil.formatDateByFormat(date6, DateUtil.SIMPLE_DATE_FORMAT);
                    Date date57 = DateUtil.parseDate(date6Str, DateUtil.SIMPLE_DATE_FORMAT);
                    Date date8 = DateUtil.addDay(date57, day);
                    // 店端保险到期时间和当前时间比 0 等于 -1小于 1大于
                    int j = insuranceVehicleRecordDTO.getAdviseInDate().compareTo(date57);
                    if(j < 0){
                        throw new ServiceBizException("保险到期时间小于当前时间!");
                    }
                    // 店端保险到期时间和最大续保到期日期比较 0 等于 -1小于 1大于
                    int k = insuranceVehicleRecordDTO.getAdviseInDate().compareTo(date8);
                    if(k > 0){
                        throw new ServiceBizException("保险到期时间大于最大续保到期日期!");
                    }

                    // 0 等于 -1小于 1大于
                    int i = f.getAdviseInDate().compareTo(date57);
                    int i1 = f.getAdviseInDate().compareTo(date8);
                    if(i < 0 || i1 > 0){
                        throw new ServiceBizException("保险到期日期必须在报价期内日期");
                    }
                    break;
                default:
                    // 厂端下发线索
                    RAtomicLong atomicLong = redissonClient.getAtomicLong(RedisConstants.RENEWAL_INSURANCE_REDIS_KEY + f.getId());
                    long l = atomicLong.get();
                    log.info("RAtomicLong>>>:{}", l);
                    if(l == 0){
                        // 第一次进来,设置过期时间
                        atomicLong.expire(CommonConstant.RENEWAL_INSURANCE_MODIFY_DAYS, TimeUnit.DAYS);
                    }
                    l = ++l;
                    if(l > count){
                        throw new ServiceBizException("厂端线索修改保险到期日期超出次数限制");
                    }
                    Date date1 = new Date();
                    String date1Str = DateUtil.formatDateByFormat(date1, DateUtil.SIMPLE_DATE_FORMAT);
                    Date date5 = DateUtil.parseDate(date1Str, DateUtil.SIMPLE_DATE_FORMAT);

                    Date date2 = insuranceVehicleRecordDTO.getAdviseInDate();
                    Date lastDayOfLastMonth = DateUtil.getLastDayOfLastMonth(date2);
                    // 0 等于 -1小于 1大于
                    int i2 = date5.compareTo(lastDayOfLastMonth);
                    if(i2 > 0){
                        throw new ServiceBizException("厂端线索修改保险到期日期必须在N-1月及以前");
                    }
                    Date date4 = DateUtil.addMonth(date2, 11);
                    int i3 = f.getAdviseInDate().compareTo(date4);
                    int i4 = f.getAdviseInDate().compareTo(date2);
                    if(i3 > 0 ||  i4 < 0){
                        throw new ServiceBizException("厂端线索修改保险到期日期不在范围内");
                    }
                    break;
            }

        }

    }
    /**
     * 查询工单信息
     *
     * @param faultLeadFollowDto 跟进参数
     * @param loginInfoDto       登录信息
     * @return 工单信息
     */
    private List<VehicleOwnerVO> fillFaultLightOrderInfo(FullLeadsFollowDto faultLeadFollowDto, CurrentLoginInfoDto loginInfoDto) {

        RestResultResponse<List<VehicleOwnerVO>> response = dmscloudServiceFeign.queryRepairOrder(loginInfoDto.getOwnerCode(),
                faultLeadFollowDto.getVin(), faultLeadFollowDto.getRoNo());
        log.info("故障灯跟进工单信息查询结果：{}", JSONObject.toJSONString(response));
        if (ObjectUtils.isEmpty(response) || CollectionUtils.isEmpty(response.getData())
                || !ObjectUtils.nullSafeEquals(response.getResultCode(), HttpStatus.OK.value())) {
            throw new ServiceBizException(FullLeadsErrorEnum.ERROR_REPAIR_ORDER_QUERY.getDesc());
        }
        VehicleOwnerVO orderInfo = response.getData().get(0);
        faultLeadFollowDto.setRoStartTime(orderInfo.getCreatedAt());
        faultLeadFollowDto.setRoType(orderInfo.getOrType());
        faultLeadFollowDto.setRoEndTime(orderInfo.getForBalanceTime());

        return response.getData();
    }

    /**
     * 查询结算金额
     *
     * @param faultLeadFollowDto 跟进参数
     * @param orderList          工单信息
     * @param dealerCode         经销商代码
     */
    private void fillFaultLightFollowAmount(FullLeadsFollowDto faultLeadFollowDto, List<VehicleOwnerVO> orderList,
                                            String dealerCode) {

        List<String> balanceNoList = orderList.stream()
                .map(VehicleOwnerVO::getBalanceNo)
                .filter(balanceNo -> !ObjectUtils.nullSafeEquals(PAYMENT_OBJECT_CODE, balanceNo))
                .collect(Collectors.toList());
        RestResultResponse<PrintDataVo> response = dmscloudServiceFeign.balancePrintData(PrintParamVo.buildParams(balanceNoList, orderList.get(0), dealerCode));
        log.info("工单金额查询结果：{}", JSONObject.toJSONString(response));
        if (ObjectUtils.isEmpty(response) || ObjectUtils.isEmpty(response.getData())
                || !ObjectUtils.nullSafeEquals(response.getResultCode(), HttpStatus.OK.value())) {
            throw new ServiceBizException(FullLeadsErrorEnum.ERROR_REPAIR_ORDER_QUERY.getDesc());
        }
        faultLeadFollowDto.setAmount(response.getData().getAmountForDiscount());
    }

    /**
     * 功能描述：全量线索通话记录保存
     *
     * @param dto 参数
     */
    private void saveCallRecord(FullLeadsCallDto dto) {

        if (ObjectUtils.isEmpty(dto.getInviteType())
                || ObjectUtils.isEmpty(dto.getId()) || ObjectUtils.isEmpty(dto.getSaId())) {
            log.info("通话记录参数不完整,参数：{}", JSONObject.toJSONString(dto));
            return;
        }
        //保存续保线索通话记录
        RestResultResponse<String> response;
        if (ObjectUtils.nullSafeEquals(dto.getInviteType(), InvitationTypeEnum.INVITE_TYPE_INSURANCE.getCode())) {
            log.info("保存续保线索通话记录，参数：{}", JSONObject.toJSONString(dto));
            InviteInsuranceVehicleCustomerNumberDTO param = InviteInsuranceVehicleCustomerNumberDTO.buildSaveParam(dto);
            response = dmscusCustomerFeign.saveInsuranceSaCustomerNumber(param);
            log.info("保存续保线索通话记录，结果：{}", JSONObject.toJSONString(response));
            dto.setWorkNum(this.findRespWorkNum(response));
        }
        //保存邀约线索通话记录
        if (InvitationTypeEnum.findInviteTypeList().contains(dto.getInviteType())) {
            log.info("保存邀约线索通话记录，参数：{}", JSONObject.toJSONString(dto));
            response = dmscusCustomerFeign.saveInviteSaCustomerNumber(SaCustomerNumberDTO.buildSaveParam(dto));
            log.info("保存邀约线索通话记录，结果：{}", JSONObject.toJSONString(response));
            dto.setWorkNum(this.findRespWorkNum(response));
        }
        //保存故障灯线索通话记录
        if (ObjectUtils.nullSafeEquals(dto.getInviteType(), InvitationTypeEnum.INVITE_TYPE_FAULT_LIGHT.getCode())) {
            log.info("保存故障灯线索通话记录，参数：{}", JSONObject.toJSONString(dto));
            response = dmscusCustomerFeign.saveFaultLightSaCustomerNumber(SaCustomerNumberDTO.buildSaveParam(dto));
            log.info("保存故障灯线索通话记录，结果：{}", JSONObject.toJSONString(response));
            dto.setWorkNum(this.findRespWorkNum(response));
        }
    }

    /**
     * 获取绑定工作号
     * @param response 响应结果
     * @return 工作号
     */
    private String findRespWorkNum(RestResultResponse<String> response){

        if (ObjectUtils.isEmpty(response) || !ObjectUtils.nullSafeEquals(response.getResultCode(), HttpStatus.OK.value())){
            throw new ServiceBizException(response.getErrMsg());
        }

        return response.getData();
    }

    /**
     * 填充区域信息
     *
     * @param params 全量线索查询参数
     */
    private void fillDealerCodeList(FullLeadQueryDto params) {

        //查询组织中心获取经销商代码列表
        CompanySelectDTO dto = new CompanySelectDTO(params.getLargeAreaId(), params.getAreaId(), params.getDealerCode());
        RestResultResponse<List<CompanyDetailDTO>> response = midEndOrgCenterFeign.selectByCompanyCode(dto);
        log.info("区域信息获取结果：{}", JSONObject.toJSONString(response));
        if (ObjectUtils.isEmpty(response)) {
            throw new ServiceBizException(FullLeadsErrorEnum.ERROR_QUERY_AREA.getDesc());
        }
        if (CollectionUtils.isEmpty(response.getData())) {
            params.setDealerCode(null);
            params.setDealerCodeList(null);
            return;
        }
        params.setDealerCodeList(response.getData().stream().map(CompanyDetailDTO::getCompanyCode).collect(Collectors.toList()));
    }

    /**
     * 建议进厂日期必填且跨度不能超过一年
     *
     * @param params 查询参数
     */
    private void checkAdviseInDate(FullLeadQueryDto params) {

        if (ObjectUtils.isEmpty(params.getAdviseInDateBegin()) || ObjectUtils.isEmpty(params.getAdviseInDateEnd())) {
            throw new ServiceBizException(FullLeadsErrorEnum.ERROR_DATE_IS_EMPTY.getDesc());
        }
        LocalDate begin = LocalDate.parse(params.getAdviseInDateBegin());
        LocalDate end = LocalDate.parse(params.getAdviseInDateEnd());
        int diffYear = end.until(begin).getYears();
        log.info("建议进场日期差值为：{}年", diffYear);
        if (diffYear != 0) {
            throw new ServiceBizException(FullLeadsErrorEnum.ERROR_DATE_EXCEED_YEAR.getDesc());
        }
    }

    /**
     * 导出字段
     */
    private List<ExcelExportColumn> buildExcelColumn() {

        List<ExcelExportColumn> exportColumnList = new ArrayList<>();
        exportColumnList.add(new ExcelExportColumn("licensePlateNum", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("inviteTypeDesc", "邀约类型"));
        exportColumnList.add(new ExcelExportColumn("adviseInDate", "建议跟进日期"));
        exportColumnList.add(new ExcelExportColumn("returnIntentionLevel", "返厂概率"));
        exportColumnList.add(new ExcelExportColumn("insuranceTypeDesc", "续保客户类型"));
        exportColumnList.add(new ExcelExportColumn("warningName", "故障类别"));
        exportColumnList.add(new ExcelExportColumn("couponCode", "券码"));
        exportColumnList.add(new ExcelExportColumn("couponName", "卡券名称"));
        exportColumnList.add(new ExcelExportColumn("followStatusDesc", "跟进状态"));
        exportColumnList.add(new ExcelExportColumn("totalScore", "AI得分"));
        exportColumnList.add(new ExcelExportColumn("callTime", "通话时间"));
        exportColumnList.add(new ExcelExportColumn("callLength", "通话时长(秒)"));
        exportColumnList.add(new ExcelExportColumn("planFollowDate", "下次跟进日期"));
        exportColumnList.add(new ExcelExportColumn("actualFollowDate", "实际跟进日期"));
        exportColumnList.add(new ExcelExportColumn("saName", "跟进人员"));
        exportColumnList.add(new ExcelExportColumn("recordNum", "线索跟进次数"));
        exportColumnList.add(new ExcelExportColumn("createdAt", "线索下发日期"));
        exportColumnList.add(new ExcelExportColumn("content", "跟进内容"));
        exportColumnList.add(new ExcelExportColumn("loseReasonDesc", "失败原因"));
        exportColumnList.add(new ExcelExportColumn("itemCodeDesc", "未用AI原因"));
        exportColumnList.add(new ExcelExportColumn("itemTypeDesc", "线索异常状态"));
        exportColumnList.add(new ExcelExportColumn("isAi", "是否二次跟进"));
        exportColumnList.add(new ExcelExportColumn("lossTypeDesc", "流失线索类型"));
        exportColumnList.add(new ExcelExportColumn("itemName", "备注"));

        return exportColumnList;
    }

    private String getMqDestination(String topic,String tag){
        return org.apache.commons.lang3.StringUtils.join(Lists.newArrayList(topic,tag),":");
    }

    private void push(Object body) {
        try {
            rocketMQTemplate.asyncSend(getMqDestination(renewalLeadTopic,renewalLeadTag), new Message<String>() {
                @Override
                public String getPayload() {
                    return JSON.toJSONString(body);
                }

                @Override
                public MessageHeaders getHeaders() {
                    HashMap<String, Object> headers = Maps.newHashMap();
                    return new MessageHeaders(headers);
                }
            }, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("pushMessage,消息投递成功,result:{}", JSON.toJSONString(sendResult));
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("pushMessage,消息投递失败:{}", throwable);
                }
            });
        } catch (Exception e) {
            log.error("pushMessage,推送消息失败:{}", e);
        }
    }
}
