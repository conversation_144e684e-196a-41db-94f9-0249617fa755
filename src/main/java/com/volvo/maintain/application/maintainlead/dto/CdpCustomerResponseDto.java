package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("cdp查询会员返回")
public class CdpCustomerResponseDto {

    //档案类型唯一标识
    private String subject_profile_type;
    //关联的档案唯一标识
    private String object_profile_type;
    //档案
    private List<RelatedProfilesDto> related_profiles;

}
