package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InsuranceBillDto implements Serializable {

    /**
     * 经销商
     */
    private String dealerCode;

    /**
     * vin
     */
    private String vin;

    /**
     * 保单号
     */
    private String insuranceNo;

    /**
     * 保单状态（'80721001','未完成'、'80721002','已成交'、'80721003','已完成'、'80721004','已作废'、'80721005','已结案'）
     */
    private Integer insuranceStatus;

    /**
     * 是否易保（10041001 = 易保， 10041002 = 经销商自建）
     */
    private Integer isChangeInsurance;

    /**
     * 交强险生效日期（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String clivtaInsuranceDate;

    /**
     * 交强险生效日期（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String clivtaFinishDate;

    /**
     * 商业险生效日期（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String viInsuranceDate;

    /**
     * 商业险结束日期（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String viFinishDate;

    /**
     * 投保人姓名
     */
    private String policyHolderName;

    /**
     * 投保人电话
     */
    private String policyHolderPhone;

    /**
     * 投保人手机
     */
    private String policyHolderMobile;

    /**
     * 投保人邮编
     */
    private String policyHolderZipcode;

    /**
     * 投保人地址
     */
    private String policyHolderAddress;


}
