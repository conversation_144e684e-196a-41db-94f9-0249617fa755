package com.volvo.maintain.application.maintainlead.dto;



import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "经销商查询信息", description="经销商查询信息")
public class CompanyDetailByCodeDto {

	private Integer allocationType;

	@ApiModelProperty(value = "经销商代码id")
	private Long    companyId;

	@ApiModelProperty(value = "经销商代码")
	private String  companyCode;

	@ApiModelProperty(value = "经销商名称中文")
	private String  companyNameCn;

	@ApiModelProperty(value = "经销商简称中文")
	private String  companyShortNameCn;

	@ApiModelProperty(value = "公司类型:1005")
	private Integer companyType;

	@ApiModelProperty(value = "集团")
	private String    groupCompanyId;

	@ApiModelProperty(value = "集团名称")
	private String  groupCompanyName;

	@ApiModelProperty(value = "省份")
	private Integer provinceId;
	@ApiModelProperty(value = "省份")
	private String provinceName;

	@ApiModelProperty(value = "城市")
	private Integer cityId;
	@ApiModelProperty(value = "城市")
	private String cityName;

	@ApiModelProperty(value = "区域")
	private Integer countyId;
	@ApiModelProperty(value = "区域")
	private String countyName;

	@ApiModelProperty(value = "地址")
	private String  addressZh;

	@ApiModelProperty(value = "经度")
	private double  longitude;

	@ApiModelProperty(value = "纬度")
	private double  latitude;

	@ApiModelProperty(value = "电话")
	private String  phone;

	@ApiModelProperty(value = "传真")
	private String  fax;

	@ApiModelProperty(value = "经销商类型")
	private Integer dealerType;

	@ApiModelProperty(value = "经销商规模")
	private Integer dealerScale;

	@ApiModelProperty(value = "是否批售授权经销商")
	private Integer wholesaleGrant;

	@ApiModelProperty(value = "状态")
	private Integer status;

	@ApiModelProperty(value = "备注")
	private String  remark;

	@ApiModelProperty(value = "销售热线")
	private String  salesLine;

	@ApiModelProperty(value = "销售ParmaCode")
	private String  salesParmaCode;

	@ApiModelProperty(value = "客户评分销售")
	private String  gradeSales;

	@ApiModelProperty(value = "开户银行销售")
	private String  bankSales;

	@ApiModelProperty(value = "开户账号销售")
	private String  bankAccountSales;

	@ApiModelProperty(value = "税号销售")
	private String  taxSales;

	@ApiModelProperty(value = "售后热线")
	private String  afterLine;

	@ApiModelProperty(value = "售后ParmaCode")
	private String  afterParmaCode;

	@ApiModelProperty(value = "客户评分售后")
	private String  gradeAfter;

	@ApiModelProperty(value = "开户银行售后")
	private String  bankAfter;

	@ApiModelProperty(value = "开户账号售后")
	private String  bankAccountAfter;

	@ApiModelProperty(value = "税号售后")
	private String  taxAfter;

	@ApiModelProperty(value = "FACILITY")
	private String  facility;

	@ApiModelProperty(value = "DEALERSHIP_OUTLET")
	private String  dealershipOutlet;

	@ApiModelProperty(value = "OPERATION_DATE_DN")
	private String    operationDateDn;

	@ApiModelProperty(value = "OPERATION_DATE_RD")
	private String    operationDateRd;

	@ApiModelProperty(value = "OPERATION_DATE_INTERNET")
	private String    operationDateInterent;

	@ApiModelProperty(value = "RELOCATION_OR_UPGRADE")
	private String  relocationOrUpgrade;

	@ApiModelProperty(value = "SHORT_DATE")
	private String    shortDate;

	@ApiModelProperty(value = "VIPS_CODE")
	private String  vipsCode;

	@ApiModelProperty(value = "VR_OOED")
	private String  vrCode;

	@ApiModelProperty(value = "VMI_LDC")
	private Integer vmiLdc;

	@ApiModelProperty(value = "零配件仓库")
	private String  partWarehouse;

	@ApiModelProperty(value = "仓库发货地址")
	private String  warehouseAddress;

	@ApiModelProperty(value = "开店时间")
	private String  openTime;

	@ApiModelProperty(value = "关店时间")
	private String  closeTime;

	@ApiModelProperty(value = "经销商门头照片url")
	private String  storefrontPhotoUrl;

	@ApiModelProperty(value = "组织id")
	private Long  orgId;

	@ApiModelProperty(value = "组织代码")
	private String  orgCode;

	@ApiModelProperty(value = "组织名")
	private String  orgName;

	@ApiModelProperty(value = "组织名称缩写")
	private String  orgShortName;

	@ApiModelProperty(value = "组织描述")
	private String  orgDesc;

	@ApiModelProperty(value = "组织类型ID")
	private Integer orgType;

	@ApiModelProperty(value = "上级组织ID")
	private String    parentOrgId;

	@ApiModelProperty(value = "售后大区ID")
	private Long    afterBigAreaId;

	@ApiModelProperty(value = "售后大区名称")
	private String  afterBigAreaName;

	@ApiModelProperty(value = "售后小区ID")
	private Long    afterSmallAreaId;

	@ApiModelProperty(value = "售后小区名称")
	private String  afterSmallAreaName;

	@ApiModelProperty(value = "销售大区ID")
	private Long    saleBigAreaId;

	@ApiModelProperty(value = "销售大区名称")
	private String  saleBigAreaName;

	@ApiModelProperty(value = "销售小区ID")
	private Long    saleSmallAreaId;

	@ApiModelProperty(value = "销售小区名称")
	private String  saleSmallAreaName;


	public Long getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getCompanyNameCn() {
		return companyNameCn;
	}

	public void setCompanyNameCn(String companyNameCn) {
		this.companyNameCn = companyNameCn;
	}

	public String getCompanyShortNameCn() {
		return companyShortNameCn;
	}

	public void setCompanyShortNameCn(String companyShortNameCn) {
		this.companyShortNameCn = companyShortNameCn;
	}

	public Integer getCompanyType() {
		return companyType;
	}

	public void setCompanyType(Integer companyType) {
		this.companyType = companyType;
	}

	public String getGroupCompanyId() {
		return groupCompanyId;
	}

	public void setGroupCompanyId(String groupCompanyId) {
		this.groupCompanyId = groupCompanyId;
	}

	public String getGroupCompanyName() {
		return groupCompanyName;
	}

	public void setGroupCompanyName(String groupCompanyName) {
		this.groupCompanyName = groupCompanyName;
	}

	public Integer getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Integer provinceId) {
		this.provinceId = provinceId;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public Integer getCityId() {
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public Integer getCountyId() {
		return countyId;
	}

	public void setCountyId(Integer countyId) {
		this.countyId = countyId;
	}

	public String getCountyName() {
		return countyName;
	}

	public void setCountyName(String countyName) {
		this.countyName = countyName;
	}

	public String getAddressZh() {
		return addressZh;
	}

	public void setAddressZh(String addressZh) {
		this.addressZh = addressZh;
	}

	public double getLongitude() {
		return longitude;
	}

	public void setLongitude(double longitude) {
		this.longitude = longitude;
	}

	public double getLatitude() {
		return latitude;
	}

	public void setLatitude(double latitude) {
		this.latitude = latitude;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public Integer getDealerType() {
		return dealerType;
	}

	public void setDealerType(Integer dealerType) {
		this.dealerType = dealerType;
	}

	public Integer getDealerScale() {
		return dealerScale;
	}

	public void setDealerScale(Integer dealerScale) {
		this.dealerScale = dealerScale;
	}

	public Integer getWholesaleGrant() {
		return wholesaleGrant;
	}

	public void setWholesaleGrant(Integer wholesaleGrant) {
		this.wholesaleGrant = wholesaleGrant;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getSalesLine() {
		return salesLine;
	}

	public void setSalesLine(String salesLine) {
		this.salesLine = salesLine;
	}

	public String getSalesParmaCode() {
		return salesParmaCode;
	}

	public void setSalesParmaCode(String salesParmaCode) {
		this.salesParmaCode = salesParmaCode;
	}

	public String getGradeSales() {
		return gradeSales;
	}

	public void setGradeSales(String gradeSales) {
		this.gradeSales = gradeSales;
	}

	public String getBankSales() {
		return bankSales;
	}

	public void setBankSales(String bankSales) {
		this.bankSales = bankSales;
	}

	public String getBankAccountSales() {
		return bankAccountSales;
	}

	public void setBankAccountSales(String bankAccountSales) {
		this.bankAccountSales = bankAccountSales;
	}

	public String getTaxSales() {
		return taxSales;
	}

	public void setTaxSales(String taxSales) {
		this.taxSales = taxSales;
	}

	public String getAfterLine() {
		return afterLine;
	}

	public void setAfterLine(String afterLine) {
		this.afterLine = afterLine;
	}

	public String getAfterParmaCode() {
		return afterParmaCode;
	}

	public void setAfterParmaCode(String afterParmaCode) {
		this.afterParmaCode = afterParmaCode;
	}

	public String getGradeAfter() {
		return gradeAfter;
	}

	public void setGradeAfter(String gradeAfter) {
		this.gradeAfter = gradeAfter;
	}

	public String getBankAfter() {
		return bankAfter;
	}

	public void setBankAfter(String bankAfter) {
		this.bankAfter = bankAfter;
	}

	public String getBankAccountAfter() {
		return bankAccountAfter;
	}

	public void setBankAccountAfter(String bankAccountAfter) {
		this.bankAccountAfter = bankAccountAfter;
	}

	public String getTaxAfter() {
		return taxAfter;
	}

	public void setTaxAfter(String taxAfter) {
		this.taxAfter = taxAfter;
	}

	public String getFacility() {
		return facility;
	}

	public void setFacility(String facility) {
		this.facility = facility;
	}

	public String getDealershipOutlet() {
		return dealershipOutlet;
	}

	public void setDealershipOutlet(String dealershipOutlet) {
		this.dealershipOutlet = dealershipOutlet;
	}

	public String getOperationDateDn() {
		return operationDateDn;
	}

	public void setOperationDateDn(String operationDateDn) {
		this.operationDateDn = operationDateDn;
	}

	public String getOperationDateRd() {
		return operationDateRd;
	}

	public void setOperationDateRd(String operationDateRd) {
		this.operationDateRd = operationDateRd;
	}

	public String getOperationDateInterent() {
		return operationDateInterent;
	}

	public void setOperationDateInterent(String operationDateInterent) {
		this.operationDateInterent = operationDateInterent;
	}

	public String getRelocationOrUpgrade() {
		return relocationOrUpgrade;
	}

	public void setRelocationOrUpgrade(String relocationOrUpgrade) {
		this.relocationOrUpgrade = relocationOrUpgrade;
	}

	public String getShortDate() {
		return shortDate;
	}

	public void setShortDate(String shortDate) {
		this.shortDate = shortDate;
	}

	public String getVipsCode() {
		return vipsCode;
	}

	public void setVipsCode(String vipsCode) {
		this.vipsCode = vipsCode;
	}

	public String getVrCode() {
		return vrCode;
	}

	public void setVrCode(String vrCode) {
		this.vrCode = vrCode;
	}

	public Integer getVmiLdc() {
		return vmiLdc;
	}

	public void setVmiLdc(Integer vmiLdc) {
		this.vmiLdc = vmiLdc;
	}

	public String getPartWarehouse() {
		return partWarehouse;
	}

	public void setPartWarehouse(String partWarehouse) {
		this.partWarehouse = partWarehouse;
	}

	public String getWarehouseAddress() {
		return warehouseAddress;
	}

	public void setWarehouseAddress(String warehouseAddress) {
		this.warehouseAddress = warehouseAddress;
	}

	public String getOpenTime() {
		return openTime;
	}

	public void setOpenTime(String openTime) {
		this.openTime = openTime;
	}

	public String getCloseTime() {
		return closeTime;
	}

	public void setCloseTime(String closeTime) {
		this.closeTime = closeTime;
	}

	public String getStorefrontPhotoUrl() {
		return storefrontPhotoUrl;
	}

	public void setStorefrontPhotoUrl(String storefrontPhotoUrl) {
		this.storefrontPhotoUrl = storefrontPhotoUrl;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgShortName() {
		return orgShortName;
	}

	public void setOrgShortName(String orgShortName) {
		this.orgShortName = orgShortName;
	}

	public String getOrgDesc() {
		return orgDesc;
	}

	public void setOrgDesc(String orgDesc) {
		this.orgDesc = orgDesc;
	}

	public Integer getOrgType() {
		return orgType;
	}

	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}

	public String getParentOrgId() {
		return parentOrgId;
	}

	public void setParentOrgId(String parentOrgId) {
		this.parentOrgId = parentOrgId;
	}

	public Long getAfterBigAreaId() {
		return afterBigAreaId;
	}

	public void setAfterBigAreaId(Long afterBigAreaId) {
		this.afterBigAreaId = afterBigAreaId;
	}

	public String getAfterBigAreaName() {
		return afterBigAreaName;
	}

	public void setAfterBigAreaName(String afterBigAreaName) {
		this.afterBigAreaName = afterBigAreaName;
	}

	public Long getAfterSmallAreaId() {
		return afterSmallAreaId;
	}

	public void setAfterSmallAreaId(Long afterSmallAreaId) {
		this.afterSmallAreaId = afterSmallAreaId;
	}

	public String getAfterSmallAreaName() {
		return afterSmallAreaName;
	}

	public void setAfterSmallAreaName(String afterSmallAreaName) {
		this.afterSmallAreaName = afterSmallAreaName;
	}

	public Long getSaleBigAreaId() {
		return saleBigAreaId;
	}

	public void setSaleBigAreaId(Long saleBigAreaId) {
		this.saleBigAreaId = saleBigAreaId;
	}

	public String getSaleBigAreaName() {
		return saleBigAreaName;
	}

	public void setSaleBigAreaName(String saleBigAreaName) {
		this.saleBigAreaName = saleBigAreaName;
	}

	public Long getSaleSmallAreaId() {
		return saleSmallAreaId;
	}

	public void setSaleSmallAreaId(Long saleSmallAreaId) {
		this.saleSmallAreaId = saleSmallAreaId;
	}

	public String getSaleSmallAreaName() {
		return saleSmallAreaName;
	}

	public void setSaleSmallAreaName(String saleSmallAreaName) {
		this.saleSmallAreaName = saleSmallAreaName;
	}

	@Override
	public String toString() {
		return "CompanyDetailByCodeDtO [ companyCode=" + companyCode + ", companyNameCn=" + companyNameCn
				+ ", companyShortNameCn=" + companyShortNameCn + ", companyType=" + companyType + ", groupCompanyId="
				+ groupCompanyId + ", groupCompanyName=" + groupCompanyName + ", provinceId=" + provinceId + ", cityId="
				+ cityId + ", countyId=" + countyId + ", addressZh=" + addressZh + ", longitude=" + longitude
				+ ", latitude=" + latitude + ", phone=" + phone + ", fax=" + fax + ", dealerType=" + dealerType
				+ ", dealerScale=" + dealerScale + ", wholesaleGrant=" + wholesaleGrant + ", status=" + status
				+ ", remark=" + remark + ", salesLine=" + salesLine + ", salesParmaCode=" + salesParmaCode
				+ ", gradeSales=" + gradeSales + ", bankSales=" + bankSales + ", bankAccountSales=" + bankAccountSales
				+ ", taxSales=" + taxSales + ", afterLine=" + afterLine + ", afterParmaCode=" + afterParmaCode
				+ ", gradeAfter=" + gradeAfter + ", bankAfter=" + bankAfter + ", bankAccountAfter=" + bankAccountAfter
				+ ", taxAfter=" + taxAfter + ", facility=" + facility + ", dealershipOutlet=" + dealershipOutlet
				+ ", operationDateDn=" + operationDateDn + ", operationDateRd=" + operationDateRd
				+ ", operationDateInterent=" + operationDateInterent + ", relocationOrUpgrade=" + relocationOrUpgrade
				+ ", shortDate=" + shortDate + ", vipsCode=" + vipsCode + ", vrCode=" + vrCode + ", vmiLdc=" + vmiLdc
				+ ", partWarehouse=" + partWarehouse + ", warehouseAddress=" + warehouseAddress + ", openTime="
				+ openTime + ", closeTime=" + closeTime + ", storefrontPhotoUrl=" + storefrontPhotoUrl + ", orgId="
				+ orgId + ", orgCode=" + orgCode + ", orgName=" + orgName + ", orgShortName=" + orgShortName
				+ ", orgDesc=" + orgDesc + ", orgType=" + orgType + ", parentOrgId=" + parentOrgId + "allocationType=" + allocationType + "]";
	}

	public Integer getAllocationType() {
		return allocationType;
	}

	public void setAllocationType(Integer allocationType) {
		this.allocationType = allocationType;
	}







}
