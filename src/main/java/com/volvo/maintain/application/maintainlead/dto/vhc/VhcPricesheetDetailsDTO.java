package com.volvo.maintain.application.maintainlead.dto.vhc;

import lombok.Data;

/**
 * 车辆健康检查报价详情dto
 */
@Data
public class VhcPricesheetDetailsDTO {
    //工单号
    private String roNo;
    //车牌号
    private String  license;
    //车架号
    private String  vin;
    //用户反馈时间
    private String vhcFeedbackTime;
    //报价转入工单时间
    private String vhcPriceOrderTime;
    //手机号
    private String delivererPhone;
    //检查状态
    private String vhcState;

    //维修类型
    private String repairTypeCode;

    //派工状态
    private String assignState;

    //检查单号
    private String vhcNo;

    //服务顾问
    private String serviceAdvisor;

    //工单开单日期
    private String roCreateDate;

    //技师
    private String technician;

    //预计交车时间
    private String endTimeSupposed;

    //车型
    private String model;

    //车主
    private String ownerName;

    //送修人
    private String deliverer;

    //送修人手机
    private String delivererMobile;

//*新增 流转节点：
//    最后点击推送给谁，此处展示流转角色；未点则为空；流转服务顾问&流转仓库
    private String remindType;
//*新增 流转时间：
//    最后一次点击流转服务顾问&流转仓库的时间
    private String remindCreatedAt;
    //车辆类型
    private String vhcType;
    //报价单号
    private String vhcPricesheetNo;













}
