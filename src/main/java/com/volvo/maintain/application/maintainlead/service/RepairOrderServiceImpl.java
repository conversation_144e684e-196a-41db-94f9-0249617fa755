package com.volvo.maintain.application.maintainlead.service;


import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.MaintainActivityInfoRespDTO;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderAndClaimTypeReqDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderAndClaimTypeRespDto;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderAndClaimTypeRespVO;
import com.volvo.maintain.infrastructure.gateway.DmscusIfserviceFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;

import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;


@ApiModel
@Service
@Slf4j
public class RepairOrderServiceImpl implements RepairOrderService {
	
	@Resource
	private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
	
	@Resource
	private DmscusIfserviceFeign dmscusIfserviceFeign;
	
	
	@Override
	public List<RepairOrderAndClaimTypeRespVO> queryRepairOrderAndClaimTypeByVin(RepairOrderAndClaimTypeReqDto repairOrderAndClaimTypeReq) {
		log.info("queryRepairOrderAndClaimTypeByVin-repairOrderAndClaimTypeReq : {}", JSON.toJSONString(repairOrderAndClaimTypeReq));
		DmsResponse<List<RepairOrderAndClaimTypeRespDto>> queryRepairOrderAndClaimTypeByVin = domainMaintainOrdersFeign.queryRepairOrderAndClaimTypeByVin(repairOrderAndClaimTypeReq);
		log.info("queryRepairOrderAndClaimTypeByVin-return: {}", JSON.toJSONString(queryRepairOrderAndClaimTypeByVin));
		if(Objects.isNull(queryRepairOrderAndClaimTypeByVin) || queryRepairOrderAndClaimTypeByVin.isFail()) {
			return Collections.emptyList();
		}
		
		// 根据活动编号查询 保修类型
		List<RepairOrderAndClaimTypeRespDto> data = queryRepairOrderAndClaimTypeByVin.getData();
		List<String> maintainNos = data.stream().filter(Objects::nonNull).map(RepairOrderAndClaimTypeRespDto::getActivityNo).filter(Objects::nonNull).collect(Collectors.toList());
		DmsResponse<List<MaintainActivityInfoRespDTO>> queryMaintainActivityList = dmscusIfserviceFeign.queryMaintainActivityList(maintainNos);
		Map<String,String> map = new HashMap<>();
		if(Objects.nonNull(queryMaintainActivityList) && queryMaintainActivityList.isSuccess()) {
			List<MaintainActivityInfoRespDTO> maintainActivityInfo = queryMaintainActivityList.getData();
			map = maintainActivityInfo.stream().filter(Objects::nonNull).collect(Collectors.toMap(MaintainActivityInfoRespDTO::getMaintainNos, MaintainActivityInfoRespDTO::getClaimType, (k1, k2)->k1));
		}
		Map<String,String> mapFin = map;
		// 调用ifservice 获取 活动 保修类型
		List<RepairOrderAndClaimTypeRespVO> repairOrderAndClaimTypeRespList = data.stream().filter(Objects::nonNull).map(obj->{
			RepairOrderAndClaimTypeRespVO repairOrderAndClaimTypeResp = new RepairOrderAndClaimTypeRespVO();
			BeanUtils.copyProperties(obj, repairOrderAndClaimTypeResp);
			String claimType = mapFin.get(obj.getActivityNo());
			repairOrderAndClaimTypeResp.setClaimType(claimType);
			return repairOrderAndClaimTypeResp;
		}).collect(Collectors.toList());
		log.info("queryRepairOrderAndClaimTypeByVin-return: {}", JSON.toJSONString(repairOrderAndClaimTypeRespList));
		return repairOrderAndClaimTypeRespList;
	}
}





