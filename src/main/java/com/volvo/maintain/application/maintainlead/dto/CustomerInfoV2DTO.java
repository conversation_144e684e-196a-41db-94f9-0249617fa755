package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("会员信息dto")
public class CustomerInfoV2DTO {
	/**
	 * 手机号： mobile
	 */
	private String mobile;

	/**
	 * 客户姓名：customerName
	 */
	private String customerName;

	/**
	 * 类型：customerType（CDP 车主/中台车主 / 自店车主 / 送修人 / 故障灯客户，枚举参见：3530）
	 */
	private Integer customerType;
}
