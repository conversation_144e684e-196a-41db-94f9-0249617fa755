package com.volvo.maintain.application.maintainlead.service.customerProfile;


import com.volvo.maintain.application.maintainlead.dto.CdpCheckInSegmentsBaseByAuthDto;
import com.volvo.maintain.application.maintainlead.dto.CdpTagListDto;
import com.volvo.maintain.application.maintainlead.dto.CdpTokenPortraitDto;
import com.volvo.maintain.application.maintainlead.dto.CustomerTagsDto;
import com.volvo.maintain.application.maintainlead.dto.TagListDto;
import com.volvo.maintain.interfaces.vo.CdpTagInfoVo;

import java.util.List;

public interface CdpTagInfoService {


    List<CdpTagListDto> queryEmpowerByVins(List<String> vin);

    CustomerTagsDto queryEmpowerByVin(String vin);

    CdpTokenPortraitDto getCdpToken(String target);

    List<TagListDto> queryTagList();

    CustomerTagsDto queryTagListByVinAndMobile(String vin, String mobile);
    List<CdpTagInfoVo> queryTagListByOneId(String oneId);

    List<List<CdpTagInfoVo>> customCdpTagList(String mobile);

    CustomerTagsDto getCacheCdpTag(String target);

    void setCacheCdpTag(List<CustomerTagsDto> tags);


    CdpCheckInSegmentsBaseByAuthDto checkInSegmentsBaseByAuth(String delivererMobile, String configValue);

    CdpCheckInSegmentsBaseByAuthDto checkInSegmentsBaseByVin(String vin, String configValue);
}
