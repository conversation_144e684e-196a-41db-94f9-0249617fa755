package com.volvo.maintain.application.maintainlead.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import com.volvo.exception.ServiceException;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.inviteClue.InvitationScripDto;
import com.volvo.maintain.application.maintainlead.dto.rights.CustExtWarPurGiveVO;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CdpTagInfoService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.InvitationService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.TagInfoService;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.ApiResult;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.VehicleFuelVo;
import com.volvo.maintain.interfaces.vo.MessagePopupVo;
import com.volvo.maintain.interfaces.vo.TagInfoVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 消息弹窗
 */
@Slf4j
@Service
public class MessagePopupServiceImpl implements MessagePopupService {

    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;


    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;


    @Autowired
    private CdpTagInfoService cdpTagInfoService;

    @Autowired
    private TagInfoService tagInfoService;

    @Autowired
    private InvitationService invitationService;

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Autowired
    private DmscusIfserviceFeign dmscusIfserviceFeign;

    @Override
    public MessageResultDTO selectPopupRecord(String ownerCode, String vin, String businessType, String businessId, BigDecimal mileage) {
        // 查询是否存在记录，存在直接返回，否则查询CDP，封装弹窗内容
        // 获取真实的业务id 经销商+工单号+群组id
        Boolean bytc = true;
        Boolean yb = true;
        DmsResponse<MessageResultDTO> response = domainMaintainOrdersFeign.selectPopupRecord(ownerCode, businessType, businessId, vin);
        //保养套餐标识
        log.info("domainMaintainOrdersFeign.selectPopupRecord: {}", JSONObject.toJSONString(response));
        if (response.isFail()) {
            throw new ServiceBizException("调用领域失败！");
        }
        // 保养套餐如果有弹窗记录，打标识
        MessageResultDTO resultDTO = response.getData();
        if (Objects.nonNull(resultDTO) && !resultDTO.getIsPopup()) {
            bytc= false;
        }
        if ("CDPBYTC".equals(businessType)){
            DmsResponse<MessageResultDTO> ybresponse = domainMaintainOrdersFeign.selectPopupRecord(ownerCode, "CDPYB", businessId, vin);
            //延保标识
            log.info("domainMaintainOrdersFeign.selectPopupRecord: {}", JSONObject.toJSONString(ybresponse));
            if (ybresponse.isFail()) {
                throw new ServiceBizException("调用领域失败！");
            }
            //延保如果有弹窗记录，打标识
            MessageResultDTO ybrResultDTO = ybresponse.getData();
            //ispopup = false  弹窗
            if (Objects.nonNull(ybrResultDTO) && !ybrResultDTO.getIsPopup()) {
                yb= false;
            }
        }
        log.info("bytc: {},yb:{}", bytc, yb);
        //都弹过窗直接返回
        if(bytc && yb){
            return resultDTO;
        }

        MessageResultDTO data = new MessageResultDTO();
        if (StringUtils.isNotEmpty(businessId)) {
            businessId = getRealBusinessId(ownerCode, businessId);
        }
        data.setBusinessId(businessId);
        data.setIsPopup(false);
        //获取系统配置 修改为 查询标签数据，标签数据是提前写好的。
        List<TagInfoVo> tagInfoList = queTagInfo();
        if (CollectionUtils.isEmpty(tagInfoList)) {
            log.info("Tag info list is null or empty");
            return data;
        }
        Map<Integer, String> tagIdMap = groupTagIdsByIsTag(tagInfoList);
        // 查询符合条件的群组id
        String groupId = getGroupIdByBusinessType(businessType);
        if (StringUtils.isBlank(groupId)) {
            log.info("groupId is null or empty");
            return data;
        }
        List<String> tagIds = Lists.newArrayList();
        if (!bytc){
            tagIds.addAll(getCdpTag(vin, groupId, data, tagIdMap, tagIds));
        }
        if (!yb){
            //判断是否延保
            if ("CDPBYTC".equals(businessType) && Objects.nonNull(mileage) ){
                String ybGroupId = getGroupIdByBusinessType("ExtendedwarrantyRemind");
                List<String> extendTags = getExtendTag(vin, mileage, ybGroupId);

                if(CollectionUtils.isNotEmpty(extendTags)){
                    if (!tagIds.isEmpty()){
                        businessType = businessType+ ",CDPYB";
                        groupId = groupId+ "," + ybGroupId;
                    }else {
                        businessType = "CDPYB";
                        groupId = ybGroupId;
                    }
                    tagIds.addAll(extendTags);
                }
            }
        }
        log.info("tagIds size: {}", JSONObject.toJSONString(tagIds));
        if (CollectionUtils.isEmpty(tagIds)){
            return data;
        }

        log.info("messagePopupVoList size: {}", JSONObject.toJSONString(tagIds));
        // 命中群组的map key：群组id，value：标签id
        Map<String, List<String>> tagMap = tagInfoList.stream().filter(e -> e.getIsTag() == CommonConstant.DICT_IS_YES).filter(e -> {
            TagValueRuleDto tagValueRuleDto = JSONObject.parseObject(e.getValueRule(), TagValueRuleDto.class);
            List<String> innerTagIdSet = tagValueRuleDto.getShowRule().getConvert().getAnd().stream().map(TagValueRuleDto.andOrDto::getTagId).filter(tagIds::contains).collect(Collectors.toList());
            if (!innerTagIdSet.isEmpty()) {
                e.setInnerTagId(innerTagIdSet.get(0));
                return true;
            }
            return false;
        }).collect(Collectors.groupingBy(TagInfoVo::getInnerTagId, Collectors.mapping(TagInfoVo::getTagId, Collectors.toList())));
        // 命中到的标签id
        List<String> outterTagIds = tagMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
        log.info("filterTagIdList size: {}", JSONObject.toJSONString(outterTagIds));
        // 查询话术标签集合  用tagIds + 类型等于1000 （保客营销） 查到匹配的保客营销数据。
        List<InvitationScripDto> scripList = invitationService.scriptManageQuery(outterTagIds, CommonConstant.BUSINESSTYPE);
        // 将  e.setInnerTagId 赋值给 List<InvitationScripDto> scripList  里面的 tagId；
        // 遍历scripList，查找每个customerTagId对应的tagMap中的key
        for (InvitationScripDto scrip : scripList) {
            String customerTagId = scrip.getCustomerTagIds();
            for (Map.Entry<String, List<String>> entry : tagMap.entrySet()) {
                if (entry.getValue().contains(customerTagId)) {
                    scrip.setTagId(entry.getKey());
                }
            }
        }
        log.info("scripList size: {}", JSONObject.toJSONString(scripList));
        // 转译内容
        List<TagInfoVo> filteredPurchaseIntention = new ArrayList<>();
        tagTranslation(filteredPurchaseIntention, tagInfoList, outterTagIds);
        scripList = scripList.stream().filter(e -> StringUtils.isNotBlank(e.getTagId())).collect(Collectors.toList());
        log.info("filter scripList size: {}", JSONObject.toJSONString(scripList));
        List<MessagePopupInfoDTO> popupMsg = createPopupMsg(filteredPurchaseIntention, scripList);
        data.setPopupContentList(popupMsg);
        if (CollectionUtils.isNotEmpty(popupMsg)) {
            ArrayList<String> content = new ArrayList<>();
            for (MessagePopupInfoDTO dto : popupMsg) {
                if (CollectionUtils.isNotEmpty(dto.getRecommendedScript())) {
                    content.addAll(dto.getRecommendedScript());
                }
            }
            data.setPopupContent(String.join(",", content));
        }
        data.setBusinessType(businessType);
        data.setGroupId(groupId);
        // 封装话术内容
        return data;
    }

    private List<String> getCdpTag(String vin, String groupId, MessageResultDTO data, Map<Integer, String> tagIdMap, List<String> tagIds) {
        CdpCheckInSegmentsBaseByAuthDto customerTagsDto = cdpTagInfoService.checkInSegmentsBaseByVin(vin, groupId);
        if ((ObjectUtils.isEmpty(customerTagsDto) || !customerTagsDto.getIn_any_segment() || Objects.isNull(customerTagsDto.getIn_segment_list())))  {
            log.info("customerTagsDto tags info is null or empty");
            return Lists.newArrayList();
        }
        //cdp返回的群组 和10041001的匹配上，（10041001是valueRule 中的tagId） 拿到tagIdList 去查询话术
        List<MessagePopupVo> messagePopupVoList = createPurchaseIntentionVo(tagIdMap.getOrDefault(CommonConstant.DICT_IS_YES, ""), customerTagsDto.getIn_segment_list());
        // 匹配上的群组id
        return messagePopupVoList.stream().map(MessagePopupVo::getTagId).collect(Collectors.toList());
    }


    /**
     * 查询配置表中的groupId，组装为字符串格式
     *
     * @param businessType
     * @return
     */
    private String getGroupIdByBusinessType(String businessType) {
        DmsResponse<CommonConfigDto> response = dmscloudServiceFeign.selectCommonConfig(businessType, businessType);
        if (Objects.isNull(response) || response.isFail()) {
            log.info("getGroupIdByBusinessType fail");
            throw new ServiceBizException("调用领域失败！");
        }
        log.info("查询配置表返回值为:{}", response.getData());
        if (Objects.isNull(response.getData()) || StringUtils.isBlank(response.getData().getConfigValue())) {
            throw new ServiceBizException("当前业务未配置群组id！");
        }
        return response.getData().getConfigValue();
    }

    /**
     * build filteredPurchaseIntention
     *
     * @param filteredPurchaseIntention 反查所有的标签匹配中的结果
     * @return 返回构建的对象，
     */
    public List<MessagePopupInfoDTO> createPopupMsg(List<TagInfoVo> filteredPurchaseIntention, List<InvitationScripDto> matchedScripts) {
        if (CollectionUtils.isEmpty(filteredPurchaseIntention)) {
            //一个车型群组（tagType = marketing_group and detail_tag_pid = 0）都未命中则没有购车意向，此时无需弹框
            log.info("createPopupMsg is null or empty");
            return null;
        }
        Map<String, List<String>> tagIdToTalkSkillsMap = matchedScripts.stream()
                .collect(Collectors.groupingBy(InvitationScripDto::getTagId,
                        Collectors.mapping(InvitationScripDto::getTagTalkskill, Collectors.toList())));
        List<MessagePopupInfoDTO> result = new ArrayList<>();
        for (TagInfoVo tagInfo : filteredPurchaseIntention) {
            MessagePopupInfoDTO purchaseIntentionVo = new MessagePopupInfoDTO();
            purchaseIntentionVo.setTagId(tagInfo.getTagId());
            purchaseIntentionVo.setTagValue("true");
            purchaseIntentionVo.setDetailTagPid(tagInfo.getDetailTagPid());
            if (StringUtils.isNotEmpty(tagInfo.getValueRule())) {
                //获取值规则
                TagValueRuleDto tagValueRuleDto = JSONObject.parseObject(tagInfo.getValueRule(), TagValueRuleDto.class);
                purchaseIntentionVo.setShowType(tagValueRuleDto.getShowType());
            }

            purchaseIntentionVo.setReasonsForRecommendation(Collections.singletonList(tagInfo.getShowName()));
            List<String> recommendedScripts = tagIdToTalkSkillsMap.getOrDefault(tagInfo.getInnerTagId(), Collections.emptyList());
            purchaseIntentionVo.setRecommendedScript(recommendedScripts);
            result.add(purchaseIntentionVo);
        }
        log.info(" popup createPurchaseIntentionVos result is " + JSONObject.toJSONString(result));
        return result;
    }

    /**
     * 转移内容
     *
     * @param filteredPurchaseIntention
     * @param tagInfoList
     * @param outterTagIds
     */
    private void tagTranslation(List<TagInfoVo> filteredPurchaseIntention, List<TagInfoVo> tagInfoList, List<String> outterTagIds) {
        // 匹配到的标签数据
        List<TagInfoVo> escapeShowName = tagInfoList.stream()
                .filter(tagInfo -> CommonConstant.DICT_IS_YES == tagInfo.getIsTag())
                .filter(tagInfo -> outterTagIds.contains(tagInfo.getTagId()))
                .collect(Collectors.toList());
        Map<String, TagInfoVo> tagDataMap = escapeShowName.stream().collect(Collectors.toMap(TagInfoVo::getTagId, Function.identity(), (k1, k2) -> k2));
        // 转译规则。
        for (TagInfoVo item : escapeShowName) {
            TagValueRuleDto tagValueRuleDto = JSONObject.parseObject(item.getValueRule(), TagValueRuleDto.class);
            if (ObjectUtils.isNotEmpty(tagValueRuleDto)) {
                tagInfoService.handleValueRule(tagValueRuleDto, item, tagDataMap);
            } else {
                item.setShowType(CommonConstant.TEXT_SHOW_TYPE);
            }
        }
        log.info("final tagTranslation escapeShowName: {}", JSONObject.toJSONString(escapeShowName));
        //escapeShowName 按照createdAt 顺序排序
        if (CollectionUtils.isNotEmpty(escapeShowName)) {
            escapeShowName.sort(Comparator.comparing(TagInfoVo::getCreatedAt));
            filteredPurchaseIntention.addAll(escapeShowName);
        }else{
            filteredPurchaseIntention.addAll(escapeShowName);
        }
        log.info("final tagTranslation filteredPurchaseIntention: {}", JSONObject.toJSONString(filteredPurchaseIntention));
    }

    /**
     * Creates a list of PurchaseIntentionVo objects based on the provided tagInfoVoList and segmentList.
     * (基于is_tag == 10041001 的数据 与is_tag == 10041002 的数据（02的数据是查询cdp返回的结果）的交集 匹配成功的数据构建对象返回客户端)
     *
     * @param tagInfoVoList The string representation of tagInfoVoList, separated by commas.
     * @param segmentList   An array of strings representing segments.
     * @return A list of PurchaseIntentionVo objects that match both tagInfoVoList and segmentList.
     */
    private List<MessagePopupVo> createPurchaseIntentionVo(String tagInfoVoList, String[] segmentList) {
        if (segmentList == null || segmentList.length == 0 || tagInfoVoList == null || tagInfoVoList.isEmpty()) {
            return Lists.newArrayList();
        }
        // 将 tagInfoVoList 拆分成一个 Set 集合
        Set<String> tagSet = new HashSet<>(Arrays.asList(tagInfoVoList.split(",")));
        // 将 segmentList 转换为一个 Set 集合
        Set<String> segmentSet = new HashSet<>(Arrays.asList(segmentList));
        // 取两个集合的交集（即相同的元素）
        Set<String> commonTags = Sets.intersection(tagSet, segmentSet);
        return commonTags.stream()
                .map(tagId -> {
                    MessagePopupVo vo = new MessagePopupVo();
                    vo.setTagId(tagId);
                    vo.setTagValue(String.valueOf(true));
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * query all tagInfo
     *
     * @return 所有的标签信息
     */
    private List<TagInfoVo> queTagInfo() {
        TagInfoDto tagInfoDto = new TagInfoDto();
        tagInfoDto.setTagType("marketing_group");
        tagInfoDto.setCurrentPage(1);
        tagInfoDto.setPageSize(100);
        Page<TagInfoVo> tagInfoVoPage = tagInfoService.queryTagInfo(tagInfoDto);
        return CollectionUtils.isNotEmpty(tagInfoVoPage.getRecords()) ? tagInfoVoPage.getRecords() : Lists.newArrayList();
    }

    /**
     * 按照 is_tag 分组。key 为tagId。
     *
     * @param tagInfoList 标签数据
     * @return 返回map  value为tagId拼接的数据。
     */
    private Map<Integer, String> groupTagIdsByIsTag(List<TagInfoVo> tagInfoList) {
        if (CollectionUtils.isEmpty(tagInfoList)) {
            return Collections.emptyMap();
        }
        Map<Integer, List<TagInfoVo>> tagsMap = tagInfoList.stream()
                .collect(Collectors.groupingBy(TagInfoVo::getIsTag));
        Map<Integer, String> tagIdMap = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();
        // 处理 isTag 为 10041002 的情况
        tagIdMap.put(CommonConstant.DICT_IS_NO, tagsMap.getOrDefault(CommonConstant.DICT_IS_NO, Collections.emptyList())
                .stream()
                .map(TagInfoVo::getTagId)
                .collect(Collectors.joining(",")));

        // 处理 isTag 为 10041001 的情况
        List<TagInfoVo> tagsTwo = tagsMap.getOrDefault(CommonConstant.DICT_IS_YES, Collections.emptyList());
        String tagIds = tagsTwo.stream()
                .map(tag -> extractTagIds(tag, mapper))
                .filter(tagId -> !tagId.isEmpty())
                .collect(Collectors.joining(","));
        tagIdMap.put(CommonConstant.DICT_IS_YES, tagIds);
        log.info("groupTagIdsByIsTag tagIdMap is {}", JSONObject.toJSONString(tagIdMap));
        return tagIdMap;
    }

    /**
     * 注意 规则定制，convert节点下 只有一个元素。所以不做其他逻辑处理。
     *
     * @param tag    标签对象
     * @param mapper json转换，
     * @return 返回10041001 规则中的tagId 拼接，。
     */
    private String extractTagIds(TagInfoVo tag, ObjectMapper mapper) {
        try {
            JsonNode root = mapper.readTree(tag.getValueRule());
            JsonNode convertNode = root.path("showRule").path("convert");
            String condition = convertNode.fieldNames().next();
            List<String> tagIdList = new ArrayList<>();
            convertNode.path(condition).elements().forEachRemaining(element ->
                    tagIdList.add(element.path("tagId").asText()));
            return String.join(",", tagIdList);
        } catch (JsonProcessingException e) {
            log.error("extractTagIds", e);
            return "";
        }
    }


    /**
     * 获取记录表中真实的业务id
     *
     * @param ownerCode
     * @param businessId
     * @return
     */
    private String getRealBusinessId(String ownerCode, String businessId) {
        return ownerCode + getMainRoNo(businessId);
    }

    /**
     * 获取主工单号
     *
     * @param roNo
     * @return
     */
    private String getMainRoNo(String roNo) {
        return roNo.substring(0, roNo.length() - 2) + "00";
    }

    @Override
    public void confirmMessage(MessageConfirmDTO dto) {
        DmsResponse<Void> response = domainMaintainOrdersFeign.confirmMessage(dto);
        if (response.isFail()) {
            throw new ServiceBizException("调用领域失败！");
        }
    }

    public List<String> getExtendTag(String vin, BigDecimal mileage, String groupId) {
        log.info("vin:{}, mileage:{}", vin, mileage);
        //根据vin查询购买记录
        DmsResponse<ApiResult<List<CustExtWarPurGiveVO>>> dmsResponse =  dmscusIfserviceFeign.queryExtWarPurGiveVByVin(vin);
        log.info("getExtendTag giveList:{}", JSON.toJSONString(dmsResponse));
        if (!dmsResponse.isFail()  && Objects.nonNull(dmsResponse.getData())  &&  Objects.nonNull(dmsResponse.getData().getObject())) {
            List<CustExtWarPurGiveVO> giveList = dmsResponse.getData().getObject();
            //排除生效失败和失效中状态,有则不具备任何延保购买条件，不显示弹窗话术
            giveList = giveList.stream().filter(give -> give.getGiveStatus() != null &&
                            !give.getGiveStatus().equals(35031005) &&
                            !give.getGiveStatus().equals(35031007) && Objects.equals(give.getBizType(), 83441001))
                    .collect(Collectors.toList());
            if (giveList.size()>0) {
                return Lists.newArrayList();
            }
        }

        // 获取油电混动类型
        VehicleFuelVo vehicleFuelVo = getVehicleFuelType(vin);
        if (Objects.isNull(vehicleFuelVo)) {
            throw new RuntimeException("车辆中心返回的车辆类型为空");
        }

        TmVehicleDto vehicleDto = getVehicleByVin(vin);
        // 查询符合条件的群组id
        //发票日期为空
        if(Objects.isNull(vehicleDto.getInvoiceDate())){
            return Lists.newArrayList();
        }

        log.info("vin:{}, mileage:{}, groupId:{}", vin, mileage, groupId);
        // 获取当前日期
        Date date = new Date();


        // 判断车辆类型，油车、混动车或电车
        switch (vehicleFuelVo.getVinType()) {
            case "燃油":
            case "混动":
                // 处理油车和混动车
                return handleVehicleReminders(vehicleDto, date, mileage, groupId, new BigDecimal("95000"), new BigDecimal("11250"));
            case "电动":
                // 处理电车
                return handleVehicleReminders(vehicleDto, date, mileage, groupId, new BigDecimal("95000"), new BigDecimal("31250"));
            default:
                // 其他类型，返回空列表
                return Lists.newArrayList();
        }
    }

    // 通用方法处理油车和电车
    private List<String> handleVehicleReminders(TmVehicleDto vehicleDto, Date currentDate, BigDecimal mileage, String groupId, BigDecimal mileageThreshold, BigDecimal reminderThreshold) {
        log.info("vin:{}, mileage:{}, groupId:{},currentDate:{},mileageThreshold:{}", vehicleDto.getVin(), mileage, groupId, currentDate, mileageThreshold);
        // 上限判断：车龄 > 33个月 或 行驶里程 > 95000KM 时，不显示弹窗话术
        if (displayCheckInvoiceDateAndMileage(vehicleDto.getInvoiceDate(), currentDate, mileage, mileageThreshold)) {
            log.info("车龄 > 33个月 或 行驶里程 > 95000KM 时，不显示弹窗话术");
            return new ArrayList<>();
        }

        // 下限判断：车龄 < 13个月 且 行驶里程 < 下限里程，显示弹窗话术
        if (checkInvoiceDateAndMileageNew(vehicleDto.getInvoiceDate(), currentDate, mileage, reminderThreshold)) {
            log.info("车龄 < 13个月 且 行驶里程 < 下限里程，显示弹窗话术");
            return new ArrayList<>(Collections.singletonList(groupId));
        }

        // 检查是否已提醒
        List<WarrantyMaintenanceReminderListDTO> reminderList = getWarrantyMaintenanceReminderList(vehicleDto.getVin());
        reminderList.sort(Comparator.comparing(WarrantyMaintenanceReminderListDTO::getDeliveryDate).reversed());
        if(CollectionUtils.isNotEmpty(reminderList)){
            Date prevDate;
            BigDecimal prevMileage;
            for(int i = 0 ; i < reminderList.size() ; i++){
                WarrantyMaintenanceReminderListDTO reminderDTO = reminderList.get(i);
                if(i == 0 ){
                    //当前时间
                    prevDate = new Date();
                    //输入进场里程
                    prevMileage = mileage;
                }else{
                    WarrantyMaintenanceReminderListDTO prevReminderDTO = reminderList.get(i-1);
                    //当前时间
                    prevDate = prevReminderDTO.getDeliveryDate();
                    //输入进场里程
                    prevMileage = prevReminderDTO.getInMileage();
                }

                if (!checkInvoiceDateAndMileage(prevDate, reminderDTO.getDeliveryDate(), prevMileage.subtract(reminderDTO.getInMileage()), reminderThreshold)) {
                    //任意一个不匹配就不满足
                    return new ArrayList<>();
                }
            }

            //再判断首保
            WarrantyMaintenanceReminderListDTO prevReminderDTO = reminderList.get(reminderList.size()-1);
            //当前时间
            prevDate = prevReminderDTO.getDeliveryDate();
            //输入进场里程
            prevMileage = prevReminderDTO.getInMileage();
            if (!checkInvoiceDateAndMileage(prevDate, vehicleDto.getInvoiceDate(), prevMileage, reminderThreshold)) {
                //任意一个不匹配就不满足
                return new ArrayList<>();
            }
            //全部匹配
            return new ArrayList<>(Collections.singletonList(groupId));
        }else{
            return new ArrayList<>();
        }
    }

    // 不显示弹窗
    public boolean displayCheckInvoiceDateAndMileage(Date invoiceDate, Date now, BigDecimal mileage, BigDecimal mileageThreshold) {
        return DateUtil.betweenMonth(now, invoiceDate, true) > 33 || mileage.compareTo(mileageThreshold) > 0;
    }

    // 显示弹窗
    public boolean checkInvoiceDateAndMileage(Date invoiceDate, Date now, BigDecimal mileage, BigDecimal mileageThreshold) {
        return DateUtil.betweenMonth(now, invoiceDate, true) <= 13 && mileage.compareTo(mileageThreshold) <= 0;
    }

    public boolean checkInvoiceDateAndMileageNew(Date invoiceDate, Date now, BigDecimal mileage, BigDecimal mileageThreshold) {
        return DateUtil.betweenMonth(now, invoiceDate, true) < 13 && mileage.compareTo(mileageThreshold) < 0;
    }

    /**
     * 获取油电混动的类型
     * @param vin
     * @return
     */
    private VehicleFuelVo getVehicleFuelType(String vin){
        // 获取油电混动的类型
        DmsResponse<List<VehicleFuelVo>> responseDTO = midEndVehicleCenterFeign.vehicleFuelType(Collections.singletonList(vin));
        log.info("车辆中心返回的车辆类型responseDTO:{}", JSON.toJSONString(responseDTO));
        if (Objects.nonNull(responseDTO) && Objects.nonNull(responseDTO.getData())) {
            return responseDTO.getData().get(0);
        }else{
            return  null;
        }
    }

    /**
     * 根据vin获取车辆信息
     * @param vin
     * @return
     */
    private TmVehicleDto getVehicleByVin(String vin){
        //获取车辆信息
        DmsResponse<TmVehicleDto> vehicleDtoResponse = midEndVehicleCenterFeign.getVehicleByVIN(vin);
        log.info("获取车辆信息:{}" , vehicleDtoResponse);
        if (vehicleDtoResponse.isFail() || Objects.isNull(vehicleDtoResponse.getData())) {
            throw new ServiceException(String.format("[%s] vin不存在", vin));
        }
        return  vehicleDtoResponse.getData();
    }

    public List<WarrantyMaintenanceReminderListDTO> getWarrantyMaintenanceReminderList(String vin){
        DmsResponse<List<WarrantyMaintenanceReminderListDTO>> response = dmscloudServiceFeign.getWarrantyMaintenanceReminderList(vin);
        if (Objects.isNull(response) || response.isFail()) {
            throw new ServiceBizException("调用领域失败！");
        }
        log.info("查询车辆延保提醒消息:{}", response.getData());
        return response.getData();
    }
}
