package com.volvo.maintain.application.maintainlead.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/11
 */
@Data
@ApiModel("查询标签")
public class TagLogParamsDto {

    @ApiModelProperty("场景类型:1.环检接车，2.线索保存")
    private int sinceType;
    @ApiModelProperty("车架号")
    private List<String> vins;
    @ApiModelProperty("时间")
    private String createdAt;
    @ApiModelProperty("子业务单号")
    private String subBizNo;


}
