package com.volvo.maintain.application.maintainlead.dto.equityInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@ApiModel(value = "权益赋权详情查询对外接口 入参 DTO")
@Data
public class EmpowerDetailQueryDTO implements Serializable {

    private static final long serialVersionUID = -3275117808508785494L;

    /**
     * 是否分页查询,0: 不分页查询 1: 分页查询
     */
    @ApiModelProperty("是否分页查询")
    private Integer isPaging;

    /**
     * 当前页
     */
    @ApiModelProperty("当前页")
    private Integer page;

    /**
     * 页容量
     */
    @ApiModelProperty("页容量")
    private Integer pageSize;

    /**
     * 车架号
     */
    @ApiModelProperty("车架号")
    private String vin;

    /**
     * 渠道（业务线）
     */
    @ApiModelProperty("渠道（业务线）")
    private String operateChannel;

    /**
     * 用户车辆权益编号
     */
    @ApiModelProperty("用户车辆权益编号")
    private String userRightNo;

    /**
     * 权益主档编号(非充电)
     */
    @ApiModelProperty("权益主档编号")
    private String rightsNo;

    /**
     * 业务订单号
     */
    @ApiModelProperty("业务订单号")
    private String orderNo;

    /**
     * 权益状态
     */
    @ApiModelProperty("权益状态 1 已生效（已发放） 0 已失效（已作废） 2 已冻结 3 已过期")
    private List<Integer> userRightsStatus;

    @Size(message = "车架号最多100", max = 100)
    @ApiModelProperty("车架号集合")
    private List<String> vinList;

    @Size(message = "权益主档编号最多100", max = 100)
    @ApiModelProperty("权益主档编号集合")
    private List<String> rightsNoList;

    @ApiModelProperty("权益分类")
    private Long categoryCode;
}
