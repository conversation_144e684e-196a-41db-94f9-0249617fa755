package com.volvo.maintain.application.maintainlead.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 商户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "DetailVO 对象", description = "DetailVO")
public class DetailVo {

    @ApiModelProperty(value = "订单详情  ")
    private OrderVo orderInfo;

    @ApiModelProperty(value = "代驾详情  ")
    private List<DrivingInfoVo> drivingInfoList;
    
    @ApiModelProperty(value = "服务商类型 0:e代驾，1:滴滴")
    private Integer supplierType;

}



