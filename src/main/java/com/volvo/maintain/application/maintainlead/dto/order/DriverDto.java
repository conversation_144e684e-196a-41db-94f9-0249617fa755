package com.volvo.maintain.application.maintainlead.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * dacon
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "DriverDTO 对象", description = "DriverDTO")
public class DriverDto {

    @ApiModelProperty(value = "取车地址纬度", required = true)
    private Double lat;
    @ApiModelProperty(value = "取车地址经度", required = true)
    private Double lng;
    @ApiModelProperty(value = "商户id", required = true)
    private String customerId;
}