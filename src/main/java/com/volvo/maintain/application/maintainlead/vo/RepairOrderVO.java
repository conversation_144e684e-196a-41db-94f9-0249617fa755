package com.volvo.maintain.application.maintainlead.vo;

import lombok.Data;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <p>
 * 工单信息
 * </p>
 */
@Data
public class RepairOrderVO {

    private static final long serialVersionUID = 1L;

    /**
     * VIN
     */
    private String vin;
    /**
     * 车牌号
     */
    private String license;
    /**
     * 出厂时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDate;
    /**
     * 店代码
     */
    private String dealerCode;
    /**
     * 车店分配后的店代码或者销售经销商
     */
    private String salesDealerCode;
    /**
     * 车店分配时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newTime;
    /**
     * 工单号
     */
    private String roNo;
    /**
     * 工单开单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date roCreateDate;
    /**
     * 出厂里程
     */
    private Double outMileage;
    /**
     * 维修类型
     */
    private String repairTypeCode;
    /**
     * 代码
     */
    private String ownerCode;
    /**
     * 送修人
     */
    private String deliverer;

    /**
     * 送修人性别
     */
    private String delivererGender;

    /**
     * 送修人电话
     */
    private String delivererPhone;

    /**
     * 送修人手机
     */
    private String delivererMobile;
    /**
     * 结算时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date balanceTime;
    /**
     * fg
     */
    private String functionGroup;
}
