package com.volvo.maintain.application.maintainlead.service.strategy.rights;

import com.google.common.collect.Lists;
import com.mysql.cj.util.StringUtils;
import com.volvo.maintain.application.maintainlead.dto.rights.PurchaseEligibilityCheckRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.PurchaseEligibilityCheckResponseDto;
import com.volvo.maintain.application.maintainlead.dto.rights.WarrantyCXWYCheckRspDTO;
import com.volvo.maintain.application.maintainlead.dto.rights.WarrantyCXWYCheckRsqDTO;
import com.volvo.maintain.application.maintainlead.emums.RightsStrategyEnum;
import com.volvo.maintain.infrastructure.gateway.DmscusIfserviceFeign;
import com.yonyou.cyx.framework.dto.ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 出险无忧策略
 */
@Slf4j
@Component
public class TravelSafetyStrategy extends AbsExtWarrantyExecuteStrategy {

    @Override
    public String mark() {
        return RightsStrategyEnum.ACCIDENT_PIECE_OF_MIND.getCode().toString();
    }

    @Autowired
    private DmscusIfserviceFeign dmscusIfserviceFeign;

    @Override
    public List<PurchaseEligibilityCheckResponseDto> buyValid(PurchaseEligibilityCheckRequestDto purchaseEligibilityCheckRequestDto) {
        List<String> productNoList = purchaseEligibilityCheckRequestDto.getProductNoList();
        if(CollectionUtils.isEmpty(productNoList)){
            return Lists.newArrayList();
        }
        List<PurchaseEligibilityCheckResponseDto> purchaseEligibilityCheckResponseDtos = new ArrayList<>();
        // 出险无忧复购校验，统一放到了ifservice且做了定制化文案
        WarrantyCXWYCheckRsqDTO dto = new WarrantyCXWYCheckRsqDTO();
        dto.setVin(purchaseEligibilityCheckRequestDto.getVin());
        dto.setProductNoList(productNoList);
        log.info("buyValid warrantyCXWYCheck dto:{}", dto);
        ResponseDTO<List<WarrantyCXWYCheckRspDTO>> responseDTO = dmscusIfserviceFeign.warrantyCXWYCheck(dto);
        log.info("buyValid warrantyCXWYCheck responseDTO:{}", responseDTO);
        if (null != responseDTO) {
            log.info("buyValid responseDTO isNotnull");
            List<WarrantyCXWYCheckRspDTO> data = responseDTO.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                for (WarrantyCXWYCheckRspDTO rspDTO : data) {
                    log.info("buyValid rspDTO:{}", rspDTO);
                    String errorCode = rspDTO.getErrorCode();
                    if (!StringUtils.isNullOrEmpty(errorCode)) {
                        log.info("buyValid errorMsg isNotNullOrNotEmpty");
                        PurchaseEligibilityCheckResponseDto checkResponseDto = new PurchaseEligibilityCheckResponseDto();
                        checkResponseDto.setProductNo(rspDTO.getProductNo());
                        dto.setVin(purchaseEligibilityCheckRequestDto.getVin());
                        checkResponseDto.setCode(errorCode);
                        checkResponseDto.setMsg(rspDTO.getErrorMsg());
                        purchaseEligibilityCheckResponseDtos.add(checkResponseDto);
                    }
                }
                if (CollectionUtils.isNotEmpty(purchaseEligibilityCheckResponseDtos)) {
                    log.info("buyValid purchaseEligibilityCheckResponseDtos isNotEmpty");
                    return purchaseEligibilityCheckResponseDtos;
                }
            }
        }
        // 调用老方法，未做任何变动
        purchaseEligibilityCheckResponseDtos = getPurchaseEligibilityCheckResponseDtos(purchaseEligibilityCheckRequestDto);
        setErrorMsg(purchaseEligibilityCheckResponseDtos);
        return purchaseEligibilityCheckResponseDtos;
    }
}
