package com.volvo.maintain.application.maintainlead.mq.listener;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.service.transparenttoc.ShortageReplenishmentETAService;
import com.volvo.maintain.infrastructure.gateway.request.ETAOperationParamsDTO;
import com.yonyou.cyx.function.exception.ServiceBizException;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@RocketMQMessageListener(nameServer = "${rocketmq.tocTransparent.name-server}",
						topic = "${rocketmq.tocTransparent.topic}",
						selectorExpression = "${rocketmq.tocTransparent.tag}",
						consumerGroup = "${rocketmq.tocTransparent.consumer.group}",
						accessKey = "${rocketmq.tocTransparent.consumer.access-key}",
						secretKey = "${rocketmq.tocTransparent.consumer.secret-key}")
public class TransparentTocListener implements RocketMQListener<MessageExt> {

	@Resource
	private ShortageReplenishmentETAService shortageReplenishmentETAService;

	@Resource
	private RedissonClient redissonClient;
	
    @Override
    public void onMessage(MessageExt message) {
        String tag = message.getTags();
        String msgId = message.getMsgId();
        String topic = message.getTopic();
        byte[] bodyByte = message.getBody();
        log.info("Receive message Topic is:{} MsgId is:{}" ,topic, msgId);
        log.info("Receive message body:{}" ,bodyByte);

        String val = UUID.randomUUID().toString();
        RBucket<Object> bucket = redissonClient.getBucket(message.getMsgId());
        RBucket<Object> index = redissonClient.getBucket(String.join("", "index:", message.getMsgId()));
        if(bucket.isExists()) {
        	log.warn("MQ消息重复消费, msgID:{}", msgId);
            return;
        }
        // 处理重试次数
        Integer valueOf = 1;
    	try {
    		if(index.isExists()) {
        		Object object = index.get();
        		valueOf = Integer.valueOf(object.toString());
        		valueOf++;
    		}
    		bucket.set(valueOf, 1, TimeUnit.HOURS);
		} catch (Exception e) {
			log.info("转换异常：{}", valueOf);
		}
        bucket.set(val, 1, TimeUnit.HOURS);
        String body=new String(bodyByte);
        try {
        	log.info("接收到ETA变更触点消息:{}", body);
            ETAOperationParamsDTO parseObject = JSON.parseObject(body, ETAOperationParamsDTO.class);
            boolean updateETAMapping = shortageReplenishmentETAService.updateETAMapping(parseObject);
            if(!updateETAMapping) {
            	throw new ServiceBizException("ETA更新处理失败");
            }
            bucket.delete();
        } catch (Exception e) {
        	bucket.delete();
            log.error("消费异常:{}  {}, {}",topic, tag, valueOf);
            log.error(e.getMessage(), e);
            if(valueOf<=5) {            	
            	throw e;
            }
        }
    }
}
