package com.volvo.maintain.application.maintainlead.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VehicleDeliverVO implements Serializable {

    //车架号
    private String vin;
    //经销商code
    private String dealerCode;
    //养修预约单号
    private String reservationNo;
    private String id;
    private String orderCode;
//    private String orderId;
//    private String vinIk;
//    private String dealerName;
//    private String dealerContacts;
//    private String dealerCell;
//    private String customerOneId;
//    private String ownerOneId;
        private String orderStatus;
//    private String channel;
//    private String customerId;
//    private String type;
//    private String mode;
//    private String createMobile;
    private String mobile;
    private String username;
    private String pickupContactName;
    private String pickupContactPhone;
//    private String pickupAddress;
//    private String pickupAddressLng;
//    private String pickupAddressLat;
//    private String returnContactName;
//    private String returnContactPhone;
//    private String returnAddress;
//    private String returnAddressLng;
//    private String returnAddressLat;
    private String bookingTime;
    private String carNo;
//    private String carBrandName;
    private String carSeriesName;
//    private String payMode;
//    private String couponCode;
//    private String midAddress;
//    private String midAddressLng;
//    private String midAddressLat;
//    private String fixedDriverId;
//    private String pushSms;
//    private String operateTime;
//    private String createTime;
//    private String updateTime;
//    private String customerOwnerOneId;
//    private String workOrderNo;
//    private String workOrderIssuingTime;
//    private String addPickUpAndDropOffFee;
//    private String orderSource;
//    private String firstResponseTime;
//    private String minProgram;
//    private String followUpOnTime;
//    private String driverName;
//    private String driverContactInfo;
//    private String takeOrderTime;
//    private String waitDriverTakeOrderTime;
//    private String takeOrderTimeConsuming;
//    private String driverArriveTimeDif;
//    private String cancelReason;
//    private String cancelSource;
//    private String reviewReason;
//    private String orderKind;
//    private String adviserId;
//    private String remarks;

    private String pickupProvinceCode;    //取车省code
    private String pickupCityCode;            //取车市code
    private String pickupAreaCode;          //取车区code
    private String returnProvinceCode;     //送车省code
    private String returnCityCode;             //送车市code
    private String returnAreaCode;           //送车区code
}
