package com.volvo.maintain.application.maintainlead.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2023/12/25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("cdp 标签同步任务")
public class CdpTagTaskDto {
    @ApiModelProperty(value = "主键id",notes = "主键id",required = true,name = "id",example = "1",dataType = "Integer")
    private Integer id;
    @ApiModelProperty(value = "场景类型   1.保养灯刷日均 2.故障灯刷日均 3.CDP标签同步",notes = "场景类型",required = true,name = "since_type",example = "1",dataType = "Integer")
    private Integer sinceType;

    //业务主键
    @ApiModelProperty(value = "业务主键",notes = "业务主键",required = true,name = "biz_no",example = "1",dataType = "String")
    private String bizNo;

    //请求参数
    @ApiModelProperty(value = "请求参数",notes = "请求参数",required = true,name = "req_params",example = "1",dataType = "String")
    private String reqParams;

    //重试次数
    @ApiModelProperty(value = "重试次数,默认3次",notes = "重试次数",required = true,name = "retry_count",example = "0",dataType = "Integer")
    private Integer retryCount;

    //最后一次重试时间
    @ApiModelProperty(value = "最后一次重试时间",notes = "最后一次重试时间",required = true,name = "last_retry_time",example = "1",dataType = "Date")
    private Date lastRetryTime;

    //响应码
    @ApiModelProperty(value = "响应码",notes = "响应码",required = true,name = "resp_code",example = "200",dataType = "String")
    private String respCode;

    //错误消息
    @ApiModelProperty(value = "错误消息 截取200长度字符",notes = "错误消息",required = true,name = "error_message",example = "200",dataType = "String")
    private String errorMessage;

    //任务状态:  0:待处理 1.处理成功 2.处理失败 -1:重试最大次数异常
    @ApiModelProperty(value = "任务状态:  0:待处理 1.处理成功 2.处理失败 -1:重试最大次数异常",notes = "任务状态",required = true,name = "task_status",example = "0",dataType = "Integer")
    private Integer taskStatus;

}
