package com.volvo.maintain.application.maintainlead.dto.equityInfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value = "权益赋权详情查询对外接口 出参 DTO")
@Data
public class EmpowerDetailDTO extends RightsCategoryDetailInfo implements Serializable {

    private static final long serialVersionUID = 379318899338183048L;

    @ApiModelProperty("用户车辆权益编号")
    private String userRightNo;

    @ApiModelProperty("车辆VIN码")
    private String vin;

    @ApiModelProperty("会员ID")
    private String memberId;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("权益子档编号")
    private String rightsNo;

    @ApiModelProperty("权益主档编号")
    private String rightsMasterNo;

    @ApiModelProperty("权益主档名称")
    private String rightsName;

    @ApiModelProperty("权益生效日期")
    private String effectiveDate;

    @ApiModelProperty("权益失效日期")
    private String expirationDate;

    @ApiModelProperty("数量（度数）")
    private BigDecimal numberQuantity;

    @ApiModelProperty("剩余数量（度数）")
    private BigDecimal leftQuantity;

    @ApiModelProperty("次数")
    private Integer numberTimes;

    @ApiModelProperty("剩余次数")
    private Integer leftTimes;

    @ApiModelProperty("周期")
    private String resetCycle;

    @ApiModelProperty("周期循环剩余次数")
    private String cycleTimes;

    @ApiModelProperty("权益赋权状态")
    private Integer userRightsStatus;

    @ApiModelProperty("作废日期")
    private String deletedTime;

    @ApiModelProperty("更新日期")
    private String updateTime;

    @ApiModelProperty("业务订单号")
    private String orderNo;

    @ApiModelProperty("权益一级分类")
    private String categoryBig;

    @ApiModelProperty("权益二级分类")
    private String categoryName;

    @ApiModelProperty("权益三级分类")
    private String categorySubName;

    @ApiModelProperty(value = "权益是否需认证")
    private Integer certificateType;

    @ApiModelProperty(value = "权益是否需认证")
    private Boolean isValid;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("权益创建日期")
    private Date createTime;

    @ApiModelProperty("C端展示标题")
    private String tocTitle;

    @ApiModelProperty("C端展示权益名称")
    private String tocRightsName;

    @ApiModelProperty("C端展示详情")
    private String tocDescription;

    @ApiModelProperty("C端权益分类ID")
    private Integer categoryTagCodeId;

    @ApiModelProperty("C端权益分类CODE")
    private String categoryTagCodeIdName;

    @ApiModelProperty("分类编号")
    private Long category;

    @ApiModelProperty(value = "属性值")
    private List<CommonAttributeInfo> attributes;

    @ApiModelProperty("权益包赋权编号")
    private String usePackageNo;

    @ApiModelProperty("权益包编号")
    private String packageCode;

    @ApiModelProperty("权益包名称")
    private String packageName;

    @ApiModelProperty("权益包C端展示名称")
    private String packageNameToC;


}
