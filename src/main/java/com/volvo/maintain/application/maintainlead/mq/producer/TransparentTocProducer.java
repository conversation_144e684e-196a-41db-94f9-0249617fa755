package com.volvo.maintain.application.maintainlead.mq.producer;

import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.infrastructure.gateway.request.ETAOperationParamsDTO;

import lombok.extern.slf4j.Slf4j;


@Component
@Slf4j
public class TransparentTocProducer {

    @Autowired
    @Qualifier("tocTransparent")
    private RocketMQTemplate rocketMQTemplate;
    
	@Value("${rocketmq.tocTransparent.producer.topic: topic_toc_transparent_eta}")
	private String topic;
    
    
    
    /**
     * 发送消息
     *
     * @param topic
     * @param msg
     */
    public void sendOrderMsg(ETAOperationParamsDTO etaOperationParams, String id) {
    	log.info("发送触点消息：{}", JSON.toJSONString(etaOperationParams));
    	String msg = JSON.toJSONString(etaOperationParams);
        sendMsg(topic.trim(), msg, id);
    }

    /**
     * 发送消息
     *
     * @param topic
     * @param msg
     */
    public void sendMsg(String topic, String msg, String id) {
        this.rocketMQTemplate.syncSendOrderly(topic,msg, String.valueOf(id));
    }
}
