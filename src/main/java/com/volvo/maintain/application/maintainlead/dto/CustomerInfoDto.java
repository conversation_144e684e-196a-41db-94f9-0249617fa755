package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("会员信息dto")
public class CustomerInfoDto {
    /**
     *  会员 ID：memberId
     */
    private Integer memberId;

    /**
     * 手机号： mobile
     */
    private String mobile;

    /**
     * 客户姓名：customerName
     */
    private String customerName;


    /**
     * 头像：memberUrl
     */
    private String memberUrl;

    /**
     * 车架号：vin
     */
    private String vin;


    /**
     * 类型：customerType（CDP 车主/中台车主 / 自店车主 / 送修人 / 故障灯客户，枚举参见：3530）
     */
    private Integer customerType;

    /**
     * 返厂概率：建议取 cdp 的 tag_id/code 属性名称，值必需返回；
     */
    private String returnFactoryProbability;

    private String dmsDefault;

    private Boolean defaultOwner = false;






}
