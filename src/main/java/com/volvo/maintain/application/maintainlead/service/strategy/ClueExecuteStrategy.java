package com.volvo.maintain.application.maintainlead.service.strategy;


import com.volvo.design.strategy.AbstractExecuteStrategy;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadOperationResultDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LiteCrmClueResultDTO;

/**
 * 线索执行策略
 */
public interface ClueExecuteStrategy extends AbstractExecuteStrategy<LeadOperationResultDto, LiteCrmClueResultDTO> {
}
