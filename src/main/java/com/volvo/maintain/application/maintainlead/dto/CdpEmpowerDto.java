package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/11/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("Cdp是否授权入参")
public class CdpEmpowerDto {
    // vin
    private List<String> vin;

}
