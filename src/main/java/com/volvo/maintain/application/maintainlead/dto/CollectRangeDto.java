package com.volvo.maintain.application.maintainlead.dto;

import com.yonyou.cyx.framework.bean.dto.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * CollectRange
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-30
 */
@Data
@ApiModel(value = "CollectRange", description = "CollectRange")
public class CollectRangeDto extends BaseDTO {

    @ApiModelProperty(value = "领取范围（0：全部 1：部分）")
    private Integer user;
    @ApiModelProperty(value = "1:沃邻/2:用户标签/3:使用用户范围")
    private Integer checkedItem;
    @ApiModelProperty(value = "使用用户范围(用户id列表)")
    private List<Long> useUserRange;

    @ApiModelProperty(value = "领用范围(沃龄上限-含)")
    private Integer collectrangeMincarage;
    @ApiModelProperty(value = "领用范围(沃龄下限-不含)")
    private Integer collectrangeMaxcarage;
    @ApiModelProperty(value = "领用范围(用户标签id)")
    private Integer collectrangeUsertag;
}