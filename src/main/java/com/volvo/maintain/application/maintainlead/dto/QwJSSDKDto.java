package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("JSSDK DTO")
public class QwJSSDKDto {
	private String appId	; // 企业微信的corpID®
	private String agentId	 ; // 应用Id
	private String timestamp; // 生成企业JS-SDK签名的时间戳
	private String noncestr; // 生成企业JS-SDK签名的随机串
	private String signature; // 生成企业JS-SDK 签名
	private String agentTimestamp; // 生成应用签名的时间戳
	private String agentsNoncestr; // 生成应用签名的随机串
	private String agentSignature; // 生成应用JS-SDK签名
}
