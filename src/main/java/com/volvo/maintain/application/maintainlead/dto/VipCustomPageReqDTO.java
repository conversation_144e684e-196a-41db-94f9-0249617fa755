package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "vip客户分页查询DTO", description = "vip客户分页查询DTO")
@Data
public class VipCustomPageReqDTO {

    @ApiModelProperty("当前页")
    private Long currentPage;

    @ApiModelProperty("每页显示条数（每次最大不能超过5000条）")
    private Long pageSize;

    @ApiModelProperty("vin")
    private String vin;

    @ApiModelProperty("车牌")
    private String licensePlate;

    @ApiModelProperty("vip群组，bizGroup，lb : 重点关照组   t: 重点维修组  90:重点服务组")
    private List<String> bizGroup;

}
