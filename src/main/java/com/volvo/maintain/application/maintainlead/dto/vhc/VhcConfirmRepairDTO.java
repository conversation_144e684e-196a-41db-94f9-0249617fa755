package com.volvo.maintain.application.maintainlead.dto.vhc;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * vhc报价-代客户反向确认
 */
@Data
public class VhcConfirmRepairDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 经销商
     */
    private String ownerCode;
    /**
     * 车辆健康检查编号
     */
    private String vhcNo;
    /**
     * 维修项
     */
    private List<VhcConfirmRepairItemDTO> itemList;

}
