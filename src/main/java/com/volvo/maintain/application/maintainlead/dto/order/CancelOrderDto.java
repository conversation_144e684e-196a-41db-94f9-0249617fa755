package com.volvo.maintain.application.maintainlead.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * dacon
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "CancelOrderDTO", description = "CancelOrderDTO")
public class CancelOrderDto {

    @ApiModelProperty(value = "id", required = true)
    private String id;

    @ApiModelProperty(value = "是否经销商操作 10041001是,10041002否 默认为：否")
    private Integer dealerOpStatus;

    @ApiModelProperty(value ="取消理由")
    private String cancelReason;

    @ApiModelProperty(value = "取消对象")
    private Integer cancelSource;

}
   