package com.volvo.maintain.application.maintainlead.dto.part;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 弹框显示缺料的信息
 * </p>
 */
@ApiModel(description = "弹框显示缺料的信息")
@Data
public class ShortPartResultDTO implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * 配件代码
     */
    @ApiModelProperty(value = "零件号", hidden = true)
    private String partNo;
    /**
     * 配件名称
     */
    @ApiModelProperty(value = "配件名称", hidden = true)
    private String partName;
    /**
     * 本次缺件数量
     */
    @ApiModelProperty(value = "本次缺件数量", hidden = true)
    private BigDecimal materialShortageNum;
    /**
     * 工单累计数量
     */
    @ApiModelProperty(value = "工单累计数量", hidden = true)
    private BigDecimal workOrderTotal;
    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量", hidden = true)
    private BigDecimal stockQuantity;
    /**
     * 在途数量
     */
    @ApiModelProperty(value = "在途数量", hidden = true)
    private BigDecimal deliveryQuantity;
    /**
     * 需补货数量
     */
    @ApiModelProperty(value = "需补货数量", hidden = true)
    private BigDecimal replenishmentNum;
    /**
     * 缺料在途说明
     */
    @ApiModelProperty(value = "缺料在途说明", hidden = true)
    private String shortageDescription;
    /**
     * 差异数量
     */
    @ApiModelProperty(value = "差异数量", hidden = true)
    private BigDecimal differenceNum;
    /**
     * 是否勾线
     */

    @ApiModelProperty(value = "是否勾线", hidden = true)
    private Integer isSelect;
    /**
     * A新增 U修改或 D删除 S未操作
     */
    @ApiModelProperty(value = "A新增 U修改或 D删除 S未操作", hidden = true)
    private String itemUpdateStatus;
    /**
     * 需补货数量NEW
     */
    @ApiModelProperty(value = "修改之后的需补货数量", hidden = true)
    private BigDecimal newReplenishmentNum;
    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", hidden = true)
    private String updateBy;
    /**
     * 零件采购价格
     */
    @ApiModelProperty(value = "零件采购价格", hidden = true)
    private BigDecimal claimPrice;
    /**
     * 埋点标识A新增 U修改
     */
    @ApiModelProperty(value = "埋点标识", hidden = true)
    private String partUpdateStatus;

    @ApiModelProperty(value = "排序标识")
    private Integer sort;



}
