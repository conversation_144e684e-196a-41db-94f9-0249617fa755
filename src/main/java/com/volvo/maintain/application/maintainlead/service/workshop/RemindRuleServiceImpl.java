package com.volvo.maintain.application.maintainlead.service.workshop;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigDto;
import com.volvo.maintain.application.maintainlead.dto.DownloadDto;
import com.volvo.maintain.application.maintainlead.dto.RoleInfoDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.*;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import com.volvo.maintain.application.maintainlead.vo.workshop.CustomUserInfoVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.RemindRuleDetailVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.RemindRuleVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.SpecialVehicleConfigVo;
import com.volvo.maintain.infrastructure.gateway.DownloadServiceFeign;
import com.volvo.maintain.infrastructure.gateway.EventServiceFeign;
import com.volvo.maintain.infrastructure.util.ExcelUtils;
import com.volvo.utils.BeanMapperUtil;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.framework.service.excel.ExcelExportColumn;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 规则提醒
 * @Date 2024/11/15 17:31
 */
@Service
@AllArgsConstructor
@Slf4j
public class RemindRuleServiceImpl implements RemindRuleService {

    private final EventServiceFeign eventServiceFeign;
    private final CommonMethodService commonMethodService;
    private final DownloadServiceFeign downloadServiceFeign;

    private static final String exportUrl = "http://application-maintain-management/remindRule/v1/queryRuleList";
    private static final String excelName = "规则列表.xlsx";
    private static final String sheetName = "规则列表";

    @Override
    public Page<RemindRuleVo> queryRulePage(RemindRuleDto remindRuleDto) {
        remindRuleDto.setOwnerCode(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode());
        return eventServiceFeign.queryRulePage(remindRuleDto).getData();
    }

    @Override
    public List<RemindRuleVo> queryRuleList(RemindRuleDto remindRuleDto) {
        remindRuleDto.setOwnerCode(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode());
        remindRuleDto.setPageNum(remindRuleDto.getCurrentPage());
        return eventServiceFeign.queryRulePage(remindRuleDto).getData().getRecords();
    }

    @Override
    public void exportRuleList(RemindRuleDto queryParams) {
        log.info("exportList enter args:{}", JSON.toJSONString(queryParams));
        // 获取导出字段
        List<ExcelExportColumn> exportColumnList = ExcelUtils.getExportColumn(RemindRuleVo.class);
        // 组装下载中心 DTO
        DownloadDto dto = new DownloadDto();
        dto.setQueryParams(BeanMapperUtil.toMap(queryParams));
        dto.setExcelName(excelName);
        dto.setSheetName(sheetName);
        dto.setServiceUrl(exportUrl);
        dto.setExcelExportColumnList(exportColumnList);
        log.info("export full leads params:{}", dto);
        // 导出
        downloadServiceFeign.downloadExportExcel(dto);
    }

    @Override
    public RemindRuleDetailVo queryRuleDetail(Integer id) {
        List<RoleInfoDto> roleInfoDtoList = commonMethodService.queryRoleListByCompanyCode(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode());
        List<MidRoleInfoDto> midRoleInfoDtos = roleInfoDtoList.stream().map(e -> MidRoleInfoDto.builder().roleId(String.valueOf(e.getRoleId())).roleCode(e.getRoleCode()).dataSource(e.getDataSource()).roleName(e.getRoleName()).build()).collect(Collectors.toList());
        return eventServiceFeign.queryRuleDetail(RemindRuleDetailDto.builder().id(id).midRoleInfoDtoList(midRoleInfoDtos).build()).getData();
    }

    @Override
    public Page<CustomUserInfoVo> queryCustomUserInfoList(QueryCustomInfoDto queryCustomInfoDto) {
        queryCustomInfoDto.setOwnerCode(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode());
        return eventServiceFeign.queryCustomUserInfoList(queryCustomInfoDto).getData();
    }

    @Override
    public Page<SpecialVehicleConfigVo> querySpecialVehicleConfigList(QueryCustomInfoDto queryCustomInfoDto) {
        queryCustomInfoDto.setOwnerCode(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode());
        return eventServiceFeign.querySpecialVehicleConfigList(queryCustomInfoDto).getData();
    }

    @Override
    public void saveRuleInfo(RuleInfoDto ruleInfoDto) {
        eventServiceFeign.saveRuleInfo(ruleInfoDto);
    }

    @Override
    public Map<String, List<CommonConfigDto>> queryCommonConfigList() {
        return eventServiceFeign.queryCommonConfigList().getData();
    }
}
