package com.volvo.maintain.application.maintainlead.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "PurchaseGiveParamsDTO", description = "C端调用保存延保记录接口")
public class PurchaseGiveParamsDTO {
    @ApiModelProperty(value = "经销商编码", name = "dealerCode")
    private String dealerCode;
    @ApiModelProperty(value = "会员id", name = "memberId")
    private String memberId;
    @ApiModelProperty(value = "车辆里程", name = "mileage")
    private Integer mileage;
    @ApiModelProperty(value = "vin", name = "vin")
    private String vin;
    @ApiModelProperty(value = "车型", name = "modelCode")
    private String modelCode;
    @ApiModelProperty(value = "车辆销售价格", name = "invoicePrice")
    private BigDecimal invoicePrice;
    @ApiModelProperty(value = "发动机代码", name = "engineNo")
    private String engineNo;
    @ApiModelProperty(value = "开票日期", name = "invoiceDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date invoiceDate;
    @ApiModelProperty(value = "商品编号", name = "skuId")
    private String skuId;
    @ApiModelProperty(value = "延保产品件号", name = "productNo")
    private String productNo;
    @ApiModelProperty(value = "订单编号", name = "orderCode")
    private String orderCode;
    @ApiModelProperty(value = "支付时间", name = "payTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;
    @ApiModelProperty(value = "车辆销售时间", name = "payTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date salesDate;
    @ApiModelProperty(value = "状态", name = "giveStatus")
    private Integer giveStatus;
    @ApiModelProperty(value = "渠道", name = "giveChannel")
    private Integer giveChannel;
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号", name = "licensePlateNum")
    private String licensePlateNum;
}
