package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/02/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("保养灯线索")
public class InviteVehicleRecordDto {

    // 开始时间
    private String beginTime;

    // 结束时间
    private String endTime;

}
