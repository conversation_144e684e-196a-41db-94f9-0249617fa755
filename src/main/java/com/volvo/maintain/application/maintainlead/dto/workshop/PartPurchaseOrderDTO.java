package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 零附件采购订单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Data
public class PartPurchaseOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id自增序列
     */
    private Long id;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 采购单号，生成规则YV+YY+MM+四位自增长数字；批量采购订单号，生成规则YI+YY+MM+四位自增长数字
     */
    private String purchaseNo;

    /**
     * 订单类型：LDC经销商：1、3；VMI经销商：1、4非批售；其中4-批售订单根据经销商是否被授权批售
     */
    private Integer orderLevel;

    /**
     * 订单状态，未上传、已上传
     */
    private Integer orderStatus;

    /**
     * 提交日期
     */
    private Date submitDate;

    /**
     * 批售审核状态，待审核、审核不通过、审核通过
     */
    private Integer wholesaleApproval;

    /**
     * 超额审批状态，待审核、审核不通过、审核通过
     */
    private Integer overfulfilApproval;

    /**
     * 根据订单类型，1类：17-空运；3类：43-紧急陆运；4类批售/非批售：20-公路
     */
    private Integer transportMode;

    /**
     * 总采购项次
     */
    private Integer purchaseItem;

    /**
     * 预估采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 关联单号
     */
    private String linkedNumber;

    /**
     * 车主/外卖对象
     */
    private String ownerObject;

    /**
     * 备注
     */
    private String remark;

    /**
     * 超额申请理由
     */
    private String overfulfilRemark;

    /**
     * 超额审批意见,可以更新成null
     */
    private String overfulfilOpinion;

    /**
     * 批售审核意见
     */
    private String wholesaleOpinion;

    /**
     * 是否锁定，1：锁定，0：未锁定，锁定后只允许锁定人进行操作
     */
    private Integer isLock;

    /**
     * 锁定人
     */
    private Long lockBy;

    /**
     * VIPS下发验收单后，更新到此字段
     */
    private String vvNo;

    /**
     * 默认为导入日期+3天，批量采购订单使用该字段
     */
    private Date demandDate;


    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 是否新店首铺
     */
    private Integer isFirstStore;


    /**
     * 批售审核时间
     */
    private Date wholesaleCheckTime;

    /**
     * 批售审核接口获取时间
     */
    private Date wholesaleApiTime;


    /**
     * 是否LDC，1：是，0：否
     */
    private Integer isLdc;

    /**
     * 锁定人姓名
     */
    private String lockByName;

    private Integer dataSources;

    private Integer orderPullStats;
    private Integer orderSource;
    private Integer orderTo;
    private Integer isUpload;
    /**
     * 已收到实物(0,未收到 1，已收到)
     */
    private Integer isGetGoods;

    /**
     * JD补货订单号
     */
    private String replenishmentNo;

    /**
     * 补货时间
     */
    private Date replenishmentDate;

    /**
     * 发货仓库
     */
    private String shipWarehouse;
    /**
     * 发货地址
     */
    private String shipAddress;
    /**
     * 收件人姓名
     */
    private String receiveName;
    /**
     * 收件人手机号
     */
    private String receivePhone;

    /**
     * jd仓库代码
     */
    private String jdRepoCode;

    /**
     * 差异索赔单号
     */
    private String diffNo;

    /**
     * 差异索赔单出库标志
    * 1：虚出
     *
     * 0：实出
    * */
    private Integer outFlag;

    /**
     * vin
     */
    private String vin;

    /**
     * 关联工单号
     */
    private String relevancyRoNo;

    /**
     * 关联技术支持工单号
     */
    private String relevancyTechnicalSupportNo;

    /**
     * 确认时间
     */
    private Date affirmTime;

    /**
     * 确认人
     */
    private String affirmBy;

    /**
     * 上传时间
     */
    private Date uploadTime;
    
    /**
     * 零件支付类型（ 现金账户-96231001/吉致金融-96231002）
     */
    private String purchasePaymentType;
    
    /**
     * OG订单状态
     */
    private String ogOrderStatus;
    
    /**
     * OG采购金额（含税）
     */
    private String ogTotalTaxAmount;
    
    /**
     * 原因
     */
    private String reason;
    
    /**
     * OG单号
     */
    private String ogOrderNo;
    
    /**
     * SAPCode
     */
    private String sapCode;
    
}
