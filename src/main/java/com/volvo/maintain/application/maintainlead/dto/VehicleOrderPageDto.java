package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VehicleOrderPageDto {
    @ApiModelProperty(value = "当前页")
    private Integer page;
    @ApiModelProperty(value = "每页数量")
    private Integer pageSize;
    @ApiModelProperty(value = "具体入参")
    private VehicleOrderRequestDto vehicleOrderRequestDto;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class VehicleOrderRequestDto {
        @ApiModelProperty(value = "订单状态")
        private Integer[] orderStatus;
        @ApiModelProperty(value = "开始时间")
        private String startCreateTime;
        @ApiModelProperty(value = "结束时间")
        private String endCreateTime;
    }
}
