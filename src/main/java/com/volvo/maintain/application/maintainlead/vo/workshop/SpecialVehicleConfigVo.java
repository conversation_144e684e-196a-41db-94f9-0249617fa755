package com.volvo.maintain.application.maintainlead.vo.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description 特殊人员配置信息
 * @Date 2024/11/14 16:49
 */

@ApiModel("自定义人员信息")
@Data
public class SpecialVehicleConfigVo {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("VIN")
    private String vin;

    @ApiModelProperty("车牌号")
    private String licensePlateNumber;

    @ApiModelProperty("品牌")
    private String vehicleBrand;

    @ApiModelProperty("车系")
    private String vehicleSeries;

    @ApiModelProperty("车型")
    private String vehicleModel;

    @ApiModelProperty("年款")
    private String vehicleYear;

    @ApiModelProperty("RD日期")
    private LocalDateTime rdData;

    @ApiModelProperty("开票日期")
    private LocalDateTime invoiceDate;
}
