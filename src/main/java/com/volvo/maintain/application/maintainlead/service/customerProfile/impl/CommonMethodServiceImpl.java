package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;

import com.google.common.collect.Lists;
import com.volvo.exception.ServiceBizException;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.message.MessageSendDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CustomerService;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ImResponse;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;
import com.volvo.utils.LoginInfoUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

@Service
public class CommonMethodServiceImpl implements CommonMethodService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;

    @Autowired
    private DomainMaintainLeadFeign maintainLeadsFegin;

    @Autowired
    private MidEndMessageCenterFeign midEndMessageCenterFeign;

    @Autowired
    private MidEndAuthCenterFeign midEndAuthCenterFeign;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DomainMaintainAuthFeign domainMaintainAuthFeign;

    @Autowired
    private MidEndOrgCenterFeign midEndOrgCenterFeign;

    @Autowired
    private CustomerService customerService;

    @Value("${volvo.message.appId}")
    private String appId1;
    
    @Value("${volvo.message.sms.num:400}")
    private Integer smsNum;
    
    @Value("${volvo.message.sms.batchNum:300}")
    private Integer batchNum;

    private static String appId;

    @PostConstruct
    void init() {
        appId = appId1;
    }

    /**
     * 获取车辆信息
     */
    @Override
    public TmVehicleDto queryVehicle(String vin) {
        //获取车辆信息
        DmsResponse<TmVehicleDto> vehicleResponse = midEndVehicleCenterFeign.getVehicleByVIN(vin);
        logger.info("获取车辆信息 selectInviteClueTag->vehicleResponse:{}", vehicleResponse);
        if (vehicleResponse == null || !CommonConstant.SUCCESS_CODE.equals(vehicleResponse.getReturnCode()) || ObjectUtils.isEmpty(vehicleResponse.getData())) {
            return null;
        }
        return vehicleResponse.getData();
    }


    /**
     * 获取车辆信息
     */
    @Override
    public Boolean pushSms(String key, PushMessageRecordVo pushMessageRecord, MessageSendDto messageSendDto) {
        logger.info("发送短信 处理Redis锁信息");
        
    	Boolean flag = false;
        String mobiles = messageSendDto.getMobiles();
    	String[] split = mobiles.split(",");
    	List<String> asList = Arrays.asList(split);
    	if(asList.size()<smsNum) {
    		flag = sendSmsMobiles(key, pushMessageRecord, messageSendDto);
    	} else {
    		List<List<String>> partition = Lists.partition(asList, batchNum);
    		String subBizNo = pushMessageRecord.getSubBizNo();
    		int bizNo = 0;
    		for (List<String> list : partition) {
    			bizNo++;
    			String join = String.join(",", list);
    			messageSendDto.setMobiles(join);
    			logger.info("发送短信拆分：第【{}】次, subBizNo: {}, list.size : {}", bizNo, String.join("-", subBizNo, String.valueOf(bizNo)), list.size());
    			if(bizNo>1) {
    				logger.info("拼接序号至结尾");
    				pushMessageRecord.setSubBizNo(String.join("-", subBizNo, String.valueOf(bizNo)));
    			}
    			flag = sendSmsMobiles(key, pushMessageRecord, messageSendDto);
    		}
    	}
        return flag;
    }

	private Boolean sendSmsMobiles(String key, PushMessageRecordVo pushMessageRecord, MessageSendDto messageSendDto) {
		boolean flag = false;
        RLock lock = redissonClient.getLock(StringUtils.join(key, pushMessageRecord.getSubBizNo()));
        try {
            // 获取锁
            if (!lock.tryLock()) {
                logger.info("执行被拦截，{}", new Date());
                throw new RuntimeException("同车牌同时执行被拦截");
            }

            DmsResponse<PushMessageRecordVo> pushMessageRecordVoDmsResponse = maintainLeadsFegin.selectPushMessageRecord(pushMessageRecord.getSinceType(), pushMessageRecord.getBizNo(), pushMessageRecord.getSubBizNo());
            logger.info("获取推送信息 pushSms->pushMessageRecordVoDmsResponse:{}", pushMessageRecordVoDmsResponse);

            if (ObjectUtils.isEmpty(pushMessageRecordVoDmsResponse)) {
                logger.info("pushSms pushMessageRecordVoDmsResponse isnull ");
                return false;
            }

            PushMessageRecordVo pushMessageRecordVo = pushMessageRecordVoDmsResponse.getData();
            logger.info("消息推送记录，pushMessageRecordVo:{}", pushMessageRecordVo);

            if (ObjectUtils.isNotEmpty(pushMessageRecordVo)) {

                if (pushMessageRecordVo.getTaskStatus() == 1) {
                    logger.info("已有短信发出成功 subBizNo{}", pushMessageRecord.getSubBizNo());
                    return false;
                }

                if (pushMessageRecordVo.getTaskStatus() == -1) {
                    logger.info("超出重试范围 subBizNo{}", pushMessageRecord.getSubBizNo());
                    return false;
                }
            }

            if (ObjectUtils.isEmpty(pushMessageRecordVo)) {
                pushMessageRecordVo = new PushMessageRecordVo();
                pushMessageRecordVo.setTaskStatus(0);
                pushMessageRecordVo.setBizNo(pushMessageRecord.getBizNo());
                pushMessageRecordVo.setSubBizNo(pushMessageRecord.getSubBizNo());
                pushMessageRecordVo.setSinceType(pushMessageRecord.getSinceType());
                pushMessageRecordVo.setRetryCount(0);
                logger.info("新增消息推送记录:{}", pushMessageRecordVo);
                DmsResponse<Integer> integerDmsResponse = maintainLeadsFegin.insertPushMessageRecord(pushMessageRecordVo);

                if (integerDmsResponse.isFail()) {
                    return false;
                }

                Integer data1 = integerDmsResponse.getData();

                if (ObjectUtils.isEmpty(data1)) {
                    return false;
                }

                pushMessageRecordVo.setId(data1);
            }

            logger.info("pushSms 开始发送短信");

            try {
            	flag = pushSms(messageSendDto);
            } catch (Exception e) {
                logger.error("发送短信失败信息");
            }

            if (flag) {
                pushMessageRecordVo.setTaskStatus(1);
            } else {
                if (pushMessageRecordVo.getRetryCount() > 2) {
                    pushMessageRecordVo.setRetryCount(pushMessageRecordVo.getRetryCount() + 1);
                    pushMessageRecordVo.setTaskStatus(-1);
                } else {
                    pushMessageRecordVo.setTaskStatus(2);
                }
            }

            pushMessageRecordVo.setReqParams(messageSendDto.toString().length() > 1500 ? messageSendDto.toString().substring(0, 1500) : messageSendDto.toString());
            pushMessageRecordVo.setReceiver(messageSendDto.getMobiles().length() > 80 ? messageSendDto.getMobiles().substring(0, 80) : messageSendDto.getMobiles());
            logger.info("更新消息推送记录:{}", pushMessageRecordVo);
            maintainLeadsFegin.updatePushMessageRecord(pushMessageRecordVo);


        } catch (Exception e) {
            logger.error("发送短信失败 ", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return flag;
	}

    @Override
    public boolean pushSms(MessageSendDto messageSendDto) {
        logger.info("短信发送参数 dto:{}", messageSendDto);
        ImResponse imResponse = midEndMessageCenterFeign.pushSms(appId, messageSendDto);

        logger.info("短信发送返回 dto:{}", imResponse);
        if (!imResponse.isFail()) {
            logger.error("发送短信失败信息 dto:{}", imResponse.getMsg());
            throw new ServiceBizException("发送短信失败！");
        }
        return true;
    }

    @Override
    public List<EmpByRoleCodeDto> getEmpByRoleCodeDtos(EmpByRoleCodeDto empByRoleCodeDto) {

        ResponseDto<EmpByRoleCodeDto> responseDto = new ResponseDto<>();
        responseDto.setData(empByRoleCodeDto);
        DmsResponse<List<EmpByRoleCodeDto>> listResponseDto = midEndAuthCenterFeign.queryDealerUser(responseDto);
        logger.info("角色信息返回 listResponseDto:{}", listResponseDto);

        if (listResponseDto.isFail()) {
            return null;
        }

        List<EmpByRoleCodeDto> data = listResponseDto.getData();

        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        return data;
    }

    /**
     * 获取车辆信息
     */
    @Override
    public Boolean pushEmail(String key, PushMessageRecordVo pushMessageRecord, EmailInfoDto emailInfoDto) {
        logger.info("发送邮件 处理Redis锁信息");
        boolean flag = false;
        RLock lock = redissonClient.getLock(StringUtils.join(key, pushMessageRecord.getSubBizNo()));
        try {
            // 获取锁
            if (!lock.tryLock()) {
                logger.info("执行被拦截，{}", new Date());
                throw new RuntimeException("同时发送邮件执行被拦截");
            }
            
            pushMessageRecord.setSinceType(1);            

            DmsResponse<PushMessageRecordVo> pushMessageRecordVoDmsResponse = maintainLeadsFegin.selectPushMessageRecord(pushMessageRecord.getSinceType(), pushMessageRecord.getBizNo(), pushMessageRecord.getSubBizNo());
            logger.info("获取邮件推送信息 pushEmail->pushMessageRecordVoDmsResponse:{}", pushMessageRecordVoDmsResponse);

            if (ObjectUtils.isEmpty(pushMessageRecordVoDmsResponse)) {
                logger.info("pushEmail pushMessageRecordVoDmsResponse isnull ");
                return false;
            }

            PushMessageRecordVo pushMessageRecordVo = pushMessageRecordVoDmsResponse.getData();
            logger.info("消息邮件推送记录，pushMessageRecordVo:{}", pushMessageRecordVo);

            if (ObjectUtils.isNotEmpty(pushMessageRecordVo)) {

                if (pushMessageRecordVo.getTaskStatus() == 1) {
                    logger.info("已有邮件发出成功 subBizNo{}", pushMessageRecord.getSubBizNo());
                    return false;
                }

                if (pushMessageRecordVo.getTaskStatus() == -1) {
                    logger.info("超出重试范围 subBizNo{}", pushMessageRecord.getSubBizNo());
                    return false;
                }
            }

            if (ObjectUtils.isEmpty(pushMessageRecordVo)) {
                pushMessageRecordVo = new PushMessageRecordVo();
                pushMessageRecordVo.setTaskStatus(0);
                pushMessageRecordVo.setBizNo(pushMessageRecord.getBizNo());
                pushMessageRecordVo.setSubBizNo(pushMessageRecord.getSubBizNo());
                pushMessageRecordVo.setSinceType(pushMessageRecord.getSinceType());
                pushMessageRecordVo.setRetryCount(0);
                logger.info("新增邮件消息推送记录:{}", pushMessageRecordVo);
                DmsResponse<Integer> integerDmsResponse = maintainLeadsFegin.insertPushMessageRecord(pushMessageRecordVo);

                if (integerDmsResponse.isFail()) {
                    return false;
                }

                Integer data1 = integerDmsResponse.getData();

                if (ObjectUtils.isEmpty(data1)) {
                    return false;
                }

                pushMessageRecordVo.setId(data1);
            }

            logger.info("pushEmail 开始发送邮件");

            try {
                flag = pushEmail(emailInfoDto);
            } catch (Exception e) {
                logger.error("发送邮件失败信息");
            }

            if (flag) {
                pushMessageRecordVo.setTaskStatus(1);
            } else {
                if (pushMessageRecordVo.getRetryCount() > 2) {
                    pushMessageRecordVo.setRetryCount(pushMessageRecordVo.getRetryCount() + 1);
                    pushMessageRecordVo.setTaskStatus(-1);
                } else {
                    pushMessageRecordVo.setTaskStatus(2);
                }
            }

            pushMessageRecordVo.setReqParams(truncateWithMarker(emailInfoDto.toString(),EmailInfoDto.MAX_LENGTH,EmailInfoDto.MARKER));
            pushMessageRecordVo.setReceiver(emailInfoDto.getFrom());
            logger.info("更新邮件消息推送记录:{}", pushMessageRecordVo);
            maintainLeadsFegin.updatePushMessageRecord(pushMessageRecordVo);
        } catch (Exception e) {
            logger.error("发送邮件失败 ", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return flag;
    }

    private static String truncateWithMarker(String input, int maxLen, String marker) {
        return input.length() > maxLen
                ? input.substring(0, maxLen) + marker
                : input;
    }

    @Override
    public boolean pushEmail(EmailInfoDto emailInfoDto) {
        DmsResponse dmsResponse = midEndMessageCenterFeign.pushEmails(appId, emailInfoDto);
        logger.info("提醒发送邮件中 dto:{}", dmsResponse);
        if (!"0".equals(dmsResponse.getReturnCode())) {
            logger.error("发送邮件失败信息 dto:{}", dmsResponse.getErrMsg());
            throw new ServiceBizException("发送邮件失败！");
        }
        return true;
    }

    /**
     * 获取车辆信息
     */
    @Override
    public TmVehicleDto queryVehicleByVin(String vin) {
        //获取车辆信息
        DmsResponse<TmVehicleDto> vehicleResponse = midEndVehicleCenterFeign.queryVehicleByVIN(vin);
        if (vehicleResponse == null || !CommonConstant.SUCCESS_CODE.equals(vehicleResponse.getReturnCode()) || ObjectUtils.isEmpty(vehicleResponse.getData())) {
            return null;
        }
        return vehicleResponse.getData();
    }

    @Override
    public List<RoleInfoDto> queryRoleListByCompanyCode(String companyCode) {
        ResponseDto<List<RoleInfoDto>> listResponseDto = midEndAuthCenterFeign.queryRoleListByCompanyCode(companyCode);
        logger.info("角色信息返回 listResponseDto:{}", listResponseDto);
        if (listResponseDto.isFail()) {
            return Collections.emptyList();
        }
        List<RoleInfoDto> data = listResponseDto.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        return data;
    }


    /**
     * 查询经销商是否存在白名单
     * @param dealer 经销商
     * @return 是否存在
     */
    @Override
    public Boolean checkWhitelist(String dealer, Integer modType, Integer rosterType, String vin, String groupCode) {
        // 查询白名单
        DmsResponse<Object> response = domainMaintainAuthFeign.checkWhitelist(dealer, modType, rosterType, vin , groupCode);
        logger.info("查询经销商是否存在白名单 dto:{}",response);
        Boolean x = getaBoolean(response);
        if (x) return x;

        if(ObjectUtils.isNotEmpty(dealer)){
            if (dealer.contains("-")){
                groupCode = dealer.split("-")[1];
            }else {
                IsExistByCodeDto build2 = IsExistByCodeDto.builder().companyCode(dealer).build();
                logger.info("根据ownerCode查询所属集团入参:{}",dealer);
                ResponseDto<List<CompanyDetailByCodeDto>> listResponseDTO = midEndOrgCenterFeign.selectByCompanyCode(build2);
                logger.info("根据经销商code查询经销商信息，listResponseDTO:{}", listResponseDTO);
                if (ObjectUtils.isEmpty(listResponseDTO)) {
                    return false;
                }

                List<CompanyDetailByCodeDto> listResponseDtoData = listResponseDTO.getData();
                logger.info("根据经销商code查询经销商信息，listResponseDtoData:{}", listResponseDtoData);
                if(CollectionUtils.isEmpty(listResponseDtoData)){
                    return false;
                }

                CompanyDetailByCodeDto companyDetailByCodeDTO = listResponseDtoData.get(0);
                logger.info("根据经销商code查询经销商信息，companyDetailByCodeDTO:{}", companyDetailByCodeDTO);
                groupCode = companyDetailByCodeDTO.getGroupCompanyId();
            }

            if (ObjectUtils.isNotEmpty(groupCode)) {
                logger.info("查询集团是否存在白名单 groupCode:{}",groupCode);
                response = domainMaintainAuthFeign.checkWhitelist("", modType, 3, vin , groupCode);
                logger.info("查询集团是否存在白名单 response:{}",response);
                x = getaBoolean(response);
            }
        }
        return x;
    }

    private Boolean getaBoolean(DmsResponse<Object> response) {
        boolean b;
        if (Objects.isNull(response) || response.isFail()){
            logger.info("checkWhitelist error");
            return false;
        }
        Object data = response.getData();
        if (null == data) {
            logger.info("checkWhitelist isnull");
            return false;
        }
        try {
            b = Boolean.parseBoolean(data.toString());
        } catch (Exception e) {
            logger.info("checkWhitelist isWhite e:{}", e);
            b = false;
        }

        if (b){
            return b;
        }
        return false;
    }

    @Override
    public  String getOwnerCode() {
        String ownerCode;
        try {
            logger.info("获取当前登录信息 CurrentLoginInfoDto:{}",LoginInfoUtil.getCurrentLoginInfo());
            ownerCode = LoginInfoUtil.getCurrentLoginInfo().getOwnerCode();
            if (org.apache.commons.lang.StringUtils.isEmpty(ownerCode) || "VOLVO".equals(ownerCode)) {
                ownerCode = null;
            }
        } catch (Exception e) {
            ownerCode = null;
        }
        return ownerCode;
    }


    @Override
    public List<CustomerInfoDto> queryCustomInfoListByVin(String vin, String ownerCode) {
        logger.info("queryCustomInfoListByVin vin:{},ownerCode:{}", vin, ownerCode);
        return customerService.queryCustomInfoListByVin(vin, ownerCode);
    }

}
