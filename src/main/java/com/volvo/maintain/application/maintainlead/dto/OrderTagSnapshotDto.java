package com.volvo.maintain.application.maintainlead.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
    * 工单标签信息快照表
    */
@ApiModel(description="工单标签信息快照表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderTagSnapshotDto {
    /**
     * 主键ID
     */
    @ApiModelProperty(value="主键ID")
    private Integer id;

    /**
     * 经销商编号
     */
    @ApiModelProperty(value="经销商编号")
    private String ownerCode;

    /**
     * 工单号
     */
    @ApiModelProperty(value="工单号")
    private String roNo;

    /**
     * 手机号
     */
    @ApiModelProperty(value="手机号")
    private String mobile;

    /**
     * 车架号
     */
    @ApiModelProperty(value="车架号")
    private String vin;

    /**
     * 标签ID
     */
    @ApiModelProperty(value="标签ID")
    private String tagId;

    /**
     * 标签名称
     */
    @ApiModelProperty(value="标签名称")
    private String tagName;

    /**
     * 标签值
     */
    @ApiModelProperty(value="标签值")
    private String tagValue;

    /**
     * 实际值
     */
    @ApiModelProperty(value="实际值")
    private String actualValue;

    /**
     * 意向车型
     */
    @ApiModelProperty(value="意向车型")
    private String intentionModel;

    /**
     * 是否已推送
     */
    @ApiModelProperty(value="是否已推送")
    private Integer isPush;

    /**
     * 意向类别
     */
    @ApiModelProperty(value="意向类别")
    private Integer intentionType;

    /**
     * 推送时间
     */
    @ApiModelProperty(value="推送时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /**
     * 删除标识（0-未删除，1-已删除）
     */
    @ApiModelProperty(value="删除标识（0-未删除，1-已删除）")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value="更新人")
    private String updateBy;

    /**
     * 创建sql人
     */
    @ApiModelProperty(value="创建sql人")
    private String createSqlby;

    /**
     * 更新sql人
     */
    @ApiModelProperty(value="更新sql人")
    private String updateSqlby;
}