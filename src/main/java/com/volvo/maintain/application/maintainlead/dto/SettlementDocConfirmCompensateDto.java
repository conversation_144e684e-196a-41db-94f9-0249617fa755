package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementDocConfirmCompensateDto implements Serializable{
	private static final long serialVersionUID = 1L;
    /**
     * 交车开始时间
     */
    private String deliveryDateStart;
    /**
     * 交车结束时间
     */
    private String deliveryDateEnd;
    /**
     * 经销商
     */
    private String ownerCode;
    /**
     * 工单号
     */
    private String roNo;
}
