package com.volvo.maintain.application.maintainlead.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 商户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "OrderVO 对象", description = "OrderVO")
public class OrderVo{

    @ApiModelProperty(value = "订单号  ")
    private String orderId;
    @ApiModelProperty(value = "商户id  ")
    private String customerId;
    @ApiModelProperty(value = "车主姓名  ")
    private String username;
    @ApiModelProperty(value = "订单类型 1-取送车 ")
    private String type;
    @ApiModelProperty(value = "车主手机号  ")
    private String mobile;
    @ApiModelProperty(value = "下单人手机号  ")
    private String createMobile;
    @ApiModelProperty(value = "车牌号  ")
    private String carNo;
    @ApiModelProperty(value = "车辆品牌名称  ")
    private String carBrandName;
    @ApiModelProperty(value = "车辆车系名称  ")
    private String carSeriesName;
    @ApiModelProperty(value = "订单状态 参考status状态枚举列表 ")
    private String status;
    @ApiModelProperty(value = "创建时间 毫秒数 ")
    private String createTime;
    @ApiModelProperty(value = "取消角色（订单列表接口用） 0用户、1客服、 2坐席、3司机 ")
    private String cancelRole;
    @ApiModelProperty(value = "取消原因（订单列表接口用）  ")
    private String cancelReason;
    @ApiModelProperty(value = "订单支付方式，默认为0 0-vip扣款 1-用户付款 ")
    private String payMode;
    @ApiModelProperty(value = "优惠券码  ")
    private String couponCode;
    @ApiModelProperty(value = "优惠券名称  ")
    private String couponName;
    @ApiModelProperty(value = "优惠券抵扣金额  ")
    private String couponMoney;

}

