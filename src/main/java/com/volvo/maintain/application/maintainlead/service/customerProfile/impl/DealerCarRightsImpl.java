package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;

import com.google.common.collect.Lists;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.rights.DealerCarRightsDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.DealerCarRights;
import com.volvo.maintain.infrastructure.gateway.CarRightsServiceFeign;
import com.volvo.maintain.interfaces.vo.rights.DealerCarRightsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.USER_RIGHTS_STATUS;

@Slf4j
@Service
public class DealerCarRightsImpl implements DealerCarRights {

    @Autowired
    private CarRightsServiceFeign carRightsServiceFeign;

    @Override
    public List<DealerCarRightsVo> dealerRightsList(String vin, String ownerCode){
        DealerCarRightsDto carRightsDto = new DealerCarRightsDto();
        carRightsDto.setVinList(Lists.newArrayList(vin));
        if (Objects.nonNull(ownerCode)){
            carRightsDto.setUsingDealers(Lists.newArrayList(Collections.singletonList(ownerCode)));
        }
        carRightsDto.setUserRightsStatusList(Lists.newArrayList(USER_RIGHTS_STATUS));
        carRightsDto.setPage(1);
        carRightsDto.setPageSize(1000);
        log.info("dealerRightsList request param:{}",carRightsDto);
        ResponseDto<List<DealerCarRightsVo>> responseDto = carRightsServiceFeign.dealerRightsList(carRightsDto);
        log.info("dealerRightsList response:{}",responseDto);
        if(responseDto.isSuccess()){
            List<DealerCarRightsVo> data = responseDto.getData();
            return Objects.isNull(data) ? Lists.newArrayList() : data;
        }else{
            return Lists.newArrayList();
        }
    }

}
