package com.volvo.maintain.application.maintainlead.vo.order;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 商户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "TraceVO 对象", description = "TraceVO")
public class TraceVo {

    @ApiModelProperty(value = "接单时间 毫秒数")
    private String acceptTime;
    @ApiModelProperty(value = "就位时间 毫秒数")
    private String arriveTime;
    @ApiModelProperty(value = "开车时间 毫秒数")
    private String driveTime;
    @ApiModelProperty(value = "完成时间 毫秒数")
    private String finishTime;
    @ApiModelProperty(value = "最后路径点时间 毫秒数")
    private String lastTime;
    @ApiModelProperty(value = "车辆vin码")
    private String vin;

    @ApiModelProperty(value = "开单到就位间的坐标点集合 ")
    private List<JSONObject> arrive;
    @ApiModelProperty(value = "就位到开车间的坐标点集合 ")
    private List<JSONObject> await;
    @ApiModelProperty(value = "开车到完成间的坐标点集合 ")
    private List<JSONObject> drive;

    @ApiModelProperty(value = "  ")
    private OrderStatesInfoVo orderStatesInfo;
}