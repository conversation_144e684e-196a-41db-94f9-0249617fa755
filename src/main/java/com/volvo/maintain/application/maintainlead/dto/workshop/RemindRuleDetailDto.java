package com.volvo.maintain.application.maintainlead.dto.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 规则详情入参
 * @Date 2024/11/15 10:50
 */

@Data
@ApiModel("查询自定义信息入参")
@Builder
public class RemindRuleDetailDto {

    @ApiModelProperty(value = "规则经销商表主键id")
    private Integer id;

    @ApiModelProperty("中台角色信息")
    private List<MidRoleInfoDto> midRoleInfoDtoList;
}
