package com.volvo.maintain.application.maintainlead.service.Em90Delivery;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.enums.BizGroupEnum;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.IcdResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class Em90DeliveryServiceImpl implements Em90DeliveryService{
    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;
    @Autowired
    private MidEndTechSupportServiceFeign midEndTechSupportServiceFeign;
    @Autowired
    private DomainMaintainLeadFeign domainMaintainLeadFeign;

    @Override
    public boolean selectEm90DeliveryFrame(String ownerCode, String roNo, String vin) {
        //校验车辆是否为em90
        DmsResponse<TmVehicleDto> vehicleByVIN = midEndVehicleCenterFeign.getVehicleByVIN(vin);
        log.info("selectEm90DeliveryFrame getVehicleByVIN DmsResponse:{}", vehicleByVIN);
        if (Objects.isNull(vehicleByVIN) || vehicleByVIN.isFail() || Objects.isNull(vehicleByVIN.getData()) || StringUtils.isEmpty(vehicleByVIN.getData().getModelCode())) {
            log.info("根据车架号vin查询车辆信息异常");
            return false;
        }
        //判断车型是否为em90
        DmsResponse<CommonConfigDto> emKey = dmscloudServiceFeign.getConfigByKey(CommonConstant.MODEL_CODE_EM90, CommonConstant.GROUP_TYPE_EM90);
        log.info("emKey :{}", emKey);
        if (Objects.isNull(emKey) || emKey.isFail() || Objects.isNull(emKey.getData())) {
            log.info("查询车型code异常");
            return false;
        }
        if (!vehicleByVIN.getData().getModelCode().equals(emKey.getData().getConfigValue())) {
            log.info("selectEm90DeliveryFrame 该车型不属于em90");
            return false;
        }
        //查询工单是否做过检查
        log.info("selectEm90DeliveryFrame owner:{},roNo:{},vin:{}",ownerCode,roNo,vin);
        DmsResponse<Boolean> em90Delivery = domainMaintainOrdersFeign.isEm90Delivery(ownerCode, roNo);
        log.info("domainMaintainOrdersFeign.isEm90Delivery:{}",em90Delivery);
        if (Objects.isNull(em90Delivery) || Objects.isNull(em90Delivery.getData())){
            log.info("查询是否检查异常");
            return false;
        }
        if (em90Delivery.getData()){
            //有检查记录 不弹框
            return false;
        }else {
            //无检查记录 校验工单是否(机电维修、保修、事故)
            DmsResponse<RepairOrderEntryTimeDto> repairOrderEntryTimeDtoDmsResponse = domainMaintainOrdersFeign.selectRepairOrderEntryTime(ownerCode, roNo);
            log.info("repairOrderEntryTimeDtoDmsResponse repairOrderEntryTimeDtoDmsResponse{}",repairOrderEntryTimeDtoDmsResponse);
            if (Objects.isNull(repairOrderEntryTimeDtoDmsResponse) || repairOrderEntryTimeDtoDmsResponse.isFail()){
                log.info("查询进场时间异常");
                return false;
            }
            if (Objects.isNull(repairOrderEntryTimeDtoDmsResponse.getData())){
                log.info("selectEm90DeliveryFrame 工单类型不为机电维修、保修、事故不需要弹框");
                return false;
            }
            //查询检查报告
            //当前时间
            String nowDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            log.info("当前时间:{}",nowDate);
            //进场时间
            String entryTime = repairOrderEntryTimeDtoDmsResponse.getData().getEntryTime();
            if (StringUtils.isEmpty(entryTime)){
                log.info("进场时间为空");
                return false;
            }
            log.info("进场时间:{}",entryTime);
            IcdResponse<List<ConsultationReportDto>> consultationReportForNb = midEndTechSupportServiceFeign.getConsultationReportForNb(vin, ownerCode, entryTime, nowDate);
            log.info("getConsultationReportForNb consultationReportForNb :{}",consultationReportForNb);
            if (Objects.isNull(consultationReportForNb) || consultationReportForNb.isFail()){
                log.info("查询检查报告异常");
                return true;
            }
            //不存在检测数据
            if (CollectionUtils.isEmpty(consultationReportForNb.getData())){
                log.info("不存在检测数据");
                return true;
            }
            //入库检测数据
            insertVehicleCheckData(ownerCode,roNo,vin,entryTime,consultationReportForNb.getData().get(0));
            return false;
        }
    }

    @Override
    public void submitReportReason(ReportReasonDto reportReasonDto) {
        log.info("submitReportReason :{}",reportReasonDto);
        domainMaintainOrdersFeign.submitReportReason(reportReasonDto);
    }

    @Override
    public List<String> queryEmphasisClientRemind(String ownerCode, String vin, String license, String roNo) {
        log.info("queryEmphasisClientRemind :{},{},{},{}",ownerCode,vin,license,roNo);
        //根据车架号换取车牌号
        DmsResponse<TmVehicleDto> vehicleByVIN = midEndVehicleCenterFeign.getVehicleByVIN(vin);
        log.info("queryEmphasisClientRemind getVehicleByVIN DmsResponse:{}", vehicleByVIN);
        if (Objects.isNull(vehicleByVIN) || vehicleByVIN.isFail() || Objects.isNull(vehicleByVIN.getData())) {
            log.info("根据车架号vin查询车辆信息异常");
            return Collections.emptyList();
        }
        //中台车牌不为空取中台车牌
        if (StringUtils.isNotBlank(vehicleByVIN.getData().getPlateNumber())){
            log.info("queryEmphasisClientRemind 中台车牌不为空取中台车牌");
            license = vehicleByVIN.getData().getPlateNumber();
        }

        //1. 触发群组：重点关照、重点维修
        List<BizGroupEnum> popUpBizGroupList = Arrays.asList(BizGroupEnum.BIZ_GROUP_T, BizGroupEnum.BIZ_GROUP_LB);

        //2. 查询车辆VIP群组，判断是否需要弹窗
        DmsResponse<List<String>> vipGroupRes = domainMaintainLeadFeign.queryVipGroupByVinOrLicense(vin, license);
        log.info("弹窗查询车辆VIP群组，vipGroupRes:{}", JSON.toJSONString(vipGroupRes));
        //2.1 vip群组为空，不弹窗
        if (ObjectUtils.isEmpty(vipGroupRes) || ObjectUtils.isEmpty(vipGroupRes.getData())) {
            return Collections.emptyList();
        }
        List<String> needPopUpBizGroupList = vipGroupRes.getData().stream().filter(vipGroup -> popUpBizGroupList.contains(BizGroupEnum.resolve(vipGroup))).collect(Collectors.toList());
        //2.2 没有要触发的群组，不弹窗
        if (ObjectUtils.isEmpty(needPopUpBizGroupList)) {
            return Collections.emptyList();
        }
        //2.3 没传roNo的情况：每次都会弹
        if (ObjectUtil.isEmpty(roNo)) {
            //返回枚举对应的text
            return needPopUpBizGroupList.stream().map(bizGroup -> BizGroupEnum.resolve(bizGroup).getRemindText()).collect(Collectors.toList());
        }

        //是否环检单转工单
        DmsResponse<Boolean> editNeedPopUpResponse = domainMaintainOrdersFeign.whetherPopUpVipCustom(roNo);
        log.info("是否环检单转工单，需要弹窗res:{}", JSON.toJSONString(editNeedPopUpResponse));
        if (ObjectUtil.isEmpty(editNeedPopUpResponse)
                || ObjectUtil.isEmpty(editNeedPopUpResponse.getData())
                || ObjectUtil.equal(editNeedPopUpResponse.getData(), Boolean.FALSE)) {
            return Collections.emptyList();
        }

        //2.4 传roNo的情况：一个roNo一种群组类型只弹一次
        //2.4.1 获取群组对应弹窗记录表的字段值RemindType
        List<String> mayNeedPopUpRemindTypes = needPopUpBizGroupList.stream()
                .map(needPopUpBizGroup -> {
                    BizGroupEnum bizGroupEnum = BizGroupEnum.resolve(needPopUpBizGroup);
                    return ObjectUtil.isNotEmpty(bizGroupEnum) && ObjectUtil.isNotEmpty(bizGroupEnum.getRemindType()) ? bizGroupEnum.getRemindType() : null;
                })
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());

        //2.4.2 查询弹窗记录
        DmsResponse<List<VehicleCheckReminderDto>> queryRemindRecordRes = domainMaintainOrdersFeign.queryEmphasisClientRemind(
                VehicleCheckReminderReqDTO.builder()
                        .ownerCode(ownerCode)
                        .roNo(roNo)
                        .remindType(mayNeedPopUpRemindTypes)
                        .build()
        );
        log.info("查询重点客户标签提醒记录 queryRemindRecordRes:{}", JSON.toJSONString(queryRemindRecordRes));
        if (ObjectUtil.isEmpty(queryRemindRecordRes) || queryRemindRecordRes.isFail()) {
            return Collections.emptyList();
        }

        List<VehicleCheckReminderDto> data = ObjectUtil.isEmpty(queryRemindRecordRes.getData()) ? Collections.emptyList() : queryRemindRecordRes.getData();
        //2.4.3 过滤已弹窗的数据
        List<String> remindTypeList = data.stream().map(VehicleCheckReminderDto::getRemindType).collect(Collectors.toList());
        List<String> needPopUpRemindTypeList = mayNeedPopUpRemindTypes.stream().filter(remindType -> !remindTypeList.contains(remindType)).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(needPopUpRemindTypeList)) {
            //都弹过了，不再弹
            return Collections.emptyList();
        }

        //3. 保存新的弹窗记录
        List<VehicleCheckReminderDto> insertPopUpRecord = needPopUpRemindTypeList.stream().map(remindType -> {
            VehicleCheckReminderDto vehicleCheckReminderDto = new VehicleCheckReminderDto();
            vehicleCheckReminderDto.setOwnerCode(ownerCode);
            vehicleCheckReminderDto.setRoNo(roNo);
            vehicleCheckReminderDto.setVin(vin);
            vehicleCheckReminderDto.setRemindType(remindType);
            return vehicleCheckReminderDto;
        }).collect(Collectors.toList());
        log.info("保存新的弹窗记录:{}",JSON.toJSONString(insertPopUpRecord));
        domainMaintainOrdersFeign.batchInsertVehicleCheckReminder(insertPopUpRecord);

        //4. 返回弹窗文案
        return needPopUpRemindTypeList.stream().map(BizGroupEnum::getTextByRemindType).collect(Collectors.toList());
    }


    /**
     * 入库检测数据
     * @param ownerCode
     * @param roNo
     * @param vin
     */
    private void insertVehicleCheckData(String ownerCode,String roNo,String vin,String entryTime,ConsultationReportDto consultationReportDto){
        log.info("insertVehicleCheckData:{},{},{},{},ConsultationReportDto:{}",ownerCode,roNo,vin,entryTime,consultationReportDto);
        if (StringUtils.isEmpty(ownerCode) || StringUtils.isEmpty(roNo) || StringUtils.isEmpty(vin)){
            log.info("工单号,车架号,经销商code不能为空");
            return;
        }
        if (Objects.isNull(consultationReportDto)){
            log.info("检测单为空");
            return;
        }
        VehicleCheckReminderDto vehicleCheckReminderDto=new VehicleCheckReminderDto();
        vehicleCheckReminderDto.setOwnerCode(ownerCode);
        vehicleCheckReminderDto.setRoNo(roNo);
        vehicleCheckReminderDto.setVin(vin);
        vehicleCheckReminderDto.setRemindType(CommonConstant.REMIND_TYPE_1);
        vehicleCheckReminderDto.setReportReason(CommonConstant.REMAIND_HAVE_REPORT);
        log.info("insertVehicleCheckData vehicleCheckReminderDto :{}",vehicleCheckReminderDto);
        DmsResponse<Long> longDmsResponse = domainMaintainOrdersFeign.insertVehicleCheckReminder(vehicleCheckReminderDto);
        if (longDmsResponse.isFail() || Objects.isNull(longDmsResponse.getData())){
            log.info("insertVehicleCheckData交车校验提醒表异常");
            return;
        }
        /**
         * ConsultationReportDto
         * (vin=LYGU8951709285227, consultationType=4, consultationNo=VR20240605000022, state=N, dealerCode=SHJ, firstSubmissionTime=2024-06-05 11:13:35)
         */
        HandoverInspectionDetailDto handoverInspectionDetailDto=new HandoverInspectionDetailDto();
        BeanUtil.copyProperties(consultationReportDto,handoverInspectionDetailDto);
        handoverInspectionDetailDto.setInitialSubmissionTime(consultationReportDto.getFirstSubmissionTime());
        handoverInspectionDetailDto.setReportNo(consultationReportDto.getConsultationNo());
        handoverInspectionDetailDto.setReportStatus(consultationReportDto.getState());
        handoverInspectionDetailDto.setReportType(consultationReportDto.getConsultationType()+"");
        handoverInspectionDetailDto.setEntryTime(entryTime);
        handoverInspectionDetailDto.setReminderId(longDmsResponse.getData());
        DmsResponse<Long> insertHandoverInspectionDetailResponse = domainMaintainOrdersFeign.insertHandoverInspectionDetail(handoverInspectionDetailDto);
        log.info("insertHandoverInspectionDetailResponse :{}",insertHandoverInspectionDetailResponse);
    }
}
