package com.volvo.maintain.application.maintainlead.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
    * 标签配置表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TagConfigTempDto implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 标签ID
     */
    private String tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 显示名称
     */
    private String showName;

    /**
     * 标签类型
     */
    private String tagType;

    /**
     * 标签来源
     */
    private Integer tagSource;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 是否支持查看详情
     */
    private Integer isGetDetail;

    /**
     * 详情获取类型
     */
    private Integer detailGetType;

    /**
     * 是否是详情标签
     */
    private Integer isDetailTag;

    /**
     * 详情标签父id
     */
    private String detailTagPid;

    /**
     * 展示优先级
     */
    private Integer showLevel;

    /**
     * 板块类型:1/2/3
     */
    private Integer blockType;

    /**
     * 展示一级板块
     */
    private Integer showFirstBlock;

    /**
     * 展示二级板块
     */
    private Integer showSecondBlock;

    /**
     * 展示三级板块
     */
    private Integer showThirdBlock;

    /**
     * 值规则
     */
    private String valueRule;

    /**
     * 删除标识(0-未删除,1-已删除)
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建sql人
     */
    private String createSqlby;

    /**
     * 更新sql人
     */
    private String updateSqlby;

    /**
     * 是否标签
     */
    private Integer isTag;

    /**
     * 排序
     */
    private Integer showSort;
}