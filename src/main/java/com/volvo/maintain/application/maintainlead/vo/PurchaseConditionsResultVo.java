package com.volvo.maintain.application.maintainlead.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 延保购买条件判断（售后中台 → newbie售后）返回vo
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
@Data
@ApiModel("延保购买条件判断（售后中台 → newbie售后）返回vo")
public class PurchaseConditionsResultVo {
    @ApiModelProperty(name = "vin", value = "Vin (c)")
    private String vin;
    @ApiModelProperty(name = "code", value = "错误枚举")
    private String code;
    @ApiModelProperty(name = "productNo", value = "产品件号")
    private String productNo;
    @ApiModelProperty(name = "lastMileage", value = "MAX里程")
    private Integer lastMileage;
    @ApiModelProperty(name = "lastAge", value = "MAX车龄")
    private Integer lastAge;

    public PurchaseConditionsResultVo() {
    }

    public PurchaseConditionsResultVo(String vin, String code, String productNo) {
        this.vin = vin;
        this.code = code;
        this.productNo = productNo;
    }

}
