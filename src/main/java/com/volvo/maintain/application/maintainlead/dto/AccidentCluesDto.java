package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


/**
 * 事故线索DTO
 * <AUTHOR>
 *
 * 2024年4月29日
 */
@Data
@Builder
@ApiModel("事故线索")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AccidentCluesDto implements Serializable{
	private static final long serialVersionUID = 1L;
	
	 /**
     * 主键ID
     */
    private Long acId;
    /**
     * 所有者代码
     */
    private String ownerCode;
    /**
     * 经销商代码
     */
    private String dealerCode;
    /**
     * 跟进人员id
     */
    private Long followPeople;
    /**
     * 跟进人名称
     */
    private String followPeopleName;
    /**
     * 事故地点
     */
    private String accidentAddress;
    /**
     * 车牌号
     */
    private String license;
    /**
     * 联系
     */
    private String contacts;
    /**
     * 创建时间
     */
    private String createdAt;
    /**
     * 车型
     */
    private String modelName;

    @ApiModelProperty("事故线索父级 CRM ID")
    private Long parentCrmId;

    @ApiModelProperty("是否重复")
    private Integer repeatLead;
    /**
     * 跟进状态
     */
    private Integer followStatus;
    private Integer cluesStatus;

    @ApiModelProperty(value="线索ID")
    private Long crmId;

    private Long id;
}
