package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.volvo.maintain.infrastructure.constants.CommonConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@Data
public class ETAResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回代码
     */
    private Integer code;

    /**
     * 返回描述
     */
    private String msg;

    /**
     * 返回代码
     */
    private Integer subCode;

    /**
     * 子错误描述
     */
    private String subMsg;


    /**
     * 返回数据
     */
    private T data;

    public boolean isSuccess() {
        return Objects.equals(this.getCode(), CommonConstant.SUCCESS_ETA_CODE);
    }

    public boolean isFail() {
        return !isSuccess();
    }
}
