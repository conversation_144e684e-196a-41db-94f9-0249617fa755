package com.volvo.maintain.application.maintainlead.dto.vhc;

import lombok.Data;

import java.io.Serializable;

/**
 * vhc报价-代客户反向确认维修项
 */
@Data
public class VhcConfirmRepairItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 经销商
     */
    private Integer id;
    /**
     * 确认状态confirm_state 未推送:89707001 待反馈:89707002 确认维修:89707003 暂不维修:89707004
     */
    private String confirmState;


}
