package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("标签日志信息")
public class TagMetadataLogDto {

    // 标签id
    private String tagId;
    // 改变前log
    private String beforeLog;

    // 改变后log
    private String afterLog;

    // 备注
    private String remarks;

}
