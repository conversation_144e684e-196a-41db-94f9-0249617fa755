package com.volvo.maintain.application.maintainlead.dto.carebuy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 张善龙
 * 2024.1.11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("服务合同套餐代码查询出参Vo")
public class CareMealDto {

    @ApiModelProperty(value = "购买ID",name = "careBuyedId")
    private Integer careBuyedId;

    @ApiModelProperty(value = "权益包含的套餐code",name = "mealCode")
    private String mealCode;

    @ApiModelProperty(value = "套餐次序",name = "mealOrder")
    private String mealOrder;

    /**
     * 自定义接收字段
     */
    @ApiModelProperty(value = "套餐名称",name = "setName")
    private String setName;

    @Override
    public String toString() {
        return "CareMealDTO{" +
                "careBuyedId=" + careBuyedId +
                ", mealCode='" + mealCode + '\'' +
                ", mealOrder='" + mealOrder + '\'' +
                ", setName='" + setName + '\'' +
                '}';
    }
}
