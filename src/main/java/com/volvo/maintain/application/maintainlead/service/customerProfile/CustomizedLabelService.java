package com.volvo.maintain.application.maintainlead.service.customerProfile;


import com.volvo.maintain.interfaces.vo.CompleteTagInfoVo;
import com.volvo.maintain.application.maintainlead.dto.CustomizedLabelDto;
import com.volvo.maintain.interfaces.vo.NbTagInfoVo;

import java.util.List;

public interface CustomizedLabelService {

    List<NbTagInfoVo> queryCustomizedLabel(CustomizedLabelDto vehicleEntranceVO);

    /**
     * 清除预览配置
     * @return
     */
    Integer clearPreviewConfiguration();

    Integer clearConfiguration(String tagId);

    List<CompleteTagInfoVo> previewTempData();

    Integer previewOrSaveSynchronizeData();

}
