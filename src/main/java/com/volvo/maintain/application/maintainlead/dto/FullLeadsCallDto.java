package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 功能描述：全量线索AI通话保存参数对象
 *
 * <AUTHOR>
 * @since 2023/12/26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FullLeadsCallDto", description = "全量线索AI通话保存参数对象")
public class FullLeadsCallDto {

    @ApiModelProperty(value = "线索ID")
    private Long id;

    @ApiModelProperty(value = "线索类型")
    private Integer inviteType;

    @ApiModelProperty(value = "批次号")
    private String batchNo;

    @ApiModelProperty(value = "跟进人id")
    private String saId;

    @ApiModelProperty(value = "客户名称")
    private String cusName;

    @ApiModelProperty(value = "客户电话")
    private String cusNumber;

    @ApiModelProperty(value = "工作号")
    private String workNum;

    @ApiModelProperty(value = "call id")
    private String callId;
}
