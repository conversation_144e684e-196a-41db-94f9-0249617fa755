package com.volvo.maintain.application.maintainlead.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 商户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "PriceVO 对象", description = "PriceVO")
public class PriceVo {

    @ApiModelProperty(value = "预估距离 单位公里")
    private BigDecimal distance;
    @ApiModelProperty(value = "预估价格 单位元")
    private BigDecimal totalFee;
    @ApiModelProperty(value = "起步费用 单位元")
    private BigDecimal startFee;
    @ApiModelProperty(value = "超出费用 单位元")
    private BigDecimal overFee;
    @ApiModelProperty("供应商")
    private String type;

}