package com.volvo.maintain.application.maintainlead.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * dacon
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "FindMerchantPriceDTO 对象", description = "FindMerchantPriceDTO")
public class FindMerchantPriceDTO {
    
    @ApiModelProperty(value = "经销商")
    private String ownerCode;
    
    @ApiModelProperty(value = "订单类型")
    private String type;

}