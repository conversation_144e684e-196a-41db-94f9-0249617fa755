package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/11
 */
@Data
@ApiModel("标签日志")
public class TagLogDto {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 场景类型:1.环检接车，2.线索保存
     */
    @ApiModelProperty("场景类型:1.环检接车，2.线索保存")
    private Integer sinceType;

    /**
     * 业务单号
     */
    @ApiModelProperty("业务单号")
    private String bizNo;

    /**
     * 标签数据
     */
    @ApiModelProperty("标签数据")
    private String tagData;

    /**
     * 子业务单号
     */
    @ApiModelProperty("子业务单号")
    private String subBizNo;
}
