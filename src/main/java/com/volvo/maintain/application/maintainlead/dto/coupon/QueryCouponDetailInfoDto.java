package com.volvo.maintain.application.maintainlead.dto.coupon;

import com.volvo.maintain.interfaces.vo.VinOrOneIdVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 卡券列表
 * 张善龙
 * 2024.1.11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("卡券查询")
public class QueryCouponDetailInfoDto {
    @ApiModelProperty(value = "是否有效期内的(1:是 0:否)")
    private Integer valid;

    @ApiModelProperty(value = "工单号")
    private String roNo;

    @ApiModelProperty(value = "激活时间（生效日期）",name = "activateDate")
    private String activateDate;

    @ApiModelProperty(value = "激活时间（生效日期）",name = "activateDate")
    private Integer useScenes;

    @ApiModelProperty(value = "激活状态",name = "activeState")
    private Integer activeState;

    @ApiModelProperty(value = "活动编号",name = "activityCode")
    private String activityCode;

    @ApiModelProperty(value = "活动名称",name = "activityName")
    private String activityName;

    @ApiModelProperty(value = "卡券id",name = "couponId")
    private Integer couponId;

    @ApiModelProperty(value = "卡券id",name = "couponIds")
    private List<Long> couponIds;

    @ApiModelProperty(value = "卡券来源",name = "couponSource")
    private Integer couponSource;

    @ApiModelProperty(value = "兑换码 核销码",name = "exchangeCode")
    private String exchangeCode;

    @ApiModelProperty(value = "卡券失效日期",name = "expirationDate")
    private String expirationDate;

    @ApiModelProperty(value = "领取日期",name = "getDate")
    private String getDate;

    @ApiModelProperty(value = "id",name = "id")
    private Integer id;

    @ApiModelProperty(value = "是否订单退券",name = "isRefunded")
    private Integer isRefunded;

    @ApiModelProperty(value = "发券人id",name = "issuerId")
    private Integer issuerId;

    @ApiModelProperty(value = "最后使用时间",name = "lastUseDate")
    private String lastUseDate;

    @ApiModelProperty(value = "余额",name = "leftValue")
    private Integer leftValue;

    @ApiModelProperty(value = "锁定单据号",name = "lockOrderNo")
    private String lockOrderNo;

    @ApiModelProperty(value = "one_id",name = "oneId")
    private Integer oneId;

    @ApiModelProperty(value = "会员Id",name = "oneId")
    private Integer memberId;

    @ApiModelProperty(value = "单据类型",name = "orderType")
    private Integer orderType;

    @ApiModelProperty(value = "二维码",name = "qrCodeUrl")
    private String qrCodeUrl;

    @ApiModelProperty(value = "来源编号",name = "sourceNumber")
    private Integer sourceNumber;

    @ApiModelProperty(value = "卡券状态",name = "ticketState")
    private Integer ticketState;

    @ApiModelProperty(value = "vin",name = "vin")
    private String vin;

    @ApiModelProperty(value = "vin",name = "vins")
    private String[] vins;

    @ApiModelProperty(value = "id",name = "ids")
    private Integer[] ids;

    @ApiModelProperty(value = "vinOrOneId",name = "vinOrOneId")
    private VinOrOneIdVo vinOrOneId;

}
