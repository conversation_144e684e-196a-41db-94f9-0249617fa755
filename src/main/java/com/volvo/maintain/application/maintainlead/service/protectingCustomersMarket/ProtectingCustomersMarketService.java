package com.volvo.maintain.application.maintainlead.service.protectingCustomersMarket;


import com.volvo.maintain.application.maintainlead.dto.OrderTagSnapshotDto;
import com.volvo.maintain.application.maintainlead.dto.customermarketing.OwnerInfoDTO;
import com.volvo.maintain.interfaces.vo.PurchaseIntentionVo;

import java.util.List;

public interface ProtectingCustomersMarketService {

    /**
     *查询是否存在换购意向
     * @param vin 车架号
     * @param delivererMobile 送修人手机号
     * @param roNo 工单号
     */
    List<PurchaseIntentionVo> queryPurchaseIntention(String vin, String delivererMobile, String roNo);

    List<OrderTagSnapshotDto> queryOrderTagSnapshotList(String mobile, String vin);

    OwnerInfoDTO queryOwnerInfo(String vin);
}
