package com.volvo.maintain.application.maintainlead.dto.vhc;

import lombok.Data;

import java.util.List;

/**
 * 车辆健康检查dto
 */
@Data
public class VhcInfoDTO {

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 经销商
     */
    private String ownerCode;

    /**
     * 车架号
     */
    private String vin;


    /**
     * 车辆健康检查编号
     */
    private String vhcNo;


    /**
     * 车牌号
     */
    private String license;


    /**
     * 检查状态
     */
    private String vhcState;

    /**
     * 报价是否推送用户
     */
    private String pushUser;

    /**
     * 检查人
     */
    private Integer vhcPeople;

    /**
     * 车辆类型{电车：84701001 油车：84701002 }
     */
    private String vhcType;
    /**
     * 派工技师id
     */
    private String dispatchTechnician;
    /**
     * 其他大类id
     */
    private List<Integer> otherClassIdList;

}
