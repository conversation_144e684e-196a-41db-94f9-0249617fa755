package com.volvo.maintain.application.maintainlead.dto.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("发送pushDTO")
public class SendPushNoticeDto {
    @ApiModelProperty(value = "appId")
    private String appId;

    @ApiModelProperty(value = "推送目标列表 oneid为O开头;memberid为M开头;employee为E开头 (NB小铃销消息)")
    private List<String> targetCodes;

    @ApiModelProperty(value = "模板code")
    private String templateCode;

    @ApiModelProperty(value = "模板参数")
    private Map<String, Object> templateParams;

    @ApiModelProperty(value = "额外参数")
    private Map<String, Object> pushExtParams;
}
