package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.*;

import javax.validation.constraints.NotNull;


/**
 * 功能描述：标签信息查询入参
 *
 * <AUTHOR>
 * @date 2023/11/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("标签信息查询入参")
public class TagInfoDto {
    // 标签名称
    private String tagName;
    // 标签id
    private String tagId;
    // 标签来源
    private Integer[] tagSource;
    /**
     * 展示一级板块
     */
    private Integer[]  showFirstBlock;

    /**
     * 展示二级板块
     */
    private Integer[]  showSecondBlock;

    /**
     * 展示优先级
     */
    private Integer[] showLevel;
    /**
     * 是否标签
     */
    private Integer[] isTag;
    @NotNull(message = "当前页不能为空")
    private int currentPage;
    @NotNull(message = "每页条数不能为空")
    private int pageSize;

    /**
     * 标签类型
     */
    private String tagType;
}
