package com.volvo.maintain.application.maintainlead.dto.icup;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigInteger;


/**
 * 湖仓返回icup里程数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DataLakeWarehouseIcupMileageDto {


    //车架号
    private String veh_vin;

    //里程
    private int mileage;

    //更新时间
    private BigInteger send_time;



}
