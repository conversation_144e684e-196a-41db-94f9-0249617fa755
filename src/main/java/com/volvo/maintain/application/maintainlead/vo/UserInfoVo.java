package com.volvo.maintain.application.maintainlead.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 功能描述：用户信息返回对象
 *
 * <AUTHOR>
 * @since 2024/03/04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "UserInfoVo", description = "用户信息返回对象")
public class UserInfoVo {

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "用户名")
    private String employeeName;

    @ApiModelProperty(value = "手机号")
    private String phone;
}
