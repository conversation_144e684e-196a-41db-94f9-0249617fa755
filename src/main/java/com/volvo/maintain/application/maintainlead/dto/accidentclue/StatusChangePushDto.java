package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Date 2023/11/16 15:21
 * @Version 1.0
 */
@ApiModel("线索状态推送LiteCRM参数信息")
public class StatusChangePushDto {

    @ApiModelProperty(value = "crmId")
    private String id;

    @ApiModelProperty(value = "线索id")
    private String sourceClueId;

    @ApiModelProperty(value = "工单状态")
    private String bizStatus;

    @ApiModelProperty(value = "线索状态")
    private String followUpStatus;

    @ApiModelProperty(value = "线索类型")
    private String leadsType;

    @ApiModelProperty(value = "经销商代码")
    private String dealerCode;
    @ApiModelProperty(value = "更改后的经销商")
    private String currentDealerCode;
    @ApiModelProperty(value = "之前的经销商")
    private String originalDealerCode;

    public StatusChangePushDto(String sourceClueId, String bizStatus, String followUpStatus, String dealerCode) {
        this.sourceClueId = sourceClueId;
        this.bizStatus = bizStatus;
        this.followUpStatus = followUpStatus;
        this.leadsType = "103";
        this.dealerCode = dealerCode;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getSourceClueId() {
        return sourceClueId;
    }

    public void setSourceClueId(String sourceClueId) {
        this.sourceClueId = sourceClueId;
    }

    public String getBizStatus() {
        return bizStatus;
    }

    public void setBizStatus(String bizStatus) {
        this.bizStatus = bizStatus;
    }

    public String getFollowUpStatus() {
        return followUpStatus;
    }

    public void setFollowUpStatus(String followUpStatus) {
        this.followUpStatus = followUpStatus;
    }

    public String getLeadsType() {
        return leadsType;
    }

    public void setLeadsType(String leadsType) {
        this.leadsType = leadsType;
    }

    public String getDealerCode() {
        return dealerCode;
    }

    public void setDealerCode(String dealerCode) {
        this.dealerCode = dealerCode;
    }

    public String getCurrentDealerCode() {
        return currentDealerCode;
    }

    public void setCurrentDealerCode(String currentDealerCode) {
        this.currentDealerCode = currentDealerCode;
    }

    public String getOriginalDealerCode() {
        return originalDealerCode;
    }

    public void setOriginalDealerCode(String originalDealerCode) {
        this.originalDealerCode = originalDealerCode;
    }

    public StatusChangePushDto() {
    }
    @Override
    public String toString() {
        return "StatusChangePushDTO{" +
                "sourceClueId='" + sourceClueId + '\'' +
                ", bizStatus='" + bizStatus + '\'' +
                ", followUpStatus='" + followUpStatus + '\'' +
                ", leadsType='" + leadsType + '\'' +
                ", dealerCode='" + dealerCode + '\'' +
                ", currentDealerCode='" + currentDealerCode + '\'' +
                ", originalDealerCode='" + originalDealerCode + '\'' +
                '}';
    }
}
