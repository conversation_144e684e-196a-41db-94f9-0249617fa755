package com.volvo.maintain.application.maintainlead.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.annotation.DistributedLock;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.*;
import com.volvo.maintain.application.maintainlead.dto.bookingOrder.BookingOrderDto;
import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueDto;
import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueResDto;
import com.volvo.maintain.application.maintainlead.dto.common.CommonConfigDTO;
import com.volvo.maintain.application.maintainlead.dto.message.MessageSendDto;
import com.volvo.maintain.application.maintainlead.emums.ClueStrategyEnum;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import com.volvo.maintain.application.maintainlead.service.faultLight.FaultLightService;
import com.volvo.maintain.application.maintainlead.service.strategy.CluesStrategyChoose;
import com.volvo.maintain.application.maintainlead.vo.*;
import com.volvo.maintain.infrastructure.constants.AccidentCluesConstant;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.constants.RedisConstants;
import com.volvo.maintain.infrastructure.enums.DataTypeEnum;
import com.volvo.maintain.infrastructure.enums.RepairTypeEum;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.util.ObjectUtil;
import com.volvo.maintain.interfaces.vo.MidVehicleVo;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;
import com.volvo.maintain.interfaces.vo.carebuy.CareBuyedVo;
import com.volvo.maintain.interfaces.vo.white.VehicleHealthCheckWhiteListVo;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.framework.util.bean.ApplicationContextHelper;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.volvo.maintain.infrastructure.constants.AccidentCluesConstant.*;
import static com.volvo.maintain.infrastructure.constants.CommonConstant.*;



@Service
@Slf4j
public class AccidentCluesServiceImpl implements AccidentCluesService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String OEM="vcdc";

    @Autowired
    private FaultLightService faultLightService;

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

	@Autowired
	private DomainMaintainOrdersFeign domainMaintainOrdersFeign;

    @Autowired
    private MidEndVehicleCenterFeign midEndVehicleCenterFeign;

    @Autowired
    private MidEndOrgCenterFeign midEndOrgCenterFeign;

    @Autowired
    private DomainMaintainLeadFeign domainMaintainLeadFeign;

    @Autowired
    private MidEndCustomerCenterClient midEndCustomerCenterClient;

    @Resource
    private DomainMaintainOrdersFeign maintainOrdersFeign;

    @Resource
    private DomainInsuranceLeadsFeign insuranceLeadsFeign;

    @Autowired
	private MidEndAuthCenterFeign midEndAuthCenterFeign;

    @Autowired
    CommonMethodService commonMethodService;
    @Autowired
    private ApplicationAftersalesManagementFeign applicationAftersalesManagementFeign;

    @Resource
    private AccidentCluesOwnerRuleService accidentCluesOwnerRuleService;

	@Resource
	private DmscusCustomerFeign dmscusCustomerFeign;

	@Autowired
	private DomainMaintainAuthFeign domainMaintainAuthFeign;

    @Autowired
    private CluesStrategyChoose cluesStrategyChoose;

    @Value("${baseUrl.leadsReceiveUrl}")
    private String virtualPhoneUrl;

    @Autowired
    private DmscusIfserviceFeign dmscusIfserviceFeign;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 通过保单号和vin 获取续保相关信息
     *
     * @param insuranceNo 保单号
     * @param vin vin
     * @return 续保信息
     */
    @Override
    public InsuranceDealerDto getInsuranceDealer(String insuranceNo, String vin) {
        log.info("get insurance dealer : insuranceNo = {}, vin = {}", insuranceNo, vin);

        //上次进场经销商
        DmsResponse<RecentRepairOrderDto> recentMaintainOrderRes = maintainOrdersFeign.getRecentMaintainOrder(vin);
        log.info("recent maintain order res:{}", recentMaintainOrderRes);
        if (Objects.isNull(recentMaintainOrderRes) || !recentMaintainOrderRes.isSuccess()) {
            throw new ServiceBizException("查询上次进场经销商失败");
        }

        //续保经销商
        DmsResponse<List<InsuranceBillDto>> insuranceBillRes = insuranceLeadsFeign.getInsuranceBill(insuranceNo, vin);
        log.info("insurance bill res:{}", insuranceBillRes);
        if (Objects.isNull(insuranceBillRes) || !insuranceBillRes.isSuccess()) {
            throw new ServiceBizException("查询续保经销商失败");
        }

        InsuranceDealerDto insuranceDealerDto = new InsuranceDealerDto().setInsuranceBill(insuranceBillRes.getData());
        if (Objects.nonNull(recentMaintainOrderRes.getData())) {
            insuranceDealerDto.setRepairOrder(recentMaintainOrderRes.getData());
        }
        //检查迁移经销商,如果有则替换
        checkMigrateDealerCode(insuranceDealerDto);
        return insuranceDealerDto;
    }

    private void checkMigrateDealerCode(InsuranceDealerDto insuranceDealerDto){
        if(Objects.isNull(insuranceDealerDto)){
            log.info("checkMigrateDealerCode,insuranceDealerDto is null");
            return;
        }
        //查询迁移关系
        List<DealerMigrationRecordPo> allData = leadsTransferAll();
        if(CollectionUtils.isEmpty(allData)){
            log.info("checkMigrateDealerCode,allData is null");
            return;
        }
        // 创建 originalDealerCode(原经销商) 到 targetDealerCode(目标经销商) 的映射
        Map<String, String> dealerCodeMap = allData.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        DealerMigrationRecordPo::getOriginalDealerCode,
                        DealerMigrationRecordPo::getTargetDealerCode,
                        (k1, k2) -> k2
                ));
        log.info("checkMigrateDealerCode,dealerCodeMap:{}", dealerCodeMap);
        //开始检查替换
        List<InsuranceBillDto> insuranceBill = insuranceDealerDto.getInsuranceBill();
        if(CollectionUtils.isNotEmpty(insuranceBill)){
            // 替换 insuranceBill 中的 dealerCode
            insuranceBill.forEach(bill -> {
                String dealerCode = bill.getDealerCode();
                String targetDealerCode = dealerCodeMap.get(dealerCode);
                if (targetDealerCode != null) {
                    log.info("dealerCode:{},targetDealerCode:{}", dealerCode, targetDealerCode);
                    bill.setDealerCode(targetDealerCode);
                }
            });
            log.info("checkMigrateDealerCode,insuranceBill,end");
        }
        RecentRepairOrderDto repairOrder = insuranceDealerDto.getRepairOrder();
        if(Objects.nonNull(repairOrder)){
            String ownerCode = repairOrder.getOwnerCode();
            String targetDealerCode = dealerCodeMap.get(ownerCode);
            log.info("ownerCode:{},targetDealerCode:{}", ownerCode, targetDealerCode);
            if(targetDealerCode != null){
                repairOrder.setOwnerCode(targetDealerCode);
            }
            log.info("checkMigrateDealerCode,repairOrder,end");
        }
    }

    private List<DealerMigrationRecordPo> leadsTransferAll(){
        List<DealerMigrationRecordPo> allData = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 500;
        Page<DealerMigrationRecordPo> page;
        do {
            log.info("checkMigrateDealerCode,pageNum:{}", pageNum);
            page = leadsTransfer(pageNum, pageSize);
            if(page == null){
                return allData;
            }
            allData.addAll(page.getRecords());
            pageNum++;
        } while (page.getCurrent() < page.getPages());
        return allData;
    }

    private Page<DealerMigrationRecordPo> leadsTransfer(int currentPage, int pageSize) {
        DmsResponse<Page<DealerMigrationRecordPo>> dmsResponse = dmscusCustomerFeign.leadsTransfer(currentPage, pageSize, null);
        if(dmsResponse == null){
            log.info("leadsTransfer,dmsResponse is null");
            return null;
        }
        if(dmsResponse.getData() != null){
            return dmsResponse.getData();
        }
        return null;
    }

    @Override
    @DistributedLock(prefix = "crmToNewbieClueDistribute:lock", key = {"#dto.id"}, delimiter = "-")
    public LiteCrmClueResultDTO crmToNewbieClueDistribute(LeadOperationResultDto dto) {
        logger.info("crmToNewbieClueDistribute start:{}", JSON.toJSONString(dto));
        ClueStrategyEnum strategyEnum = ClueStrategyEnum.getStrategy(dto.getLeadsType());
        if(Objects.isNull(strategyEnum)){
            logger.info("crmToNewbieClueDistribute leadsType is not support");
            throw new ServiceBizException("不支持的线索类型!");
        }
        return cluesStrategyChoose.chooseAndExecuteResp(strategyEnum.name(), dto);
    }

    @Override
    public boolean clueDistributePostProcess(LeadOperationResultDto dto) {
        DistributeClueDto distributeClueDto = dto.getDistributeClueDto();
        try{
            // 发送短信
            if (dto.getDataStatus() == NUM_1) {
                logger.info("crmToNewbieClueDistribute dealer isBlank");
                cluesMessageReminder(dto);
                // 请注意这里不能return，需要继续走更新逻辑
            }
        }catch (Exception e){
            logger.info("dealer is null,send message failed :{}",e);
        }
        DistributeClueResDto distributeClueResDto = dto.getDistributeClueResDto();
        DmsResponse<String> dmsResponse = new DmsResponse<>();
        dmsResponse.setData(distributeClueResDto.getData().toString());
        // 后续更新操作
        return processPostLeadActivities(dto,distributeClueDto.getDealerCode(),dmsResponse);
    }

	private boolean isWhite(String dealer) {
		log.info("crmToNewbieClueDistribute isWhite dealer:{}", dealer);
		DmsResponse<Object> response = domainMaintainAuthFeign.checkWhitelist(dealer, CommonConstant.WECOM_ACCIDENT_MODE_TYPE, CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE, "");
		log.info("crmToNewbieClueDistribute isWhite response:{}",response);
		if (Objects.isNull(response) || response.isFail()){
			log.info("crmToNewbieClueDistribute isWhite error");
			return false;
		}
		Object data = response.getData();
		if (null == data) {
			log.info("crmToNewbieClueDistribute isWhite data isnull");
			return false;
		}
		try {
			return Boolean.parseBoolean(data.toString());
		} catch (Exception e) {
			log.info("crmToNewbieClueDistribute isWhite e:{}", e);
			return false;
		}
	}

    /**
     * 其他信息获取更新线索
     * @param dto 更新线索信息对象
     * @param dealer 入参经销商
     * @param domainResponse 入库线索返回的信息包括id
     * @return true/false
     */
    private Boolean processPostLeadActivities(LeadOperationResultDto dto, String dealer, DmsResponse<String> domainResponse) {
//        modificationLogRecord(id, domainResponse);
        // 查询组织中心获取经销商代码列表 获取经销商大区小区信息
        CompanyDetailByCodeDto companyDetailByCodeDto = queryCompanyDetailByCodeDto(dealer);
        // 查询车辆中心 获取modelYear，modelCode 车辆信息
        TmVehicleDto vehicleDto = queryVehicleDetail(dto);
        // 根据vin 手机号姓名
        PadVehiclePreviewResultVo vehicleInfo = queryDmsVehicleInfo(dto, dealer);
        if(null != vehicleInfo){
            if(Objects.isNull(vehicleInfo.getOwnerName()) && Objects.isNull(vehicleInfo.getMobile())
                    || ("null".equals(vehicleInfo.getOwnerName()) && "null".equals(vehicleInfo.getMobile()))
            ){
                // 去中台查询车主和手机号
                MidVehicleVo midVehicleVo = this.getMidVehicleVo(dto.getVehicleVin());
                log.info("processPostLeadActivities:{}", midVehicleVo);
                if(Objects.nonNull(midVehicleVo)){
                    vehicleInfo.setOwnerName(StringUtils.isNotEmpty(midVehicleVo.getName()) ? midVehicleVo.getName() : null);
                    String mobile = null;
                    if (ObjectUtils.isNotEmpty(midVehicleVo.getMobile())){
                        mobile = midVehicleVo.getMobile();
                    } else if (ObjectUtils.isNotEmpty(midVehicleVo.getContactorMobile())) {
                        mobile = midVehicleVo.getContactorMobile();
                    } else if (ObjectUtils.isNotEmpty(midVehicleVo.getContactorPhone())) {
                        mobile = midVehicleVo.getContactorPhone();
                    }
                    vehicleInfo.setMobile(mobile);
                }
            }
        }
        // 获取oneId
        Long oneId = queryOneIdByNameAndMobile(dto);
        // 获取人员
        OwnerRuleDto ownerRuleDto = selectAllocatedInfo(dto, dealer);
        CompanyDetailDto companyDetailDto = buildUpdateParams(vehicleDto, companyDetailByCodeDto, oneId, vehicleInfo, ownerRuleDto);
        dto.setCompanyDetail(companyDetailDto);
        if (domainResponse.getData().length() != CommonConstant.PROCESSING_FAILED && !domainResponse.getData().contains(",")) {
            return Boolean.FALSE;
        }
        updateCluesDetails(dto, domainResponse);
        // 反向关联工单
        ClueDataDto.AccidentInfo accidentInfo = dto.getData().getAccidentInfo();
        this.associationOrder(dto.getVehicleVin(),accidentInfo.getAccidentDate(), dto.getDataStatus());
        // 下发线索给 即将分配人发送 消息 （sms 企微）
        boolean flag =  false;
        // 查询配置信息
        CommonConfigDTO configInfo = this.getConfigInfo(ACCIDENT_CLUES_PICC, ACCIDENT_CLUES_GROUPTYPE);
        // 事故线索信息
        if(Objects.nonNull(configInfo)
                && configInfo.getConfigValue().equals(SUCCESS_CODE)
                && Objects.nonNull(accidentInfo)
                && StringUtils.isNotEmpty(accidentInfo.getInsuranceCompanyCode())
                && accidentInfo.getInsuranceCompanyCode().equals(ACCIDENT_CLUES_PICC)){
            flag = true;
        }
        cluesRemindGiveAssignor(dto, ownerRuleDto, flag);
        AccidentCluesServiceImpl proxy = ApplicationContextHelper.getBeanByType(AccidentCluesServiceImpl.class);
        proxy.cluesRemindGiveDealerManager(dto, dealer, flag);
        return Boolean.TRUE;
    }
    /**
     * 获取配置信息
     */
    private CommonConfigDTO getConfigInfo(String  key,String groupType) {
        try {
            logger.info("application.getConfigInfo:{},{}", key, groupType);
            RBucket<CommonConfigDTO> bucket = redissonClient.getBucket(RedisConstants.ACCIDENT_CLUES_REDIS_KEY + key + ":" + groupType);
            if (bucket.isExists()) {
                logger.info("application.redis.getConfigInfo:{},{}={}",key,groupType, JSON.toJSONString(bucket.get()));
                CommonConfigDTO configInfo = bucket.get();
                return configInfo;
            }else {
                DmsResponse<CommonConfigDTO> configByKey = domainMaintainAuthFeign.getConfigByKey(key, groupType);
                logger.info("application.getConfigInfo.configByKey:{},{}={}", key, groupType, JSON.toJSONString(configByKey));
                if(Objects.nonNull(configByKey) && Objects.nonNull(configByKey.getData())){
                    bucket.set(configByKey.getData(), 5, TimeUnit.MINUTES);
                    return configByKey.getData();
                }
                return null;
            }
        }catch (Exception e){
            logger.error("application.getConfigInfo.error:{},{}={}",key, groupType, e);
            return null;
        }
    }
    /**
     * 事故线索反向关联工单
     * vin 车架号
     * accidentDate 出险时间
     */
    private void associationOrder(String vin, String accidentDate, Integer dataStatus){
        try {
            log.info("associationOrder:{},{}", vin, accidentDate);
            if(dataStatus == 1 || StringUtils.isEmpty(vin)){
                log.info("associationOrder params is null");
                return;
            }
            // 查询可用工单
            DmsResponse<RepairOrderDto> orderByVin = domainMaintainOrdersFeign.getOrderByVin(vin, accidentDate);
            log.info("associationOrder orderByVin:{}", JSON.toJSONString(orderByVin));
            if(Objects.isNull(orderByVin) || orderByVin.isFail() || Objects.isNull(orderByVin.getData())){
                log.info("associationOrder order is null:{}", JSON.toJSONString(orderByVin));
                return;
            }
            RepairOrderDto orderDto = orderByVin.getData();
            AccidentCluesOrderVo accidentCluesVo=new AccidentCluesOrderVo();
            accidentCluesVo.setIntoDealerCode(orderDto.getOwnerCode());
            accidentCluesVo.setIntoDealerDate(orderDto.getRoCreateDate());
            accidentCluesVo.setIsDelete(DICT_IS_NO);
            //accidentCluesVo.setIsReverse(Integer.parseInt(CommonConstants.DATA_IS_VALID_NO));
            accidentCluesVo.setLicense(orderDto.getLicense());
            accidentCluesVo.setOriginalRepairTypeCode(orderDto.getRepairTypeCode());
            accidentCluesVo.setRepairTypeCode(orderDto.getRepairTypeCode());
            accidentCluesVo.setRoNo(orderDto.getRoNo());
            accidentCluesVo.setRoStatus(Integer.parseInt(orderDto.getRoStatus().toString()));
            accidentCluesVo.setVin(vin);
            // 查询配置信息
            CommonConfigDTO configInfo = this.getConfigInfo(ORDER_FOLLOW_STATUS_MAX_DAY, ACCIDENT_CLUES_GROUPTYPE);
            if(Objects.isNull(configInfo)){
                accidentCluesVo.setMaxDay("90");
            }else{
                accidentCluesVo.setMaxDay(configInfo.getConfigValue());
            }
            // 调用
            dmscusIfserviceFeign.associationOrder(accidentCluesVo);
            log.info("associationOrder end");
        }catch (Exception e){
            log.info("反向关联工单失败:{}", e);
        }

    }
    @Async
    public void cluesRemindGiveDealerManager(LeadOperationResultDto dto, String dealer, boolean flag) {
        if(dto.getDataStatus() == NUM_1 || flag){
            log.info("cluesRemindGiveDealerManager异常线索:{}", dto.getId());
            return;
        }
        List<OwnerRuleDto> managerList = accidentCluesOwnerRuleService.selectOwnerRuleNew(dealer, 2);
        if (CollectionUtils.isEmpty(managerList)) {
            return;
        }
        try {
            for (OwnerRuleDto manager : managerList) {
                if (StringUtils.isNotBlank(manager.getUserMobile())) {
                    ClueDataDto.ContactInfo contactInfo = getContactInfoWithHighestPriority(dto);
                    TmVehicleDto vehicleData = queryVehicleInfo(dto);
                    MessageSendDto messageSendDto = new MessageSendDto();
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("leadsReceiveTime", checkNullOrEmpty(dto.getLeadsReceiveTime()));
                    paramMap.put("plateNumber", checkNullOrEmpty(vehicleData.getPlateNumber()));
                    paramMap.put("modelName", checkNullOrEmpty(vehicleData.getModelName()));
                    paramMap.put("customerName", ObjectUtils.isEmpty(contactInfo) ? "null" : checkNullOrEmpty(contactInfo.getCustomerName()));
                    paramMap.put("accidentAddress", ObjectUtils.isEmpty(dto.getData().getAccidentInfo()) ? "null" : checkNullOrEmpty(dto.getData().getAccidentInfo().getAccidentAddress()));
                    messageSendDto.setParamMap(paramMap);
                    messageSendDto.setMobiles(manager.getUserMobile());
                    messageSendDto.setTemplateId(AccidentCluesConstant.MESSAGE_ASSIGNOR_SMS);
                    PushMessageRecordVo pushMessageRecord = buildPushRecord(dto, messageSendDto);
                    // send messages to sms
                    log.info("cluesMessageReminderToSMSByCluesRemindGiveDealerManagerRequestParamsMessageSendDto: {},PushMessageRecordVo:{}", JSONObject.toJSONString(messageSendDto), JSONObject.toJSONString(pushMessageRecord));
                    commonMethodService.pushSms(AccidentCluesConstant.ACCIDENT_CLUES_FACTORY_TO_STORE, pushMessageRecord, messageSendDto);
                }
                if (null != manager.getUserId()) {
                    PushMessageRecordDto pushRecordDto = createPushRecord(dto, AccidentCluesConstant.MESSAGE_ASSIGNOR);
                    pushRecordDto.setUserId(String.valueOf(manager.getUserId()));
                    pushRecordDto.setSubBizNo(String.format("%s/%s", manager.getUserId(), dto.getId()));
                    // send messages to weCom
                    log.info("cluesMessageReminderToWeComByCluesRemindGiveDealerManagerRequestParams: {}", JSONObject.toJSONString(pushRecordDto));
                    applicationAftersalesManagementFeign.pushMessage(pushRecordDto);
                }
            }
        } catch (Exception e) {
            logger.info("Assignor is null,send message failed :{}", e);
        }
    }

    /**
     * 下发线索 给 即将分配人。
     *
     * @param dto        the lead operation result DTO
     */
    @Async
    public void cluesRemindGiveAssignor(LeadOperationResultDto dto, OwnerRuleDto ownerRuleDto, boolean flag) {
        try {
            if((Objects.nonNull(dto.getDataStatus()) && dto.getDataStatus() == NUM_1) || flag){
                log.info("cluesRemindGiveAssignor异常线索:{}", dto.getId());
                return;
            }
            if (ObjectUtils.isNotEmpty(ownerRuleDto) && ObjectUtils.isNotEmpty(ownerRuleDto.getUserId())) {
                PushMessageRecordDto pushRecordDto = createPushRecord(dto, AccidentCluesConstant.MESSAGE_ASSIGNOR);
                pushRecordDto.setUserId(String.valueOf(ownerRuleDto.getUserId()));
                pushRecordDto.setSubBizNo(String.format("%s/%s", ownerRuleDto.getUserId(), dto.getId()));
                // send messages to weCom
                log.info("cluesMessageReminderToWeComBySelectAllocatedInfoRequestParams: {}", JSONObject.toJSONString(pushRecordDto));
                applicationAftersalesManagementFeign.pushMessage(pushRecordDto);
                MessageSendDto messageSendDto = buildMessageSendDto(String.valueOf(ownerRuleDto.getUserId()), dto, AccidentCluesConstant.MESSAGE_ASSIGNOR_SMS);
                PushMessageRecordVo pushMessageRecord = buildPushRecord(dto, messageSendDto);
                // send messages to sms
                log.info("cluesMessageReminderToSMSBySelectAllocatedInfoRequestParamsMessageSendDto: {},PushMessageRecordVo:{}", JSONObject.toJSONString(messageSendDto), JSONObject.toJSONString(pushMessageRecord));
                commonMethodService.pushSms(AccidentCluesConstant.ACCIDENT_CLUES_FACTORY_TO_STORE, pushMessageRecord, messageSendDto);
            }
        } catch (Exception e) {
            logger.info("Assignor is null,send message failed :{}", e);
        }

    }

    /**
     * 中台查询车主信息
     * @param vin
     * @return
     */
    private MidVehicleVo getMidVehicleVo(String vin) {
        RequestDto<CareBuyedVo> objectRequestDto = new RequestDto<>();
        CareBuyedVo careBuyedVo = new CareBuyedVo();
        careBuyedVo.setVin(vin);
        objectRequestDto.setData(careBuyedVo);
        objectRequestDto.setPage(1L);
        objectRequestDto.setPageSize(1L);
        DmsResponse<Page<MidVehicleVo>> listDmsResponse = midEndVehicleCenterFeign.listOwnerVehiclePage(objectRequestDto);

        logger.info("queryMidCustomer->listOwnerVehiclePage:{}",listDmsResponse);
        if (listDmsResponse.isFail()){
            logger.info("queryMidCustomer,listDmsResponse->Response 异常");
            return null;
        }
        Page<MidVehicleVo> data = listDmsResponse.getData();
        if (Objects.isNull(data)){
            logger.info("queryMidCustomer,data->Response 车主信息为空");
            return null;
        }
        List<MidVehicleVo> records = data.getRecords();
        if (CollectionUtils.isEmpty(records)){
            logger.info("queryMidCustomer,records->Response 车主信息为空");
            return null;
        }
        MidVehicleVo midVehicleVo = records.get(0);
        if (ObjectUtils.isEmpty(midVehicleVo)){
            logger.info("queryMidCustomer,midVehicleVo->Response 车主信息为空");
            return null;
        }

        if(ObjectUtils.isEmpty(midVehicleVo.getOneId())){
            logger.info("queryMidCustomer,midVehicleVo,OneId->Response 车主信息为空");
            return null;
        }
        return midVehicleVo;
    }
    private OwnerRuleDto selectAllocatedInfo(LeadOperationResultDto dto, String dealer) {
		logger.info("selectAllocatedInfo dealer:{}", dealer);
		if (null == dto || StringUtils.isBlank(dealer)) {
			logger.info("selectAllocatedInfo dto isNull or dealer isBlank");
			return null;
		}
		// 如果当前是一个重复线索则找到父级的分配人
		ClueDataDto data = dto.getData();
		if (null == data) {
			logger.info("selectAllocatedInfo data isNull");
			return selectAllocatedInfo(dealer);
		}
		ClueDataDto.AccidentInfo accidentInfo = data.getAccidentInfo();
		if (null == accidentInfo) {
			logger.info("selectAllocatedInfo accidentInfo isNull");
			return selectAllocatedInfo(dealer);
		}
		List<Long> repeatIdList = accidentInfo.getRepeatIdList();
		if (CollectionUtils.isEmpty(repeatIdList)) {
			logger.info("selectAllocatedInfo repeatIdList isEmpty");
			return selectAllocatedInfo(dealer);
		}
		// 查询pid
		JSONObject object = getParentCrmId(repeatIdList);
		logger.info("selectAllocatedInfo object:{}", object);
		if (null == object) {
			logger.info("selectAllocatedInfo parentCrmId isNull");
			return selectAllocatedInfo(dealer);
		}
		AccidentCluesDto cluesDto = JSONObject.toJavaObject(object, AccidentCluesDto.class);
		if (null == cluesDto || null == cluesDto.getParentCrmId() || 0 == cluesDto.getParentCrmId()) {
			logger.info("selectAllocatedInfo cluesDto isNull");
			return selectAllocatedInfo(dealer);
		}
		logger.info("selectAllocatedInfo cluesDto isNotNull");
		OwnerRuleDto ownerRuleDto = new OwnerRuleDto();
		ownerRuleDto.setOwnerCode(dealer);
		ownerRuleDto.setRuleType(AccidentCluesConstant.OWNER_RULE_TYPE_ALLOT);
		ownerRuleDto.setUserId(cluesDto.getFollowPeople());
		ownerRuleDto.setUserName(cluesDto.getFollowPeopleName());
		ownerRuleDto.setIsOnjob(CommonConstant.IS_ON_JOB_IN);
		ownerRuleDto.setIsSelect(AccidentCluesConstant.OWNER_RULE_SELECT_YES);
		logger.info("selectAllocatedInfo end ownerRuleDto:{}", ownerRuleDto);
		return ownerRuleDto;
	}

	private JSONObject getParentCrmId(List<Long> repeatIdList) {
		DmsResponse<JSONObject> response = domainMaintainLeadFeign.getParentCrmId(repeatIdList);
		if (response.isFail()) {
			logger.info("selectAllocatedInfo response isFail");
			return null;
		}
		return response.getData();
	}

	private OwnerRuleDto selectAllocatedInfo(String dealer) {
		return accidentCluesOwnerRuleService.selectAllocatedInfo(dealer);
	}

    /**
     * @param dto            更新线索信息对象
     * @param domainResponse domainResponse 入库线索返回的信息包括id
     */
    private void updateCluesDetails(LeadOperationResultDto dto, DmsResponse<String> domainResponse) {
        List<Long> idList = Arrays.stream(domainResponse.getData().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        domainMaintainLeadFeign.updateClues(dto, idList.get(0), idList.get(1));
    }

    /**
     * 查询售后-维修 vehicle 信息客户
     * @param dto 获取入参vin
     * @param dealer 经销商
     * @return 返回车辆客户信息
     */
    private PadVehiclePreviewResultVo queryDmsVehicleInfo(LeadOperationResultDto dto, String dealer) {
		if (StringUtils.isBlank(dealer)) {
			log.info("queryDmsVehicleInfo dealer isBlank dealer:{}", dealer);
			return null;
		}
        PadVehiclePreviewDto padVehiclePreviewDto = new PadVehiclePreviewDto();
        padVehiclePreviewDto.setOwnerCode(dealer);
        padVehiclePreviewDto.setVin(dto.getVehicleVin());
        DmsResponse<PadVehiclePreviewResultVo> pageDtoDmsResponse = dmscloudServiceFeign.queryOwnerVehicle(padVehiclePreviewDto);
        logger.info("queryDmsVehicleInfo responseParams:{}", JSONObject.toJSONString(pageDtoDmsResponse));
        if (pageDtoDmsResponse.isFail() || Objects.isNull(pageDtoDmsResponse.getData())) {
            return null;
        }
        return pageDtoDmsResponse.getData();
    }

    /**
     * 查询经销商是否存在黑名单
     * @param dealer 线索经销商
     * @return 是否存在
     */
    public Boolean isExistBackList(String dealer, String vin) {
        // 查询白名单 （黑名单）
        DmsResponse<Boolean> response = dmscloudServiceFeign.checkWhitelist(dealer, CommonConstant.WECOM_ACCIDENT_LEAD_TYPE, CommonConstant.WECOM_ACCIDENT_LEAD_CODE, vin);
        if (response.isFail() || Objects.isNull(response.getData())) {
            logger.info("crmToNewbieClueDistribute Service call failed");
            return Boolean.FALSE;
        }
        //存在黑名单直接返回true
        if (!Boolean.TRUE.equals(response.getData())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 通过vin 获取车辆信息
     * @param dto liteCrm传递对象
     * @return 车辆信息
     */
    private TmVehicleDto queryVehicleDetail(LeadOperationResultDto dto) {
        if (dto.getVehicleVin().length() >= 18) {
            return new TmVehicleDto();
        }
        DmsResponse<TmVehicleDto> vehicleDtoResponse = midEndVehicleCenterFeign.getVehicleByVIN(dto.getVehicleVin());
        logger.info("queryVehicleDetail vehicleDtoResponse:{}" , vehicleDtoResponse);
        if (vehicleDtoResponse.isFail() || Objects.isNull(vehicleDtoResponse.getData())) {
            return null;
        }
        return vehicleDtoResponse.getData();
    }

    /**
     * 构建更新对象，作用于liteCrm 传递缺失数据补充。
     * @param vehicleDto 查询返回的车辆信息
     * @param companyDetailByCodeDto 查询返回的公司信息
     * @param id 查询返回的oneId
     * @return 返回构建更新对象
     */
    private CompanyDetailDto buildUpdateParams(TmVehicleDto vehicleDto,
                                               CompanyDetailByCodeDto companyDetailByCodeDto, Long id,
                                               PadVehiclePreviewResultVo vehicleInfo, OwnerRuleDto ownerRuleDto) {
        //设置 modelName modelId  modelYear
        // 构建大区小区，oneId
        CompanyDetailDto companyDetailDto = new CompanyDetailDto();
        if (ObjectUtils.isNotEmpty(vehicleDto)) {
            companyDetailDto.setModelId(vehicleDto.getModelId());
            companyDetailDto.setModelName(vehicleDto.getModelName());
            companyDetailDto.setModelYear(vehicleDto.getModelYear());
        }
        if (ObjectUtils.isNotEmpty(companyDetailByCodeDto)) {
			log.info("buildUpdateParams companyDetailByCodeDto nonNull");
            companyDetailDto.setAfterBigAreaId(companyDetailByCodeDto.getAfterBigAreaId());
            companyDetailDto.setAfterBigAreaName(companyDetailByCodeDto.getAfterBigAreaName());
            companyDetailDto.setAfterSmallAreaId(companyDetailByCodeDto.getAfterSmallAreaId());
            companyDetailDto.setAfterSmallAreaName(companyDetailByCodeDto.getAfterSmallAreaName());
        }
        companyDetailDto.setOneId(id);
        if (ObjectUtils.isNotEmpty(vehicleInfo)) {
			log.info("buildUpdateParams vehicleInfo nonNull");
            companyDetailDto.setOwnerName(vehicleInfo.getOwnerName());
            companyDetailDto.setOwnerMobile(vehicleInfo.getMobile());
        }
        if (ObjectUtils.isNotEmpty(ownerRuleDto)) {
			log.info("buildUpdateParams ownerRuleDto nonNull ownerRuleDto:{}", ownerRuleDto);
            companyDetailDto.setFollowPeopleName(ownerRuleDto.getUserName());
            companyDetailDto.setFollowPeople(ownerRuleDto.getUserId());
			// 当前获取到的规则id
			companyDetailDto.setOwnerRuleId(ownerRuleDto.getId());
        }
        return companyDetailDto;
    }

    /**
     * 取优先级最小的客户信息 获取oneId
     * @param dto liteCrm 总入参对象
     * @return oneId
     */
    private Long queryOneIdByNameAndMobile(LeadOperationResultDto dto) {
        Optional<ClueDataDto.ContactInfo> contactInfo = dto.getData().getContactInfo().stream().filter(Objects::nonNull).min(Comparator.comparing(ClueDataDto.ContactInfo::getPriority,Comparator.nullsLast(String::compareTo)));
        if (!contactInfo.isPresent()) {
            return null;
        }
        // 查询客户的oneId
        VehicleOwnerDto vehicleOwnerDto = new VehicleOwnerDto();
        vehicleOwnerDto.setName(contactInfo.get().getCustomerName());
        vehicleOwnerDto.setMobile(contactInfo.get().getCustomerMobile());
        DmsResponse<List<VehicleOwnerDto>> ownerListDmsResponse = midEndCustomerCenterClient.saveList(vehicleOwnerDto);
        logger.info("queryOneIdByNameAndMobile ownerListDmsResponse:{}", JSONObject.toJSONString(ownerListDmsResponse));
        if (ownerListDmsResponse.isFail() || CollectionUtils.isEmpty(ownerListDmsResponse.getData())) {
            return null;
        }
        Optional<VehicleOwnerDto> ownerDto = ownerListDmsResponse.getData().stream().filter(Objects::nonNull).findFirst();
        return ownerDto.map(VehicleOwnerDto::getId).orElse(null);
    }

    /**
     * 获取大区小区信息
     * @param dealer 经销商
     * @return 经销商详细信息
     */
    private CompanyDetailByCodeDto queryCompanyDetailByCodeDto(String dealer) {
		if (StringUtils.isBlank(dealer)) {
			log.info("queryCompanyDetailByCodeDto dealer isBlank dealer:{}", dealer);
			return null;
		}
        IsExistByCodeDto isExistByCodeDTO = new IsExistByCodeDto();
        isExistByCodeDTO.setCompanyCode(dealer);
        ResponseDto<List<CompanyDetailByCodeDto>> listResponseDto = midEndOrgCenterFeign.selectByCompanyCode(isExistByCodeDTO);
        logger.info("queryCompanyDetailResponseParams:{}", listResponseDto);
        if (listResponseDto.isFail() || CollectionUtils.isEmpty(listResponseDto.getData())) {
            return null;
        }
        List<CompanyDetailByCodeDto> companyDetailList = listResponseDto.getData();
        Optional<CompanyDetailByCodeDto> Company = companyDetailList.stream().filter(Objects::nonNull).findFirst();
        return Company.orElse(null);
    }

    /**
     * 校验入参
     * @param dto 入参线索对象
     * @return 返回布尔
     */
    private boolean isValidDto(LeadOperationResultDto dto) {
        return Objects.nonNull(dto) && StringUtils.isNotBlank(dto.getVehicleVin()) &&
                StringUtils.isNotBlank(dto.getSourceClueId()) &&
                StringUtils.isNotBlank(dto.getLeadsType()) &&
                StringUtils.isNotBlank(dto.getLeadsReceiveTime()) &&
                Objects.nonNull(dto.getData()) && dto.getId() != null &&
                CollectionUtils.isNotEmpty(dto.getData().getContactInfo()) &&
                Objects.nonNull(dto.getData().getCarInfo());
    }

    /**
     * 处理前端传入的字符串，确保其长度不超过MySQL varchar(200)的限制。
     * 如果超过，将会被截断到200个字符
     *
     * @param input 从前端传入的字符串
     * @return 调整后的字符串
     */
    public String processUserInput(String input) {
        if (input == null) {
            return null;  // 传入为null，直接返回null
        }
        // 去除前后的空白字符
        input = input.trim();
        // 处理特殊字符和转义字符
        input = input.replaceAll("\\s{2,}", " "); // 多个空白替换为一个空白
        input = escapeSpecialCharacters(input);
        // 超过200个字符的长度限制进行截断
        if (input.length() > 200) {
            input = input.substring(0, 200);
        }
        return input;
    }

    /**
     * 对字符串中的特殊字符进行转义
     * @param input 需要转义的字符串
     * @return 转义后的字符串
     */
    private String escapeSpecialCharacters(String input) {
        // 转义逻辑   单引号转义
        String escapedInput = input.replace("'", "''");
        // 防止HTML注入的基本转义
        escapedInput = escapedInput.replaceAll("&", "&amp;");
        escapedInput = escapedInput.replaceAll("<", "&lt;");
        escapedInput = escapedInput.replaceAll(">", "&gt;");
        escapedInput = escapedInput.replaceAll("\"", "&quot;");
        return escapedInput;
    }

	@Override
	public void updateAccidentCluesTimeOutJob(AccidentCluesFollowStatusChangeTaskDto dto) {
		DmsResponse<List<AccidentCluesDto>> updateAccidentCluesTimeOut = domainMaintainLeadFeign.updateAccidentCluesTimeOut(dto);
		log.info("updateAccidentCluesTimeOutJob data:{}",JSON.toJSONString(updateAccidentCluesTimeOut));
        if (null == updateAccidentCluesTimeOut || updateAccidentCluesTimeOut.isFail()) {
			log.info("updateAccidentCluesTimeOut response isfail");
			return;
		}
		List<AccidentCluesDto> data = updateAccidentCluesTimeOut.getData();
		// 超时未跟进发送短信
        this.accidentCluesTimeOutPushSMS(data);
		// 同步跟进状态到litecrm
		pushLiteCrmClueStatus(data);
	}

    /**
     * 事故线索超时未跟进发送短信
     * @param data
     */
    @Async
    public void accidentCluesTimeOutPushSMS(List<AccidentCluesDto> data){
        if(CollectionUtils.isNotEmpty(data)) {
            for (AccidentCluesDto accidentCluesDto : data) {
                if (null == accidentCluesDto) {
                    continue;
                }
                if (null != accidentCluesDto.getFollowPeople()) {
                    log.info("updateAccidentCluesTimeOut FollowPeople isNotNull");
                    // 如果不是试点店则不发送短信
                    if (StringUtils.isNotBlank(accidentCluesDto.getDealerCode()) && isWhite(accidentCluesDto.getDealerCode())) {
                        log.info("updateAccidentCluesTimeOut DealerCode isNotNull and isWhite");
                        // 查询员工信息,并发送短信,企微
                        selectEmpInfoByUserId(accidentCluesDto);
                        // 查询服务经理信息,并发送短信,企微
                        selectFWJLByOwnerCode(accidentCluesDto);
                    }
                }
            }
        }
    }

	@Override
	public void updateAccidentCluesCloseJob(AccidentCluesFollowStatusChangeTaskDto dto) {
		log.info("updateAccidentCluesCloseJob start");
		DmsResponse<List<AccidentCluesDto>> response = domainMaintainLeadFeign.updateAccidentCluesClose(dto);
		if (null == response || response.isFail()) {
			log.info("updateAccidentCluesCloseJob response isfail");
			return;
		}

		// 同步跟进状态到litecrm
		pushLiteCrmClueStatus(response.getData());
	}

    /**
     * 事故线索集合
     */
    @Override
    public List<AccidentCluesVo> list(AccidentCluesDto query) {
        DmsResponse<List<AccidentCluesVo>> res = domainMaintainLeadFeign.accidentClueList(query);
        log.info("query accident clue list res:{}",res);
        if (res.isFail()){
            throw new ServiceBizException("查询事故线索集合失败");
        }
        return res.getData();
    }

    /**

	/**
	 * 同步状态到litecrm
	 */
	private void pushLiteCrmClueStatus(List<AccidentCluesDto> data) {
		log.info("pushLiteCrmClueStatus start");
		if (CollectionUtils.isEmpty(data)) {
			log.info("pushLiteCrmClueStatus data isEmpty");
			return;
		}
		List<StatusChangePushDto> pushInfoList = data.stream()
				.filter(Objects::nonNull)
				.map(i -> {
					StatusChangePushDto pushDto = new StatusChangePushDto();
                    pushDto.setSourceClueId(null == i.getAcId() ? "0" : String.valueOf(i.getAcId())); // acId
                    Optional.ofNullable(i.getCrmId()).ifPresent(v -> {
                        pushDto.setId(String.valueOf(v)); // crmId
                    });
                    Optional.ofNullable(i.getCluesStatus()).ifPresent(v -> {
                        pushDto.setBizStatus(String.valueOf(v)); // 跟进状态
                    });
                    Optional.ofNullable(i.getFollowStatus()).ifPresent(v -> {
                        pushDto.setFollowUpStatus(String.valueOf(v)); // 跟进状态
                    });
                    pushDto.setLeadsType(CommonConstant.LEADS_TYPE_103); // 线索类型
					return pushDto;
				}).collect(Collectors.toList());
		// 推送状态到litecrm
		dmscusCustomerFeign.acPushLiteCrmClueStatus(pushInfoList);
		log.info("pushLiteCrmClueStatus end");
	}

	/**
	 * 根据userId查询员工信息,发送短信
	 */
	private void selectEmpInfoByUserId(AccidentCluesDto accidentCluesDto) {
		log.info("queryUserInfoById start accidentCluesDto:{}", accidentCluesDto);
		ResponseDto<UserInfoVo> response = midEndAuthCenterFeign.queryUserInfoById(String.valueOf(accidentCluesDto.getFollowPeople()));
		if (null == response || response.isFail()) {
			log.info("UserInfoVo response isfail");
			return;
		}
		UserInfoVo data = response.getData();
		if (null == data || null == data.getPhone()) {
			log.info("UserInfoVo data isEmpty");
			return;
		}
		// 填充短信内容参数
		HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("leadsReceiveTime", StringUtils.isNotEmpty(accidentCluesDto.getCreatedAt()) ? accidentCluesDto.getCreatedAt() : "null");
        paramMap.put("plateNumber", StringUtils.isNotEmpty(accidentCluesDto.getLicense()) ? accidentCluesDto.getLicense() : "null");
        paramMap.put("modelName", StringUtils.isNotEmpty(accidentCluesDto.getModelName()) ? accidentCluesDto.getModelName() : "null");
        paramMap.put("customerName", StringUtils.isNotEmpty(accidentCluesDto.getContacts()) ? accidentCluesDto.getContacts() : "null");
        paramMap.put("accidentAddress", StringUtils.isNotEmpty(accidentCluesDto.getAccidentAddress()) ? accidentCluesDto.getAccidentAddress() : "null");

        // 发送短信
        StringBuilder buff = new StringBuilder();
        buff.append(AccidentCluesConstant.FOLLOW_PEOPER);
        buff.append(accidentCluesDto.getAcId());
        buff.append(data.getPhone());
        buff.append(data.getUserId());
        buff.append(accidentCluesDto.getDealerCode());
        sendSMS(paramMap, data.getPhone(), buff.toString());

        // 清理集合，防止数据过多，new了过多的hashMap
        paramMap.clear();
        // 发送企微
        pushWeCom(accidentCluesDto, String.valueOf(data.getUserId()), buff.toString());
	}
	/**
     * 发送短信
     */
    public void sendSMS(HashMap<String, String> paramMap, String phone, String subBizNo){
        try {
            MessageSendDto messageSendDto = new MessageSendDto();
            messageSendDto.setMobiles(phone); // 手机号
            messageSendDto.setTemplateId(AccidentCluesConstant.ACCIDENT_CLUES_30M_TIMEOUT_NOT_FOLLOW_TEMPLATEID); // 短信模板
            messageSendDto.setParamMap(paramMap);
            //消息通知
            PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
            pushMessageRecordVo.setSinceType(AccidentCluesConstant.PUSH_SMS);
            pushMessageRecordVo.setBizNo(AccidentCluesConstant.ACCIDENT_CLUES_30M_TIMEOUT_NOT_FOLLOW);
            pushMessageRecordVo.setSubBizNo(subBizNo);
            commonMethodService.pushSms(AccidentCluesConstant.ACCIDENT_CLUES_30M_TIMEOUT_NOT_FOLLOW, pushMessageRecordVo, messageSendDto);
            log.info("sendSMS end");
        } catch (Exception e) {
            log.error("sendSMS 异常", e);
        }
    }


    /**
	 * 根据经销商查询服务经理
	 */
	private void selectFWJLByOwnerCode(AccidentCluesDto accidentCluesDto) {
		log.info("selectFWJLByOwnerCode start ownerCode:{}", accidentCluesDto.getDealerCode());
		ResponseDto<EmpByRoleCodeDto> reqDos = new ResponseDto<>();
		EmpByRoleCodeDto empDto = new EmpByRoleCodeDto();
		// 经销商
		empDto.setCompanyCode(accidentCluesDto.getDealerCode());
		// 在职状态
		empDto.setIsOnjob(CommonConstant.IS_ON_JOB_IN);
		// 角色code
		empDto.setRoleCode(Collections.singletonList(AccidentCluesConstant.OWNER_RULE_SELECT_YSE_ROLECODE));
		reqDos.setData(empDto);
		DmsResponse<List<EmpByRoleCodeDto>> response = midEndAuthCenterFeign.queryDealerUser(reqDos);
		if (null == response || response.isFail()) {
			log.info("selectFWJLByOwnerCode response isfail");
			return;
		}
		List<EmpByRoleCodeDto> data = response.getData();
		if(CollectionUtils.isNotEmpty(data)) {
			// 填充短信内容参数
			HashMap<String, String> paramMap = new HashMap<>();
	        paramMap.put("leadsReceiveTime", StringUtils.isNotEmpty(accidentCluesDto.getCreatedAt()) ? accidentCluesDto.getCreatedAt() : "null");
	        paramMap.put("plateNumber", StringUtils.isNotEmpty(accidentCluesDto.getLicense()) ? accidentCluesDto.getLicense() : "null");
	        paramMap.put("modelName", StringUtils.isNotEmpty(accidentCluesDto.getModelName()) ? accidentCluesDto.getModelName() : "null");
	        paramMap.put("customerName", StringUtils.isNotEmpty(accidentCluesDto.getContacts()) ? accidentCluesDto.getContacts() : "null");
	        paramMap.put("accidentAddress", StringUtils.isNotEmpty(accidentCluesDto.getAccidentAddress()) ? accidentCluesDto.getAccidentAddress() : "null");


	        for (EmpByRoleCodeDto empByRoleCodeDto : data) {
	        	if(null == empByRoleCodeDto || StringUtils.isEmpty(empByRoleCodeDto.getPhone())) {
	        		continue;
	        	}
				StringBuilder buff = new StringBuilder();
				buff.append(AccidentCluesConstant.SERVICE_MANAGE);
		        buff.append(accidentCluesDto.getAcId());
		        buff.append(empByRoleCodeDto.getPhone());
		        buff.append(empByRoleCodeDto.getUserId());
		        buff.append(accidentCluesDto.getDealerCode());
				// 发送短信
				sendSMS(paramMap, empByRoleCodeDto.getPhone(), buff.toString());

				 // 发送企微
		        pushWeCom(accidentCluesDto, String.valueOf(empByRoleCodeDto.getUserId()), buff.toString());
			}
			// 清理集合，防止数据过多，new了过多的hashMap
	        paramMap.clear();
		}

	}
	/**
	 *  企微推送
	 */
	private void pushWeCom(AccidentCluesDto accidentCluesDto, String userId, String subBizNo) {
		PushMessageRecordDto pushMessageRecordDto = new PushMessageRecordDto();
		pushMessageRecordDto.setBizNo(AccidentCluesConstant.ACCIDENT_CLUES_30M_TIMEOUT_NOT_FOLLOW);
		pushMessageRecordDto.setSubBizNo(subBizNo);
		pushMessageRecordDto.setSinceType(AccidentCluesConstant.PUSH_WE_COM);
		pushMessageRecordDto.setContent(templateContent(accidentCluesDto));
		pushMessageRecordDto.setUserId(userId);
		applicationAftersalesManagementFeign.pushMessage(pushMessageRecordDto);
	}
  /**
     * 	模板内容 -厂端线索
     * @param accidentCluesDto 消息对象
     * @return 消息内容
     */
	private String templateContent(AccidentCluesDto accidentCluesDto){
        logger.info("template->dto:{}",JSONObject.toJSONString(accidentCluesDto));
        return ObjectUtil.replacePlaceholders(ObjectUtil.emailDtoToMap(accidentCluesDto), AccidentCluesConstant.PUSH_WE_COM_TEMPLATE);
    }

	@Override
	public String getVirtualNumberById(Long acId) {
		DmsResponse<Long> queryCrmIdByAcId = domainMaintainLeadFeign.queryCrmIdByAcId(acId);
        log.info("getVirtualNumber-crmId:{}", queryCrmIdByAcId);
		if (null == queryCrmIdByAcId || queryCrmIdByAcId.isFail() || null == queryCrmIdByAcId.getData()) {
			log.info("getVirtualNumberById response isfail");
			throw new ServiceBizException(CommonConstant.GET_VIRTUAL_PHONE_FAIL);
		}
        // 调用litecrm获取虚拟手机号
        String json = queryVirtualPhone(queryCrmIdByAcId.getData());
        JSONObject jsonObject = JSON.parseObject(json);
        String queryVirtualPhone = jsonObject.getJSONObject("data").getString("virtualPhone");
        // 获取虚拟手机号成功，开始进行呼叫登记
		AccidentCluesSaNumberDto build = AccidentCluesSaNumberDto.builder()
                .acId(acId)
                .virtualPhone(queryVirtualPhone)
                .loginPhone(jsonObject.getString("loginPhone"))
                .build();
		domainMaintainLeadFeign.saveAccidentCluesSaNumber(build);
		return queryVirtualPhone;
	}
    /**
     * 查询litemcrm虚拟手机号
     */
    @Retryable(value = {com.volvo.exception.ServiceBizException.class}, maxAttempts = 2, backoff = @Backoff(delay = 500))
    public String queryVirtualPhone(Long crmId){
        DmsResponse<String> liteCrmToken = dmscusCustomerFeign.getLiteCrmToken();
        log.info("liteCrmToken:{}", liteCrmToken);
        if(null == liteCrmToken || liteCrmToken.isFail()){
            log.info("liteCrmToken isfail");
            throw new ServiceBizException(CommonConstant.GET_VIRTUAL_PHONE_FAIL);
        }
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        ResponseDto<UserInfoVo> resp = midEndAuthCenterFeign.queryUserInfoById(currentLoginInfo.getUserId());
        if (null == resp || resp.isFail()) {
            log.info("UserInfoVo response isfail");
            throw new ServiceBizException(CommonConstant.GET_VIRTUAL_PHONE_FAIL);
        }
        UserInfoVo data = resp.getData();
        if (null == data || null == data.getPhone()) {
            log.info("UserInfoVo data isEmpty");
            throw new ServiceBizException(CommonConstant.GET_VIRTUAL_PHONE_FAIL);
        }
        log.info("virtualPhone:{},{}", crmId, data.getPhone());
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(virtualPhoneUrl)
                .path("/leads/api/v1/virtualPhone")
                .queryParam("id",crmId)
                .queryParam("dealerPhone",data.getPhone());

        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.AUTHORIZATION, liteCrmToken.getData());
        RequestEntity<Void> requestEntity = RequestEntity.<Void>get(builder.build().toUri()).headers(headers).build();
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<JSONObject> response = restTemplate.exchange(requestEntity, JSONObject.class);
        log.info("queryVirtualPhone:{}", response);
        Assert.isTrue(!ObjectUtils.isEmpty(response) && !ObjectUtils.isEmpty(response.getBody()), CommonConstant.GET_VIRTUAL_PHONE_FAIL);
        JSONObject body = response.getBody();
        if(body.getIntValue("code") != 0){
            log.info("queryVirtualPhone response isfail:{}", body.getString("message"));
            throw new ServiceBizException(CommonConstant.GET_VIRTUAL_PHONE_FAIL);
        }
        body.put("loginPhone", data.getPhone());
        return body.toJSONString();
    }

    /**
     * Sends reminders for clues messages.
     * （发送信息提醒）
     * @param dto The LeadOperationResultDto object containing the result of the lead operation.
     * @return True if the reminders were sent successfully, false otherwise.
     */
    @Async
    public Boolean cluesMessageReminder(LeadOperationResultDto dto) {
        // 查询 配置信息 发给谁
        String repairConfigQueryResult = queryRepairConfig();
        if (StringUtils.isBlank(repairConfigQueryResult)) {
            log.info("repairConfigQueryResultResponseParams:{}", repairConfigQueryResult);
            return Boolean.FALSE;
        }
        for (String value : repairConfigQueryResult.split(",")) {
            PushMessageRecordDto pushRecordDto = createPushRecord(dto, AccidentCluesConstant.MESSAGE_TEMPLATE);
            pushRecordDto.setUserId(value);
            pushRecordDto.setSubBizNo(String.format("%s/%s", value, dto.getId()));
            // send messages to weCom
            log.info("cluesMessageReminderToWeComRequestParams: {}", JSONObject.toJSONString(pushRecordDto));
            applicationAftersalesManagementFeign.pushMessage(pushRecordDto);
            MessageSendDto messageSendDto = buildMessageSendDto(value, dto, AccidentCluesConstant.messageTemplate);
            PushMessageRecordVo pushMessageRecord = buildPushRecord(dto, messageSendDto);
            // send messages to sms
            log.info("cluesMessageReminderToSMSRequestParamsMessageSendDto: {},PushMessageRecordVo:{}", JSONObject.toJSONString(messageSendDto), JSONObject.toJSONString(pushMessageRecord));
            commonMethodService.pushSms(AccidentCluesConstant.ACCIDENT_CLUES_FACTORY_TO_STORE, pushMessageRecord, messageSendDto);
        }
        return Boolean.TRUE;
    }

    /**
     * Builds a push message record based on the given LeadOperationResultDto.
     * （构建消息记录基于给定的 对象dto）
     * @param dto The LeadOperationResultDto used to build the push message record.
     * @return The constructed PushMessageRecordVo object.
     */
    private PushMessageRecordVo buildPushRecord(LeadOperationResultDto dto, MessageSendDto messageSendDto) {
        PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
        pushMessageRecordVo.setSinceType(2);
        pushMessageRecordVo.setBizNo(AccidentCluesConstant.ACCIDENT_CLUES_FACTORY_TO_STORE);
        pushMessageRecordVo.setSubBizNo(String.format("%s/%s", StringUtils.isBlank(messageSendDto.getMobiles()) ? dto.getVehicleVin() : messageSendDto.getMobiles(), dto.getId()));
        return pushMessageRecordVo;
    }

    /**
     * Builds a MessageSendDto object with the given value and LeadOperationResultDto.
     * (构建消息发送对象)
     * @param value The value to query user information.
     * @param dto The LeadOperationResultDto object to use for building the message.
     * @return A MessageSendDto object with the required parameters set.
     */
    private MessageSendDto buildMessageSendDto(String value, LeadOperationResultDto dto, String template) {
        UserInfoVo userInfo = queryUserInfoById(value);
        ClueDataDto.ContactInfo contactInfo = getContactInfoWithHighestPriority(dto);
        TmVehicleDto vehicleData = queryVehicleInfo(dto);
        MessageSendDto messageSendDto = new MessageSendDto();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("leadsReceiveTime", checkNullOrEmpty(dto.getLeadsReceiveTime()));
        paramMap.put("plateNumber", checkNullOrEmpty(vehicleData.getPlateNumber()));
        paramMap.put("modelName", checkNullOrEmpty(vehicleData.getModelName()));
        paramMap.put("customerName", ObjectUtils.isEmpty(contactInfo) ? "null" : checkNullOrEmpty(contactInfo.getCustomerName()));
        paramMap.put("accidentAddress", ObjectUtils.isEmpty(dto.getData().getAccidentInfo()) ? "null" : checkNullOrEmpty(dto.getData().getAccidentInfo().getAccidentAddress()));
        messageSendDto.setParamMap(paramMap);
        messageSendDto.setMobiles(userInfo.getPhone());
        messageSendDto.setTemplateId(template);
        return messageSendDto;
    }

    /**
     * Queries user information by ID.
     * (查询用户信息根据id)
     * @param value the ID of the user
     * @return the user information
     * @throws ServiceBizException if failed to query the user
     */
    private UserInfoVo queryUserInfoById(String value) {
        ResponseDto<UserInfoVo> userResponse = midEndAuthCenterFeign.queryUserInfoById(String.valueOf(value));
        if (userResponse.isFail() || ObjectUtils.isEmpty(userResponse)) {
            throw new ServiceBizException("查询用户失败！");
        }
        return userResponse.getData();
    }

    /**
     * Creates a {@link PushMessageRecordDto} object based on the given {@link LeadOperationResultDto}.
     * (创建一个 PushMessageRecordDto 基于给定的 LeadOperationResultDto)
     * @param dto The {@link LeadOperationResultDto} object used to create the push record.
     * @return The {@link PushMessageRecordDto} object created.
     */
    private PushMessageRecordDto createPushRecord(LeadOperationResultDto dto, String template) {
        PushMessageRecordDto pushRecordDto = new PushMessageRecordDto();
        pushRecordDto.setBizNo(AccidentCluesConstant.ACCIDENT_CLUES_FACTORY_TO_STORE);
        pushRecordDto.setSinceType(CommonConstant.SINCE_TYPE_WECOM);
        EmailBodyDto body = new EmailBodyDto();
        body.setVin(dto.getVehicleVin());
        pushRecordDto.setReqParams(JSONObject.toJSONString(body));
        pushRecordDto.setContent(buildPushContent(dto, template));
        return pushRecordDto;
    }

    /**
     * Builds the push content for a given LeadOperationResultDto.
     * The push content includes information such as lead receive time, vehicle plate number, vehicle model name,
     * customer name, and accident address.
     * （构建消息内容基于给定的对象，消息内容包含车牌号，车型名称 客户信息 和事故地址等信息。。）
     * @param dto The LeadOperationResultDto object containing lead operation result data.
     * @return The push content as a String.
     */
    private String buildPushContent(LeadOperationResultDto dto, String template) {
        ClueDataDto.ContactInfo contactInfo = getContactInfoWithHighestPriority(dto);
        TmVehicleDto vehicleData = queryVehicleInfo(dto);

        return String.format(
                template,
                checkNullOrEmpty(dto.getLeadsReceiveTime()),
                checkNullOrEmpty(vehicleData.getPlateNumber()),
                checkNullOrEmpty(vehicleData.getModelName()),
                ObjectUtils.isEmpty(contactInfo) ? "null" : checkNullOrEmpty(contactInfo.getCustomerName()),
                ObjectUtils.isEmpty(dto.getData().getAccidentInfo()) ? "null" : checkNullOrEmpty(dto.getData().getAccidentInfo().getAccidentAddress())
        );
    }

    private String checkNullOrEmpty(String value) {
        return StringUtils.isBlank(value) ? "null" : value;
    }

    /**
     * Retrieves the contact information with the highest priority from the given LeadOperationResultDto.
     * (检索联系人信息优先级最小的)
     * @param dto the LeadOperationResultDto object containing the contact information
     * @return the contact information with the highest priority, or null if no contact information is present
     */
    private ClueDataDto.ContactInfo getContactInfoWithHighestPriority(LeadOperationResultDto dto) {
        return dto.getData().getContactInfo().stream()
                .filter(Objects::nonNull)
                .min(Comparator.comparing(ClueDataDto.ContactInfo::getPriority, Comparator.nullsLast(String::compareTo)))
                .orElse(null);
    }

    /**
     * Queries vehicle information based on the given LeadOperationResultDto object.
     * (查询车辆信息基于给定的对象)
     * @param dto the LeadOperationResultDto object containing the vehicle VIN
     * @return the TmVehicleDto object representing the queried vehicle information
     * @throws ServiceBizException if the vehicle information cannot be fetched or is empty
     */
    private TmVehicleDto queryVehicleInfo(LeadOperationResultDto dto) {
        if (dto.getVehicleVin().length() >= 18) {
            // 事故线索下发的异常线索，vin带斜杠，导致接口报404
            return new TmVehicleDto();
        }
        DmsResponse<TmVehicleDto> vehicleDto = midEndVehicleCenterFeign.getVehicleByVIN(dto.getVehicleVin());
        log.info("queryVehicleInfoResponseParams: {}", JSONObject.toJSONString(vehicleDto));
        if (vehicleDto.isFail() || ObjectUtils.isEmpty(vehicleDto.getData())) {
            log.info("query vehicle center failed:{}",vehicleDto);
            return new TmVehicleDto();
        }
        return vehicleDto.getData();
    }

    /**
     * Queries the repair configuration from DMS.
     * (查询维修配置)
     * @return The repair configuration as a String.
     * @throws ServiceBizException If the query fails or the data is missing.
     */
    private String queryRepairConfig() {
        CommonConfigDTO configInfo = this.getConfigInfo(FACTORY_BIZ_MANAGE, ACCIDENT_CLUES_GROUPTYPE);
        if(Objects.isNull(configInfo)){
            return null;
        }
        return configInfo.getConfigValue();
    }

    /**
     * Queries whether accident clues exists for the given ID.
     * （查询一个事故线索是否存在基于 id）
     * @param id The ID to query.
     * @return The existing AccidentCluesDto if it exists.
     * @throws ServiceBizException if there was an error querying the domain lead or if the returned data is empty.
     */
    private AccidentCluesDto getAccidentCluesById(Long id) {
        // 查询是否存在线索
        DmsResponse<AccidentCluesDto> cluesResult = domainMaintainLeadFeign.queryCluesExistsByCrmId(id);
        if (cluesResult.isFail()) {
            throw new ServiceBizException("调用领域线索失败！");
        }
        return cluesResult.getData();
    }

    /**
     * 导出数据查询
     * <p>
     * 下载中心回调
     */
    @Override
    public List<AccidentCluesExportDto> exportOss(AccidentCluesExportQueryDto dto) {
        log.info("accident clue export oss start:{}", dto);
        //非厂端
        if (!OEM.equals(dto.getSource())) {
            dto.setDealerCode(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode());
            if (Objects.nonNull(dto.getIsself()) && "10371001".equals(dto.getIsself())) {
                dto.setFollowPeople(Integer.parseInt(LoginInfoUtil.getCurrentLoginInfo().getUserId()));
            }
        }
        log.info("accident clue export oss query:{}", dto);
        DmsResponse<CommonConfigDto> configDto = dmscloudServiceFeign.getConfigByKey(FACTORY_BIZ_MANAGE, ACCIDENT_CLUES_GROUPTYPE);
        log.info("accident clue export configDto:{}", JSON.toJSONString(configDto));
        if(configDto.isSuccess() && Objects.nonNull(configDto.getData())){
            String configValue = configDto.getData().getConfigValue();
            String[] split = configValue.split(",");
            List<String> factoryBizManage = Arrays.asList(split);
            if(!factoryBizManage.contains(LoginInfoUtil.getCurrentLoginInfo().getUserId())){
                dto.setDataStatus(1);
            }else{
                dto.setDataStatus(0);
            }
        }
        //时间处理
        log.info("fix end date start");
        this.fixEndDate(dto);

        DmsResponse<List<AccidentCluesExportDto>> res = domainMaintainLeadFeign.accidentClueExportOss(dto);
        if (res.isFail()){
            throw new ServiceBizException("查询事故线索失败");
        }
        List<AccidentCluesExportDto> exportData=res.getData();
        log.info("query export data size:{}", Objects.isNull(exportData) ? 0 : exportData.size());
        if (CollUtil.isEmpty(exportData)) {
            return Collections.emptyList();
        }

        //填充预约 & 养修 工单信息
        this.fillMaintainInfo(exportData);

        return exportData;
    }

    /**
     * 填充预约 & 养修 工单信息
     *
     */
    private void fillMaintainInfo(List<AccidentCluesExportDto> exportData) {
        log.info("fill maintain info start");
        //养修工单集合
        Map<String, RepairOrderDto> repairOrderMap = repairOrderMap(exportData);
        //预约单集合
        Map<String, BookingOrderDto> bookingOrderMap = bookingOrderMap(exportData);

        for (AccidentCluesExportDto clue : exportData) {
            //填充养修
            RepairOrderDto repairOrder = repairOrderMap.get(clue.getIntoDealerCode() + clue.getIntoRoNo());
            if (Objects.nonNull(repairOrder)) {
                //工单金额隔离，允许查看：厂端 or 自店
                if (Objects.equals(LoginInfoUtil.getCurrentLoginInfo().getDataType(), DataTypeEnum.DATA_TYPE_OEM.getDataType())||Objects.equals(LoginInfoUtil.getCurrentLoginInfo().getOwnerCode(),repairOrder.getOwnerCode())){
                    clue.setRepairAmount(repairOrder.getRepairAmount());
                    clue.setLabourAmount(repairOrder.getLabourAmount());
                    clue.setRepairPartAmount(repairOrder.getRepairPartAmount());
                }
                clue.setRepairTypeCode(RepairTypeEum.getCodeByValue(repairOrder.getRepairTypeCode()));
            }
            //填充预约
            BookingOrderDto bookingOrder = bookingOrderMap.get(clue.getDealerCode() + clue.getBookingOrderNo());
            if (Objects.nonNull(bookingOrder)) {
                clue.setBookingComeTime(bookingOrder.getBookingComeTime());
            }
        }
    }

    /**
     * 预约单集合
     *
     */
    private Map<String, BookingOrderDto> bookingOrderMap(List<AccidentCluesExportDto> exportData) {
        log.info("batch query maintain order start");
        HashMap<String, BookingOrderDto> bookingOrderMap = new HashMap<>();
        List<BookingOrderDto> bookingOrderList = maintainOrdersFeign.batchQueryBookingOrder(exportData).getData();
        log.info("batch query maintain order size:{}",Objects.isNull(bookingOrderList)?0:bookingOrderList.size());
        if (CollUtil.isEmpty(bookingOrderList)) {
            return bookingOrderMap;
        }
        for (BookingOrderDto order : bookingOrderList) {
            bookingOrderMap.put(order.getOwnerCode() + order.getBookingOrderNo(), order);
        }
        return bookingOrderMap;
    }
    /**
     * 全网经销商分页查询
     * @param params
     * @return
     */
    @Override
    public DealerVO getDealerList(DealerDTO params) {
        log.info("accident_allotDealer getDealerList:{}", JSON.toJSONString(params));
        RestResultResponse<DealerVO.DealerPageInfo> dealerList = midEndOrgCenterFeign.getDealerList(params);
        log.info("查询全网经销商:{}", JSON.toJSONString(dealerList));
        if (Objects.isNull(dealerList) || Objects.isNull(dealerList.getData())) {
           log.error("查询全网经销商失败:{}", JSON.toJSONString(dealerList));
           throw new ServiceBizException("查询经销商失败");
        }
        DmsResponse<CommonConfigDto> configDto = dmscloudServiceFeign.getConfigByKey(ACCIDENT_ALLOTDEALER, ACCIDENT_ALLOTDEALER);
        logger.info("getDealerList configByKey:{}", JSON.toJSONString(configDto));
        if(configDto.isFail() || Objects.isNull(configDto.getData())){
            log.error("查询全网分配经销商配置失败:{}", JSON.toJSONString(configDto));
            throw new ServiceBizException("查询全网分配经销商配置失败");
        }
        CommonConfigDto config = configDto.getData();
        if(config.getConfigValue().equals(REMIND_TYPE_1)){
            // 全网放开
            List<DealerVO.DealerInfo> records = dealerList.getData().getRecords();
            List<String> collect = records.stream().map(DealerVO.DealerInfo::getCompanyCode).collect(Collectors.toList());
            log.info("全网经销商:{}", JSON.toJSONString(collect));
            DmsResponse<List<VehicleHealthCheckWhiteListVo>> listDmsResponse = domainMaintainAuthFeign.existBlackListNew(collect, CommonConstant.WECOM_ACCIDENT_LEAD_TYPE);
            if (Objects.isNull(listDmsResponse) || listDmsResponse.isFail()) {
                logger.error("调用domain-maintain-auth查询黑名单失败");
                throw new ServiceBizException("查询经销商失败");
            }
            List<VehicleHealthCheckWhiteListVo> data = listDmsResponse.getData();
            records = records.stream().filter(item -> {
                int hit = 0;//是否命中; 0：未命中  >0：命中
                for (VehicleHealthCheckWhiteListVo black:data) {
                    if(item.getCompanyCode().equals(black.getOwnerCode())){
                        hit++;
                    }
                }
                return hit == 0 ? true : false;
            }).collect(Collectors.toList());
            DealerVO.DealerPageInfo build1 = DealerVO.DealerPageInfo.builder().records(records).build();
            DealerVO build = DealerVO.builder().data(build1).build();
            return build;
        }else{
            // 未全网放开
            // 查询事故线索白名单
            DmsResponse<List<VehicleHealthCheckWhiteListVo>> listDmsResponse1 = domainMaintainAuthFeign.selectWhitelistDetail(CommonConstant.WECOM_ACCIDENT_LEAD_TYPE_9129, CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE_0);
            if (Objects.isNull(listDmsResponse1) || listDmsResponse1.isFail()) {
                logger.error("调用domain-maintain-auth白名单经销商失败:{}", listDmsResponse1);
                throw new ServiceBizException("查询经销商失败");
            }
            List<DealerVO.DealerInfo> result = new LinkedList<>();
            List<VehicleHealthCheckWhiteListVo> data1 = listDmsResponse1.getData();
            if(CollectionUtils.isNotEmpty(data1)){
                if(StringUtils.isNotBlank(params.getData().getCompanyCode())){
                    data1 = data1.stream().filter(item -> item.getOwnerCode().toUpperCase().equals(params.getData().getCompanyCode().toUpperCase())).collect(Collectors.toList());
                }
                if(CollectionUtils.isEmpty(data1)){
                    return DealerVO.builder().build();
                }
                String collect1 = data1.stream().filter(Objects::nonNull).map(VehicleHealthCheckWhiteListVo::getOwnerCode).collect(Collectors.joining(","));
                IsExistByCodeDto build2 = IsExistByCodeDto.builder().companyCode(collect1).build();
                ResponseDto<List<CompanyDetailByCodeDto>> listResponseDto = midEndOrgCenterFeign.selectByCompanyCode(build2);
                if (Objects.isNull(listResponseDto) || listResponseDto.isFail()) {
                    logger.error("调用mid-end-org-center查询经销商名称失败:{}", listResponseDto);
                    throw new ServiceBizException("查询经销商失败");
                }
                if(CollectionUtils.isNotEmpty(listResponseDto.getData())){
                    List<CompanyDetailByCodeDto> data = listResponseDto.getData();
                    for (CompanyDetailByCodeDto item : data) {
                        DealerVO.DealerInfo build = DealerVO.DealerInfo.builder()
                                .companyCode(item.getCompanyCode())
                                .companyNameCn(item.getCompanyNameCn())
                                .build();
                        result.add(build);
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(result)){
                List<String> collect = result.stream().map(DealerVO.DealerInfo::getCompanyCode).collect(Collectors.toList());
                DmsResponse<List<VehicleHealthCheckWhiteListVo>> listDmsResponse = domainMaintainAuthFeign.existBlackListNew(collect, CommonConstant.WECOM_ACCIDENT_LEAD_TYPE);
                if (Objects.isNull(listDmsResponse) || listDmsResponse.isFail()) {
                    logger.error("调用domain-maintain-auth查询黑名单失败");
                    throw new ServiceBizException("查询经销商失败");
                }
                List<VehicleHealthCheckWhiteListVo> data = listDmsResponse.getData();
                result = result.stream().filter(item -> {
                    int hit = 0;//是否命中; 0：未命中  >0：命中
                    for (VehicleHealthCheckWhiteListVo black:data) {
                        if(item.getCompanyCode().equals(black.getOwnerCode())){
                            hit++;
                        }
                    }
                    return hit == 0 ? true : false;
                }).collect(Collectors.toList());
                DealerVO.DealerPageInfo build1 = DealerVO.DealerPageInfo.builder().records(result).build();
                DealerVO build = DealerVO.builder().data(build1).build();
                return build;
            }
        }
        return null;
    }
    @Override
    public void allotDealer(List<AllotDealerDTO> params) {
        // 只有配置的的人才有分配权限
        String factoryBizManage = this.queryRepairConfig();
        if (StringUtils.isEmpty(factoryBizManage)) {
            log.info("factoryBizManage:{}", factoryBizManage);
            throw new ServiceBizException("未获取到分配管理员!");
        }
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        String[] split = factoryBizManage.split(",");
        List<String> factoryBizManageList = Arrays.asList(split);
        if(StringUtils.isEmpty(currentLoginInfo.getUserId())
        || !factoryBizManageList.contains(currentLoginInfo.getUserId())){
            log.info("currentLoginInfo.getUserId():{}", currentLoginInfo.getUserId());
            throw new ServiceBizException("当前用户无分配权限!");
        }
        // 校验是否存在黑名单
        List<String> collect = params.stream().map(AllotDealerDTO::getCompanyCode).collect(Collectors.toList());
        log.info("allotDealer-collect1:{}", collect);
        DmsResponse<List<VehicleHealthCheckWhiteListVo>> listDmsResponse = domainMaintainAuthFeign.queryWhiteList(collect, CommonConstant.WECOM_ACCIDENT_LEAD_TYPE);
        if (Objects.isNull(listDmsResponse) || listDmsResponse.isFail()) {
            logger.error("调用domain-maintain-auth查询黑名单失败");
            throw new ServiceBizException("查询黑名单经销商失败!");
        }
        if(CollectionUtils.isNotEmpty(listDmsResponse.getData())){
            throw new ServiceBizException("存在黑名单经销商,不允许分配");
        }
        log.info("allotDealer-collect2:{}", collect);
        DmsResponse<CommonConfigDto> configDto = dmscloudServiceFeign.getConfigByKey(ACCIDENT_ALLOTDEALER, ACCIDENT_ALLOTDEALER);
        logger.info("getDealerList configByKey:{}", JSON.toJSONString(configDto));
        if(configDto.isFail() || Objects.isNull(configDto.getData())){
            log.error("查询全网分配经销商配置失败:{}", JSON.toJSONString(configDto));
            throw new ServiceBizException("查询全网分配经销商配置失败");
        }
        CommonConfigDto config = configDto.getData();
        if(!config.getConfigValue().equals(REMIND_TYPE_1)){
            params.stream().forEach(item -> {
                DmsResponse<Object> booleanDmsResponse = domainMaintainAuthFeign.checkWhitelist(item.getCompanyCode(), CommonConstant.WECOM_ACCIDENT_LEAD_TYPE, CommonConstant.ERRORDATA, "");
                log.info("allotDealer03:{}", JSON.toJSONString(booleanDmsResponse));
                if (Objects.isNull(booleanDmsResponse) || booleanDmsResponse.isFail()) {
                    logger.error("调用domain-maintain-auth校验白名单失败");
                    throw new ServiceBizException("校验白名单失败!");
                }
                if(Objects.isNull(booleanDmsResponse.getData()) || !Boolean.parseBoolean(booleanDmsResponse.getData().toString())){
                    logger.error("调用domain-maintain-auth校验白名单失败");
                    throw new ServiceBizException("校验白名单失败!");
                }
            });
        }
        DmsResponse<List<StatusChangePushDto>> listDmsResponse1 = domainMaintainLeadFeign.allotDealer(params);
        if (Objects.isNull(listDmsResponse1) || listDmsResponse1.isFail() || CollectionUtils.isEmpty(listDmsResponse1.getData())) {
            logger.error("调用domainMaintainLeadFeign分配经销商失败:{}", listDmsResponse1);
            throw new ServiceBizException(listDmsResponse1.getErrMsg());
        }
        // 推送liteCrm
        dmscusCustomerFeign.acPushLiteCrmClueStatus(listDmsResponse1.getData());
    }

    @Override
    public CompanyDetailByCodeDto getDealerInfo(String dealerCode) {
        return this.queryCompanyDetailByCodeDto(dealerCode);
    }

    @Override
    public AccidentCluesSumInfoDTO getSumAccidentInfo(AccidentCluesExportQueryDto dto) {
        DmsResponse<CommonConfigDto> configDto = dmscloudServiceFeign.getConfigByKey(FACTORY_BIZ_MANAGE, ACCIDENT_CLUES_GROUPTYPE);
        log.info("accident clue getSumAccidentInfo configDto:{}", JSON.toJSONString(configDto));
        if(configDto.isSuccess() && Objects.nonNull(configDto.getData())){
            String configValue = configDto.getData().getConfigValue();
            String[] split = configValue.split(",");
            List<String> factoryBizManage = Arrays.asList(split);
            if(!factoryBizManage.contains(LoginInfoUtil.getCurrentLoginInfo().getUserId())){
                dto.setDataStatus(1);
            }else{
                dto.setDataStatus(0);
            }
        }
        DmsResponse<AccidentCluesSumInfoDTO> sumAccidentInfo = domainMaintainLeadFeign.getSumAccidentInfo(dto);
        if (Objects.isNull(sumAccidentInfo) || sumAccidentInfo.isFail()) {
            logger.error("调用domainMaintainLeadFeign查询线索统计数据失败:{}", sumAccidentInfo);
            throw new ServiceBizException(sumAccidentInfo.getErrMsg());
        }
        return sumAccidentInfo.getData();
    }

    /**
     * 养修工单集合
     *
     */
    private Map<String, RepairOrderDto> repairOrderMap(List<AccidentCluesExportDto> exportData) {
        log.info("batch query maintain order start");
        HashMap<String, RepairOrderDto> repairOrderMap = new HashMap<>();
        List<RepairOrderDto> repairOrderList = maintainOrdersFeign.batchQueryMaintainOrder(exportData).getData();
        log.info("batch query maintain order size:{}",Objects.isNull(repairOrderList)?0:repairOrderList.size());
        if (CollUtil.isEmpty(repairOrderList)) {
            return repairOrderMap;
        }
        for (RepairOrderDto order : repairOrderList) {
            repairOrderMap.put(order.getOwnerCode() + order.getRoNo(), order);
        }
        return repairOrderMap;
    }

    /**
     * 时间精度到天，endDate多算一天
     */
    private void fixEndDate(AccidentCluesExportQueryDto dto) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            if (Objects.nonNull(dto.getReportDateEnd())) {
                Date parse = format.parse(dto.getReportDateEnd());
                DateTime dateTime = DateUtil.offsetDay(parse, 1);
                dto.setReportDateEnd(format.format(dateTime));
            }
            if (Objects.nonNull(dto.getNextFollowDateEnd())) {
                Date parse = format.parse(dto.getNextFollowDateEnd());
                DateTime dateTime = DateUtil.offsetDay(parse, 1);
                dto.setNextFollowDateEnd(format.format(dateTime));
            }
            if (Objects.nonNull(dto.getCreatedDateEnd())) {
                Date parse = format.parse(dto.getCreatedDateEnd());
                DateTime dateTime = DateUtil.offsetDay(parse, 1);
                dto.setCreatedDateEnd(format.format(dateTime));
            }
        }catch (Exception e){
            log.info("fixEndDate 转换失败:{}", e);
        }

    }
}
