package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VehicleEntranceCountDto {

    /**
     * 未分拨
     */
    private Long Unallocated;

    /**
     * 已分拨
     */
    private Long allocated;

    /**
     * 全部
     */
    private Long allCount;

    /**
     * 接待超时数量
     */
    private Long receptionOverTime;
}
