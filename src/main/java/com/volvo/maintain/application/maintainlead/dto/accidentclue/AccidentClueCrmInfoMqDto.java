package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("事故线索上报更新，MQ消息")
public class AccidentClueCrmInfoMqDto {
    @ApiModelProperty("litecrmId")
    private Long id;
    @ApiModelProperty("newbieId")
    private String sourceClueId;
    @ApiModelProperty("是否重复")
    private Integer repeatFlag;
    @ApiModelProperty("重复线索ID")
    private List<Long> repeatIdList;
    @ApiModelProperty("渠道标签")
    private String channelTag;
    @ApiModelProperty("来源渠道（NewBie，YB，400）")
    private String sourceChannel;
    @ApiModelProperty("经销商代码")
    private String dealerCode;

    @ApiModelProperty("跟进失败原因")
    private String followFailWhy;

    @ApiModelProperty("售后大区ID")
    private Long afterBigAreaId;

    @ApiModelProperty("售后大区名")
    private String afterBigAreaName;

    @ApiModelProperty("售后小区ID")
    private Long afterSmallAreaId;

    @ApiModelProperty("售后小区名")
    private String afterSmallAreaName;

    @ApiModelProperty("跟进人ID")
    private Long followPeoPer;

    @ApiModelProperty("跟进人名称")
    private String followPeoPerName;

    @ApiModelProperty(value = "省ID")
    private String    provinceId;

    @ApiModelProperty(value = "市ID")
    private String    cityId;

    @ApiModelProperty(value = "省名称")
    private String    provinceName;

    @ApiModelProperty(value = "市名称")
    private String    cityName;
}
