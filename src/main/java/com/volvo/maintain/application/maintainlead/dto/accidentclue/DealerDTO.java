package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ApiModel("全网经销商查询")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DealerDTO implements Serializable {
    private static final long serialVersionUID = 53432435L;
    @ApiModelProperty("当前页")
    private Integer page = 1;
    @ApiModelProperty("页面容量")
    private Integer pageSize = 10;
    @ApiModelProperty("网点及经销商查询条件")
    private DealerInfoDTO data;

    @ApiModel("事故线索集合查询条件")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DealerInfoDTO implements Serializable {
        private static final long serialVersionUID = 85135464L;

        @ApiModelProperty("经销商代码")
        private String companyCode;
    }
}
