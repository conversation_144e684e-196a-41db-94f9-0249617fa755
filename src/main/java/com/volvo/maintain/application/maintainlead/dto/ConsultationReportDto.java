package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(value = "em90检测报告", description="em90检测报告")
@Data
public class ConsultationReportDto {

    //车架号
    private String vin;
    //1、技术咨询， 2、个例质量报告 ,3、诊断仪咨询
    private Integer consultationType;
    //
    private String consultationNo;
    //N、IS、UD、ID、US、C
    private String state;
    //经销商代码
    private String dealerCode;
    //首次提交时间
    private String firstSubmissionTime;




}
