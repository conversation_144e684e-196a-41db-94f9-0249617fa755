package com.volvo.maintain.application.maintainlead.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 取送车订单
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "SaveVehicleDeliverVO", description = "SaveVehicleDeliverVO")
public class SaveVehicleDeliverVo {

    @ApiModelProperty(value = "id", required = true)
    private Long id;

    @ApiModelProperty(value = "养修预约单号")
    private String reservationNo;

    @ApiModelProperty(value = "预约时间 必须比当前时间晚至少半个小时，当mode=3预约时间可以不传 (yyyy-MM-dd HH:mm:ss)")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bookingTime;

    @ApiModelProperty(value = "创建时间 yyyy-MM-dd HH:mm:ss", dataType = "java.lang.String")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


}