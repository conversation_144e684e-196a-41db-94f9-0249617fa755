package com.volvo.maintain.application.maintainlead.service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigDto;
import com.volvo.maintain.application.maintainlead.dto.RequestDto;
import com.volvo.maintain.application.maintainlead.dto.SettlementDocConfirmCompensateDto;
import com.volvo.maintain.application.maintainlead.dto.order.CancelOrderDto;
import com.volvo.maintain.application.maintainlead.dto.order.DriverDto;
import com.volvo.maintain.application.maintainlead.dto.order.PlaceOrderDto;
import com.volvo.maintain.application.maintainlead.dto.order.PriceDto;
import com.volvo.maintain.application.maintainlead.dto.order.QueryVehicleDeliverDto;
import com.volvo.maintain.application.maintainlead.dto.order.SaveVehicleDeliverDto;
import com.volvo.maintain.application.maintainlead.dto.order.ShopVehicleDeliverDto;
import com.volvo.maintain.application.maintainlead.vo.order.DetailVo;
import com.volvo.maintain.application.maintainlead.vo.order.DriverInfoVo;
import com.volvo.maintain.application.maintainlead.vo.order.DriverVo;
import com.volvo.maintain.application.maintainlead.vo.order.DrivingInfoVo;
import com.volvo.maintain.application.maintainlead.vo.order.FeeDetailVo;
import com.volvo.maintain.application.maintainlead.vo.order.PlaceOrderVo;
import com.volvo.maintain.application.maintainlead.vo.order.PriceVo;
import com.volvo.maintain.application.maintainlead.vo.order.SaveVehicleDeliverVo;
import com.volvo.maintain.application.maintainlead.vo.order.ShopVehicleDeliverVo;
import com.volvo.maintain.application.maintainlead.vo.order.TraceVo;
import com.volvo.maintain.application.maintainlead.vo.order.VehicleDeliverDetailVo;
import com.volvo.maintain.application.maintainlead.vo.order.VehicleDeliverVo;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.DominAutoFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import com.volvo.maintain.infrastructure.util.ObjectUtil;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	DominAutoFeign dominAutoFeign;
	@Autowired
	private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
	
	@Autowired
	private DmscloudServiceFeign dmscloudServiceFeign;
	
    @Autowired
    private RedissonClient redissonClient;

	@Override
	public Page<ShopVehicleDeliverVo> shopSearchVehiicleDeliverPage(RequestDto<ShopVehicleDeliverDto> shopVehicleDeliverDto) {
		MidResponse<Page<ShopVehicleDeliverVo>> pageMidResponse = dominAutoFeign.shopSearchVehiicleDeliverPage(shopVehicleDeliverDto);
		logger.info("shopSearchVehiicleDeliverPage,pageMidResponse, bookingListDmsResponse bookingListDmsResponse:{} ", JSON.toJSONString(pageMidResponse));
		if (pageMidResponse.isFail()) {
			throw new ServiceBizException("订单中心异常:" + pageMidResponse.getReturnMessage());
		}
		return pageMidResponse.getData();
	}

	@Override
	public boolean cancelVehicleOrder(RequestDto<CancelOrderDto> cancelOrderDto) {
		MidResponse<Boolean> booleanMidResponse = dominAutoFeign.cancelVehicleOrder(cancelOrderDto);
		logger.info("cancelVehicleOrder,booleanMidResponse:{} ", booleanMidResponse);
		if (booleanMidResponse.isFail()) {
			throw new ServiceBizException("订单中心异常:" + booleanMidResponse.getReturnMessage());
		}
		return booleanMidResponse.getData();
	}

	@Override
	public PlaceOrderVo placeOrder(RequestDto<PlaceOrderDto> placeOrderDto) {
		PlaceOrderDto placeOrder = placeOrderDto.getData();
		String key = "QSC:placeOrder:" + placeOrder.getId();
		RLock lock = redissonClient.getLock(key);
		boolean locked = false;
		try {
            // 尝试获取锁，最多等待1秒
			locked = lock.tryLock(1, -1, TimeUnit.SECONDS);
            if (!locked) {
            	// 获取锁失败，返回缓存数据（即使可能过期）
            	log.info("placeOrder,获取锁失败");
            	throw new ServiceBizException("订单正在处理中，请稍后重试！");
            }
            try {
            	MidResponse<PlaceOrderVo> placeOrderVoMidResponse = dominAutoFeign.placeOrder(placeOrderDto);
            	logger.info("placeOrder,placeOrderVoMidResponse:{} ", placeOrderVoMidResponse);
            	if (placeOrderVoMidResponse.isFail()) {
            		throw new ServiceBizException("订单中心异常:" + placeOrderVoMidResponse.getReturnMessage());
            	}
            	
            	PlaceOrderVo data = placeOrderVoMidResponse.getData();
            	Long id = data.getId();
            	ShopVehicleDeliverVo shopVehicleDeliverVo = queryShopVehicleDeliverList(id, "");
            	
            	if(Objects.nonNull(shopVehicleDeliverVo)) {
            		data.setCarNo(shopVehicleDeliverVo.getCarNo());
            		data.setModelCode(shopVehicleDeliverVo.getModelCode());
            		data.setVin(shopVehicleDeliverVo.getVin());
            	}
            	log.info(JSON.toJSONString(data));
            	return data;
            } finally {
            	if (lock.isHeldByCurrentThread()) {                		
            		lock.unlock();
            	}
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            log.error("订单处理中断异常 [key:{}]: {}", key, e.getMessage(), e);
            throw new ServiceBizException("系统繁忙，请稍后重试");
        } finally {
        	if (locked && lock.isHeldByCurrentThread()) {
        		lock.unlock();
        	}
        }
	}

	/**
	 * 获取取送车列表
	 * @param id
	 * @param orderId
	 * @return
	 */
	private ShopVehicleDeliverVo queryShopVehicleDeliverList(Long id, String orderId) {
		try {			
			ShopVehicleDeliverDto shopVehicleDeliver = new ShopVehicleDeliverDto();
			shopVehicleDeliver.setId(id);
			if(Objects.nonNull(orderId)) {
				shopVehicleDeliver.setOrderId(orderId);
			}
			RequestDto<ShopVehicleDeliverDto> requestDto = new RequestDto<>();
			requestDto.setPage(1L);
			requestDto.setPageSize(5L);
			requestDto.setData(shopVehicleDeliver);	
			// 增加列表接口查询
			log.info("查询列表入参：{}", JSON.toJSONString(requestDto));
			MidResponse<Page<ShopVehicleDeliverVo>> pageMidResponse = dominAutoFeign.shopSearchVehiicleDeliverPage(requestDto);
			log.info("列表数据返回：{}", JSON.toJSONString(pageMidResponse));
			Page<ShopVehicleDeliverVo> shopVehicleDeliverData = pageMidResponse.getData();
			List<ShopVehicleDeliverVo> records = shopVehicleDeliverData.getRecords();
			ShopVehicleDeliverVo shopVehicleDeliverVo = records.get(0);
			log.info("shopVehicleDeliverVo:{}", JSON.toJSONString(shopVehicleDeliverVo));
			return shopVehicleDeliverVo;
		} catch (Exception e) {
			log.info("获取列表数据异常：", e);
		}
		return null;
	}

	@Override
	public List<ShopVehicleDeliverVo> shopSearchVehiicleDeliverList(RequestDto<ShopVehicleDeliverDto> shopVehicleDeliverDto) {
		MidResponse<List<ShopVehicleDeliverVo>> shopSearchVehicleDeliverList = dominAutoFeign.shopSearchVehiicleDeliverList(shopVehicleDeliverDto);
		logger.info("shopSearchVehiicleDeliverList,shopSearchVehicleDeliverList:{} ", shopSearchVehicleDeliverList.getReturnMessage());
		if (shopSearchVehicleDeliverList.isFail()) {
			throw new ServiceBizException("订单中心异常:" + shopSearchVehicleDeliverList.getReturnMessage());
		}
		return shopSearchVehicleDeliverList.getData();
	}

	@Override
	public List<VehicleDeliverDetailVo> allDetail(RequestDto<QueryVehicleDeliverDto> queryVehicleDeliverDto) {
		log.info("queryVehicleDeliverDto: {}", JSON.toJSONString(queryVehicleDeliverDto));
		MidResponse<List<VehicleDeliverDetailVo>> listMidResponse = dominAutoFeign.allDetails(queryVehicleDeliverDto);
		logger.info("allDetail,listMidResponse:{} ", listMidResponse.getReturnMessage());
		if (listMidResponse.isFail()) {
			throw new ServiceBizException("订单中心异常:" + listMidResponse.getReturnMessage());
		}
		List<VehicleDeliverDetailVo> data = listMidResponse.getData();
		log.info("allDetail: {}", JSON.toJSONString(data));
		try {
			QueryVehicleDeliverDto queryVehicleDeliver = queryVehicleDeliverDto.getData();
			Integer supplierTypeTemp = null;
			String modelCodeTemp = queryVehicleDeliver.getModelCode();
			if(StringUtils.isBlank(modelCodeTemp)) {				
				ShopVehicleDeliverVo queryShopVehicleDeliverList = queryShopVehicleDeliverList(queryVehicleDeliver.getId(),"");
				if(Objects.nonNull(queryShopVehicleDeliverList)) {
					modelCodeTemp = queryShopVehicleDeliverList.getModelCode();
					supplierTypeTemp = queryShopVehicleDeliverList.getSupplierType();
				}
			}
			
			String modelCode = modelCodeTemp;
			Integer supplierType = supplierTypeTemp;
			log.info("modelCode: {}", modelCode);
			DmsResponse<CommonConfigDto> configByKey = dmscloudServiceFeign.getConfigByKey("PICKUP_DELIVERY_OVER_20_KILOMETERS_REMIND", "CAR_PICKUP_DELIVERY_DETAILS_MSG");
			CommonConfigDto commonConfig = configByKey.getData();
			log.info("commonConfig: {}", JSON.toJSONString(commonConfig));
			if(Objects.nonNull(commonConfig) && !CollectionUtils.isEmpty(data)) {
				String pickupAlertMessage = commonConfig.getConfigValue();
				String deliveryAlertMessage = commonConfig.getConfigExt1();
				data.stream().filter(Objects::nonNull).forEach(obj->handleEM90Alert(supplierType, modelCode, pickupAlertMessage, deliveryAlertMessage, obj));
			}
		} catch (Exception e) {
			log.info("EM90判定+20公里判定失败", e);
		}
		return data;
	}

	/**
	 * 处理EM90
	 * @param alertMessage
	 * @param obj
	 */
	private void handleEM90Alert(Integer supplierType, String modelCode, String pickupAlertMessage, String deliveryAlertMessage, VehicleDeliverDetailVo obj) {
		log.info("handleEM90Alert: {}, pickupAlertMessage: {}, deliveryAlertMessage: {}, supplierType: {}", JSON.toJSONString(obj), pickupAlertMessage, deliveryAlertMessage, supplierType);
		try {
			VehicleDeliverVo vehicleDeliver = obj.getVehicleDeliverVO();
			Integer type = vehicleDeliver.getType();
			
			String msg = pickupAlertMessage;
			if(Objects.equals("82711002", String.valueOf(type))) {
				msg = deliveryAlertMessage;
			}
			
			// 查询预计里程
			DetailVo detail = obj.getDetailVO();
			
			Integer detailSupplierType = detail.getSupplierType();
			if(Objects.nonNull(detailSupplierType)) {
				log.info("detailSupplierType: {}", detailSupplierType);
				supplierType = detailSupplierType;
			}
			if(Objects.isNull(obj.getSupplierType()) && Objects.nonNull(supplierType)) {				
				obj.setSupplierType(supplierType);
			}
			
			List<DrivingInfoVo> drivingInfoList = detail.getDrivingInfoList();
			DrivingInfoVo drivingInfoVo = drivingInfoList.get(0);
			
			List<FeeDetailVo> feeDetail = drivingInfoVo.getFeeDetail();
			FeeDetailVo feeDetailVo = feeDetail.get(0);
			String distance = feeDetailVo.getDistance();
			if(new BigDecimal(distance).compareTo(new BigDecimal("20"))>0 && !"895".equals(modelCode)){
				obj.setAlertMessage(msg);
			}
		} catch (Exception e) {
			log.info("EM90判定+20公里判定失败", e);
		}
	}

	@Override
	public BigDecimal balanceCustomerId(String customerId) {
		MidResponse<BigDecimal> balanceCustomerId = dominAutoFeign.balanceCustomerId(customerId);
		logger.info("balanceCustomerId,balanceCustomerId:{} ", balanceCustomerId);
		if (balanceCustomerId.isFail()) {
			throw new ServiceBizException("订单中心异常:" + balanceCustomerId.getReturnMessage());
		}
		return balanceCustomerId.getData();
	}

	@Override
	public List<DriverVo> getFixList(RequestDto<DriverDto> driverDto) {
		MidResponse<List<DriverVo>> listMidResponse = dominAutoFeign.getFixList(driverDto);
		logger.info("getFixList,listMidResponse:{} ", listMidResponse.getReturnMessage());
		if (listMidResponse.isFail()) {
			throw new ServiceBizException("订单中心异常:" + listMidResponse.getReturnMessage());
		}
		return listMidResponse.getData();
	}

	@Override
	public PriceVo price(RequestDto<PriceDto> priceDto) {
		try {
			// 获取经销商
			String ownerCode = LoginInfoUtil.getCurrentLoginInfo().getOwnerCode();
			PriceDto data = priceDto.getData();
			data.setOwnerCode(ownerCode);
		} catch (Exception e) {
			log.info("获取经销商异常：{}", e);
		}
		
		log.info("OrderServiceImpl-price: {}", JSON.toJSONString(priceDto));
		MidResponse<List<PriceVo>> estimatePriceResponse = dominAutoFeign.estimatePrice(priceDto.getData());
		if (estimatePriceResponse.isFail()) {
			throw new ServiceBizException("订单中心异常:" + estimatePriceResponse.getReturnMessage());
		}
		List<PriceVo> priceList = estimatePriceResponse.getData();
		
		PriceVo price = ObjectUtil.getMaxObjectWithStream(priceList, Comparator.comparing(PriceVo::getTotalFee));
		log.info("最大值对象：price: {}", JSON.toJSONString(price));
		return price;
	}

	@Override
	public DetailVo detailOrderId(String orderId) {
		MidResponse<DetailVo> detailOrderId = dominAutoFeign.detailOrderId(orderId);
		logger.info("detailOrderId,detailOrderId:{} ", detailOrderId);
		if (detailOrderId.isFail()) {
			throw new ServiceBizException("订单中心异常:" + detailOrderId.getReturnMessage());
		}
		return detailOrderId.getData();
	}

	@Override
	public TraceVo traceOrderId(String orderId, Integer type) {
		MidResponse<TraceVo> detailOrderId = dominAutoFeign.traceOrderId(orderId,type);
		logger.info("traceOrderId,detailOrderId:{} ", detailOrderId);
		if (detailOrderId.isFail()) {
			throw new ServiceBizException("订单中心异常:" + detailOrderId.getReturnMessage());
		}
		return detailOrderId.getData();
	}

	@Override
	public DriverInfoVo driverInfoOrderId(String orderId, Integer type) {
		MidResponse<DriverInfoVo> detailOrderId = dominAutoFeign.driverInfoOrderId(orderId, type);
		logger.info("driverInfoOrderId,detailOrderId:{} ", JSON.toJSONString(detailOrderId));
		if (detailOrderId.isFail()) {
			throw new ServiceBizException("订单中心异常:" + detailOrderId.getReturnMessage());
		}
		return detailOrderId.getData();
	}

	@Override
	public Map<String, Object> getCarPhotosOrderId(String orderId, Integer daijiaType) {
		MidResponse<Map<String, Object>> detailOrderId = dominAutoFeign.getCarPhotosOrderId(orderId,daijiaType);
		logger.info("getCarPhotosOrderId,detailOrderId:{} ", detailOrderId);
		if (detailOrderId.isFail()) {
			throw new ServiceBizException("订单中心异常:" + detailOrderId.getReturnMessage());
		}
		return detailOrderId.getData();
	}

	@Override
	public SaveVehicleDeliverVo addVehicleDeliverOldDms(SaveVehicleDeliverDto saveVehicleDeliverDto, BindingResult bindingResult) {
		return null;
	}

	@Override
	public void settlementDocConfirmCompensate(SettlementDocConfirmCompensateDto parseObject) {
		domainMaintainOrdersFeign.updateSettlementDocConfirmReason(parseObject);
	}
}
