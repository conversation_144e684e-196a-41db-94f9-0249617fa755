package com.volvo.maintain.application.maintainlead.service.vhc;


import com.volvo.maintain.application.maintainlead.dto.vhc.*;

import java.math.BigDecimal;
import java.util.List;

public interface VhcWorkOrderService {
    /**
     * 查询维修项目明细
     * @param vhcNo
     * @return
     */
    VhcQuotedDTO queryMaintenanceItems(String vhcNo);
    /**
     * 查询零件是否缺料
     * @param partNo
     * @return
     */
    Integer checkedPartShort(String partNo, BigDecimal practicalQuantity, String storageCode);
    /**
     * 查询零件是否在工单
     * @param partNo
     * @return
     */
    Integer checkedWorkOrderHavePart(String partNo, String labourCode, String roNo);
    /**
     * 保存草稿&报价完成
     * @param dto
     * @return
     */
    void saveMaintenanceItems(VhcMaintanceReqDTO dto);
    /**
     * 推送用户
     * @return
     */
    void pushCustomer(String vhcNo, String roNo, Integer flag, String itemIds);
    /**
     * 健康检查消息提醒查询
     * @param userId
     * @return
     */
    List<VhcRemindDTO> queryVhcRemind(Long userId);

    /**
     * 根据id删除已读消息
     * @param id
     */
    void removeInfoById(Integer id);

    /**
     * 代客户反向确认维修
     * @param dto
     * @return
     */
    void confirmRepair(VhcConfirmRepairDTO dto);

    /**
     * 维修质检校验按钮
     * @param roNo
     * @param ownerCode
     * @return
     */
    void verification(String roNo, String ownerCode, String isPad);

    /**
     * 保存不修原因
     * @param dto
     */
    void saveNotRepair(VhcNotRepairReqDTO dto);
}
