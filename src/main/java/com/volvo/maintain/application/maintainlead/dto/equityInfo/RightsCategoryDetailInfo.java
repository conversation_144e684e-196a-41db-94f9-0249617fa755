package com.volvo.maintain.application.maintainlead.dto.equityInfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description
 */
@Data
@Accessors(chain = true)
public class RightsCategoryDetailInfo implements Serializable {

    private static final long serialVersionUID = -4095465932875979750L;

    /**
     * 权益一级分类
     */
    @ApiModelProperty(value = "权益一级分类编号")
    private Long firstCategoryCode;

    /**
     * 权益一级分类
     */
    @ApiModelProperty(value = "权益一级分类名称")
    private String firstCategoryName;


    /**
     * 权益二级分类
     */
    @ApiModelProperty(value = "权益二级分类编号")
    private Long secondCategoryCode;

    /**
     * 权益二级分类
     */
    @ApiModelProperty(value = "权益二级分类名称")
    private String secondCategoryName;

    /**
     * 权益三级分类
     */
    @ApiModelProperty(value = "权益三级分类编号")
    private Long thirdCategoryCode;

    /**
     * 权益三级分类
     */
    @ApiModelProperty(value = "权益三级分类编号")
    private String thirdCategoryName;

}
