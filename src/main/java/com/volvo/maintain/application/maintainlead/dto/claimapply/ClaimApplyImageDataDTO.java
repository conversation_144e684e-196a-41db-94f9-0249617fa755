package com.volvo.maintain.application.maintainlead.dto.claimapply;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/09/13
 */
@Data
public class ClaimApplyImageDataDTO implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 系统id
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织id
     */
    private Integer orgId;

    /**
     * 理赔申请id 关联tt_claim_apply_use的id
     */
    private Long applyId;

    /**
     * 业务类型(3506)
     */
    private Integer businessType;

    /**
     * 数据类型(3505）
     */
    private Integer dataType;

    /**
     * 文件uuid
     */
    private String fileUuid;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * url
     *
     */
    private String url;
}
