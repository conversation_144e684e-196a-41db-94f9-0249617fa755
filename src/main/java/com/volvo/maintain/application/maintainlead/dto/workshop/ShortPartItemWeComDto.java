package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShortPartItemWeComDto {

    /**
     * 缺料记录ID
     */
    private Integer shortId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 车牌号
     */
    private String license;

    /**
     * 单据号码
     */
    private String sheetNo;

    private String ownerCode;

    /**
     * 关联 采购明细id (缺料表字段)
     */
    private Long purchaseOrderDetailId;


    /**
     * 缺件数量
     */
    private BigDecimal shortQuantity;

    /**
     * 是否背靠背
     */
    private Long isLinked;


    /**
     * 缺料日期
     */
    private String createdAt;

    /**
     * 零件号
     */
    private String partNo;

    private String partName;

    private String roStatus;

    /**
     * 预计交车时间
     */
    private String endTimeSupposed;


    private String modelCode;


    private String vin;


    /**
     * 手机号
     */
    private String phone;

    private String serviceAdvisor;

    private String roCreateDate;

    private String modelName;

    /**
     * 备货状态
     */
    private Integer isStockUp;

    private Long inviteTagId;

    private String tagName;

    /**
     * 标签状态 亮不亮
     */
    private String tagState;

    /**
     * 业务类型
     */
    private String businessType;
}
