package com.volvo.maintain.application.maintainlead.dto.vhc;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("VHC消息推送")
public class VhcMessageRequestDto {

    //检查单号
    private String healthNo;
    //车架号
    private String vin;
    //工单号
    private String roNo;
    //检查时间
    private String examineTime;
    //经销商
    private String dealerCode;





}
