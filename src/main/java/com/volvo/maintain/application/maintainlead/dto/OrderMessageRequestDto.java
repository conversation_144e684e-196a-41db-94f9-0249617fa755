package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 预约单推送c端
 *
 * <AUTHOR>
 * @Date 2024/3/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("预约单推送c端")
public class OrderMessageRequestDto {

    //经销商
    private String dealerCode;
    //工单号
    private String ticketId;
    //车架号
    private String vin;
    //预约单号（中台）
    private String reservationId;
    //事件类型 1-超时未确认，2-超时未到店
    private String activityType;

}