package com.volvo.maintain.application.maintainlead.dto.workshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 提醒规则查询入参
 * @Date 2024/11/13 15:30
 */
@ApiModel("提醒规则查询入参")
@Data
public class RemindRuleDto {

    @ApiModelProperty("提醒类型")
    private List<String> businessType;

    @ApiModelProperty(value = "经销商代码")
    private String ownerCode;

    @ApiModelProperty("是否启用")
    private String isEnabled;

    @ApiModelProperty("提醒规则")
    private String remindRule;

    @ApiModelProperty(value = "当前页")
    private Long pageNum;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "当前页", name = "currentPage")
    private Long currentPage;

}
