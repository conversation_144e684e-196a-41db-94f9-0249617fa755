package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OutboundOrderETAResponseDto implements Serializable {

    /**
     * 采购单明细表  id 关联条件
     */
    private Long purchaseOrderDetailId;

    private String ownerCode;

    private String partsNo;

    private String roNo;

    /**
     * ETA时间 预计到货
     * 时间
     */

    private String etaTime;

    /**
     * 缺料记录ID
     */
    private Integer shortId;
    /**
     * 仓库代码
     */
    private String storageCode;
    /**
     * 库位代码
     */
    private String storagePositionCode;
    /**
     * 出入库类型
     */
    private Integer inOutType;

    /**
     * 缺料类型
     */
    private Integer shortType;

    /**
     * 是否已结案
     */
    private Integer closeStatus;

    /**
     * 是否急件
     */
    private Integer isUrgent;

    /**
     * 车牌号
     */
    private String license;

    /**
     * 缺件数量
     */
    private BigDecimal shortQuantity;

    /**
     * 经手人
     */
    private String handler;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 电话
     */
    private String phone;


    /**
     * 发料时间
     */
    private Date sendTime;

    /**
     * 是否BO订单
     */
    private Integer isBo;
    /**
     * 缺料日期
     */
    private Date createdAt;
    /**
     * 是否背靠背
     */
    private Long isLinked;
    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 缺件状态
     */
    private Long missingPartsStatus;

    /**
     * 预计到货时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    private String expectedDeliveryTime;

    /**
     * 零件状态,替换件;block
     */
    private Long partsStatus;

    /**
     * 替换件号
     */
    private String replaceParts;
}
