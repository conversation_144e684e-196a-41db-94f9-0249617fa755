package com.volvo.maintain.application.maintainlead.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("上门取送车余额校验")
public class CarPickupDeliveryCheckBalanceResDto implements Serializable {
	
    private static final long serialVersionUID = 1L;

    /**
     * 提示类型 84141001-不弹窗,84141002-弹窗不拦截,84141003-弹窗拦截
     */
	@ApiModelProperty("提示类型")
    private String type;

    @ApiModelProperty("弹窗文案")
    private String popupMessage;
}
