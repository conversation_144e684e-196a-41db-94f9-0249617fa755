package com.volvo.maintain.application.maintainlead.dto.dtcclues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("ECU和DCT联动关系")
public class EcuDtcRelationDto {
    @ApiModelProperty(value = "ecu")
    protected String ecu;

    @ApiModelProperty(value = "DTC")
    protected String dtc;
}
