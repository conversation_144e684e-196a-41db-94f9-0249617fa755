package com.volvo.maintain.application.maintainlead.dto.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 卡券列表
 * 张善龙
 * 2024.11.4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("卡券查询")
public class QueryCouponDto {

    @ApiModelProperty(value = "会员id(和vin二选一)")
    private Integer memberId;

    @ApiModelProperty(value = "vin号集合(和memberId二选一)")
    private List<String> vinList;

    @ApiModelProperty(value = "所属机构(经销商code  or 集团组织id)")
    private String couponInstitution;

    @ApiModelProperty(value = "是否包含集团的券")
    private Integer includeGroup;

    @ApiModelProperty(value = "状态集合(卡券状态(31061001:已领取, 31061002:已锁定, 31061003:已使用, 31061004:已过期, 31061005:已作废,))")
    private List<Integer> ticketStateList;

    @ApiModelProperty(value = "使用场景集合(83171001:线上商城  83171002:线下门店  83171003:通用  83171004:E代驾 83171005: 权益激活  83171006:线下零售 83171007:卡密 83171008:三方品牌充电站 83171009:一键加电 83171010:充电桩 83171011:官方品牌充电站)")
    private List<Integer> useScenesList;

    @ApiModelProperty(value = "限制经销商集合")
    private List<String> limitDealer;

}
