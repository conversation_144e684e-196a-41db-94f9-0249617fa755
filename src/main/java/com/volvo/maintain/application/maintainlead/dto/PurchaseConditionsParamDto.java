package com.volvo.maintain.application.maintainlead.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 延保购买条件判断（售后中台 → newbie售后）vo
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
@ApiModel("延保购买条件判断（售后中台 → newbie售后）")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PurchaseConditionsParamDto {
    @ApiModelProperty(name = "mileage", value = "车辆里程(c)")
    private Integer mileage;
    @ApiModelProperty(name = "modelCode", value = "车型(中台)")
    private String modelCode;
    @ApiModelProperty(name = "vin", value = "Vin (c)")
    private String vin;
    @ApiModelProperty(name = "invoiceDate", value = "开票日期(中台)")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date invoiceDate;
    @ApiModelProperty(name = "invoicePrice", value = "车辆销售价格(中台)")
    private Double invoicePrice;
    @ApiModelProperty(name = "age", value = "车龄")
    private Integer age;
    @ApiModelProperty(name = "engineNo", value = "发动机代码(中台)")
    private String engineNo;
    @ApiModelProperty(name = "productNolList", value = "产品件号(c)")
    private List<String> productNoList;


}
