package com.volvo.maintain.application.maintainlead.vo;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <p>
 * 工单信息
 * </p>
 */
@Data
public class RepairOrderV2VO {

    private static final long serialVersionUID = 1L;
    
    /**
     * 工单号
     */
    private String roNo;
    /**
     * 工单开单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date roCreateDate;
    /**
     * 维修类型
     */
    private String repairTypeCode;
    /**
     * 代码
     */
    private String ownerCode;
    /**
     * 送修人
     */
    private String deliverer;
    
    /**
     * 工单状态
     */
    private Long roStatus;
    
    /**
     * 服务专员
     */
    private String serviceAdvisor;
    
    /**
     * 车主
     */
    private String ownerName;
    
    /**
     * 是否终检标识
     */
    private String finalInspectionTag;
    
    /**
     * 是否竣工
     */
    private Long completeTag;
}
