package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("车架号vin")
public class InviteTypeVinDto {

    @ApiModelProperty(value = "车架号",name = "vin")
    private String vin;

    @ApiModelProperty(value = "是否VIP ",name = "whetherVip")
    private Integer inviteType;

    @ApiModelProperty("线索类型")
    private String inviteTypeName;

}
