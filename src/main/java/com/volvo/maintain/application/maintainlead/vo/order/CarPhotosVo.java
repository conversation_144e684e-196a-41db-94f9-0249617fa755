package com.volvo.maintain.application.maintainlead.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 商户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "CarPhotosVO 对象", description = "CarPhotosVO")
public class CarPhotosVo {

    @ApiModelProperty(value = "车身是否较脏，1是，0否  ")
    private String car_dirty;
    @ApiModelProperty(value = "龟背图选中的点  ")
    private List<Integer> selected;
    @ApiModelProperty(value = "备注  ")
    private String comment;
    @ApiModelProperty(value = "车辆左前方照片  ")
    private String left_front;
    @ApiModelProperty(value = "车辆右前方照片  ")
    private String right_front;
    @ApiModelProperty(value = "接车单照片  ")
    private String certificate;
    @ApiModelProperty(value = "仪表盘照片  ")
    private String dashboard;
    @ApiModelProperty(value = "车内照片  ")
    private String interior;
    @ApiModelProperty(value = "后备箱照片  ")
    private String trunk;
    @ApiModelProperty(value = "受损细节,最多4张 ")
    private List<String> damage_detail;

}