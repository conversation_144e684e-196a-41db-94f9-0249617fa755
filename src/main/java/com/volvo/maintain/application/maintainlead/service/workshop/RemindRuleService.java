package com.volvo.maintain.application.maintainlead.service.workshop;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.QueryCustomInfoDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.RemindRuleDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.RuleInfoDto;
import com.volvo.maintain.application.maintainlead.vo.workshop.CustomUserInfoVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.RemindRuleDetailVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.RemindRuleVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.SpecialVehicleConfigVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 规则提醒服务
 * @Date 2024/11/14 16:12
 */
public interface RemindRuleService {

    /**
     * 分页查询规则配置列表
     * @param remindRuleDto
     * @return
     */
    Page<RemindRuleVo> queryRulePage(RemindRuleDto remindRuleDto);

    /**
     * 查询规则配置列表
     * @param remindRuleDto
     * @return
     */
    List<RemindRuleVo> queryRuleList(RemindRuleDto remindRuleDto);

    /**
     * 到处规则列表
     * @param queryParams
     */
    void exportRuleList(RemindRuleDto queryParams);

    /**
     * 查询规则配置详情
     * @param id
     * @return
     */
    RemindRuleDetailVo queryRuleDetail(Integer id);

    /**
     * 查询自定义人员信息
     * @param queryCustomInfoDto
     * @return
     */
    Page<CustomUserInfoVo> queryCustomUserInfoList(QueryCustomInfoDto queryCustomInfoDto);

    /**
     * 查询特殊车辆信息
     * @param queryCustomInfoDto
     * @return
     */
    Page<SpecialVehicleConfigVo> querySpecialVehicleConfigList(QueryCustomInfoDto queryCustomInfoDto);

    /**
     * 保存规则信息
     * @param ruleInfoDto
     */
    void saveRuleInfo(RuleInfoDto ruleInfoDto);

    /**
     * 获取透明车间通用下拉
     *
     * @return
     */
    Map<String, List<CommonConfigDto>> queryCommonConfigList();
}
