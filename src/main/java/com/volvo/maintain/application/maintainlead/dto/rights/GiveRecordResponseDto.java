package com.volvo.maintain.application.maintainlead.dto.rights;

import lombok.Data;

@Data
public class GiveRecordResponseDto {

    /**
     * 产品件号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * vin
     */
    private String vin;

    /**
     * 经销商
     */
    private String ownerCode;

    /**
     * 生效日期
     */
    private String effectiveDate;

    /**
     * 失效日期
     */
    private String expireDate;

    /**
     * 订单号
     */
    private String orderCode;

    /**
     * 购买状态 参考枚举：3503  35031001：待生效  35031002：生效中  35031003：生效成功  35031004：待人工介入  35031005：生效失败
     */
    private String giveStatus;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 渠道参考枚举：3504  35041001：APP  35041002：小程序  35041003：nb
     */
    private String giveChannel;

    /**
     * 保养活动编号
     */
    private String activityNo;

    /**
     * 保养活动名称
     */
    private String activityName;

    /**
     * 保养活动可用次数
     */
    private String availableTimes;

    /**
     * 保养活动剩余次数
     */
    private String activityLeftTimes;

    /**
     * 保养活动购买ID
     */
    private String id;

    /**
     * 保养活动类型
     */
    private String activityType;

    /**
     * 车辆权益车龄上限
     */
    private String carAgeMax;

    /**
     * 车辆权益里程上限
     */
    private String mileageMax;

    /**
     * 可使用范围
     */
    private Integer useScope;

    /**
     * 自定义经销商
     */
    private String useDealerCodes;
}
