package com.volvo.maintain.application.maintainlead.service;

import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.application.maintainlead.dto.EmailBodyDto;
import com.volvo.maintain.application.maintainlead.dto.PushMessageRecordDto;
import com.volvo.maintain.infrastructure.gateway.ApplicationAftersalesManagementFeign;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;

@Service
public class JobTaskGeneratorImpl implements JobTaskGenerator {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private ApplicationAftersalesManagementFeign applicationAftersalesManagementFeign;

    @Autowired
    private EM90ServiceLeadService em90ServiceLeadService;



    @Override
    public <T> void planCompensateAndPush(List<String> bizNo, String minute, BiFunction<List<String>, String , List<PushMessageRecordVo>> function) {
        List<PushMessageRecordVo> resultList = function.apply(bizNo,minute);
        logger.info("planCompensateAndPush->compensateQuery，共{}条", resultList.size());
        if (CollectionUtils.isNotEmpty(resultList)) {
            resultList.forEach(v -> {
                PushMessageRecordDto pushMessageRecordDto = new PushMessageRecordDto();
                BeanUtils.copyProperties(v,pushMessageRecordDto);
                logger.info("planCompensateAndPush->compensateQueryAndPushMessageRecordDto:{}", JSONObject.toJSONString(pushMessageRecordDto));
                String dataBaseBizNo = pushMessageRecordDto.getBizNo();
                EmailBodyDto emailBodyDto = JSONObject.parseObject(pushMessageRecordDto.getReqParams(), EmailBodyDto.class);
                if (bizNo.contains(dataBaseBizNo)) {
                    switch (dataBaseBizNo) {
                        case "360_FACE_CUSTOMER_REMINDER":    //360面客提醒
                            break;
                        case "EM90_BOOKIN_ORDER_REMINDER":    //EM90-代下预约单
                            break;
                        case "EM90_GT15M_NOT_STATION_REMINDER":   //EM90-开单后15分钟未上工位异常提醒
                            break;
                        case "EM90_GT15M_NOT_ALLOCATION_REMINDER":   //EM90-进店后15分钟未分拨异常提醒
                            break;
                        case "EM90_VEHICLE_ENTRY_REMINDER":      //EM90-车辆进店提醒
                            break;

                        case "EM90_MANUFACTURER_CLUE_REMINDER":   //EM90-厂端自建线索提醒
                        case "EM90_PART_CLUE_REMINDER":     //EM90-零附件线索提醒
                            String content = em90ServiceLeadService.templateContent(emailBodyDto,dataBaseBizNo);
                            pushMessageRecordDto.setContent(content);
                            applicationAftersalesManagementFeign.pushMessage(pushMessageRecordDto);
                            break;
                        case "EM90_MAINTAIN_CLUE_REMINDER":    //EM90-保养线索提醒
                            break;
                        default:
                            break;
                    }
                }
            });
        }
    }
}
