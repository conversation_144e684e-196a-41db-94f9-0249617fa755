package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 模版表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkshopCallRecordDto {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 服务专员
     */
    private String serviceAdvisor;

    /**
     * 通话时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date talkTime;

    /**
     * 通话时长
     */
    private Integer callDuration;

    /**
     * 删除标识（0-未删除，1-已删除）
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建人
     */
    private String createSqlby;

    /**
     * 更新人
     */
    private String updateSqlby;
}