package com.volvo.maintain.application.maintainlead.dto.workshop;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BookingOrderParamDto {

    /**
     * 已到店
     */
    private Integer bookingArrived;

    /**
     * 未到店
     */
    private Integer bookingNotArrived;

    /**
     * 全部
     */
    private Integer bookingAll;

    private Integer status;

    private Integer count;
}
