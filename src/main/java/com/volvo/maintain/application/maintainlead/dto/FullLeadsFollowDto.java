package com.volvo.maintain.application.maintainlead.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 功能描述：全量线索跟进保存dto对象
 *
 * <AUTHOR>
 * @since 2024/01/02
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FullLeadsFollowDto", description = "全量线索跟进保存dto对象")
public class FullLeadsFollowDto {

    @ApiModelProperty(value = "线索id")
    private Long id;

    @ApiModelProperty(value = "vin")
    private String vin;

    @ApiModelProperty(value = "线索类型")
    private Integer inviteType;

    @ApiModelProperty(value = "跟进状态")
    private Integer status;

    @ApiModelProperty(value = "跟进方式")
    private Integer mode;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "下次跟进日期、二次预约时间", name = "planDate")
    private Date planDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "建议进厂时间")
    private Date adviseInDate;

    @ApiModelProperty("预约进店时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date forecastTime;

    @ApiModelProperty(value = "跟进内容")
    private String content;

    @ApiModelProperty(value = "失败原因")
    private Integer loseReason;

    @ApiModelProperty(value = "客户反馈")
    private String feedback;

    @ApiModelProperty(value = "未使用AI原因")
    private String noAIReason;

    @ApiModelProperty(value = "线索异常")
    private Integer errorStatus;

    @ApiModelProperty(value = "批次号")
    private String batchNo;

    @ApiModelProperty(value = "不需跟进原因")
    private Integer notFollowReason;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "故障灯失败原因")
    private String failureReason;

    @ApiModelProperty(value = "关联工单号")
    private String roNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "工单开始时间")
    private Date roStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "工单结束日期")
    private Date roEndTime;

    @ApiModelProperty(value = "工单类型")
    private String roType;

    @ApiModelProperty(value = "结算金额")
    private String amount;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    @ApiModelProperty(value = "二次预约时间")
    private Date reInviteTime;

    @ApiModelProperty(value = "故障灯是否首次跟进")
    private boolean isFirstFollow;

    @ApiModelProperty(value = "邀约响应时间(h)")
    private String inviteResTime;

    @ApiModelProperty(value = "联络是否超时: 0.无 1.否 2.是")
    private Integer inviteOvertime;

    @ApiModelProperty(value = "首次跟进日期")
    private Date inviteTime;

    @ApiModelProperty(value = "故障灯状态")
    private Integer faultLightStatus;

    @ApiModelProperty(value = "选中标识（参见枚举：1004）")
    private Integer selectStatus;
    @ApiModelProperty(value = "是否白名单")
    private Boolean isWhite;
}
