package com.volvo.maintain.application.maintainlead.dto.dtcclues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("DTC线索类别")
public class DtcCluesCategoryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    protected Long id;

    @ApiModelProperty(value = "故障类别")
    protected String faultCategory;

    @ApiModelProperty(value = "ecu")
    protected String ecu;

    @ApiModelProperty(value = "DTC")
    protected String dtc;

    @ApiModelProperty(value = "Confirm状态 （0-全部，1-是, 2-否）")
    protected Integer confirmStatus;

    @ApiModelProperty(value = "Indicator状态 （0-全部，1-是, 2-否）")
    protected Integer indicatorStatus;

    @ApiModelProperty(value = "优先级")
    protected Integer priority;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "车型ID")
    private String vehicleModelIds;
}
