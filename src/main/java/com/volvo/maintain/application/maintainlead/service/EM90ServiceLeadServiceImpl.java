package com.volvo.maintain.application.maintainlead.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.message.MessageSendDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import com.volvo.maintain.application.maintainlead.vo.InviteVehicleRecordVo;
import com.volvo.maintain.application.maintainlead.vo.PadVehiclePreviewResultVo;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderVO;
import com.volvo.maintain.application.maintainlead.vo.UserInfoVo;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.enums.InviteTypeEnum;
import com.volvo.maintain.infrastructure.enums.RepairTypeEum;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ImResponse;
import com.volvo.maintain.infrastructure.util.DateUtil;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;
import com.volvo.maintain.interfaces.vo.VehicleEntranceVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能描述：EM90服务线索接口
 *
 * <AUTHOR>
 * @since 2024/02/22
 */
@Service
@Slf4j
public class EM90ServiceLeadServiceImpl implements EM90ServiceLeadService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    @Autowired
    private DomainMaintainLeadFeign maintainLeadsFegin;
    @Autowired
    private ApplicationAftersalesManagementFeign applicationAftersalesManagementFeign;
    @Autowired
    private DomainMaintainLeadFeign domainMaintainLeadFeign;

    @Autowired
    private MidEndOrgCenterFeign midEndOrgCenterFegin;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ApplicationMaintenanceentertainFeign applicationMaintenanceentertainFeign;
    @Autowired
    private MidEndAuthCenterFeign midEndAuthCenterFeign;

    @Autowired
    private CommonMethodService commonMethodService;

    @Autowired
    JobTaskGenerator jobTaskGenerator;

    public static final String STATUS_CODE = "200";
    /**
     * 功能描述：根据车牌号查询对应vin
     *
     * @param vehicleEntranceVO 车牌号
     * @return vin
     */
    @Override
    public String queryOwnerVehicle(VehicleEntranceVo vehicleEntranceVO) {
        log.info("MQ车辆进厂信息，padVehiclePreviewDto:{}", vehicleEntranceVO);
        Objects.requireNonNull(vehicleEntranceVO, "queryOwnerVehicle vehicleEntranceVO isNull");
        PadVehiclePreviewDto padVehiclePreviewDto = new PadVehiclePreviewDto();
        padVehiclePreviewDto.setOwnerCode(vehicleEntranceVO.getDealerCode());
        padVehiclePreviewDto.setLicense(vehicleEntranceVO.getLicensePlate());
        DmsResponse<PadVehiclePreviewResultVo> pageDtoDmsResponse = dmscloudServiceFeign.queryOwnerVehicle(padVehiclePreviewDto);
        log.info("车辆feigon信息，pageDtoDmsResponse:{}", pageDtoDmsResponse);
        if(pageDtoDmsResponse == null || !STATUS_CODE.equals(pageDtoDmsResponse.getResultCode())){
            return null;
        }
        PadVehiclePreviewResultVo data = pageDtoDmsResponse.getData();
        log.info("车辆主信息，data:{}", data);
        if(null == data){
            return null;
        }
        return Objects.isNull(data.getVin()) ? "" : data.getVin();
    }

    /**
     * 功能描述：查询当前车辆在本店中是否有未结算的维修工单（非PDS）
     *
     * @param dealerCode 经销商信息
     * @param vin        车架号
     * @return RepairOrderVO 工单信息
     */
    @Override
    public RepairOrderVO queryUnsettledRepairOrder(String ownerCode, String vin) {
        DmsResponse<RepairOrderVO> dmsResponse = dmscloudServiceFeign.queryUnsettledRepairOrder(ownerCode, vin);
        if (dmsResponse.isFail()) {
            throw new ServiceBizException("当前车辆在本店中是否有未结算的维修工单查询失败:{}", JSONObject.toJSONString(dmsResponse));
        }
        return dmsResponse.getData();
    }

    /**
     * 功能描述：推送EM90专属管家
     *
     * @param content 推送内容
     */
    @Override
    public void pushQwExclusiveButler(String vin, String content) {
        DmsResponse<QwJSSDKDto> response = applicationAftersalesManagementFeign.pushQwExclusiveButler(vin, content);
        if (response.isFail()) {
            throw new ServiceBizException("推送EM90专属管家失败:{}", JSONObject.toJSONString(response));
        }
    }

    @Override
    public Integer insertPushMessageRecord(Integer sinceType, String subBizNo, String bizNo) {
        PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
        pushMessageRecordVo.setTaskStatus(0);
        pushMessageRecordVo.setBizNo(bizNo);
        pushMessageRecordVo.setSubBizNo(subBizNo);
        pushMessageRecordVo.setSinceType(sinceType);
        log.info("新增消息推送记录:{}", pushMessageRecordVo);
        DmsResponse<Integer> integerDmsResponse = maintainLeadsFegin.insertPushMessageRecord(pushMessageRecordVo);
        if (integerDmsResponse.isFail()) {
            throw new ServiceBizException("新增消息推送记录失败:{}", JSONObject.toJSONString(integerDmsResponse));
        }
        return integerDmsResponse.getData();
    }

    @Override
    public void updatePushMessageRecord(Integer pushId, boolean flag) {
        PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
        if (flag) {
            pushMessageRecordVo.setTaskStatus(1);
        } else {
            pushMessageRecordVo.setTaskStatus(2);
        }
        pushMessageRecordVo.setId(pushId);
        log.info("更新消息推送记录:{}", pushMessageRecordVo);
        maintainLeadsFegin.updatePushMessageRecord(pushMessageRecordVo);
    }

    @Override
    public Boolean queryConfig(String configKey,String modelCode) {
        DmsResponse<CommonConfigDto> result = dmscloudServiceFeign.getConfigByKey(configKey, CommonConstant.EM90_GROUP_TYPE);
        if (result.isFail()) {
            throw new ServiceBizException("查询系统配置失败:{}", JSONObject.toJSONString(result));
        }
        if (!ObjectUtils.isEmpty(result.getData()) && result.getData().getConfigValue().equals(modelCode)) {
            return true;
        }
        return false;
    }

    @Override
    public void EM90MaintenanceClueReminder() {
        String content = "车辆【${license}、${vin}】有一条邀约保养线索【${inviteName}】，其建议进场日期：${adviseInDate}，跟进经销商【${ownerCode}】";
        //获取保养灯线索
        InviteVehicleRecordDto inviteVehicleRecordDto = new InviteVehicleRecordDto();
        inviteVehicleRecordDto.setBeginTime(DateUtil.getNowBegin());
        inviteVehicleRecordDto.setEndTime(DateUtil.getNowEnd());
        DmsResponse<List<InviteVehicleRecordVo>> inviteVehicleRecordVos = domainMaintainLeadFeign.queryMaintenanceLightClues(inviteVehicleRecordDto);
        log.info("EM90MaintenanceClueReminder inviteVehicleRecordVos response:{} ", JSONObject.toJSONString(inviteVehicleRecordVos));
        if (inviteVehicleRecordVos.isFail()) {
            throw new ServiceBizException("获取保养灯线索失败:{}", JSONObject.toJSONString(inviteVehicleRecordVos));
        }
        //保养灯线索去重
        Map<String, List<InviteVehicleRecordVo>> dataMap = inviteVehicleRecordVos
                .getData()
                .stream()
                .collect(Collectors.groupingBy(i -> i.getVin() + "_" + i.getLicensePlateNum() + "_" + i.getInviteType() + "_" + DateUtil.formatDateByFormat(i.getAdviseInDate(), DateUtil.SIMPLE_DATE_FORMAT)));
        log.info("保养灯线索Map:{}", JSONObject.toJSONString(dataMap));

        dataMap.forEach((k, v) -> {
            try {
                sendmsg(content, k, v);
            } catch (Exception e) {
                log.info("{}", e);
            }
        });
    }

    private void sendmsg(String content, String k, List<InviteVehicleRecordVo> v) {
        StringBuilder onwerCodes = new StringBuilder();
        StringBuilder onwerCodes2 = new StringBuilder();
        v.forEach(i -> {
            onwerCodes.append(i.getDealerCode());
            onwerCodes.append("+");
            onwerCodes.append(queryDealerName(i.getDealerCode()));
            onwerCodes.append("，");

            onwerCodes2.append(i.getDealerCode());
            onwerCodes2.append("|");
        });
        String onwerCode = onwerCodes.toString();
        if (onwerCode.contains("，")) {
            onwerCode = onwerCode.substring(0, onwerCode.length() - 1);
        }
        String[] ks = k.split("_");
        log.info("onwerCode:{},ks:{}", onwerCode, ks);
        Map<String, String> map = new HashMap<>();
        map.put("vin", ks[0]);
        map.put("license", ks[1]);
        map.put("inviteName", InviteTypeEnum.getNameByCode(String.valueOf(ks[2])));
        map.put("adviseInDate", ks[3]);
        map.put("ownerCode", onwerCode);
        PushMessageRecordDto pushMessageRecordDto = new PushMessageRecordDto();
        String pushContent = replacePlaceholders(map, content);
        String subBizNo = ks[0].concat(ks[2]).concat(ks[3]).concat("|").concat(onwerCodes2.toString());
        if (subBizNo.length() > 100) {
            subBizNo = subBizNo.substring(0, 100);
        }
        log.info("pushContent:{},subBizNo:{}", pushContent, subBizNo);
        pushMessageRecordDto.setBizNo(CommonConstant.EM90_MAINTAIN_CLUE_REMINDER);
        pushMessageRecordDto.setSubBizNo(subBizNo);
        pushMessageRecordDto.setSinceType(6);
        EmailBodyDto emailBodyDto = new EmailBodyDto();
        emailBodyDto.setVin(ks[0]);
        pushMessageRecordDto.setReqParams(JSONObject.toJSONString(emailBodyDto));
        pushMessageRecordDto.setContent(pushContent);
        DmsResponse<String> vehicleByUserId = applicationAftersalesManagementFeign.getVehicleByUserId(ks[0]);
        if(vehicleByUserId.isFail() && StringUtils.isBlank(vehicleByUserId.getData())){
            throw new ServiceBizException("获取服务顾问失败:{}", JSONObject.toJSONString(vehicleByUserId));
        }
        pushMessageRecordDto.setUserId(vehicleByUserId.getData());
        //根据vin获取车辆信息
        TmVehicleDto tmVehicleDto = commonMethodService.queryVehicle(ks[0]);
        log.info("服务线索对应车辆信息:{}", JSONObject.toJSONString(tmVehicleDto));
        //获取本地配置
        Boolean flag = this.queryConfig(CommonConstant.EM90_CONFIG_KEY,tmVehicleDto.getModelCode());
        if (flag) {
            applicationAftersalesManagementFeign.pushMessage(pushMessageRecordDto);
        }
    }

    @Override
    public String queryDealerName(String dealerCode) {
        IsExistByCodeDto isExistByCodeDTO = new IsExistByCodeDto();
        isExistByCodeDTO.setCompanyCode(dealerCode);
        ResponseDto<List<CompanyDetailByCodeDto>> listResponseDto = midEndOrgCenterFegin.selectByCompanyCode(isExistByCodeDTO);
        log.info("根据经销商code查询经销商信息，listResponseDto:{}", listResponseDto);

        List<CompanyDetailByCodeDto> listResponseDtoData = listResponseDto.getData();
        log.info("根据经销商code查询经销商信息，listResponseDtoData:{}", listResponseDtoData);

        CompanyDetailByCodeDto companyDetailByCodeDTO = listResponseDtoData.get(0);
        log.info("根据经销商code查询经销商信息，companyDetailByCodeDTO:{}", companyDetailByCodeDTO);
        return companyDetailByCodeDTO.getCompanyShortNameCn();
    }


    public String replacePlaceholders(Map<String, String> replacements, String content) {
        // 遍历替换值的映射，依次替换占位符
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            if (StringUtils.isNotEmpty(entry.getValue())){
                content = content.replace(placeholder, entry.getValue());
            }else {
                content = content.replace(placeholder, "null");
            }
        }
        return content;
    }

    @Override
    public void EM90UpperWorkstationToBeAllocated(Integer beginTimeOut, Integer endTimeOut) {
        try {
            String beginTime = DateUtil.queryNowTime(beginTimeOut);
            String endTime = DateUtil.queryNowTime(endTimeOut);
            //未分拨车辆推送
            UnallocatedVehiclePush(beginTime, endTime, endTimeOut);
            //上工位车辆推送
            EM90UpperWorkstationPush(endTimeOut);
        }catch (Exception e){
            log.info("EM90UpperWorkstationToBeAllocated error:{}",JSONObject.toJSONString(e));
        }

    }

    private void EM90UpperWorkstationPush(Integer endTimeOut) {
        //查询未上工位的维修工单
        DmsResponse<List<RepairOrderVO>> response = dmscloudServiceFeign.notOnWorkShopOrder();
        log.info("未上工位维修工单:{}",JSONObject.toJSONString(response));
        if (response.isFail()) {
            throw new ServiceBizException("查询未上工位的维修工单失败：{}", JSONObject.toJSONString(response));
        }
        response.getData().stream().forEach(i -> {
            try {
            //根据vin获取车辆信息
            TmVehicleDto tmVehicleDto = commonMethodService.queryVehicle(i.getVin());
            log.info("上工位车辆信息:{}", JSONObject.toJSONString(tmVehicleDto));
            //获取本地配置
            Boolean flag = this.queryConfig(CommonConstant.EM90_CONFIG_KEY,tmVehicleDto.getModelCode());
            if (flag) {
                String content = "【${license}、${vin}】本次开单后已超过${timeout}分钟还未上工位。其中送修人【${deliverer}、${delivererMobile}】，工单信息【${roNo}、${repairTypeName}】，服务顾问【${saName}、${saMobile}】、店信息【${ownerName}+${ownerCode}】，开单时间：${roCreateDate}";
                //获取服务顾问id
                DmsResponse<String> result = applicationAftersalesManagementFeign.getVehicleByUserId(i.getVin());
                log.info("服务顾问id:{}",JSONObject.toJSONString(result));
                if (result.isFail()) {
                    throw new ServiceBizException("获取服务顾问失败:{}", JSONObject.toJSONString(response));
                }
                String dearlerName = this.queryDealerName(i.getOwnerCode());
                //获取服务顾问信息
                ResponseDto<UserInfoVo> userInfoVoResponseDto = midEndAuthCenterFeign.queryUserInfoById(result.getData());
                log.info("服务顾问信息:{}", JSONObject.toJSONString(userInfoVoResponseDto));
                Map<String, String> map = new HashMap<>();
                map.put("license", i.getLicense());
                map.put("vin", i.getVin());
                map.put("deliverer", i.getDeliverer());
                map.put("delivererMobile", i.getDelivererMobile());
                map.put("roNo", i.getRoNo());
                map.put("saName", userInfoVoResponseDto.getData().getEmployeeName());
                map.put("saMobile", userInfoVoResponseDto.getData().getPhone());
                map.put("repairTypeName", RepairTypeEum.getCodeByValue(i.getRepairTypeCode()));
                map.put("ownerName", dearlerName);
                map.put("ownerCode", i.getOwnerCode());
                map.put("roCreateDate", DateUtil.formatDefaultDateTimes(i.getRoCreateDate()));
                map.put("timeout", String.valueOf(endTimeOut));
                //推送企微
                pushQw(i, content, map);
                //推送短信
                MessageSendDto messageSendDto = new MessageSendDto();
                messageSendDto.setMobiles(userInfoVoResponseDto.getData().getPhone());
                messageSendDto.setParamMap(map);
                messageSendDto.setTemplateId("aa6f1f6c-c16b-435d-9054-d3ed38497572");
                PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
                pushMessageRecordVo.setSinceType(2);
                pushMessageRecordVo.setBizNo(CommonConstant.EM90_GT15M_NOT_STATION_REMINDER);
                pushMessageRecordVo.setSubBizNo(i.getOwnerCode()+"/"+i.getRoNo());
                commonMethodService.pushSms("EM90UpperWorkstationPush", pushMessageRecordVo, messageSendDto);
            }
            }catch (Exception e){
                log.info("EM90UpperWorkstationPush error:{}",JSONObject.toJSONString(e));
            }
        });

    }

    private void pushQw(RepairOrderVO i, String content, Map<String, String> map) {
        try {
            PushMessageRecordDto pushMessageRecordDto = new PushMessageRecordDto();
            String pushContent =replacePlaceholders(map, content);
            pushMessageRecordDto.setBizNo(CommonConstant.EM90_GT15M_NOT_STATION_REMINDER);
            pushMessageRecordDto.setSubBizNo(i.getOwnerCode() + "/" + i.getRoNo());
            pushMessageRecordDto.setSinceType(6);
            EmailBodyDto emailBodyDto = new EmailBodyDto();
            emailBodyDto.setVin(i.getVin());
            pushMessageRecordDto.setReqParams(JSONObject.toJSONString(emailBodyDto));
            pushMessageRecordDto.setContent(pushContent);
            DmsResponse<String> vehicleByUserId = applicationAftersalesManagementFeign.getVehicleByUserId(i.getVin());
            if(vehicleByUserId.isFail() && StringUtils.isBlank(vehicleByUserId.getData())){
                throw new ServiceBizException("获取服务顾问失败:{}", JSONObject.toJSONString(vehicleByUserId));
            }
            pushMessageRecordDto.setUserId(vehicleByUserId.getData());
            applicationAftersalesManagementFeign.pushMessage(pushMessageRecordDto);
        }catch (Exception e){
            log.info("EM90上工位异常推送企微失败:{}",JSONObject.toJSONString(e));
        }

    }


    private void UnallocatedVehiclePush(String beginTime, String endTime, Integer endTimeOut) {
        try {
            String content = "【${license}、${vin}】EM90车辆于${arrivalTime}进店【${ownerName}+${ownerCode}】，已超过${timeout}分钟未分拨";
            Map<String, List<PushMessageRecordVo>> mapData = getMapData(beginTime, endTime);
            if (mapData == null) return;
            mapData.forEach((k, v) -> {
                try {
                    if (CollectionUtils.isNotEmpty(v)) {
                        //集合根据时间排序
                        v.stream()
                                .map(PushMessageRecordVo::getSubBizNoEnd_)
                                .min(String::compareTo)
                                .orElse("");
                    }else {
                        log.info("UnallocatedVehiclePush value isEmpty");
                        return;
                    }
                    log.info("UnallocatedVehiclePush list:{}", JSONObject.toJSONString(v));
                    log.info("UnallocatedVehiclePush mapData key:" + k);
                    if (StringUtils.isBlank(v.get(0).getSubBizNoEnd_())){
                        return;
                    }
                    String entryTime = v.get(0).getSubBizNoEnd_();
                    String[] sb = k.split("/");
                    log.info("UnallocatedVehiclePush sb size:" + sb.length);
                    if (sb.length < 2) {
                        return;
                    }
                    String dealerCode = sb[0];
                    String licensePlate = sb[1];
                    log.info("UnallocatedVehiclePush dealerCode:{},licensePlate:{},entryTime:{}", dealerCode, licensePlate, entryTime);
                    if (StringUtils.isBlank(dealerCode) || StringUtils.isBlank(licensePlate) || StringUtils.isBlank(entryTime)) {
                        log.info("dealerCode isBlank,licensePlate isBlank,entryTime isBlank");
                        return;
                    }
                    //查询未分拨车辆
                    ImResponse<VehicleEntranceVo> vehicleEntranceVOImResponse = applicationMaintenanceentertainFeign.queryUnallocatedVehicleList(dealerCode, licensePlate, entryTime);
                    log.info("UnallocatedVehiclePush 未分拨车辆数据：{}", JSONObject.toJSONString(vehicleEntranceVOImResponse));
                    if (vehicleEntranceVOImResponse.isFail()) {
                        log.info("queryUnallocatedVehicleList error");
                        return;
                    }
                    DmsResponse<String> vehicleByUserId = applicationAftersalesManagementFeign.getVehicleByUserId(vehicleEntranceVOImResponse.getData().getVin());
                    log.info("专属服务顾问id:{}", JSONObject.toJSONString(vehicleByUserId));
                    if (vehicleByUserId.isFail() || StringUtils.isBlank(vehicleByUserId.getData())) {
                        log.info("获取服务顾问失败:{}", JSONObject.toJSONString(vehicleByUserId));
                        return;
                    }
                    //获取经销商名称
                    String dearlerName = this.queryDealerName(dealerCode);
                    Map<String, String> map = new HashMap<>();
                    map.put("license", licensePlate);
                    map.put("vin", vehicleEntranceVOImResponse.getData().getVin());
                    map.put("arrivalTime", DateUtil.formatDefaultDateTimes(vehicleEntranceVOImResponse.getData().getEntryTime()));
                    map.put("ownerName", dearlerName);
                    map.put("ownerCode", dealerCode);
                    map.put("timeout", String.valueOf(endTimeOut));
                    PushMessageRecordDto pushMessageRecordDto = new PushMessageRecordDto();
                    String pushContent = replacePlaceholders(map, content);
                    pushMessageRecordDto.setBizNo(CommonConstant.EM90_GT15M_NOT_ALLOCATION_REMINDER);
                    pushMessageRecordDto.setSubBizNo(dealerCode + "/" + licensePlate+"/"+entryTime);
                    log.info("EM90_GT15M_NOT_ALLOCATION_REMINDER subBizNo:"+pushMessageRecordDto.getSubBizNo());
                    pushMessageRecordDto.setSinceType(6);
                    EmailBodyDto emailBodyDto = new EmailBodyDto();
                    emailBodyDto.setVin(vehicleEntranceVOImResponse.getData().getVin());
                    pushMessageRecordDto.setReqParams(JSONObject.toJSONString(emailBodyDto));
                    pushMessageRecordDto.setContent(pushContent);
                    pushMessageRecordDto.setUserId(vehicleByUserId.getData());
                    applicationAftersalesManagementFeign.pushMessage(pushMessageRecordDto);
                } catch (Exception e) {
                    log.info("UnallocatedVehiclePush error:", JSONObject.toJSONString(e));
                }
            });

        } catch (Exception e) {
            log.info("EM90未分拨推送企微失败:{}", JSONObject.toJSONString(e));
        }

    }

    private Map<String, List<PushMessageRecordVo>> getMapData(String beginTime, String endTime) {
        //获取EM90进店推送总条数
        DmsResponse<Integer> totalCount = domainMaintainLeadFeign.queryPushMessageRecordCount(beginTime, endTime, CommonConstant.EM90_VEHICLE_ENTRY_REMINDER);
        log.info("获取EM90进店推送总条:{}", totalCount);
        if (totalCount.isFail()) {
            throw new ServiceBizException("获取EM90进店推送总条数失败:{}", JSONObject.toJSONString(totalCount));
        }
        List<PushMessageRecordVo> pushMessageRecordVos = new ArrayList<>();
        Page page = new Page();
        page.setSize(CommonConstant.EM90_BATCH);
        page.setTotal(totalCount.getData());
        Integer pages = Integer.valueOf(String.valueOf(page.getPages()));
        //分页获取推送记录
        for (int i = 0; i < pages; i++) {
            DmsResponse<Page<PushMessageRecordVo>> listDmsResponse = domainMaintainLeadFeign.queryPushMessageRecordList(i, pages, beginTime, endTime, CommonConstant.EM90_VEHICLE_ENTRY_REMINDER);
            if (listDmsResponse.isFail()) {
                throw new ServiceBizException("获取车辆进店推送记录失败:{}", JSONObject.toJSONString(listDmsResponse));
            }
            pushMessageRecordVos.addAll(listDmsResponse.getData().getRecords());
        }

        if (org.springframework.util.CollectionUtils.isEmpty(pushMessageRecordVos)) {
            logger.info("pushMessageRecordVos isEmpty");
            return null;
        }
        //由于车辆进店就会新增一条推送记录 故此根据子业务编号去重
        Map<String, List<PushMessageRecordVo>> mapData = pushMessageRecordVos.stream().collect(Collectors.groupingBy(PushMessageRecordVo::getSubBizNo_));
        log.info("UnallocatedVehiclePush mapData:{}", JSONObject.toJSONString(mapData));
        return mapData;
    }


    /**
     * 补偿消息推送
     * @param bizNo 业务号
     */
    @Override
    public void compensateMessagePush(List<String> bizNo, String minutes) {
        jobTaskGenerator.planCompensateAndPush(bizNo,minutes,this::queryPushMessageRecordVo);
    }

    /**
     * 查询推送记录   补偿查询
     * @return 推送记录
     */
    private List<PushMessageRecordVo> queryPushMessageRecordVo(List<String> bizNo ,String minutes) {
        DmsResponse<List<PushMessageRecordVo>> response = domainMaintainLeadFeign.queryPushMessageRecordList(bizNo,minutes);
        if (response.isFail() || CollectionUtils.isEmpty(response.getData())) {
            return Collections.emptyList();
        }
        List<PushMessageRecordVo> data = response.getData();
        data.forEach(s -> {
            EmailBodyDto emailBodyDto = JSONObject.parseObject(s.getReqParams(), EmailBodyDto.class);
            if (Objects.isNull(emailBodyDto)) {
                return;
            }
            DmsResponse<String> vehicleByUserId = applicationAftersalesManagementFeign.getVehicleByUserId(emailBodyDto.getVin());
            if(vehicleByUserId.isFail() && StringUtils.isBlank(vehicleByUserId.getData())){
                log.info("queryPushMessageRecordVoQueryServiceAdvisorFail:{}correspondingVin:{}",JSONObject.toJSONString(vehicleByUserId),emailBodyDto.getVin());
                return;
            }
            s.setUserId(vehicleByUserId.getData());
        });
        return data;
    }


    /**
     * 模板内容 -厂端线索
     * @param dto 消息对象
     * @return 消息内容
     */
    public String templateContent(EmailBodyDto dto,String bizNo){
        String template;
        if (CollectionUtils.isNotEmpty(dto.getDealerList())) {
            template = "车辆【%s、%s】有一条零附件线索，其建议进场日期：%s，跟进经销商【%s】";
            String dealerInfos = String.join(" ， ", dto.getDealerList());
            return String.format(template, dto.getLicense(), dto.getVin(), dto.getAdviseDate(), dealerInfos);
        }
        String bizNoManufacturer = "EM90_MANUFACTURER_CLUE_REMINDER";
        logger.info("templateContent->dto:{}",JSONObject.toJSONString(dto));
        String contentZero = "车辆【${license}、${vin}】有一条零附件线索，其建议进场日期：${adviseDate}，跟进经销商【${dealerCode}+${dealerName}】";
        String contentVCDC = "车辆【${license}、${vin}】有一条厂端自建线索，其建议进场日期：${adviseDate}，跟进经销商【${dealerCode} + ${dealerName}】";
        template = bizNo.equals(bizNoManufacturer) ? contentVCDC : contentZero ;
        return replacePlaceholders(emailDtoToMap(dto),template);
    }

    /**
     * 反射对象转map
     * @param object 对象
     * @return map key属性，value值
     */
    public Map<String, String> emailDtoToMap(Object object) {
        Map<String, String> map = new HashMap<>();
        Field[] fields = object.getClass().getDeclaredFields(); // 获取当前类的所有字段
        for (Field field : fields) {
            try {
                field.setAccessible(true); // 设置字段可访问，即便它们是private的
                Object value = field.get(object); // 获取字段的值
                if (value instanceof Map) {
                    ((Map<?, ?>) value).forEach((key, val) -> {
                        if (val != null) { // 在 Map 内部也检查来跳过 null 值
                            String mapKey = key.toString();
                            String mapValue = val.toString();
                            // 如果Map中已有同名的key，则追加值，否则直接放置
                            map.merge(mapKey, mapValue, (oldVal, newVal) -> oldVal + "," + newVal);
                        }
                    });
                } else {
                    String fieldKey = field.getName();
                    String fieldValue = String.valueOf(value);
                    // 如果Map中已有同名的key，则追加值，否则直接放置
                    map.merge(fieldKey, fieldValue, (oldVal, newVal) -> oldVal + "," + newVal);
                }
            } catch (IllegalAccessException e) {
                logger.info("转换失败:{}", e.getMessage());
            }
        }
        return map;
    }
}
