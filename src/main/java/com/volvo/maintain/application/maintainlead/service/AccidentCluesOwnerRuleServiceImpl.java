package com.volvo.maintain.application.maintainlead.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.volvo.annotation.DistributedLock;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.AccidentClueCrmInfoMqDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.ClueDataDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadOperationResultDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.OwnerRuleDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.SaveOwnerRuleDto;
import com.volvo.maintain.application.maintainlead.vo.AccidentCluesVo;
import com.volvo.maintain.infrastructure.constants.AccidentCluesConstant;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainAuthFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainLeadFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndAuthCenterFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.util.StringUtil;
import com.yonyou.cyx.framework.util.bean.ApplicationContextHelper;
import com.yonyou.cyx.function.exception.ServiceBizException;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.SELECT_OWNER_RULE;

@Service
@Slf4j
public class AccidentCluesOwnerRuleServiceImpl implements AccidentCluesOwnerRuleService {
	@Qualifier("thread360Pool")
	@Autowired
	private ThreadPoolTaskExecutor thread360Pool;

	@Autowired
	private DomainMaintainOrdersFeign domainMaintainOrdersFeign;

	@Autowired
	private DomainMaintainLeadFeign domainMaintainLeadFeign;

	@Autowired
	private MidEndAuthCenterFeign midEndAuthCenterFeign;
	@Lazy
	@Resource
	AccidentCluesServiceImpl accidentCluesService;

	@Autowired
	private DomainMaintainAuthFeign domainMaintainAuthFeign;
	@Autowired
	protected RedissonClient redissonClient;

	/**
	 * 批量-查询配置信息并组合数据，用于初始化
	 */
	@Override
	public void selectOwnerRules(List<String> ownerCodes, Integer ruleType) {
		log.info("selectOwnerRules start ownerCodes:{},ruleType:{}", ownerCodes, ruleType);
		if (CollectionUtils.isEmpty(ownerCodes) || null == ruleType) {
			log.info("selectOwnerRules ownerCodes isEmpty or ruleType isNull");
			return;
		}
		for (String ownerCode : ownerCodes) {
			selectOwnerRule(ownerCode, ruleType);
		}
		log.info("selectOwnerRules start");
	}

	/**
	 * 查询配置信息并组合数据
	 */

	@Override
	public List<OwnerRuleDto> selectOwnerRule(String ownerCode, Integer ruleType) {
		try {
			// 代理调用，否则 DistributedLock 不生效
			AccidentCluesOwnerRuleServiceImpl proxy = ApplicationContextHelper.getBeanByType(AccidentCluesOwnerRuleServiceImpl.class);
			return proxy.selectOwnerRule_(ownerCode, ruleType);
		} catch (Exception e) {
			log.info("selectOwnerRule e:{}", e);
			throw new ServiceBizException(e.getMessage());
		}
	}
	@Override
	public List<OwnerRuleDto> selectOwnerRuleNew(String ownerCode, Integer ruleType) {
		try {
			log.info("selectOwnerRule redis:{},{}", ownerCode, ruleType);
			RBucket<List<OwnerRuleDto>> bucket = redissonClient.getBucket(SELECT_OWNER_RULE + ownerCode + ":" + ruleType);
			List<OwnerRuleDto> ownerRuleDtos = bucket.get();
			if(!CollectionUtils.isEmpty(ownerRuleDtos)){
				log.info("selectOwnerRule redis:{}", ownerRuleDtos);
				return ownerRuleDtos;
			}
			// 代理调用，否则 DistributedLock 不生效
			AccidentCluesOwnerRuleServiceImpl proxy = ApplicationContextHelper.getBeanByType(AccidentCluesOwnerRuleServiceImpl.class);
			List<OwnerRuleDto> ownerRuleDtos1 = proxy.selectOwnerRule_(ownerCode, ruleType);
			bucket.set(ownerRuleDtos1, 10, TimeUnit.MINUTES);
			return ownerRuleDtos1;
		} catch (Exception e) {
			log.info("selectOwnerRule e:{}", e);
			throw new ServiceBizException(e.getMessage());
		}
	}

	@DistributedLock(prefix = "accidentCluesOwnerRule:lock:selectOwnerRule:", key = {"#ownerCode", "#ruleType"}, delimiter = "-")
	public List<OwnerRuleDto> selectOwnerRule_(String ownerCode, Integer ruleType) throws Exception {
		log.info("selectByOwnerCode start ownerCode:{},ruleType:{}", ownerCode, ruleType);
		Objects.requireNonNull(ownerCode, "selectByOwnerCode ownerCode isNull");
		Objects.requireNonNull(ruleType, "selectByOwnerCode ruleType isNull");
		// 分配规则
		if (Objects.equals(ruleType, AccidentCluesConstant.OWNER_RULE_TYPE_ALLOT)) {
			// 获取配置信息
			CompletableFuture<List<OwnerRuleDto>> futureOwnerRule = selectOwnerRuleOwnerCodeFuture(ownerCode, ruleType);
			// 获取经销商员工信息
			CompletableFuture<Map<Long, EmpByRoleCodeDto>> futureStaff = selectStaffByOwnerCodeFuture(ownerCode);
			// 获取经销商服务经理
			CompletableFuture<Map<Long, EmpByRoleCodeDto>> futureFWJL = selectFWJLByOwnerCodeFuture(ownerCode);
			// 收集future等待执行完成
			CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureOwnerRule, futureStaff);

			// 等待所有查询完成，超时时间设置为：30s
			allFutures.get(30, TimeUnit.SECONDS);
			// 获取配置信息
			List<OwnerRuleDto> ownerRuleDtos = futureOwnerRule.get();
			// 获取员工信息
			Map<Long, EmpByRoleCodeDto> staffDtos = futureStaff.get();
			// 获取服务经理
			Map<Long, EmpByRoleCodeDto> fwjlDtos = futureFWJL.get();
			// 渲染普通员工服务经理角色
			setStaffFwjl(staffDtos, fwjlDtos);

			// 新增缺失员工&剔除离职员工
			saveAnddeleteAllot(ownerRuleDtos, staffDtos);
			// 再次查询分配规则
			return selectOwnerRuleOwnerCode(ownerCode, ruleType);
		} else {
			// 获取配置信息
			List<OwnerRuleDto> ownerRuleDtos = selectOwnerRuleOwnerCode(ownerCode, ruleType);
			// 剔除离职员工
			saveAnddeleteMessage(ownerRuleDtos);
			// 再次查询消息规则
			return selectOwnerRuleOwnerCode(ownerCode, ruleType);
		}
	}

	/**
	 * 渲染普通员工服务经理角色
	 */
	private void setStaffFwjl(Map<Long, EmpByRoleCodeDto> staffDtos, Map<Long, EmpByRoleCodeDto> fwjlDtos) {
		log.info("setStaffFwjl start");
		if (CollectionUtils.isEmpty(staffDtos) || CollectionUtils.isEmpty(fwjlDtos)) {
			log.info("setStaffFwjl staffDtos isEmpty or fwjlDtos isEmpty");
			return;
		}
		staffDtos.forEach((k, v) -> {
			if (null == k) {
				return;
			}
			EmpByRoleCodeDto staffDto = staffDtos.get(k);
			if (null == staffDto) {
				log.info("setStaffFwjl staffDto isNull");
				return;
			}
			// 默认状态
			staffDto.setIsSelect(AccidentCluesConstant.OWNER_RULE_SELECT_NO);
			// 获取服务经理
			EmpByRoleCodeDto fwjlDto = fwjlDtos.get(k);
			if (null == fwjlDto) {
				log.info("setStaffFwjl fwjlDto isNull");
				return;
			}
			log.info("setStaffFwjl staffDto setIsSelect");
			// 勾选状态
			staffDto.setIsSelect(AccidentCluesConstant.OWNER_RULE_SELECT_YES);
		});
		log.info("setStaffFwjl end");
	}

	/**
	 * 查询即将分配人
	 */
	@Override
	public OwnerRuleDto selectAllocatedInfo(String ownerCode) {
		try {
			// 代理调用，否则 DistributedLock 不生效
			AccidentCluesOwnerRuleServiceImpl proxy = ApplicationContextHelper.getBeanByType(AccidentCluesOwnerRuleServiceImpl.class);
			return proxy.selectAllocatedInfo_(ownerCode);
		} catch (Exception e) {
			log.info("selectAllocatedInfo e:{}", e);
			throw new ServiceBizException(e.getMessage());
		}
	}

	@DistributedLock(prefix = "accidentCluesOwnerRule:lock:selectAllocatedInfo:", key = {"#ownerCode"}, delimiter = "-")
	public OwnerRuleDto selectAllocatedInfo_(String ownerCode) {
		log.info("selectMessageInfo start ownerCode:{}", ownerCode);
		Objects.requireNonNull(ownerCode, "selectMessageInfo ownerCode isNull");
		// 查询即将分配人
		DmsResponse<OwnerRuleDto> response = domainMaintainLeadFeign.acSelectAllocatedInfo(ownerCode);
		if (null == response || response.isFail()) {
			log.info("selectAllocatedInfo response isfail");
			return null;
		}

		// 查询即将分配人是否已离职
		OwnerRuleDto data = response.getData();
		if (null == data || null == data.getId()) {
			log.info("selectAllocatedInfo data isNull");
			// 尝试查询服务经理
			List<EmpByRoleCodeDto> roleCodeDtos = selectFWJLByOwnerCode(ownerCode);
			if (CollectionUtils.isEmpty(roleCodeDtos)) {
				log.info("selectAllocatedInfo roleCodeDtos isEmpty");
				return null;
			}
			// 获取第一个服务经理（数据库物理排序）
			EmpByRoleCodeDto emp = roleCodeDtos.get(0);
			if (null == emp) {
				log.info("selectAllocatedInfo emp isNull");
				return null;
			}
			// 通过服务经理组装OwnerRuleDto
			OwnerRuleDto ownerRuleDto = new OwnerRuleDto();
			ownerRuleDto.setOwnerCode(ownerCode);
			ownerRuleDto.setRuleType(AccidentCluesConstant.OWNER_RULE_TYPE_ALLOT);
			ownerRuleDto.setUserId(emp.getUserId());
			ownerRuleDto.setUserMobile(emp.getPhone());
			ownerRuleDto.setUserName(emp.getEmployeeName());
			ownerRuleDto.setIsOnjob(emp.getIsOnjob());
			ownerRuleDto.setIsSelect(AccidentCluesConstant.OWNER_RULE_SELECT_YES);
			return ownerRuleDto;
		} else {
			Long acId = data.getId();
			Long userId = data.getUserId();
			log.info("selectAllocatedInfo data isnotnull acId:{},userId:{}", acId, userId);
			// 判断核心字段
			if (null == userId) {
				log.info("selectAllocatedInfo userId isNull");
				return data;
			}
			// 查询员工信息
			EmpUserInfoDto userInfoDto = selectEmpInfoByUserId(userId);
			if (null == userInfoDto) {
				log.info("selectAllocatedInfo userInfoDto isNull");
				// 如果已非法则尝试删除
				int row = delete(acId);
				log.info("selectAllocatedInfo row:{}", row);
				if (row < 1) {
					log.info("selectAllocatedInfo row < 1");
					return null;
				}
				// 并尝试再次获取
				return selectAllocatedInfo(ownerCode);
			}
			// 判断是否在职
			if (Objects.equals(userInfoDto.getIsOnJob(), CommonConstant.IS_ON_JOB_DIMISSION)) {
				log.info("selectAllocatedInfo isOnjob 10081002");
				// 如果已经离职则尝试删除
				int row = delete(acId);
				log.info("selectAllocatedInfo row:{}", row);
				if (row < 1) {
					log.info("selectAllocatedInfo row < 1");
					return null;
				}
				// 并尝试再次获取
				return selectAllocatedInfo(ownerCode);
			}

			// 未离职则正常返回
			return data;
		}
	}

	/**
	 * 查询消息配置
	 */
	@Override
	public List<OwnerRuleDto> selectMessageInfo(String ownerCode) {
		try {
			// 代理调用，否则 DistributedLock 不生效
			AccidentCluesOwnerRuleServiceImpl proxy = ApplicationContextHelper.getBeanByType(AccidentCluesOwnerRuleServiceImpl.class);
			return proxy.selectMessageInfo_(ownerCode);
		} catch (Exception e) {
			log.info("selectMessageInfo e:{}", e);
			throw new ServiceBizException(e.getMessage());
		}
	}

	/**
	 * 自建线索推送消息
	 *
	 */
	@Override
	public void newbieClueReminder(List<AccidentClueCrmInfoMqDto> list) {
		log.info("newbie clue reminder start :{}",list);
		if (CollUtil.isEmpty(list)) {
			log.info("newbie clue reminder list empty");
			return;
		}
		List<AccidentClueCrmInfoMqDto> newbieClueList = list.stream()
			.filter(a -> Objects.equals(a.getSourceChannel(), "NewBie"))
			.collect(Collectors.toList());
		if (CollUtil.isEmpty(newbieClueList)) {
			log.info("newbie clue reminder newbie list empty");
			return;
		}
		for (AccidentClueCrmInfoMqDto clue : newbieClueList) {
			List<AccidentCluesVo> res = domainMaintainLeadFeign.accidentClueList(new AccidentCluesDto().setAcId(Long.valueOf(clue.getSourceClueId())))
				.getData();
			if (CollUtil.isNotEmpty(res) && Objects.isNull(res.get(0).getCrmId())) {
				AccidentCluesVo clueInfo = res.get(0);
				log.info("newbie clue reminder clueInfo:{}",clueInfo);
				if (!isWhite(clueInfo.getDealerCode())) {
					log.info("newbie clue reminder dealerCode isNotBlank and not isWhite dealerCode:{}", clueInfo.getDealerCode());
					continue;
				}
				LeadOperationResultDto dto = LeadOperationResultDto.builder()
					.id(clue.getId())
					.vehicleVin(clueInfo.getVin())
					.leadsReceiveTime(clueInfo.getCreatedAt())
					.data(ClueDataDto.builder()
						.accidentInfo(ClueDataDto.AccidentInfo.builder().accidentAddress(clueInfo.getAccidentAddress()).build())
						.contactInfo(Collections.singletonList(ClueDataDto.ContactInfo.builder().customerName(clueInfo.getContacts()).build()))
						.build())
					.build();
				OwnerRuleDto ownerRuleDto = new OwnerRuleDto().setUserId(Long.valueOf(clueInfo.getFollowPeople()));
				log.info("newbie clue reminder send param :{}:{}",dto,ownerRuleDto);
				accidentCluesService.cluesRemindGiveAssignor(dto, ownerRuleDto, false);
			}
		}
	}

	/**
	 * 查询经销商是否是试点店
	 * @param dealerCode
	 * @return
	 */
	private boolean isWhite(String dealerCode) {
		log.info("newbie clue reminder isWhite dealer:{}", dealerCode);
		DmsResponse<Object> response = domainMaintainAuthFeign.checkWhitelist(dealerCode, CommonConstant.WECOM_ACCIDENT_MODE_TYPE, CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE, "");
		log.info("newbie clue reminder isWhite response:{}",response);
		if (Objects.isNull(response) || response.isFail()){
			log.info("newbie clue reminder isWhite error");
			return false;
		}
		Object data = response.getData();
		if (null == data) {
			log.info("newbie clue reminder isWhite data isnull");
			return false;
		}
		try {
			return Boolean.parseBoolean(data.toString());
		} catch (Exception e) {
			log.info("newbie clue reminder isWhite e:{}", e);
			return false;
		}
	}
	@DistributedLock(prefix = "accidentCluesOwnerRule:lock:selectMessageInfo:", key = {"#ownerCode"}, delimiter = "-")
	public List<OwnerRuleDto> selectMessageInfo_(String ownerCode) {
		log.info("selectMessageInfo start");
		Objects.requireNonNull(ownerCode, "selectMessageInfo ownerCode isNull");
		// 查询消息配置
		List<OwnerRuleDto> dtos = selectMessageByOwnerCode(ownerCode);
		if (CollectionUtils.isEmpty(dtos)) {
			log.info("selectMessageInfo data isEmpty");
			return new ArrayList<>();
		}
		// 获取map userInfo
		Map<Long, EmpUserInfoDto> mapUserInfo = getUserInfoMapByUserId(dtos);
		if (CollectionUtils.isEmpty(mapUserInfo)) {
			log.info("selectMessageInfo mapUserInfo isEmpty");
			return dtos;
		}
		// 如果是非法用户、离职用户则尝试删除
		log.info("selectMessageInfo userInfoDtos isNotEmpty");
		boolean isDelete = false;
		for (OwnerRuleDto dto : dtos) {
			if (null == dto || null == dto.getUserId()) {
				continue;
			}
			Long userId = dto.getUserId();
			EmpUserInfoDto userInfoDto = mapUserInfo.get(userId);
			if (null == userInfoDto || Objects.equals(userInfoDto.getIsOnJob(), CommonConstant.IS_ON_JOB_DIMISSION)) {
				log.info("selectMessageInfo userInfoDto isNull or IsOnjob 10081002");
				// 删除
				delete(dto.getId());
				isDelete = true;
			}
		}
		// 判断是否需要重新查询
		return isDelete ? selectMessageByOwnerCode(ownerCode) : dtos;
	}

	/**
	 * 删除配置信息
	 */
	@Override
	public Integer deleteOwnerRule(String ids) {
		try {
			// 代理调用，否则 DistributedLock 不生效
			AccidentCluesOwnerRuleServiceImpl proxy = ApplicationContextHelper.getBeanByType(AccidentCluesOwnerRuleServiceImpl.class);
			return proxy.deleteOwnerRule_(ids);
		} catch (Exception e) {
			log.info("deleteOwnerRule e:{}", e);
			throw new ServiceBizException(e.getMessage());
		}
	}

	@DistributedLock(prefix = "accidentCluesOwnerRule:lock:deleteOwnerRule:", key = {"#ids"}, delimiter = "-")
	public Integer deleteOwnerRule_(String ids) {
		log.info("deleteOwnerRule start");
		Objects.requireNonNull(ids, "deleteOwnerRule ids isNull");
		DmsResponse<Integer> response = domainMaintainLeadFeign.acDeleteByIds(ids);
		if (null == response || response.isFail()) {
			log.info("deleteOwnerRule response isfail");
			throw new ServiceBizException("删除失败!");
		}
		// 校验消息配置
		log.info("deleteOwnerRule end");
		return response.getData();
	}

	/**
	 * 更新分配规则&新增消息配置
	 */
	@Override
	public void saveOwnerRule(SaveOwnerRuleDto saveOwnerRuleDto) {
		try {
			// 代理调用，否则 DistributedLock 不生效
			AccidentCluesOwnerRuleServiceImpl proxy = ApplicationContextHelper.getBeanByType(AccidentCluesOwnerRuleServiceImpl.class);
			proxy.saveOwnerRule_(saveOwnerRuleDto);
			List<OwnerRuleDto> allotList = saveOwnerRuleDto.getAllotList();
			List<OwnerRuleDto> messageList = saveOwnerRuleDto.getMessageList();
			String ownerCode = null;
			if(!CollectionUtils.isEmpty(allotList)){
				ownerCode = allotList.get(0).getOwnerCode();
			}else if(!CollectionUtils.isEmpty(messageList)){
				ownerCode = messageList.get(0).getOwnerCode();
			}
			if(StringUtils.isNotEmpty(ownerCode)){
				log.info("saveOwnerRule redis delete:{}", ownerCode);
				RBucket<List<OwnerRuleDto>> bucket = redissonClient.getBucket(SELECT_OWNER_RULE + ownerCode + ":" + 2);
				bucket.delete();
				RBucket<List<OwnerRuleDto>> bucket1 = redissonClient.getBucket(SELECT_OWNER_RULE + ownerCode + ":" + 1);
				bucket1.delete();
			}
		} catch (Exception e) {
			log.info("saveOwnerRule e:{}", e);
			throw new ServiceBizException(e.getMessage());
		}
	}

	@DistributedLock(prefix = "accidentCluesOwnerRule:lock:saveOwnerRule:", key = {"#ownerCode"}, delimiter = "-")
	public void saveOwnerRule_(SaveOwnerRuleDto saveOwnerRuleDto) {
		log.info("saveOwnerRule start");
		Objects.requireNonNull(saveOwnerRuleDto, "saveOwnerRule saveOwnerRuleDto isNull");
		List<OwnerRuleDto> allotList = saveOwnerRuleDto.getAllotList();
		List<OwnerRuleDto> messageList = saveOwnerRuleDto.getMessageList();
		if (CollectionUtils.isEmpty(allotList) && CollectionUtils.isEmpty(messageList)) {
			log.info("saveOwnerRule dtos isEmpty");
			throw new ServiceBizException("报文非法!");
		}

		// 处理分配规则
		allotHandle(allotList);
		// 处理消息配置
		messageHandle(messageList);
		log.info("saveOwnerRule end");
	}

	/**
	 * 处理分配规则
	 */
	private void allotHandle(List<OwnerRuleDto> dtos) {
		log.info("allotHandle start");
		if (CollectionUtils.isEmpty(dtos)) {
			log.info("allotHandle dtos isEmpty");
			return;
		}

		// 获取map userInfo
		Map<Long, EmpUserInfoDto> mapUserInfo = getUserInfoMapByUserId(dtos);
		if (CollectionUtils.isEmpty(mapUserInfo)) {
			log.info("allotHandle mapUserInfo isEmpty");
			throw new ServiceBizException("登录账号不存在!");
		}

		// 新增集合
		List<OwnerRuleDto> addList = new ArrayList<>();
		// 编辑集合
		List<OwnerRuleDto> updateList = new ArrayList<>();

		// 校验并收集数据
		for (OwnerRuleDto dto : dtos) {
			// dto校验
			Objects.requireNonNull(dto, "数据非法!");
			// 获取error信息
			String errorMsg = getUserInfoStr("分配规则配置-", dto);
			// 基础校验
			Objects.requireNonNull(dto.getOwnerCode(), errorMsg.concat("经销商code为必填项"));
			Objects.requireNonNull(dto.getUserId(), errorMsg.concat("用户Id为必填项"));
			//Objects.requireNonNull(dto.getUserName(), errorMsg.concat("用户名称为必填项"));
			Objects.requireNonNull(dto.getIsSelect(), errorMsg.concat("选中标识为必填项"));
			// 规则类型必须为合法类型
			Integer ruleType = dto.getRuleType();
			if (!Objects.equals(ruleType, AccidentCluesConstant.OWNER_RULE_TYPE_ALLOT)) {
				throw new ServiceBizException(errorMsg.concat("规则类型必须为1"));
			}

			// 获取用户信息
			Long acId = dto.getId();
			Long userId = dto.getUserId();
			EmpUserInfoDto userInfoDto = mapUserInfo.get(userId);
			log.info("allotHandle acId:{},userId:{},userInfoDto:{}", acId, userId, userInfoDto);
			// 如果userInfoDto已经非法需要删除
			if (null == userInfoDto) {
				log.info("allotHandle userInfoDto isNull");
				// 尝试删除
				delete(acId);
				continue;
			}

			// 如果离职则尝试删除
			if (Objects.equals(userInfoDto.getIsOnJob(), CommonConstant.IS_ON_JOB_DIMISSION)) {
				log.info("allotHandle isOnjob 10081002");
				// 尝试删除
				delete(acId);
			} else {
				log.info("allotHandle isOnjob 10081001");
				if (null == acId) {
					// 收集新增对象
					log.info("allotHandle id isNull");
					addList.add(dto);
				} else {
					// 收集跟新对象
					log.info("allotHandle id isNotNull");
					updateList.add(dto);
				}
			}
		}

		// 批处理新增对象
		if (!CollectionUtils.isEmpty(addList)) {
			domainMaintainLeadFeign.acSaves(addList);
		}

		// 批处理更新对象
		if (!CollectionUtils.isEmpty(updateList)) {
			domainMaintainLeadFeign.acUpdates(updateList);
		}

		// 清理map
		mapUserInfo.clear();
		log.info("allotHandle end");
	}

	/**
	 * 处理消息配置
	 */
	private void messageHandle(List<OwnerRuleDto> dtos) {
		log.info("messageHandle start");
		if (CollectionUtils.isEmpty(dtos)) {
			log.info("messageHandle dtos isEmpty");
			return;
		}

		// 检查最大次数
		checkMaxCount(dtos);

		// 获取map userInfo
		Map<String, UserListOutDto> mapUserInfo = getUserInfoMapByUserCode(dtos);
		if (CollectionUtils.isEmpty(mapUserInfo)) {
			log.info("messageHandle mapUserInfo isEmpty");
			throw new ServiceBizException("登录账号不存在!");
		}

		// 新增集合
		List<OwnerRuleDto> addList = new ArrayList<>();
		// 编辑集合
		List<OwnerRuleDto> updateList = new ArrayList<>();

		// 校验并处理数据
		for (OwnerRuleDto dto : dtos) {
			messageHandle(mapUserInfo, addList, updateList, dto);
		}

		// 批处理新增对象
		if (!CollectionUtils.isEmpty(addList)) {
			domainMaintainLeadFeign.acSaves(addList);
		}

		// 批处理更新对象
		if (!CollectionUtils.isEmpty(updateList)) {
			domainMaintainLeadFeign.acUpdates(updateList);
		}

		// 清理map
		mapUserInfo.clear();
		log.info("allotHandle end");
	}

	/**
	 * 处理消息配置
	 */
	private void messageHandle(Map<String, UserListOutDto> mapUserInfo, List<OwnerRuleDto> addList, List<OwnerRuleDto> updateList, OwnerRuleDto dto) {
		// dto校验
		Objects.requireNonNull(dto, "数据非法!");
		// 获取error信息
		String errorMsg = getUserInfoStr("消息规则配置-", dto);
		// 基础校验
		Objects.requireNonNull(dto.getOwnerCode(), errorMsg.concat("经销商code为必填项"));
		Objects.requireNonNull(dto.getUserCode(), errorMsg.concat("用户code为必填项"));
		Objects.requireNonNull(dto.getUserMobile(), errorMsg.concat("用户手机号为必填项"));
		if (!StringUtil.isMobile(dto.getUserMobile())) {
			throw new ServiceBizException(errorMsg.concat("手机号格式有误！"));
		}
		// 规则类型必须为合法类型
		Integer ruleType = dto.getRuleType();
		if (!Objects.equals(ruleType, AccidentCluesConstant.OWNER_RULE_TYPE_MESSAGE)) {
			throw new ServiceBizException(errorMsg.concat("规则类型必须为2"));
		}
		// 获取用户信息
		String userCode = dto.getUserCode();
		UserListOutDto userDto = mapUserInfo.get(userCode);
		log.info("messageHandle userCode:{},userInfoDto:{}", userCode, userDto);
		Objects.requireNonNull(userDto, errorMsg.concat("未查询到用户信息"));
		// 校验是否离职
		if (Objects.equals(userDto.getIsOnjob(), CommonConstant.IS_ON_JOB_DIMISSION)) {
			throw new ServiceBizException(errorMsg.concat("登录账号不存在"));
		} else {
			log.info("messageHandle isOnjob 10081001");
			if (null == dto.getId()) {
				// 收集新增对象
				log.info("messageHandle id isNull");
				dto.setOwnerCode(dto.getOwnerCode());
				dto.setUserId(userDto.getUserId());
				dto.setUserName(userDto.getEmployeeName());
				dto.setIsOnjob(CommonConstant.IS_ON_JOB_IN);
				dto.setIsSelect(AccidentCluesConstant.OWNER_RULE_SELECT_YES);
				addList.add(dto);
			} else {
				// 收集跟新对象
				log.info("messageHandle id isNotNull");
				updateList.add(dto);
			}
		}
	}

	/**
	 * 检查最大次数
	 */
	private void checkMaxCount(List<OwnerRuleDto> dtos) {
		// 获取系统配置
		int mobileMaxCount = getMobileMaxCount();
		int size = dtos.size();
		log.info("messageHandle mobileMaxCount:{},size:{}", mobileMaxCount, size);
		// 获取已经配置的总数(自动剔除离职员工)
		String ownerCode = dtos.get(0).getOwnerCode();
		log.info("messageHandle ownerCode:{}", ownerCode);
		List<OwnerRuleDto> ownerRuleDtos = selectMessageInfo(ownerCode);
		// 如果存在已有数据则进行累加
		if (!CollectionUtils.isEmpty(ownerRuleDtos)) {
			log.info("messageHandle ownerRuleDtos isNotEmpty");
			size += ownerRuleDtos.size();
			log.info("messageHandle ownerRuleDtos isNotEmpty size:{}", size);
		}
		// 校验最大配置数量
		if (size > mobileMaxCount) {
			throw new ServiceBizException("最大仅支持配置" + mobileMaxCount + "个手机号");
		}
	}

	/**
	 * 获取最大限制次数
	 */
	private int getMobileMaxCount() {
		log.info("getMobileMaxCount start");
		// 查询系统配置信息
		CommonConfigDto dto = getConfigByKey(AccidentCluesConstant.OWNER_RULE_RULE_MOBILE_MAX_COUNT);
		if (null == dto) {
			log.info("getMobileMaxCount dto isNull");
			return AccidentCluesConstant.OWNER_RULE_MOBILE_MAX_COUNT;
		}
		return Integer.parseInt(dto.getConfigValue());
	}

	/**
	 * 获取默认角色
	 */
	private String getRuleCodeSelectDef() {
		log.info("getRuleCodeSelectDef start");
		// 查询系统配置信息
		CommonConfigDto dto = getConfigByKey(AccidentCluesConstant.OWNER_RULE_RULE_CODE_SELECT_DEF);
		if (null == dto) {
			log.info("getRuleCodeSelectDef dto isNull");
			return AccidentCluesConstant.OWNER_RULE_SELECT_YSE_ROLECODE;
		}
		return dto.getConfigValue();
	}

	/**
	 * 获取系统配置
	 */
	private CommonConfigDto getConfigByKey(String key) {
		log.info("getMobileMaxCount key:{}", key);
		// 查询系统配置信息
		DmsResponse<CommonConfigDto> response = domainMaintainOrdersFeign.queryDmsDefaultParam2(key, AccidentCluesConstant.ACCIDENT_CLUES_GROUPTYPE);
		log.info("getMobileMaxCount configResult:{}", response);
		if (null == response || response.isFail()) {
			return null;
		}
		return response.getData();
	}

	/**
	 * 根据userId获取 Map userInfo
	 */
	private Map<Long, EmpUserInfoDto> getUserInfoMapByUserId(List<OwnerRuleDto> dtos) {
		log.info("getUserInfoMapByUserId start");
		List<Long> userIds = getUserIds(dtos);
		if (CollectionUtils.isEmpty(userIds)) {
			log.info("getUserInfoMapByUserId userIds isEmpty");
			return new HashMap<>();
		}

		log.info("getUserInfoMapByUserId userIds isNotEmpty");
		// 根据userId查询员工信息
		List<EmpUserInfoDto> empUserInfoDtos = selectEmpInfoByUserIds(userIds);
		// 如果为null则new一个空对象，兼容后面代码
		if (CollectionUtils.isEmpty(empUserInfoDtos)) {
			log.info("getUserInfoMapByUserId empUserInfoDtos isEmpty");
			return new HashMap<>();
		}

		// 收集用户信息并返回map userInfo
		return empUserInfoDtos
				.stream()
				.filter(Objects::nonNull)
				.filter(i -> null != i.getUserId())
				.collect(Collectors.toMap(EmpUserInfoDto::getUserId, Function.identity(), ((k1, k2) -> k1)));
	}

	/**
	 * 获取userIds
	 */
	private List<Long> getUserIds(List<OwnerRuleDto> dtos) {
		// 收集userId
		return dtos
				.stream()
				.filter(Objects::nonNull)
				.filter(i -> null != i.getUserId())
				.map(OwnerRuleDto::getUserId)
				.collect(Collectors.toList());
	}

	/**
	 * 根据userCode获取 Map userInfo
	 */
	private Map<String, UserListOutDto> getUserInfoMapByUserCode(List<OwnerRuleDto> dtos) {
		log.info("getUserInfoMapByUserCode start");
		// 收集userId
		List<String> userCodes = dtos
				.stream()
				.filter(Objects::nonNull)
				.filter(i -> null != i.getUserCode())
				.map(OwnerRuleDto::getUserCode)
				.collect(Collectors.toList());
		// 如果userId收集不到获取则转换map无意义
		if (CollectionUtils.isEmpty(userCodes)) {
			log.info("getUserInfoMapByUserCode userCodes isEmpty");
			return new HashMap<>();
		}

		log.info("getUserInfoMapByUserCode userCodes isNotEmpty");
		// 根据userId查询员工信息
		List<UserListOutDto> empUserInfoDtos = selectEmpInfoByUserCodes(userCodes);
		// 如果为null则new一个空对象，兼容后面代码
		if (CollectionUtils.isEmpty(empUserInfoDtos)) {
			log.info("getUserInfoMapByUserCode empUserInfoDtos isEmpty");
			return new HashMap<>();
		}

		// 收集用户信息并返回map userInfo
		return empUserInfoDtos
				.stream()
				.filter(Objects::nonNull)
				.filter(i -> null != i.getUserCode())
				.collect(Collectors.toMap(UserListOutDto::getUserCode, Function.identity()));
	}

	/**
	 * 获取用户字符串信息
	 */
	private String getUserInfoStr(String prefix, OwnerRuleDto dto) {
		StringBuilder builder = new StringBuilder(prefix);
		builder.append("userInfo:【userId:");
		builder.append(dto.getUserId());
		builder.append(",userCode:");
		builder.append(dto.getUserCode());
		builder.append(",userName:");
		builder.append(dto.getUserName());
		builder.append(",userMobile:");
		builder.append(dto.getUserMobile());
		builder.append("】");
		return builder.toString();
	}

	/**
	 * 查询配置信息Future
	 */
	private CompletableFuture<List<OwnerRuleDto>> selectOwnerRuleOwnerCodeFuture(String ownerCode, Integer ruleType) {
		return CompletableFuture.supplyAsync(() -> {
			return getOwnerRuleDtos(ownerCode, ruleType);
		}, thread360Pool);
	}

	/**
	 * 查询配置信息
	 */
	private List<OwnerRuleDto> getOwnerRuleDtos(String ownerCode, Integer ruleType) {
		return selectOwnerRuleOwnerCode(ownerCode, ruleType);
	}

	/**
	 * 查询配置信息
	 */
	private List<OwnerRuleDto> selectOwnerRuleOwnerCode(String ownerCode, Integer ruleType) {
		log.info("selectOwnerRuleOwnerCode start ownerCode:{},ruleType:{}", ownerCode, ruleType);
		DmsResponse<List<OwnerRuleDto>> response = domainMaintainLeadFeign.acSelectOwnerRuleOwnerCode(ownerCode, ruleType);
		if (null == response || response.isFail()) {
			log.info("selectOwnerRuleOwnerCode response isfail");
			return null;
		}
		log.info("selectOwnerRuleOwnerCode end");
		return response.getData();
	}

	/**
	 * 查询消息配置
	 */
	private List<OwnerRuleDto> selectMessageByOwnerCode(String ownerCode) {
		log.info("selectOwnerRuleOwnerCode start ownerCode:{}", ownerCode);
		DmsResponse<List<OwnerRuleDto>> response = domainMaintainLeadFeign.acSelectMessageByOwnerCode(ownerCode);
		if (null == response || response.isFail()) {
			log.info("selectMessageByOwnerCode response isfail");
			return null;
		}
		log.info("selectMessageByOwnerCode end");
		return response.getData();
	}

	/**
	 * 根据经销商查询员工信息Future
	 */
	private CompletableFuture<Map<Long, EmpByRoleCodeDto>> selectStaffByOwnerCodeFuture(String ownerCode) {
		return CompletableFuture.supplyAsync(() -> {
			return getLongEmpByRoleCodeDtoMap(ownerCode);
		}, thread360Pool);
	}

	/**
	 * 根据经销商查询合法员工信息
	 */
	private Map<Long, EmpByRoleCodeDto> getLongEmpByRoleCodeDtoMap(String ownerCode) {
		return selectRoleCodeDto(selectStaffByOwnerCode(ownerCode));
	}

	/**
	 * 根据经销商查询员工信息
	 */
	private List<EmpByRoleCodeDto> selectStaffByOwnerCode(String ownerCode) {
		log.info("selectStaffByOwnerCode start ownerCode:{}", ownerCode);
		ResponseDto<EmpByRoleCodeDto> reqDos = new ResponseDto<>();
		EmpByRoleCodeDto empDto = new EmpByRoleCodeDto();
		// 经销商
		empDto.setCompanyCode(ownerCode);
		reqDos.setData(empDto);
		DmsResponse<List<EmpByRoleCodeDto>> response = midEndAuthCenterFeign.queryDealerUser(reqDos);
		if (null == response || response.isFail()) {
			log.info("selectStaffByOwnerCode response isfail");
			return null;
		}
		log.info("selectStaffByOwnerCode end");
		return response.getData();
	}

	/**
	 * 根据经销商查询服务经理Future
	 */
	private CompletableFuture<Map<Long, EmpByRoleCodeDto>> selectFWJLByOwnerCodeFuture(String ownerCode) {
		return CompletableFuture.supplyAsync(() -> {
			return getEmpByRoleCodeDtoMap(selectFWJLByOwnerCode(ownerCode));
		}, thread360Pool);
	}

	/**
	 * 转换成map
	 */
	private Map<Long, EmpByRoleCodeDto> getEmpByRoleCodeDtoMap(List<EmpByRoleCodeDto> roleCodeDtos) {
		log.info("getEmpByRoleCodeDtoMap start");
		if (CollectionUtils.isEmpty(roleCodeDtos)) {
			log.info("selectRoleCodeDto roleCodeDtos isEmpty");
			return new HashMap<>();
		}
		// 转换成map
		Map<Long, EmpByRoleCodeDto> mapUserInfo = roleCodeDtos
				.stream()
				.filter(Objects::nonNull)
				.filter(i -> null != i.getUserId())
				.collect(Collectors.toMap(EmpByRoleCodeDto::getUserId, Function.identity(), ((k1, k2) -> k1)));
		if (CollectionUtils.isEmpty(mapUserInfo)) {
			log.info("selectRoleCodeDto mapUserInfo isEmpty");
			return new HashMap<>();
		}
		log.info("getEmpByRoleCodeDtoMap end");
		return mapUserInfo;
	}

	/**
	 * 转换合法用户
	 */
	private Map<Long, EmpByRoleCodeDto> selectRoleCodeDto(List<EmpByRoleCodeDto> roleCodeDtos) {
		log.info("selectRoleCodeDto start");
		Map<Long, EmpByRoleCodeDto> data = new HashMap<>();
		if (CollectionUtils.isEmpty(roleCodeDtos)) {
			log.info("selectRoleCodeDto roleCodeDtos isEmpty");
			return data;
		}
		// 获取userIds
		List<Long> userIds = roleCodeDtos
				.stream()
				.filter(Objects::nonNull)
				.filter(i -> null != i.getUserId())
				.map(EmpByRoleCodeDto::getUserId)
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(userIds)) {
			log.info("selectRoleCodeDto userIds isEmpty");
			return data;
		}
		// 查询用户信息，因为selectFWJLByOwnerCode接口会把作废的部门也查询出来，故此还需要跟进userId查询合法数据
		List<EmpUserInfoDto> userInfoDtos = selectEmpInfoByUserIds(userIds);
		if (CollectionUtils.isEmpty(userInfoDtos)) {
			log.info("selectRoleCodeDto userInfoDtos isEmpty");
			return data;
		}
		// 转换成map
		Map<Long, EmpUserInfoDto> mapUserInfo = userInfoDtos
				.stream()
				.filter(Objects::nonNull)
				.filter(i -> null != i.getUserId())
				.collect(Collectors.toMap(EmpUserInfoDto::getUserId, Function.identity(), ((k1, k2) -> k1)));
		if (CollectionUtils.isEmpty(mapUserInfo)) {
			log.info("selectRoleCodeDto mapUserInfo isEmpty");
			return data;
		}
		// 剔除roleCodeDtos作废掉的数据，已selectEmpInfoByUserIds结果为准
		for (EmpByRoleCodeDto dto : roleCodeDtos) {
			if (null == dto || null == dto.getUserId()) {
				log.info("selectRoleCodeDto dto isNull");
				continue;
			}
			Long userId = dto.getUserId();
			EmpUserInfoDto userInfoDto = mapUserInfo.get(userId);
			if (null == userInfoDto) {
				log.info("selectRoleCodeDto userInfoDto isNull userId:{}", userId);
				// 非法数据（如、部门被删除）当成离职处理
				dto.setIsOnjob(CommonConstant.IS_ON_JOB_DIMISSION);
			}
			data.put(userId, dto);
		}
		log.info("selectRoleCodeDto end");
		return data;
	}

	/**
	 * 根据经销商查询服务经理
	 */
	private List<EmpByRoleCodeDto> selectFWJLByOwnerCode(String ownerCode) {
		log.info("selectFWJLByOwnerCode start ownerCode:{}", ownerCode);
		ResponseDto<EmpByRoleCodeDto> reqDos = new ResponseDto<>();
		EmpByRoleCodeDto empDto = new EmpByRoleCodeDto();
		// 经销商
		empDto.setCompanyCode(ownerCode);
		// 在职状态
		empDto.setIsOnjob(CommonConstant.IS_ON_JOB_IN);
		// 获取角色code
		String roleCode = getRuleCodeSelectDef();
		// 角色code
		empDto.setRoleCode(Collections.singletonList(roleCode));
		reqDos.setData(empDto);
		DmsResponse<List<EmpByRoleCodeDto>> response = midEndAuthCenterFeign.queryDealerUser(reqDos);
		if (null == response || response.isFail()) {
			log.info("selectFWJLByOwnerCode response isfail");
			return null;
		}
		log.info("selectFWJLByOwnerCode end");
		return response.getData();
	}

	/**
	 * 根据userId查询员工信息
	 */
	private EmpUserInfoDto selectEmpInfoByUserId(Long userId) {
		List<EmpUserInfoDto> empUserInfoDtos = selectEmpInfoByUserIds(Collections.singletonList(userId));
		log.info("selectEmpInfoByUserIds empUserInfoDtos:{}", empUserInfoDtos);
		return CollectionUtils.isEmpty(empUserInfoDtos) ? null : empUserInfoDtos.get(0);
	}

	/**
	 * 根据userIds查询员工信息
	 */
	private List<EmpUserInfoDto> selectEmpInfoByUserIds(List<Long> userIds) {
		log.info("selectEmpInfoByUserIds start userIds:{}", userIds);
		List<EmpUserInfoDto> userInfoDtos = new ArrayList<>();
		Lists.partition(userIds, AccidentCluesConstant.OWNER_RULE_SELECT_USER_MAX_SIZE).forEach(i -> {
			log.info("selectEmpInfoByUserIds partition 100");
			UserInfoByUserIdsDto dto = new UserInfoByUserIdsDto();
			dto.setUserIds(i);
			List<EmpUserInfoDto> dtos = midEndAuthCenterFeign.queryUserInfoByUserIds(dto);
			if (!CollectionUtils.isEmpty(dtos)) {
				log.info("selectEmpInfoByUserIds dtos isNotEmpty");
				userInfoDtos.addAll(dtos);
			}
		});
		log.info("selectEmpInfoByUserIds end");
		return userInfoDtos;
	}

	/**
	 * 根据userCode查询员工信息
	 */
	private List<UserListOutDto> selectEmpInfoByUserCodes(List<String> userCodes) {
		log.info("selectEmpInfoByUserCodes start userCodes:{}", userCodes);
		// userCodes
		JSONObject reqData = new JSONObject();
		reqData.put("userCodes", userCodes);
		// data
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("data", reqData);
		ResponseDto<List<UserListOutDto>> response = midEndAuthCenterFeign.queryEmpList(jsonObject);
		if (null == response || response.isFail()) {
			log.info("selectEmpInfoByUserCodes response isfail");
			return null;
		}
		List<UserListOutDto> data = response.getData();
		if (CollectionUtils.isEmpty(data)) {
			log.info("selectEmpInfoByUserCodes data isEmpty");
			return null;
		}
		log.info("selectEmpInfoByUserCodes end");
		return response.getData();
	}

	/**
	 * 剔除离职员工
	 */
	private void saveAnddeleteMessage(List<OwnerRuleDto> dtos) {
		log.info("saveAnddeleteMessage start");
		// dtos信息都查询不到则已无意义
		if (CollectionUtils.isEmpty(dtos)) {
			log.info("saveAnddeleteMessage dtos isEmpty");
			return;
		}
		// 获取map userInfo
		Map<String, UserListOutDto> mapUserInfo = getUserInfoMapByUserCode(dtos);
		if (CollectionUtils.isEmpty(mapUserInfo)) {
			log.info("messageHandle mapUserInfo isEmpty");
			throw new ServiceBizException("未获取到任何用户数据!");
		}

		// 校验并处理数据
		for (OwnerRuleDto dto : dtos) {
			// dto校验
			if (null == dto) {
				continue;
			}
			// 获取用户信息
			String userCode = dto.getUserCode();
			UserListOutDto userDto = mapUserInfo.get(userCode);
			log.info("messageHandle userCode:{},userInfoDto:{}", userCode, userDto);
			// 校验是否离职
			if (null == userDto || Objects.equals(userDto.getIsOnjob(), CommonConstant.IS_ON_JOB_DIMISSION)) {
				// 删除
				delete(dto.getId());
			}
		}

		// 清理map
		mapUserInfo.clear();
		log.info("saveAnddeleteMessage end");
	}

	/**
	 * 新增缺失员工&剔除离职员工
	 */
	private void saveAnddeleteAllot(List<OwnerRuleDto> ownerRuleDtos, Map<Long, EmpByRoleCodeDto> mapStaffDtos) {
		log.info("saveAnddeleteAllot start");
		// 如果连经销商的员工信息都查询不到则已无意义
		if (CollectionUtils.isEmpty(mapStaffDtos)) {
			log.info("saveAnddeleteAllot staffDtos isEmpty");
			return;
		}

		// ownerRuleMap对象
		Map<Long, List<OwnerRuleDto>> mapOwnerRule = null;
		// 如果不存在ownerRuleDtos则默认创建一个空map兼容后续代码逻辑
		if (CollectionUtils.isEmpty(ownerRuleDtos)) {
			log.info("saveAnddeleteAllot ownerRuleDtos isEmpty");
			mapOwnerRule = new HashMap<>();
		} else {
			// 将ownerRules转换成map
			mapOwnerRule = ownerRuleDtos
					.stream()
					.filter(Objects::nonNull)
					.filter(i -> null != i.getUserId())
					.collect(Collectors.groupingBy(OwnerRuleDto::getUserId));
			// 如果存在数据但是非法则已无意义
			if (CollectionUtils.isEmpty(mapOwnerRule)) {
				log.info("saveAnddelete mapOwnerRule isEmpty");
				return;
			}
		}

		// 没有数据则进行新增，有数据但已离职则进行删除
		for (EmpByRoleCodeDto staffDto : mapStaffDtos.values()) {
			// saveAnddelete
			saveAnddelete(mapOwnerRule, staffDto);
		}

		// 清理map
		mapOwnerRule.clear();
		log.info("saveAnddeleteAllot end");
	}

	/**
	 * saveAnddelete
	 */
	private void saveAnddelete(Map<Long, List<OwnerRuleDto>> mapOwnerRule, EmpByRoleCodeDto staffDto) {
		if (null == staffDto) {
			log.info("saveAnddeleteAllot dto isNull");
			return;
		}
		Long userId = staffDto.getUserId();
		Integer isOnjob = staffDto.getIsOnjob();
		log.info("saveAnddeleteAllot userId:{},isOnjob:{}", userId, isOnjob);
		if (null == userId) {
			log.info("saveAnddeleteAllot userId isNull");
			return;
		}

		// 根据合法数据获取配置信息
		List<OwnerRuleDto> ownerRuleDto = mapOwnerRule.get(userId);
		// 合法已存在数据：如果存在且在职则不做任何处理
		if (Objects.equals(isOnjob, CommonConstant.IS_ON_JOB_IN) && !CollectionUtils.isEmpty(ownerRuleDto)) {
			log.info("saveAnddeleteAllot isOnjob 10081001 and ownerRuleDto isEmpty");
			return;
		}

		// 合法不存在数据：如果不存在且在职则进行save操作
		if (Objects.equals(isOnjob, CommonConstant.IS_ON_JOB_IN) && CollectionUtils.isEmpty(ownerRuleDto)) {
			log.info("saveAnddeleteAllot isOnjob 10081001 and ownerRuleDto isNotEmpty");
			// 保存
			this.save(AccidentCluesConstant.OWNER_RULE_TYPE_ALLOT, staffDto);
			return;
		}

		// 离职数据：如果离职且存在ownerRuleDto则删除ownerRuleDto
		if (Objects.equals(isOnjob, CommonConstant.IS_ON_JOB_DIMISSION) && !CollectionUtils.isEmpty(ownerRuleDto)) {
			log.info("saveAnddeleteAllot isOnjob 10081002 and ownerRuleDto isNotEmpty");
			// 这里数据量理论最大为4，不用考虑批量处理
			for (OwnerRuleDto dto : ownerRuleDto) {
				if (null == dto || null == dto.getId()) {
					continue;
				}
				// 删除
				this.delete(dto.getId());
			}
			return;
		}
	}

	/**
	 * 保存配置信息
	 */
	private void save(Integer ruleType, EmpByRoleCodeDto emp) {
		log.info("save start ruleType:{}", ruleType);
		OwnerRuleDto dto = new OwnerRuleDto();
		dto.setRuleType(ruleType);
		Integer isSelect = emp.getIsSelect();
		dto.setIsSelect(null == isSelect ? AccidentCluesConstant.OWNER_RULE_SELECT_NO : isSelect);
		dto.setOwnerCode(emp.getCompanyCode());
		dto.setUserId(emp.getUserId());
		dto.setUserCode(emp.getUserCode());
		dto.setUserMobile(emp.getPhone());
		dto.setUserName(emp.getEmployeeName());
		dto.setIsOnjob(emp.getIsOnjob());

		log.info("save dto:{}", dto);
		DmsResponse<Long> response = domainMaintainLeadFeign.acSave(dto);
		log.info("save end response:{}", response);
		if (response.isFail()) {
			throw new ServiceBizException("save errors");
		}
	}

	/**
	 * 删除配置信息
	 */
	private int delete(Long id) {
		log.info("delete start id:{}", id);
		if (null == id) {
			log.info("delete id isNull");
			return 0;
		}
		DmsResponse<Integer> response = domainMaintainLeadFeign.acDeleteById(id);
		log.info("delete end response:{}", response);
		if (response.isFail()) {
			throw new ServiceBizException("delete errors");
		}
		Integer data = response.getData();
		return null == data ? 0 : data;
	}

	/**
	 * 渲染服务经理为默认勾选项
	 */
	private List<OwnerRuleDto> setDefFwjlOwnerRule(String ownerCode, Integer ruleType, Map<Long, EmpByRoleCodeDto> mapFwjlDto) {
		log.info("setDefFwjlOwnerRule ownerCode:{},ruleType:{}", ownerCode, ruleType);
		// 再次查询配置信息得到最新数据（故此必须将新增的在职和离职逻辑写到此逻辑前）
		List<OwnerRuleDto> ownerRuleDtos = selectOwnerRuleOwnerCode(ownerCode, ruleType);
		if (CollectionUtils.isEmpty(ownerRuleDtos)) {
			log.info("setDefFwjlOwnerRule ownerRuleDtos isEmpty");
			return ownerRuleDtos;
		}
		// 如果没有合法的fwjl信息无需二次渲染
		if (CollectionUtils.isEmpty(mapFwjlDto)) {
			log.info("setDefFwjlOwnerRule mapFwjlDto isEmpty");
			return ownerRuleDtos;
		}

		// 如果是服务经理则还需要默认渲染为勾选状态
		for (OwnerRuleDto ownerRuleDto : ownerRuleDtos) {
			if (null == ownerRuleDto) {
				log.info("setDefFwjlOwnerRule ownerRuleDto isNull");
				continue;
			}
			Long userId = ownerRuleDto.getUserId();
			log.info("setDefFwjlOwnerRule userId:{}", userId);
			if (null == userId) {
				log.info("setDefFwjlOwnerRule userId isNull");
				continue;
			}
			// 获取服务经理角色，如果存在则默认set为10041001
			EmpByRoleCodeDto emp = mapFwjlDto.get(userId);
			Optional.ofNullable(emp).ifPresent(i -> {
				log.info("setDefFwjlOwnerRule emp isSelect");
				if (Objects.equals(i.getIsOnjob(), CommonConstant.IS_ON_JOB_IN)) {
					log.info("setDefFwjlOwnerRule IsOnjob 10081001");
					ownerRuleDto.setIsSelect(AccidentCluesConstant.OWNER_RULE_SELECT_YES);
				}
			});
		}

		// 清理map
		mapFwjlDto.clear();
		log.info("setDefFwjlOwnerRule end");
		return ownerRuleDtos;
	}
}
