package com.volvo.maintain.application.maintainlead.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2024/02/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("保养灯线索")
public class InviteVehicleRecordVo {
    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 邀约ID
     */
    private Integer id;

    /**
     * 邀约父类ID
     */
    private Long parentId;

    /**
     * 功能描述：是否主要线索:1主要线索、0附属线索
     *
     * @since 2023-11-03 后续只有主线索，所有此处写死isMain = 1
     */
    private Integer isMain = 1;

    /**
     * 邀约来源类型：1VCDC下发邀约、2经销商自建邀约、3VOC事故邀约，4voc
     */
    private Integer sourceType;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String licensePlateNum;

    /**
     * 车主姓名
     */
    private String name;

    /**
     * 车主电话
     */
    private String tel;

    /**
     * 邀约类型
     */
    private Integer inviteType;

    /**
     * 建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date adviseInDate;

    /**
     * 新建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newAdviseInDate;

    /**
     * 最新建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newestAdviseInDate;

    /**
     * 计划跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planFollowDate;

    /**
     * 实际跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualFollowDate;

    /**
     * 计划提醒日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planRemindDate;

    /**
     * 实际提醒日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualRemindDate;

    /**
     * 首次跟进时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstFollowDate;

    /**
     * 线索完成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderFinishDate;

    /**
     * 跟进服务顾问ID
     */
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    private String saName;

    /**
     * 上次跟进服务顾问ID
     */
    private String lastSaId;

    /**
     * 上次跟进服务顾问姓名
     */
    private String lastSaName;

    /**
     * 跟进状态
     */
    private Integer followStatus;

    /**
     * 是否预约单：1 是，0 否
     */
    private Integer isBook;

    /**
     * 预约单号
     */
    private String bookNo;

    /**
     * 线索完成状态
     */
    private Integer orderStatus;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除 (后续使用类型不合适)
     */
    @ApiModelProperty(value = "是否删除",name = "isDeleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 车主年龄
     */
    private String age;

    /**
     * 车主性别
     */
    private String sex;

    /**
     * 车型
     */
    private String model;

    /**
     * 客户唯一id
     */
    private Long oneId;

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 维修类型
     */
    private String repairTypeCode;

    /**
     * 出厂里程
     */
    private Double outMileage;

    /**
     * 开单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date roCreateDate;

    /**
     * 完成经销商
     */
    private String finishDealerCode;

    /**
     * 上次进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastInDate;

    /**
     * 易损件规则id
     */
    private Long partItemRuleId;

    /**
     * 易损件上次更换时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastChangeDate;

    /**
     * 易损件code
     */
    private String itemCode;

    /**
     * 易损件名称
     */
    private String itemName;

    /**
     * 易损件类型
     */
    private Integer itemType;

    /**
     * 最新失败原因
     */
    private Integer loseReason;

    /**
     * 最新跟进内容
     */
    private String content;

    /**
     * 验证状态
     */
    private String verifyStatus;

    /**
     * crm线索ID
     */
    private Long icmId;
}
