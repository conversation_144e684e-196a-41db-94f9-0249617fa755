package com.volvo.maintain.application.maintainlead.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.yonyou.cyx.function.utils.jsonserializer.date.JsonSimpleDateTimeDeserializer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 工单表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-06
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RepairOrderDto implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 配件销售单号
     */
    private String salesPartNo;

    /**
     * 预约单号
     */
    private String bookingOrderNo;

    /**
     * 估价单号
     */
    private String estimateNo;

    /**
     * 工单类型
     */
    private Integer roType;

    /**
     * 维修类型代码
     */
    private String repairTypeCode;

    /**
     * 其他维修类型
     */
    private String otherRepairType;

    /**
     * 原工单号
     */
    private String primaryRoNo;

    /**
     * 理赔单号
     */
    private String insurationNo;

    /**
     * 保险公司代码
     */
    private String insurationCode;

    /**
     * 客户是否在厂
     */
    private Long isCustomerInAsc;

    /**
     * 是否质检
     */
    private Long isSeasonCheck;

    /**
     * 进厂油料
     */
    private Long oilRemain;

    /**
     * 是否洗车
     */
    private Long isWash;

    /**
     * 是否第一次保养
     */
    private Long is1stGuarantee;

    /**
     * 是否三日电访
     */
    private Integer isTrace;

    /**
     * 三日电访时间
     */
    private Integer traceTime;

    /**
     * 不回访原因
     */
    private String noTraceReason;

    /**
     * 是否路试
     */
    private Long needRoadTest;

    /**
     * 推荐单位
     */
    private String recommendEmpName;

    /**
     * 推荐人
     */
    private String recommendCustomerName;

    /**
     * 服务专员
     */
    private String serviceAdvisor;

    /**
     * 开单人员
     */
    private String serviceAdvisorAss;

    /**
     * 工单状态
     */
    private Long roStatus;

    /**
     * 工单开单时间
     */
    private String roCreateDate;

    /**
     * 预交车时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTimeSupposed;

    /**
     * 指定技师
     */
    private String chiefTechnician;

    /**
     * 车主编号
     */
    private String ownerNo;

    /**
     * 车主
     */
    private String ownerName;

    /**
     * 车主性质
     */
    private Integer ownerProperty;

    /**
     * 车牌号
     */
    private String license;

    /**
     * 车辆唯一识别代码
     */
    private String vin;

    /**
     * 发动机号
     */
    private String engineNo;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 车系
     */
    private String series;

    /**
     * 车型
     */
    private String model;

    /**
     * 进厂行驶里程
     */
    private BigDecimal inMileage;

    /**
     * 出厂行驶里程
     */
    private Long outMileage;

    /**
     * 是否换表
     */
    private Integer isChangeOdograph;

    /**
     * 换表里程
     */
    private BigDecimal changeMileage;

    /**
     * 累计换表里程
     */
    private BigDecimal totalChangeMileage;

    /**
     * 行驶总里程
     */
    private BigDecimal totalMileage;

    /**
     * 送修人
     */
    private String deliverer;

    /**
     * 送修人性别
     */
    private Integer delivererGender;

    /**
     * 送修人电话
     */
    private String delivererPhone;

    /**
     * 送修人手机
     */
    private String delivererMobile;

    /**
     * 完工验收人
     */
    private String finishUser;

    /**
     * 是否竣工
     */
    private Long completeTag;

    /**
     * 待信标志
     */
    private Long waitInfoTag;

    /**
     * 待料标志
     */
    private Long waitPartTag;

    /**
     * 竣工时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    /**
     * 提交结算时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime forBalanceTime;

    /**
     * 交车标识
     */
    private Long deliveryTag;

    /**
     * 交车日期
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveryDate;

    /**
     * 交车人
     */
    private String deliveryUser;

    /**
     * 工时单价
     */
    private BigDecimal labourPrice;

    /**
     * 工时费
     */
    private BigDecimal labourAmount;

    /**
     * 维修材料费
     */
    private BigDecimal repairPartAmount;

    /**
     * 销售材料费
     */
    private BigDecimal salesPartAmount;

    /**
     * 附加项目费
     */
    private BigDecimal addItemAmount;

    /**
     * 辅料管理费
     */
    private BigDecimal overItemAmount;

    /**
     * 维修金额
     */
    private BigDecimal repairAmount;

    /**
     * 预估金额
     */
    private BigDecimal estimateAmount;

    /**
     * 结算金额
     */
    private BigDecimal balanceAmount;

    /**
     * 收款金额
     */
    private BigDecimal receiveAmount;

    /**
     * 去零金额
     */
    private BigDecimal subObbAmount;

    /**
     * 减免金额
     */
    private BigDecimal derateAmount;

    /**
     * 首次预估金额
     */
    private BigDecimal firstEstimateAmount;

    /**
     * 跟踪标识
     */
    private Long traceTag;

    /**
     * 试车员
     */
    private String testDriver;

    /**
     * 工单打印时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime printRoTime;

    /**
     * 首次打印时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstPrintTime;

    /**
     * 工单付费类型
     */
    private Integer roChargeType;

    /**
     * 预先捡料单打印时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime printRpTime;

    /**
     * 预计开工时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime estimateBeginTime;

    /**
     * 是否参加活动
     */
    private Long isActivity;

    /**
     * 是否仓库关单
     */
    private Long isCloseRo;

    /**
     * 是否保养
     */
    private Long isMaintain;

    /**
     * 顾客描述
     */
    private String customerDesc;

    /**
     * 工单拆分状态
     */
    private Long roSplitStatus;

    /**
     * 进厂原因
     */
    private Long inReason;

    /**
     * 工单不进车间
     */
    private Long notEnterWorkshop;

    /**
     * 是否带走旧件
     */
    private Long isTakePartOld;

    /**
     * 维修故障描述
     */
    private String roTroubleDesc;

    /**
     * 服务活动跟踪标识
     */
    private Long activityTraceTag;

    /**
     * 是否赠送保养
     */
    private Long isLargessMaintain;

    /**
     * 是否修改预交车时间
     */
    private Long isUpdateEndTimeSupposed;

    /**
     * 出险单号
     */
    private String occurInsuranceNo;

    /**
     * 定损与实际赔付差额
     */
    private BigDecimal dsFactBalance;

    /**
     * 定损金额
     */
    private BigDecimal dsAmount;

    /**
     * 收案日期
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime caseClosedDate;

    /**
     * 旧件处理方式
     */
    private Long oldpartTreatMode;

    /**
     * 旧件处理时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime oldpartTreatDate;

    /**
     * 旧件备注
     */
    private String oldpartRemark;

    /**
     * 是否旧件
     */
    private Long isOldPart;

    /**
     * 理赔备注
     */
    private String settlementRemark;

    /**
     * 赔付收款日期
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime settleColDate;

    /**
     * 赔付收款状态
     */
    private Integer settlementStatus;

    /**
     * 赔付金额
     */
    private BigDecimal settlementAmount;

    /**
     * 送单人
     */
    private String deliverBillMan;

    /**
     * 送单理赔日期
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliverBillDate;

    /**
     * 出险完成
     */
    private Long insuranceOver;

    /**
     * 发料单首次打印时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime printSendTime;

    /**
     * 接车单号
     */
    private String receptionNo;

    /**
     * 工位代码
     */
    private String labourPositionCode;

    /**
     * 故障发生日期
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime troubleOccurDate;

    /**
     * 索赔单提报状态
     */
    private Long roClaimStatus;

    /**
     * 是否购买维护
     */
    private Long isPurchaseMaintenance;

    /**
     * 配置代码
     */
    private String configCode;

    /**
     * 车间反馈结果
     */
    private String eworkshopRemark;

    /**
     * 结算是否打印
     */
    private Integer isPrint;

    /**
     * 打印交车服务单时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime printCarTime;

    /**
     * 上次保养日期
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastMaintenanceDate;

    /**
     * 上次保养里程
     */
    private BigDecimal lastMaintenanceMileage;

    /**
     * 是否放弃活动
     */
    private Long isAbandonActivity;

    /**
     * 三包状态
     */
    private Long schemeStatus;

    /**
     * 是否APP
     */
    private Integer isApp;

    /**
     * 警告
     */
    private Integer isTgWarningsExist;

    /**
     * 是否索赔工单
     */
    private Integer isClaimOrder;

    /**
     * 是否由估价单转工单
     */
    private Long isEotoro;

    /**
     * 索赔号
     */
    private String claimNo;

    /**
     * 联系人邮箱
     */
    private String delivererEmail;

    /**
     * 索赔项目金额
     */
    private BigDecimal claimLabourAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 备注1
     */
    private String remark1;

    /**
     * 备注2
     */
    private String remark2;

    /**
     * 锁定人
     */
    private String lockUser;

    /**
     * 是否首次保养
     */
    private Integer isFirstMaintain;

    /**
     * 洗车类型
     */
    private Integer washType;

    /**
     * 维修类型代码
     */
    private String repairCategoryCode;

    /**
     * 技术经理处理结果
     */
    private String technicalHandelResult;

    /**
     * 是否售前维修
     */
    private Integer ifPreSaleRepair;

    /**
     * 供应商代码
     */
    private String providerCode;

    /**
     * 税率
     */
    private BigDecimal tax;

    /**
     * 维修开始时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime repairBeginDate;

    /**
     * 废弃标识
     */
    private Integer discardData;

    /**
     * 废弃说明
     */
    private String discardReason;

    /**
     * 原预交车时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime oridinalTimeSupposed;

    /**
     * FD上传
     */
    private Integer fdStatus;

    /**
     * 电子首保卡号
     */
    private String efwCard;

    /**
     * FD日期
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fdDate;

    /**
     * Pad录音url
     */
    private String mp3File;

    /**
     * 处理结果
     */
    private String dealResult;

    /**
     * 工时定额(故障标准)代码
     */
    private String manHourQuotaCode;

    /**
     * 工时定额(故障标准)名称
     */
    private String manHourQuotaName;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 车系ID
     */
    private Integer seriesId;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 接车日期
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pickupDate;

    /**
     * 是否快修
     */
    private Integer isQuickRepair;

    /**
     * 是否上门服务
     */
    private Integer isHomeDelivery;

    /**
     * 保养次数
     */
    private Integer maintainTimes;

    /**
     * 是否终检
     */
    private Integer isCheck;

    /**
     * 可使用卡券oneid
     */
    private Long oneId;

    /**
     * 是否备货
     */
    private Integer isStockUp;

    /**
     * 创建时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonDeserialize(using = JsonSimpleDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    private String assignStatus;

}
