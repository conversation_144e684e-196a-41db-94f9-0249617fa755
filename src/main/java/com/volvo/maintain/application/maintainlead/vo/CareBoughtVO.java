
package com.volvo.maintain.application.maintainlead.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CareBoughtVO implements Serializable {

	/** serialVersionUID */
	private static final long serialVersionUID = 1L;

	/**
	 * 保养活动可享用次数
     * -- GETTER --
     *
     */
	private Integer activityAvailableTimes;

	/**
	 * 保养活动剩余享用次数
     * -- GETTER --
     *
     */
	private Integer activityLeftTimes;

	/**
	 * 保养活动名称
     * -- GETTER --
     *
     */
	private String activityName;

	/**
	 * 保养活动编号
     * -- GETTER --
     *
     */
	private String activityNo;

	/**
	 * 活动类型(1—90系免费保养套餐 2—售后保养合同 3—服务合同 4—新车免费基础保养)
     * -- GETTER --
     *
     */
	private Integer activityType;

	/**
	 * 活动类型(1—90系免费保养套餐 2—售后保养合同 3—服务合同 4—新车免费基础保养)
	 */
	private String activityTypeName;

	/**
	 * 购买者名称
     * -- GETTER --
     *
     */
	private String buyerName;

	/**
	 * 该车使用权益的车龄上限
     * -- GETTER --
     *
     */
	private Integer carAgeMax;

	/**
	 * 汽车销售日期 yyyy-MM-dd HH:mm:ss
     * -- GETTER --
     *
     */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String carsaleTime;

	/**
	 * 服务生效日期 yyyy-MM-dd HH:mm:ss
	 *
     *
     */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String effectiveDate;

	/**
	 * 结束时间
     * -- GETTER --
     *
     */
	private String endTime;

	/**
	 * 服务失效日期 yyyy-MM-dd HH:mm:ss
     * -- GETTER --
     *
     */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String expirationDate;

	/**
	 * id
     * -- GETTER --
     *
     */
	private Integer id;

	/**
	 * 该车使用权益的里程上限
     * -- GETTER --
     *
     */
	private Integer mileageMax;

	/**
	 * 车型名称
     * -- GETTER --
     *
     */
	private String modelName;

	/**
	 * 购买日期 yyyy-MM-dd HH:mm:ss
     * -- GETTER --
     *
     */
	private String purchaseDate;

	/**
	 * 销售经销商
     * -- GETTER --
     *
     */
	private String saleDealer;

	/**
	 * 销售时间
     * -- GETTER --
     *
     */
	private String saleTime;

	/**
	 * 工单结算日期 yyyy-MM-dd HH:mm:ss
     * -- GETTER --
     *
     */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String settlementDate;

	/**
	 * 权益来源(NEWBIE/DMS)
     * -- GETTER --
     *
     */
	private String source;

	/**
	 * 车身编号(车架号)
     * -- GETTER --
     *
     */
	private String vin;

	/**
	 * 销售工单号
     * -- GETTER --
     *
     */
	private String workNo;

	/**
	 * 零件号
     * -- GETTER --
     *
     */
	private String partNo;

	/**
	 * 保修类别
     * -- GETTER --
     *
     */
	private String claimType;

	/**
	 * 来源备注
     * -- GETTER --
     *
     */
	private String sourceRemark;

	/**
	 * 导入时间
	 */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String createTime;
	/**
	 * 更新时间
	 */
	private String updateTime;
	/**
	 * 保养类型(83321001 基础保养 83321002 机油更新)
	 */
	private String upkeepType;

	/**
	 * 更新时间
	 */
	private List<Map> careMeals;

	/**
	 * 使用范围
	 */
	private String useScope;

	private String saleDealerName;

	private String useScopeName;

	private String sourceActive;

	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String originalEndDate;

	private String endDateSource;

	private String useDealerCode;

	private Long careBuyedId;


	/**
	 * 购买状态 35031001：待生效 35031002：生效中  35031003：生效成功 35031004：待人工介入 35031005：生效失败 35031006：已失效
	 */
	private String giveStatus;
}
