package com.volvo.maintain.application.maintainlead.service.vhc;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.vhc.*;

import java.util.List;
import java.util.Map;

public interface VhcRepairService {

    //校验工单组是否存在vhc套餐code
    boolean verifyOrderGroupVHC(String ownerCode,String roNo,String setCode,String vin);

    //创建车辆健康检查
    void createdVhcInfo(VhcInfoDTO vhcInfoDTO);

    //检查页面查询
    Page<VhcDetailsDTO> selectVhcList(QueryVhcDto queryVhcDto);
    //报价页面查询
    Page<VhcPricesheetDetailsDTO> selectVhcPricesheetList(QueryVhcDto queryVhcDto);
    //根据车架号经销商查询最近一次检查未修项
    List<NoRepairItemDto> selectNoRepairItem(String ownerCode, String vin,String roNo);
    //360客户画像车辆健康检查
    Map<String, List<VhcItemPoDTO>> select360VhcItem(String ownerCode, String vin);

    /**
     * 根据车型查询零件工时信息
     * @param vhcQueryLabourDto
     * @return
     */
    Page<VhcQueryLabourVo> queryLabourList(VhcQueryLabourDto vhcQueryLabourDto);

    //预约单关联未修项目
    void vhcConnectBookOrder(String ownerCode,String bookingOrderNo,String vin);

    //查询预约单关联未修项目
    List<NoRepairItemDto> selectVhcConnectBookOrder(String ownerCode,String bookingOrderNo,String vin);

}
