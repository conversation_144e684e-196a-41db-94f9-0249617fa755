package com.volvo.maintain.application.maintainlead.service.healthcheck;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.volvo.maintain.application.maintainlead.dto.healthcheck.HealthCheckDto;
import com.volvo.maintain.application.maintainlead.vo.healthcheck.VehicleHealthRecordInfoVo;
import com.volvo.maintain.application.maintainlead.vo.healthcheck.VehicleHealthVo;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainOrdersFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.yonyou.cyx.function.exception.ServiceBizException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class VehicleHealthCheckServiceImpl implements VehicleHealthCheckService{

    private final DomainMaintainOrdersFeign domainMaintainOrdersFeign;

    public VehicleHealthCheckServiceImpl(DomainMaintainOrdersFeign domainMaintainOrdersFeign) {
        this.domainMaintainOrdersFeign = domainMaintainOrdersFeign;
    }


    /**
     * 根据车架号查询健康检查数据分页
     * @param vin 车架号
     * @param pageNum 分页参数
     * @param pageSize 分页参数
     * @return 分页数据
     */
    @Override
    public IPage<VehicleHealthVo> queryHealthByVin(String vin, Integer pageNum, Integer pageSize) {
        if (ObjectUtils.isEmpty(vin)) {
            throw new ServiceBizException("Perhaps the vin is empty !");
        }
       return domainMaintainOrdersFeign.queryHealthByVin(vin, pageNum, pageSize);
    }

    /**
     * 批量vin 查询健康检查单
     * @param dto 入参对象
     * @return 要反回的数据
     */
    @Override
    public List<VehicleHealthRecordInfoVo> queryVehicleHealthCheckDetails(HealthCheckDto dto) {
        if (ObjectUtils.isEmpty(dto) || ObjectUtils.isEmpty(dto.getVins()) || StringUtils.isBlank(dto.getStartTime()) || StringUtils.isBlank(dto.getEndTime())) {
            throw new ServiceBizException("Perhaps the time or vin is empty !");
        }
       DmsResponse<List<VehicleHealthRecordInfoVo>> response = domainMaintainOrdersFeign.queryVehicleHealthCheckDetails(dto);
        if (Objects.isNull(response)) {
            throw new ServiceBizException("feign domain-maintain-orders result is null !");
        }
        if (response.isFail()) {
            throw new ServiceBizException(response.getErrMsg());
        }
        return response.getData();
    }


}
