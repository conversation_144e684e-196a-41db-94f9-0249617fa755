package com.volvo.maintain.application.maintainlead.vo.healthcheck;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
    * 车辆健康检查主档表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VehicleHealthInfoVo implements Serializable {


    private static final long serialVersionUID=1L;


    /**
    * 主键id
    */
    private Integer id;

    /**
    * 经销商code
    */
    private String ownerCode;

    /**
    * 工单号
    */
    private String roNo;

    /**
    * 车辆健康检查编号
    */
    private String healthNo;

    /**
    * 车架号
    */
    private String vin;

    /**
    * 车牌号
    */
    private String license;

    /**
    * 检查时间
    */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date examineTime;

    /**
    * 检查状态
    */
    private String examineState;

    /**
    * 检查人
    */
    private Integer examinePeople;

    /**
    * 车辆类型
    */
    private String vehicleType;

    /**
     * 维修反馈备注
     */
    private String repairFeedbackRemark;

    /**
     * 是否初终检
     */
    private Integer isFirst;

    /**
     * 是否客户反馈
     */
    private Integer isClientFeedback;


    /**
     * 是否已推送过终检单
     */
    private Integer isEndPush;


    /**
     * 是否SA已阅读客户反馈（84801001已阅读）
     */
    private Integer isReadFeedback;

    /**
    * 是否删除
    */
    private Integer isDeleted;

    /**
    * 创建人
    */
    private String createdBy;

    /**
    * 创建时间
    */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
    * 修改人
    */
    private String updatedBy;

    /**
    * 修改时间
    */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /**
     * 完工验收人
     */
    @ApiModelProperty(name="完工验收人")
    @TableField(exist = false)
    private String finishUser;

    /**
     * 是否竣工
     */
    @ApiModelProperty(name="是否竣工")
    @TableField(exist = false)
    private Long completeTag;

    /**
     * 检查完成时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date examineFinishTime;

    /**
     * 报价完成时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pricesheetFinishTime;

    /**
     * 维修反馈完成时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackFinishTime;

    /**
     * 终检推送时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endExamineFinishTime;
}