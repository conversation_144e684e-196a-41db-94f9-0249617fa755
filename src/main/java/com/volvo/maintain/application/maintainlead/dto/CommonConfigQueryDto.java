package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonConfigQueryDto {
    /**
     * 分组类型
     */
    @ApiModelProperty("分组类型")
    private String groupType;

    /**
     * 配置key
     */
    @ApiModelProperty("配置key")
    private String configKey;

}
