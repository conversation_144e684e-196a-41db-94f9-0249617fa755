package com.volvo.maintain.application.maintainlead.dto.vhc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * vhc报价-保存草稿&报价完成
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VhcNotRepairReqDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 小类id
     */
    private Integer itemId;
    /**
     * 检查单号
     */
    private String vhcNo;
    /**
     * 工单号
     */
    private String roNo;
    /**
     * 经销商
     */
    private String ownerCode;
    /**
     * 不修原因反馈
     */
    private String noRepairCause;
    /**
     * 维修结果
     */
    private String classResult;
    /**
     * 不修原因反馈是否有效（10011001有效，10011002无效）
     */
    private String noRepairCauseValid;
    /**
     * 不修原因来源渠道（b端，c端）
     */
    private String noRepairCauseChannel;
}
