package com.volvo.maintain.application.maintainlead.dto;

import com.volvo.maintain.application.maintainlead.vo.VehicleDeliverVO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

//取送车
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "取送车VehicleDeliverDTO", description = "取送车VehicleDeliverDTO")
public class VehicleDeliverDTO implements Serializable {


    private VehicleDeliverVO vehicleDeliverVO;
}
