package com.volvo.maintain.application.maintainlead.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 功能描述：全量线索tag返回对象
 *
 * <AUTHOR>
 * @since 2023/12/22
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FullLeadsTagVo", description = "全量线索tag返回对象")
public class FullLeadsTagVo {

    @ApiModelProperty(value = "入参的“线索ID”，如果未匹配到则返回 null")
    private Long id;

    @ApiModelProperty(value = "入参的“线索TYPE”，如果未匹配到则返回 null")
    private String vin;

    @ApiModelProperty(value = "批次号")
    private String batchNo;

    @ApiModelProperty(value = "tag列表")
    List<FullLeadVo> tagList;

    public FullLeadsTagVo(Long id, String vin, String batchNo, List<FullLeadVo> tagList) {
        this.id = id;
        this.vin = vin;
        this.batchNo = batchNo;
        this.tagList = tagList;
    }
}
