package com.volvo.maintain.application.maintainlead.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "续保分配人Excel导入实体")
public class InsuranceAssigPersonImportExcel {

	/**
	 * VIN
	 */
	@Excel(name = "*VIN", orderNum = "1")
	private String vin;
	/**
	 * 跟进人员姓名
	 */
	@Excel(name = "跟进人员姓名", orderNum = "2")
	private String saName;
	/**
	 * 跟进人员账号
	 */
	@Excel(name = "*跟进人员账号", orderNum = "3")
	private String saCode;

}
