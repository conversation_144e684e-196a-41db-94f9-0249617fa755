package com.volvo.maintain.application.maintainlead.dto.part;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 配件缺料记录DTO
 * </p>
 */
@Data
public class ShortPartDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 仓库代码
     */
    private String storageCode;
    /**
     * 配件代码
     */
    private String partNo;
    /**
     * 配件名称
     */
    private String partName;
    /**
     * 单据号码
     */
    private String sheetNo;

    /**
     * 车牌号
     */
    private String license;
    /**
     * 缺件数量
     */
    private BigDecimal shortQuantity;
    /**
     * 经手人
     */
    private String handler;
    /**
     * 电话
     */
    private String phone;
    /**
     * 客户名称
     */
    private String customerName;
    private String itemUpdateStatus;//A新增 U修改或 D删除 S未修改
    /**
     * 差异数量
     */
    private BigDecimal differenceNum;

}
