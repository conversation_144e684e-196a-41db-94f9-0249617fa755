package com.volvo.maintain.application.maintainlead.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("上门取送车验车图片明细")
public class CarPhotosDetailsDto implements Serializable {
	
    private static final long serialVersionUID = 1L;
    
	@ApiModelProperty("验车照片字段名")
    private String fieldField;

    @ApiModelProperty("验车照片字段描述")
    private String fieldName;

    @ApiModelProperty("验车照片url")
    private String imageUrl;
}
