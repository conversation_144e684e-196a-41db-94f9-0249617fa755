package com.volvo.maintain.application.maintainlead.service.clues.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.log.Log;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.clues.*;
import com.volvo.maintain.application.maintainlead.dto.company.CompanyNewSelectDto;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceAssigPersonImportDTO;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceRecordImportDTO;
import com.volvo.maintain.application.maintainlead.dto.insurance.InviteInsuranceReqDto;
import com.volvo.maintain.application.maintainlead.dto.insurance.InviteInsuranceVehicleOwnerInfoDTO;
import com.volvo.maintain.application.maintainlead.excel.InsuranceAssigPersonImportExcel;
import com.volvo.maintain.application.maintainlead.excel.InsuranceRecordImportExcel;
import com.volvo.maintain.application.maintainlead.service.FullLeadsService;
import com.volvo.maintain.application.maintainlead.service.clues.RenewalOfInsuranceService;
import com.volvo.maintain.application.maintainlead.vo.QueryVehicleOwnerDataVo;
import com.volvo.maintain.application.maintainlead.vo.UserInfoVo;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.constants.RenewalOfInsuranceCluesConstant;
import com.volvo.maintain.infrastructure.enums.FullLeadsErrorEnum;
import com.volvo.maintain.infrastructure.enums.InsuranceTypeEnum;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.request.CompanySelectDTO;
import com.volvo.maintain.infrastructure.gateway.response.CompanyDetailDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ImportTempResult;
import com.volvo.maintain.infrastructure.util.DateUtil;
import com.volvo.maintain.infrastructure.util.ParamsValidator;
import com.volvo.maintain.interfaces.controller.AccidentCluesController;
import com.volvo.maintain.interfaces.vo.clues.InsuranceLeadsConfigVo;
import com.volvo.utils.BeanMapperUtil;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.framework.service.excel.ExcelDataType;
import com.yonyou.cyx.framework.service.excel.ExcelExportColumn;
import com.yonyou.cyx.function.exception.ServiceBizException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.*;

@Service
public class RenewalOfInsuranceServiceImpl implements RenewalOfInsuranceService {

    private final Logger logger = LoggerFactory.getLogger(AccidentCluesController.class);
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    @Autowired
    private MidEndOrgCenterFeign midEndOrgCenterFeign;

    @Autowired
    private DomainMaintainAuthFeign domainMaintainAuthFeign;

    @Resource
    private DownloadServiceFeign downloadServiceFeign;

    @Autowired
    private MidEndAuthCenterFeign midEndAuthCenterFeign;

    @Autowired
    private DomainMaintainLeadFeign domainMaintainLeadFeign;

    @Autowired
    private DmscusReportFeign dmscusReportFeign;

    @Autowired
    private FullLeadsService fullLeadsService;


    /**
     * 初始化经销商配置（单店保护，）
     * @param ownerCode 经销商
     * @return 返回字符串 成功失败 文字提示
     */
    @Override
    public String initConfig(String ownerCode, String allocationType) {
        List<CompanyDetailByCodeDto> companyDetailByCode = getCompanyDetailByCode(ownerCode, true);
        companyDetailByCode.forEach(s -> s.setAllocationType(Integer.valueOf(allocationType)));
        return StringUtils.isNotBlank(ownerCode)
                ? initCompany(companyDetailByCode, true)
                : initCompany(selectCompanyByStatus(Arrays.asList(RenewalOfInsuranceCluesConstant.DEALER_STATUS_PREPARATION,
                                                                  RenewalOfInsuranceCluesConstant.DEALER_STATUS_OPEN,
                                                                  RenewalOfInsuranceCluesConstant.DEALER_STATUS_SUSPENDED,
                                                                  RenewalOfInsuranceCluesConstant.DEALER_STATUS_OVERSELLING,
                                                                  RenewalOfInsuranceCluesConstant.DEALER_STATUS_TERMINATED)), null);
    }

    /**
     *  根据参数查询配置信息
     * @param afterBigAreaId 大区Id
     * @param afterSmallAreaId 小区Id
     * @param ownerCode 经销商
     * @return 根据参数查询配置信息
     */
    @Override
    public Page<InsuranceLeadsConfigDto> queryCompanyConfig(String afterBigAreaId, String afterSmallAreaId, String ownerCode, Integer pageNum, Integer pageSize) {
        DmsResponse<Page<InsuranceLeadsConfigDto>> queryCompanyConfigResponse = domainMaintainAuthFeign.queryCompanyConfig(afterBigAreaId, afterSmallAreaId, ownerCode, pageNum, pageSize);
        if (queryCompanyConfigResponse.isFail()) {
            throw new ServiceBizException("调用领域权限失败！");
        }
        Page<InsuranceLeadsConfigDto> data = queryCompanyConfigResponse.getData();
        List<InsuranceLeadsConfigDto> records = data.getRecords();
        if(CollectionUtils.isNotEmpty(records)){
            List<Long> userIds = new ArrayList<>();
            records.stream().forEach(item -> {
                userIds.add(Long.valueOf(item.getUpdatedBy()));
            });
            if(CollectionUtils.isNotEmpty(userIds)){
                List<Long> collect = userIds.stream().distinct().collect(Collectors.toList());
                UserInfoByUserIdsDto userInfoByUserIdsDto = UserInfoByUserIdsDto.builder().userIds(collect).build();
                RequestDto<UserInfoByUserIdsDto> build = new RequestDto<>();
                build.setData(userInfoByUserIdsDto);
                logger.info("queryUserInfoByIds params:{}", JSON.toJSONString(build));
                ResponseDto<List<UserInfoVo>> listResponseDto = midEndAuthCenterFeign.queryUserInfoByIds(build);
                if(listResponseDto.isFail() || CollectionUtils.isEmpty(listResponseDto.getData())){
                    logger.info("queryUserInfoByIds is fail:{}", JSON.toJSONString(listResponseDto));
                    return data;
                }
                Map<Integer, UserInfoVo> collect1 = listResponseDto.getData().stream().collect(Collectors.toMap(UserInfoVo::getUserId, Function.identity()));
                records.stream().forEach(item -> {
                    if(collect1.containsKey(Integer.valueOf(item.getUpdatedBy()))){
                        item.setUpdatedBy(collect1.get(Integer.valueOf(item.getUpdatedBy())).getEmployeeName());
                    }
                });
            }
        }
        return data;
    }

    /**
     * 根据对应参数进行修改数据
     * @param InsuranceLeadsConfigVo 经销商信息
     * @return 返回修改的数据集合
     */
    @Override
    public List<InsuranceLeadsConfigDto> modifyOwnerCodeConfig(InsuranceLeadsConfigVo InsuranceLeadsConfigVo) {
        DmsResponse<List<InsuranceLeadsConfigDto>> modifyOwnerCodeConfigResponse = domainMaintainAuthFeign.modifyOwnerCodeConfig(InsuranceLeadsConfigVo);
        if (modifyOwnerCodeConfigResponse.isFail()) {
            throw new ServiceBizException("调用领域权限失败！");
        }
        return modifyOwnerCodeConfigResponse.getData();
    }

    @Override
    public ImportTempResult<InsuranceLeadsConfigDto> importDealerConfigInfo(MultipartFile importFile, Boolean tab) {
        if (importFile.isEmpty()) {
            return new ImportTempResult<>();
        }
        ImportTempResult<InsuranceLeadsConfigDto> resultList;
        try {
            ImportParams importParams = new ImportParams();
            importParams.setNeedVerify(true);
            List<InsuranceLeadsConfigDto> parsingDataList = ExcelImportUtil.importExcel(
                    importFile.getInputStream(),
                    InsuranceLeadsConfigDto.class,
                    importParams);
            logger.info("export data params ：{}", JSONObject.toJSONString(parsingDataList));
            if (CollectionUtils.isEmpty(parsingDataList)) {
                throw new ServiceBizException("导入文件不能为空");
            }
            parsingDataList = parsingDataList.stream().peek(data -> {
                if (data.getRowNum() != null) {
                    data.setRowNum(data.getRowNum() + 1);
                }
            }).collect(Collectors.toList());
            // 解析数据
            resultList = parsingFilterData(parsingDataList, tab);
        } catch (ServiceBizException serviceBizException) {
            throw serviceBizException;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return resultList;
    }

    private ImportTempResult<InsuranceLeadsConfigDto> parsingFilterData(List<InsuranceLeadsConfigDto> parsingDataList, Boolean tab) {
        Map<Boolean, List<InsuranceLeadsConfigDto>> partitionedMap = parsingDataList.stream()
                .collect(Collectors.partitioningBy(dto -> dto.getOwnerCode() == null || dto.getOwnerCode().isEmpty()));
        // 获取 ownerCode 为空的组
        List<InsuranceLeadsConfigDto> ownerCodeEmptyList = partitionedMap.get(true);
        ownerCodeEmptyList.forEach(dto -> dto.setErrorReason("经销商为空"));
        // 获取 ownerCode 不为空的组
        List<InsuranceLeadsConfigDto> ownerCodeNotEmptyList = partitionedMap.get(false);
        ImportTempResult<InsuranceLeadsConfigDto> importTempResult = new ImportTempResult<>();
        String ownerCodeStr = ownerCodeNotEmptyList.stream().map(InsuranceLeadsConfigDto::getOwnerCode).collect(Collectors.joining(","));
        //查询所有  有效的经销商 (过滤掉终止运营的经销商) 为空说明所有的经销商都是无效的，
        List<InsuranceLeadsConfigDto> allocationIsEmpty = ownerCodeNotEmptyList.stream()
                .filter(s -> s.getAllocationType() == null)
                .peek(s -> s.setErrorReason("规则为空"))
                .collect(Collectors.toList());
        ownerCodeEmptyList.addAll(allocationIsEmpty);
        List<String> ruleIsEmpty = allocationIsEmpty.stream().map(InsuranceLeadsConfigDto::getOwnerCode).collect(Collectors.toList());
        List<CompanyDetailByCodeDto> companyDetailByCode = this.getCompanyDetailByCode(ownerCodeStr, false);
        if (CollectionUtils.isEmpty(companyDetailByCode)) {
            ownerCodeNotEmptyList.forEach(dto -> dto.setErrorReason("无效经销商"));
            ownerCodeEmptyList.addAll(ownerCodeNotEmptyList);
            importTempResult.setErrorList(ownerCodeEmptyList);
            importTempResult.setSuccessCount(0);
            importTempResult.setSuccessList(Collections.emptyList());
            return  importTempResult;
        }
        // 找出差集 查询到的经销商 和 为true的数据 差集为 无效经销商 (companyCodeList) 所有有效的经销商
        List<String> companyCodeList = companyDetailByCode.stream().map(CompanyDetailByCodeDto::getCompanyCode).collect(Collectors.toList());
        List<InsuranceLeadsConfigDto> missingOwnerCodeDto = ownerCodeNotEmptyList.stream()
                .filter(v -> !companyCodeList.contains(v.getOwnerCode()))
                .peek(dto -> dto.setErrorReason("无效经销商"))
                .collect(Collectors.toList());
        ownerCodeEmptyList.addAll(missingOwnerCodeDto);
        // 查询 是否已经存在经销商规则！
        List<InsuranceLeadsConfigDto> insuranceLeadsConfigList = queryInsuranceLeadsExport(InsuranceLeadsConfigVo.builder().ownerCode(String.join(",", companyCodeList)).currentPage(1).pageSize(20).build());
        Map<String, InsuranceLeadsConfigDto> dtoMap = ownerCodeNotEmptyList.stream().collect(Collectors.toMap(InsuranceLeadsConfigDto::getOwnerCode, Function.identity(), (k1, k2) -> k2));
        List<CompanyDetailByCodeDto> companyDetails = companyDetailByCode
                .stream().peek(s -> {
                    if (dtoMap.containsKey(s.getCompanyCode())) {
                        s.setAllocationType(dtoMap.get(s.getCompanyCode()).getAllocationType());
                    }
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(insuranceLeadsConfigList) && tab) {
            this.initCompany(companyDetails, false);  //添加数据
        }
        //insuranceLeadsConfigList 包含存在规则和不存在规则的数据，
        List<String> isExistOwnerCode = insuranceLeadsConfigList.stream().map(InsuranceLeadsConfigDto::getOwnerCode).collect(Collectors.toList());
        List<InsuranceLeadsConfigDto> existDataList =
        ownerCodeNotEmptyList.stream()
                .filter(v -> isExistOwnerCode.contains(v.getOwnerCode()))
                .filter(v1 -> companyCodeList.contains(v1.getOwnerCode()))
                .peek(dto -> dto.setErrorReason("当前经销商分配规则已存在"))
                .collect(Collectors.toList());
        ownerCodeEmptyList.addAll(existDataList);
        // 筛选出 不在表中的数据。
        List<CompanyDetailByCodeDto> filteredList = companyDetailByCode.stream()
                .filter(company -> !isExistOwnerCode.contains(company.getCompanyCode()) && !ruleIsEmpty.contains(company.getCompanyCode()))
                .collect(Collectors.toList());
        if (tab) {
            this.initCompany(filteredList, false);  //添加数据
        }
        List<String> companyList = filteredList.stream().map(CompanyDetailByCodeDto::getCompanyCode).collect(Collectors.toList());
        List<InsuranceLeadsConfigDto> successDataList = ownerCodeNotEmptyList.stream().filter(v -> companyList.contains(v.getOwnerCode())).collect(Collectors.toList());
        importTempResult.setSuccessList(successDataList);
        importTempResult.setErrorList(ownerCodeEmptyList);
        importTempResult.setSuccessCount(successDataList.size()); // 这里不会为null 。
        return importTempResult;
    }

    @Override
    public void dataResultExport(InsuranceLeadsConfigDto dto) {
        String currentTime = LocalDateTime.now().format(formatter);
        logger.info("company rule configuration data export Time: {}", currentTime);
        try{
            //获取导出字段
            List<ExcelExportColumn> exportColumnList = buildExcelColumn(dto.getFlag());
            DownloadDto downloadDto = new DownloadDto();
            logger.info("company rule configuration data export requestParams:{}", JSONObject.toJSONString(dto));
            //batchNo转map
            downloadDto.setQueryParams(BeanUtil.beanToMap(dto));
            downloadDto.setExcelName(CommonConstant.DICT_IS_YES == dto.getFlag() ? "经销商规则配置导出.xlsx" : "经销商提前出单日期导出.xlsx");
            downloadDto.setSheetName(CommonConstant.DICT_IS_YES == dto.getFlag() ? "经销商规则配置" : "经销商提前出单");
            downloadDto.setServiceUrl(RenewalOfInsuranceCluesConstant.EXPORT_URL);
            downloadDto.setExcelExportColumnList(exportColumnList);
            downloadServiceFeign.downloadExportExcel(downloadDto);
        } catch (Exception e){
            logger.error("company rule configuration data export：fail",e);
            e.printStackTrace();
        }
    }

    @Override
    public List<InsuranceLeadsConfigDto> modifyOwnerCodeConfigDate(Integer advanceDays) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        InsuranceLeadsConfigVo vo = new InsuranceLeadsConfigVo();
        vo.setAdvanceDays(advanceDays);
        vo.setOwnerCode(currentLoginInfo.getOwnerCode());
        vo.setFlag(CommonConstant.ALLOCATION_TYPE_ONE);
        logger.info("modifyOwnerCodeConfigDate******:{}", JSON.toJSONString(vo));
        DmsResponse<List<InsuranceLeadsConfigDto>> modifyOwnerCodeConfigResponse = domainMaintainAuthFeign.modifyOwnerCodeConfig(vo);
        if (modifyOwnerCodeConfigResponse.isFail()) {
            throw new ServiceBizException("调用领域权限失败！");
        }
        return modifyOwnerCodeConfigResponse.getData();
    }

    @Override
    public Page<InsuranceLeadsConfigLogDto> queryInsuranceLeadsModifyLog(String ownerCode, Integer flag, Integer pageNum, Integer pageSize) {
        DmsResponse<Page<InsuranceLeadsConfigLogDto>> pageDmsResponse = domainMaintainAuthFeign.queryInsuranceLeadsModifyLog(ownerCode, flag, pageNum, pageSize);
        if (pageDmsResponse.isFail()) {
            logger.info("queryInsuranceLeadsModifyLog is fail:{}", JSON.toJSONString(pageDmsResponse));
            throw new ServiceBizException("查询经销商提前出单日期修改记录失败");
        }
        Page<InsuranceLeadsConfigLogDto> data = pageDmsResponse.getData();
        List<InsuranceLeadsConfigLogDto> records = data.getRecords();
        List<Long> userIds = new ArrayList<>();
        Optional.ofNullable(records).ifPresent(items -> items.forEach(item -> {
            userIds.add(Long.valueOf(item.getOldCreatedBy()));
            userIds.add(Long.valueOf(item.getCreatedBy()));
        }));
        if(CollectionUtils.isNotEmpty(userIds)){
            List<Long> collect = userIds.stream().filter(item -> Objects.nonNull(item)).distinct().collect(Collectors.toList());
            UserInfoByUserIdsDto userInfoByUserIdsDto = UserInfoByUserIdsDto.builder().userIds(collect).build();
            RequestDto<UserInfoByUserIdsDto> build = new RequestDto<>();
            build.setData(userInfoByUserIdsDto);
            logger.info("queryUserInfoByIds params:{}", JSON.toJSONString(build));
            ResponseDto<List<UserInfoVo>> listResponseDto = midEndAuthCenterFeign.queryUserInfoByIds(build);
            if(listResponseDto.isFail() || CollectionUtils.isEmpty(listResponseDto.getData())){
                logger.info("queryUserInfoByIds is fail:{}", JSON.toJSONString(listResponseDto));
                return data;
            }
            Map<Integer, UserInfoVo> collect1 = listResponseDto.getData().stream().collect(Collectors.toMap(UserInfoVo::getUserId, Function.identity()));
            records.stream().forEach(item -> {
                if(collect1.containsKey(Integer.valueOf(item.getCreatedBy()))){
                    item.setCreatedBy(collect1.get(Integer.valueOf(item.getCreatedBy())).getEmployeeName());
                }
                if(collect1.containsKey(Integer.valueOf(item.getOldCreatedBy()))){
                    item.setOldCreatedBy(collect1.get(Integer.valueOf(item.getOldCreatedBy())).getEmployeeName());
                }
            });
        }
        return data;
    }


    /**
     * 根据经销商查询 门店状态  （支持批量）
     * @param ownerCode 经销商
     * @return 入参经销商所有详情信息
     */
    private List<CompanyDetailByCodeDto> getCompanyDetailByCode(String ownerCode, Boolean flag) {
        IsExistByCodeDto isExistByCodeInfo = new IsExistByCodeDto();
        isExistByCodeInfo.setCompanyCode(ownerCode);
        ResponseDto<List<CompanyDetailByCodeDto>> listResponseDto = midEndOrgCenterFeign.selectByCompanyCode(isExistByCodeInfo);
        logger.info("queryCompanyDetailResponseParams:{}", JSONObject.toJSONString(listResponseDto));
        if (listResponseDto.isFail()) {
            throw new ServiceBizException("获取领域权限失败！");
        }
        if (CollectionUtils.isEmpty(listResponseDto.getData()) && flag) {
            throw new ServiceBizException("当前为无效经销商代码，请修改!");
        }
        return listResponseDto.getData();
    }

    /**
     * 根据经销商查询 门店状态
     * @param statusList
     * 经销商门店状态 营业状态列表，16031001:开业准备, 16031002:营业中, 16031003:暂停营业, 16031004:终止运营, 16031005:过度销售
     * @return 入参经销商所有详情信息
     */
    private List<CompanyDetailByCodeDto> selectCompanyByStatus(List<Integer> statusList) {
        CompanySelectDTO companyInfo = new CompanySelectDTO();
        companyInfo.setStatusList(statusList);
        ResponseDto<List<CompanyDetailByCodeDto>> listResponseDto = midEndOrgCenterFeign.selectCompanyByStatus(companyInfo);
        logger.info("queryCompanyDetailByStatusResponseParams:{}", JSONObject.toJSONString(listResponseDto));
        if (listResponseDto.isFail() || CollectionUtils.isEmpty(listResponseDto.getData())) {
            return Collections.emptyList();
        }
        return listResponseDto.getData();
    }

    /**
     *  初始化 经销商信息配置
     * @param companyDetailLists 所有经销商详情信息
     * @return 字符串成功失败提示！
     */
    private String initCompany(List<CompanyDetailByCodeDto> companyDetailLists, Boolean flag) {
        DmsResponse<String> resultResponse = domainMaintainAuthFeign.init(companyDetailLists, flag);
        logger.info("initCompanyResponseParams:{}", JSONObject.toJSONString(resultResponse));
        if (resultResponse.isFail()) {
            throw new ServiceBizException(resultResponse.getErrMsg());
        }
        if (StringUtils.isBlank(resultResponse.getData())) {
            throw new ServiceBizException("initialization configuration failed.");
        }
        return resultResponse.getData();
    }


    /**
     * 导出字段
     */
    private List<ExcelExportColumn> buildExcelColumn(Integer flag) {
        List<ExcelExportColumn> exportColumnList = new ArrayList<>();
        exportColumnList.add(new ExcelExportColumn("afterBigareaName", "大区名称"));
        exportColumnList.add(new ExcelExportColumn("afterSmallareaName", "小区名称"));
        exportColumnList.add(new ExcelExportColumn("ownerCode", "经销商代码"));
        if (CommonConstant.DICT_IS_YES == flag) {
            exportColumnList.add(new ExcelExportColumn("allocationTypeStr", "规则", ExcelDataType.Dict));
        } else {
            exportColumnList.add(new ExcelExportColumn("advanceDays", "提前出单日期", ExcelDataType.Dict));
        }
        exportColumnList.add(new ExcelExportColumn("createdAt", "创建时间"));
        exportColumnList.add(new ExcelExportColumn("updatedAt", "更新时间"));
//        exportColumnList.add(new ExcelExportColumn("updatedBy", "修改人"));
        return exportColumnList;
    }

    @Override
    public List<InsuranceLeadsConfigDto> queryInsuranceLeadsExport(InsuranceLeadsConfigVo vo) {
        DmsResponse<List<InsuranceLeadsConfigDto>> queryCompanyConfigResponse = domainMaintainAuthFeign.queryInsuranceLeadsExport(vo.getOwnerCode(), vo.getCurrentPage(), vo.getPageSize(), vo.getAfterBigareaIds(), vo.getAfterSmallareaIds());
        if (queryCompanyConfigResponse.isFail()) {
            throw new ServiceBizException("调用领域权限失败！");
        }
        List<InsuranceLeadsConfigDto> insuranceLeadsConfigList = queryCompanyConfigResponse.getData();
        if (CollectionUtils.isEmpty(insuranceLeadsConfigList)) {
            logger.info("import InsuranceLeadsConfig data is empty.");
            return Collections.emptyList();
        }
        return queryUserNameByUserId(insuranceLeadsConfigList, vo);
    }

    @Override
    public void importRenewalOfInsurance(MultipartFile importFile) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        // 校验经销商是否是白名单
        if (!isWhite(currentLoginInfo.getCompanyCode())) {
            // 非白名单，删除元素
            throw new ServiceBizException("经销商非白名单,不允许导入续保线索");
        }
        DmsResponse<Page<InsuranceLeadsConfigDto>> pageDmsResponse = domainMaintainAuthFeign.queryCompanyConfig("", "", currentLoginInfo.getCompanyCode(), 1, 10);
        logger.info("importRenewalOfInsurance>>>:{}", JSON.toJSONString(pageDmsResponse));
        if (ObjectUtils.isEmpty(pageDmsResponse)) {
            throw new ServiceBizException("请先设置提前出单日期!");
        }
        if(CollectionUtils.isEmpty(pageDmsResponse.getData().getRecords())){
            throw new ServiceBizException("请先设置提前出单日期!");
        }
        Integer days = pageDmsResponse.getData().getRecords().get(0).getAdvanceDays();
        // 处理数据
        ImportParams importParams = new ImportParams();
        importParams.setNeedVerify(true);
        List<InsuranceRecordImportExcel> parsingDataList = null;
        try {
            parsingDataList = ExcelImportUtil.importExcel(
                    importFile.getInputStream(),
                    InsuranceRecordImportExcel.class,
                    importParams);
        } catch (Exception e) {
            throw new ServiceBizException("导入文件不能为空");
        }
        if (CollectionUtils.isEmpty(parsingDataList)) {
            throw new ServiceBizException("导入文件不能为空");
        }
        // 通过经销商代码和角色代码查询员工列表  -- 调中台接口
        Map<String, EmpByRoleCodeDto> collect = this.queryRole(currentLoginInfo);
        // 通过ADB查询车主车辆信息
        Map<String, InviteInsuranceVehicleOwnerInfoDTO> vehicleList = this.queryVehicle(parsingDataList);
        logger.info("collect.size():{}", parsingDataList.size());
        logger.info("vehicleList.size():{}", vehicleList.size());
        Map<String, Object> map = new HashMap<>();
        List<String> vinlist = new LinkedList<>();
        List<InsuranceRecordImportDTO> tmpDtoList = new ArrayList<InsuranceRecordImportDTO>();
        for (int i = 0; i< parsingDataList.size() ; i++){
            InsuranceRecordImportExcel item = parsingDataList.get(i);
            if(Objects.isNull(item.getVin())
                    && Objects.isNull(item.getName())
                    && Objects.isNull(item.getTel())
                    && Objects.isNull(item.getStoreInsuranceExpiryDate())
                    && Objects.isNull(item.getInsuranceType())
                    && Objects.isNull(item.getInsuranceName())
                    && Objects.isNull(item.getSaId())
            ){
                logger.info("读取到了空行,直接跳过");
                continue;
            }
            logger.info("readRowCallBack>>:{}", JSON.toJSONString(item));
            StringBuilder errMsg = new StringBuilder();
            this.checkedParams(item, errMsg);
            Optional.ofNullable(item.getStoreInsuranceExpiryDate()).ifPresent(v -> {
                // 只能补充进入报价期的线索
                Date date6 = new Date();
                String date6Str = DateUtil.formatDateByFormat(date6, DateUtil.SIMPLE_DATE_FORMAT);
                Date date57 = DateUtil.parseDate(date6Str, DateUtil.SIMPLE_DATE_FORMAT);
                Date date8 = DateUtil.addDay(date57, days);
                // 店端保险到期时间和当前时间比 0 等于 -1小于 1大于
                int j = v.compareTo(date57);
                if(j < 0){
                    errMsg.append("店端续保到期日期小于当前时间!");
                }
                // 店端保险到期时间和最大续保到期日期比较 0 等于 -1小于 1大于
                int k = v.compareTo(date8);
                if(k > 0){
                    errMsg.append("店端续保到期日期大于最大续保到期日期!");
                }
            });

            InsuranceRecordImportDTO detailPO = new InsuranceRecordImportDTO();
            if(errMsg.length() > 0){
                detailPO.setIsError(NUM_1);
                detailPO.setErrorMsg(errMsg.toString());
            }
            detailPO.setLineNumber(i+2);
            detailPO.setVin(StringUtils.isNotBlank(item.getVin()) ? item.getVin().replaceAll(" ", "") : item.getVin()); // 车架号
            detailPO.setName(item.getName()); // 客户姓名
            detailPO.setTel(StringUtils.isNotBlank(item.getTel()) ? item.getTel().replaceAll(" ","") : item.getTel()); // 客户电话
            if(StringUtils.isNotBlank(item.getInsuranceType())){
                detailPO.setInsuranceType(InsuranceTypeEnum.fromChineseName(item.getInsuranceType()).getValue()); // 客户续保类型
            }
            detailPO.setInsuranceName(item.getInsuranceName()); // 保险公司类型
            detailPO.setSaId(item.getSaId()); // 跟进人员
            detailPO.setDataSources(DATA_SOURCE_DEALER);
            detailPO.setStoreInsuranceExpiryDate(item.getStoreInsuranceExpiryDate());
            detailPO.setDealerCode(currentLoginInfo.getCompanyCode());
            if(StringUtils.isNotBlank(item.getSaId()) && collect.containsKey(item.getSaId())){
                detailPO.setLastSaId(String.valueOf(collect.get(item.getSaId()).getUserId()));
                detailPO.setLastSaName(collect.get(item.getSaId()).getEmployeeName());
                detailPO.setSaName(collect.get(item.getSaId()).getEmployeeName());
            }
            if(StringUtils.isNotBlank(item.getVin()) && vehicleList.containsKey(item.getVin())){
                detailPO.setLicensePlateNum(vehicleList.get(item.getVin()).getLicense());
                detailPO.setModel(vehicleList.get(item.getVin()).getModel());
            }
            vinlist.add(item.getVin());
            tmpDtoList.add(detailPO);
        }
        // 导入数据
        logger.info("sendData>>:{}", tmpDtoList.size());
        Map<String, List<InsuranceRecordImportDTO>> collect2 = tmpDtoList.stream().collect(Collectors.groupingBy(vin -> {
            return StringUtils.isNotBlank(vin.getVin()) ? vin.getVin() : "";
        }));
        collect2.forEach((key, value) -> {
            if(value.size() > 1){
                value.forEach(item -> {
                    item.setIsError(NUM_1);
                    item.setErrorMsg(StringUtils.isNotBlank(item.getErrorMsg()) ? item.getErrorMsg() + "重复vin:"+item.getVin() : "重复vin:"+item.getVin());
                });
            }
        });
        domainMaintainLeadFeign.importRenewalOfInsurance(tmpDtoList);
    }

    /**
     * 校验参数
     * @return
     */
    private void checkedParams(InsuranceRecordImportExcel item, StringBuilder errMsg){
        if(StringUtils.isBlank(item.getVin())) errMsg.append("VIN不能为空!");
        if(StringUtils.isBlank(item.getName())) errMsg.append("客户姓名不能为空!");
        if(StringUtils.isBlank(item.getTel())) errMsg.append("电话不能为空!");
        if(Objects.isNull(item.getStoreInsuranceExpiryDate())) errMsg.append("店端续保到期日期不能为空!");
        if(StringUtils.isBlank(item.getInsuranceType())) errMsg.append("续保客户类型不能为空!");
        if(("新转续".equals(item.getInsuranceType()) || "续转续".equals(item.getInsuranceType())) && StringUtils.isBlank(item.getInsuranceName()))
            errMsg.append("新转续/续转续时保险公司不能为空!");
        if(!StringUtils.isBlank(item.getVin()) && !ParamsValidator.validateVin(item.getVin())){
            errMsg.append("VIN不合法!");
        }
        if(!StringUtils.isBlank(item.getTel()) && !ParamsValidator.validatePhoneNumber(item.getTel())){
            errMsg.append("电话不合法!");
        }
        if(!StringUtils.isBlank(item.getName()) && !ParamsValidator.validateNumber(item.getName())){
            errMsg.append("客户姓名不合法!");
        }
    }
    /**
     * 查询保险专员信息
     * @return
     */
    private Map<String, EmpByRoleCodeDto> queryRole(CurrentLoginInfoDto currentLoginInfo){
        List<String> roleCode = new ArrayList<>();
        roleCode.add(ROLE_CODE_BXZY);
        EmpByRoleCodeDto eto = new EmpByRoleCodeDto();
        eto.setCompanyCode(currentLoginInfo.getOwnerCode());
        eto.setRoleCode(roleCode);
        eto.setIsOnjob(CommonConstant.IS_ON_JOB_IN);
        ResponseDto<EmpByRoleCodeDto> build = new ResponseDto<>();
        build.setData(eto);
        DmsResponse<List<EmpByRoleCodeDto>> listDmsResponse = midEndAuthCenterFeign.queryDealerUser(build);
        Map<String, EmpByRoleCodeDto> collect = new HashMap<>();
        if(CollectionUtils.isNotEmpty(listDmsResponse.getData())){
            collect = listDmsResponse.getData().stream().collect(Collectors.toMap(EmpByRoleCodeDto::getUserCode, Function.identity(), (P1, P2) -> P1));
        }
        return collect;
    }
    /**
     * 查询车辆信息
     * @return
     */
    private Map<String, InviteInsuranceVehicleOwnerInfoDTO> queryVehicle(List<InsuranceRecordImportExcel> parsingDataList){
        Map<String, InviteInsuranceVehicleOwnerInfoDTO> vehicleList = new HashMap<>();
        List<String> collect2 = parsingDataList.stream()
                .filter(item -> Objects.nonNull(item.getVin()))
                .distinct()
                .map(InsuranceRecordImportExcel::getVin)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(collect2)){
            logger.info("queryVehicle:{}", JSON.toJSONString(collect2));
            DmsResponse<QueryVehicleOwnerDataVo> vehicleResponse = dmscusReportFeign.selectVehicleOwnerInfoByList(collect2);
            if(Objects.nonNull(vehicleResponse)
                    && !vehicleResponse.isFail()
                    && Objects.nonNull(vehicleResponse.getData())
                    && CollectionUtils.isNotEmpty(vehicleResponse.getData().getVehicleOwnerInfoList())){
                vehicleList = vehicleResponse.getData().getVehicleOwnerInfoList().stream().collect(Collectors.toMap(InviteInsuranceVehicleOwnerInfoDTO::getVin, Function.identity(), (P1, P2) -> P1));
            }
        }
        return vehicleList;
    }
    @Override
    public Page<InsuranceRecordImportDTO> importQuery(Integer flag, Integer pageNum, Integer pageSize) {
        DmsResponse<Page<InsuranceRecordImportDTO>> pageDmsResponse = domainMaintainLeadFeign.importQuery(flag, pageNum, pageSize);
        if (pageDmsResponse.isFail()) {
            logger.info("importQuery is fail:{}", JSON.toJSONString(pageDmsResponse));
            throw new ServiceBizException("查询导入线索信息失败");
        }
        return pageDmsResponse.getData();
    }

    @Override
    public String initConfigJob() {
        return initCompany(selectCompanyByStatus(Arrays.asList(RenewalOfInsuranceCluesConstant.DEALER_STATUS_PREPARATION,
                RenewalOfInsuranceCluesConstant.DEALER_STATUS_OPEN,
                RenewalOfInsuranceCluesConstant.DEALER_STATUS_SUSPENDED,
                RenewalOfInsuranceCluesConstant.DEALER_STATUS_OVERSELLING,
                RenewalOfInsuranceCluesConstant.DEALER_STATUS_TERMINATED)), false);
    }

    private List<InsuranceLeadsConfigDto> queryUserNameByUserId(List<InsuranceLeadsConfigDto> insuranceLeadsConfigList, InsuranceLeadsConfigVo vo) {
        insuranceLeadsConfigList.stream().forEach(item -> {
            item.setAllocationTypeStr(Objects.equals(item.getAllocationType(), RenewalOfInsuranceCluesConstant.SINGLE_STORE_PROTECTION) ? "单店保护" : "解除单店保护");
            // 如果是导出经销商规则，则取厂端修改时间，如果是导出提前出单日期，则取店端修改时间
            if (StringUtils.isNotBlank(vo.getFlag())) {
                if (CommonConstant.DICT_IS_YES == Integer.valueOf(vo.getFlag())) {
                    item.setUpdatedAt(Objects.nonNull(item.getFactoryUpdateAt()) ? item.getFactoryUpdateAt() : null);
                } else {
                    item.setUpdatedAt(Objects.nonNull(item.getStoreUpdateAt()) ? item.getStoreUpdateAt() : null);
                }
            }
        });
        // 1. 从insuranceLeadsConfigList中提取每个对象的updateBy属性
//        List<Long> updateByIds = insuranceLeadsConfigList.stream()
//                .filter(dto -> Validator.isNumber(dto.getUpdatedBy()))
//                .map(config -> Long.parseLong(config.getUpdatedBy())).collect(Collectors.toList());
//        logger.info("queryUserNameByUserIdResponseParams:{}", JSONObject.toJSONString(updateByIds));
//        // 2. 使用midEndAuthCenterFeign.queryUserInfoByIds查询用户信息
//        UserInfoByUserIdsDto buildUserInfoIds = UserInfoByUserIdsDto.builder().userIds(updateByIds).build();
//        RequestDto<UserInfoByUserIdsDto> requestDto = new RequestDto<>();
//        requestDto.setData(buildUserInfoIds);
//        ResponseDto<List<UserInfoVo>> userInfoList = midEndAuthCenterFeign.queryUserInfoByIds(requestDto);
//        if (userInfoList.isFail()) {
//            throw new ServiceBizException("获取组织中心用户信息失败！");
//        }
//        if (CollectionUtils.isEmpty(userInfoList.getData())) {
//            return insuranceLeadsConfigList.stream().peek(config -> {
//                if (config.getUpdatedBy().equals("-1")) {
//                    config.setUpdatedBy(null);
//                }
//            }).collect(Collectors.toList());
//        }
//        // 3. 将查询回来的用户信息赋值给insuranceLeadsConfigList中的每一个对象
//        Map<Integer, UserInfoVo> userInfoMap = userInfoList.getData().stream()
//                .collect(Collectors.toMap(UserInfoVo::getUserId, Function.identity(), (k1, k2) -> k1));
//
//        insuranceLeadsConfigList.stream()
//                .filter(dto -> Validator.isNumber(dto.getUpdatedBy()))
//                .forEach(config -> {
//                    config.setAllocationTypeStr(Objects.equals(config.getAllocationType(), RenewalOfInsuranceCluesConstant.SINGLE_STORE_PROTECTION) ? "单店保护" : "解除单店保护");
//                    UserInfoVo userInfo = userInfoMap.get(Integer.parseInt(config.getUpdatedBy()));
//                    if (userInfo != null) {
//                        config.setUpdatedBy(userInfo.getEmployeeName());
//                    }
//                    if (config.getUpdatedBy().equals("-1")) {
//                        config.setUpdatedBy(null);
//                    }
//                });
        return insuranceLeadsConfigList;
    }
    /**
     * 查询是否是白名单店
     * @param dealer
     * @return
     */
    private boolean isWhite(String dealer) {
        logger.info("importRenewalOfInsurance isWhite dealer:{}", dealer);
        DmsResponse<Object> response = domainMaintainAuthFeign.checkWhitelist(dealer, CommonConstant.RENEWAL_INSURANCE_WHITE, CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE, "");
        logger.info("importRenewalOfInsurance isWhite response:{}",response);
        if (Objects.isNull(response) || response.isFail()){
            logger.info("importRenewalOfInsurance isWhite error");
            return false;
        }
        Object data = response.getData();
        if (null == data) {
            logger.info("importRenewalOfInsurance isWhite data isnull");
            return false;
        }
        try {
            return Boolean.parseBoolean(data.toString());
        } catch (Exception e) {
            logger.info("importRenewalOfInsurance isWhite e:{}", e);
            return false;
        }
    }

    /**
     * 续保线索超时关闭
     */
    @Override
    public void closeRenewalLead(Date adviseInDate) {
        DmsResponse<Boolean> response = domainMaintainAuthFeign.checkIfWhitelistEnabled(CommonConstant.RENEWAL_INSURANCE_WHITE);
        if (response.isFail()){
            logger.info("查询续保白名单接口失败");
            return;
        }

        Boolean data = response.getData();

        if(Objects.isNull(data)){
            logger.info("续保白名单未开启");
            return;
        }

        List<String> dealerCodes = Lists.newArrayList();
        if(data){
            DmsResponse<List<String>> dealerResponse = domainMaintainAuthFeign.getWhitelistedDealers(CommonConstant.RENEWAL_INSURANCE_WHITE, CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE);
            if(dealerResponse.isFail()){
                logger.info("查询续保白名单经销商接口失败");
                return;
            }else{
                dealerCodes = dealerResponse.getData();
                if(CollectionUtils.isEmpty(dealerCodes)){
                    logger.info("续保白名单经销商未配置");
                    return;
                }
                logger.info("续保白名单经销商 dealerCodes:{}", dealerCodes);
            }
        }else{
            logger.info("续保白名单经销商全网开放");
        }

        logger.info("开始关闭续保定时");
        InviteInsuranceReqDto reqDto = new InviteInsuranceReqDto();
        if(Objects.nonNull(adviseInDate)){
            reqDto.setAdviseInDate(adviseInDate);
        }
        reqDto.setDealerCodes(dealerCodes);
        domainMaintainLeadFeign.closeRenewalLead(reqDto);
        logger.info("结束关闭续保定时");
    }

    @Override
    public void executeImportRenewalOfInsurance() {
        DmsResponse<Void> voidDmsResponse = domainMaintainLeadFeign.executeImportRenewalOfInsurance();
        if (voidDmsResponse.isFail()) {
            logger.info("executeImportRenewalOfInsurance:{}", JSON.toJSONString(voidDmsResponse));
            throw new ServiceBizException(voidDmsResponse.getErrMsg());
        }
    }

    /**
     * 续保线索自动分撇
     */
    @Override
    public void assignClue() {
        logger.info("assignClue自动分配续保定时任务开始");
        //查询所有待分配经销商
        DmsResponse<List<String>> response = domainMaintainLeadFeign.clueDealer();
        List<String> dealerCodes = response.getData();
        if(CollectionUtils.isNotEmpty(dealerCodes)){
            dealerCodes = dealerCodes.stream().filter(Objects::nonNull).collect(Collectors.toList());
            Lists.partition(dealerCodes,20).forEach(codes -> {
                try {
                    List<DistributeClueDto> distributeClueDtos = codes.stream().map(e -> {
                        DistributeClueDto clueDto = new DistributeClueDto();
                        clueDto.setDealerCode(e);
                        List<EmpByRoleCodeDto> empByRoleCodeDtos = selectFWJLByOwnerCode(e, ROLE_CODE_BXZY);
                        if (CollectionUtils.isNotEmpty(empByRoleCodeDtos)) {
                            List<Long> saIds = empByRoleCodeDtos.stream().map(EmpByRoleCodeDto::getUserId).collect(Collectors.toList());
                            clueDto.setSaIds(saIds);
                        }
                        return clueDto;
                    }).filter(e -> CollectionUtils.isNotEmpty(e.getSaIds())).collect(Collectors.toList());
                    DmsResponse<Void> assignClueResponse = domainMaintainLeadFeign.assignClue(distributeClueDtos);
                    logger.info("assignClue:{}", JSON.toJSONString(assignClueResponse));
                } catch (Exception e) {
                    logger.error("assignClue error", e);
                }
            });
        }
        //续保跟进透明记录触点
        /*List<DistributeClueDto> dtos = dealerCodes.stream().map(e -> {
            DistributeClueDto clueDto = new DistributeClueDto();
            clueDto.setDealerCode(e);
            return clueDto;
        }).collect(Collectors.toList());
        try{
            DmsResponse<List<Long>> inviteIds = domainMaintainLeadFeign.getDistributionInsuranceVehicleRecordIds(dtos);
            logger.info("续保跟进透明记录触点 inviteIds:{}", JSON.toJSONString(inviteIds));
            if (inviteIds.isSuccess() && CollectionUtils.isNotEmpty(inviteIds.getData())) {
                fullLeadsService.buildRenewalLeadStaticDto(inviteIds.getData());
            }
        }catch (Exception e){
            logger.error("续保线索跟进报表记录失败",e);
        }*/
        logger.info("assignClue自动分配续保定时任务结束");
    }


    @Override
    public void importAssigPerson(MultipartFile importFile) {
        logger.info("importAssigPerson start");
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if (StringUtils.isBlank(currentLoginInfo.getOwnerCode())) {
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        // 处理数据
        ImportParams importParams = new ImportParams();
        importParams.setNeedVerify(true);
        List<InsuranceAssigPersonImportExcel> parsingDataList = null;
        try {
            parsingDataList = ExcelImportUtil.importExcel(
                    importFile.getInputStream(),
                    InsuranceAssigPersonImportExcel.class,
                    importParams);
        } catch (Exception e) {
            throw new ServiceBizException("导入文件不能为空");
        }
        if (CollectionUtils.isEmpty(parsingDataList)) {
            throw new ServiceBizException("导入文件不能为空");
        }
        List<InsuranceAssigPersonImportDTO> tmpDtoList = new ArrayList<InsuranceAssigPersonImportDTO>();
        for (int i = 0; i< parsingDataList.size() ; i++){
            InsuranceAssigPersonImportExcel item = parsingDataList.get(i);
            if(Objects.isNull(item.getVin())
                    && Objects.isNull(item.getSaCode())
                    && Objects.isNull(item.getSaName())){
                logger.info("读取到了空行,直接跳过");
                continue;
            }
            logger.info("importAssigPerson>>:{}", JSON.toJSONString(item));
            // 校验参数
            InsuranceAssigPersonImportDTO detailPO = new InsuranceAssigPersonImportDTO();
            detailPO.setIsError(NUM_0);
            detailPO.setLineNumber(i+NUM_2);
            detailPO.setVin(StringUtils.isNotBlank(item.getVin()) ? item.getVin().replaceAll(" ", "") : item.getVin()); // 车架号
            detailPO.setSaName(item.getSaName()); // 跟进人员姓名
            detailPO.setSaAccount(StringUtils.isNotBlank(item.getSaCode()) ? item.getSaCode().replaceAll(" ","") : item.getSaCode()); // 跟进人员账号
            tmpDtoList.add(detailPO);
        }
        // 导入数据
        domainMaintainLeadFeign.importAssigPerson(tmpDtoList, currentLoginInfo.getOwnerCode());
        logger.info("importAssigPerson end");
    }

    @Override
    public Page<InsuranceAssigPersonImportDTO> queryImportAssigPerson(Integer flag, Integer pageNum, Integer pageSize) {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if (StringUtils.isBlank(currentLoginInfo.getOwnerCode())) {
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        DmsResponse<Page<InsuranceAssigPersonImportDTO>> pageDmsResponse = domainMaintainLeadFeign.queryImportAssigPerson(flag, pageNum, pageSize, currentLoginInfo.getOwnerCode());
        if (pageDmsResponse.isFail()) {
            logger.info("queryImportAssigPerson is fail:{}", JSON.toJSONString(pageDmsResponse));
            throw new ServiceBizException("查询导入分配人信息失败");
        }
        return pageDmsResponse.getData();
    }

    @Override
    public void executeImportAssigPerson() {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if (StringUtils.isBlank(currentLoginInfo.getOwnerCode())) {
            throw new ServiceBizException("获取登录用户信息失败，请重新登录！");
        }
        DmsResponse<Void> voidDmsResponse = domainMaintainLeadFeign.executeImportAssigPerson(currentLoginInfo.getOwnerCode());
        if (voidDmsResponse.isFail()) {
            logger.info("executeImportAssigPerson:{}", JSON.toJSONString(voidDmsResponse));
            throw new ServiceBizException(voidDmsResponse.getErrMsg());
        }
    }

    /**
     * 根据经销商查询服务经理
     */
    protected List<EmpByRoleCodeDto> selectFWJLByOwnerCode(String ownerCode,String roleCode) {
        logger.info("selectFWJLByOwnerCode start ownerCode:{}", ownerCode);
        ResponseDto<EmpByRoleCodeDto> reqDos = new ResponseDto<>();
        EmpByRoleCodeDto empDto = new EmpByRoleCodeDto();
        // 经销商
        empDto.setCompanyCode(ownerCode);
        // 在职状态
        empDto.setIsOnjob(CommonConstant.IS_ON_JOB_IN);
        // 角色code
        empDto.setRoleCode(Collections.singletonList(roleCode));
        reqDos.setData(empDto);
        DmsResponse<List<EmpByRoleCodeDto>> response = midEndAuthCenterFeign.queryDealerUser(reqDos);
        if (null == response || response.isFail()) {
            logger.info("selectFWJLByOwnerCode response isfail");
            return null;
        }
        logger.info("selectFWJLByOwnerCode end");
        return response.getData();
    }

    /**
     * 续保线索跟进透明查询接口
     */
    public  Page<InsuranceLeadsTransparencyDto> queryTransparency(InsuranceLeadsTransparencyQueryDto queryDto){
        return  queryTransparencyPage(queryDto);
    }

    public Page<InsuranceLeadsTransparencyDto> queryTransparencyPage(InsuranceLeadsTransparencyQueryDto queryDto) {
        logger.info("queryTransparencyPage start queryDto:{}", JSON.toJSONString(queryDto));
        CompanyNewSelectDto companyNewSelectDto = new CompanyNewSelectDto();
        //根据大小区查询经销商。如果type传c，则代表厂端
        if (queryDto.getType().equals("c")) {
            if (StringUtils.isNotEmpty(queryDto.getAfterSmallareaId())){
                companyNewSelectDto.setAfterSmallArea(queryDto.getAfterSmallareaId());
            }
            if (StringUtils.isNotEmpty(queryDto.getAfterBigareaId())){
                companyNewSelectDto.setAfterBigArea(queryDto.getAfterBigareaId());
            }
            if (ObjectUtils.isNotEmpty(queryDto.getOwnerCode())){
                companyNewSelectDto.setCompanyCodes(queryDto.getOwnerCode());
            }
        }else {
            CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
            companyNewSelectDto.setCompanyCodes(new ArrayList<>(Collections.singletonList(currentLoginInfo.getOwnerCode())));
            queryDto.setOwnerCode(new ArrayList<>(Collections.singletonList(currentLoginInfo.getOwnerCode())));
        }
        //获取所有的大小区
        logger.info("查询大小区入参:{}", JSON.toJSONString(companyNewSelectDto));
        RestResultResponse<List<CompanyDetailDTO>> response = midEndOrgCenterFeign.companyInfo(companyNewSelectDto);
        logger.info("查询大小区返回结果:{}", JSON.toJSONString(response));
        if (org.springframework.util.ObjectUtils.isEmpty(response)) {
            throw new ServiceBizException(FullLeadsErrorEnum.ERROR_QUERY_AREA.getDesc());
        }
        List<CompanyDetailDTO> companyDetailDTOList = response.getData();
        if (queryDto.getType().equals("c") && CollectionUtils.isEmpty(queryDto.getOwnerCode())){
            queryDto.setOwnerCode(companyDetailDTOList.stream().map(CompanyDetailDTO::getCompanyCode).collect(Collectors.toList()));
        }

        DmsResponse<Page<InsuranceLeadsTransparencyDto>> pageDmsResponse = domainMaintainLeadFeign.queryTransparency(queryDto);
        logger.info("queryTransparency result:{}", JSON.toJSONString(pageDmsResponse));
        if (pageDmsResponse.isFail()) {
            logger.info("queryTransparency is fail:{}", JSON.toJSONString(pageDmsResponse));
            throw new ServiceBizException("续保线索跟进透明查询失败");
        }
        //获取所有的经销商数据
        List<String> companyCodes = pageDmsResponse.getData().getRecords().stream().map(InsuranceLeadsTransparencyDto::getOwnerCode).collect(Collectors.toList());
        //根据经销商找到大小区
        List<CompanyDetailDTO> companyDetailDTOList1 = companyDetailDTOList.stream().filter(item -> companyCodes.contains(item.getCompanyCode())).collect(Collectors.toList());
        //查询大小区经理
        RequestDto<GetOrgUserDTO> requestDto = new RequestDto<>();
        GetOrgUserDTO getOrgUserDTO = new GetOrgUserDTO();
        //getOrgUserDTO.setOrgType(15061003);
        getOrgUserDTO.setRoleCode("SHQYJL");
        requestDto.setData(getOrgUserDTO);
        ResponseDto<List<UserOrgInfoDTO>> userOrg=midEndAuthCenterFeign.getOrgUser(requestDto);
        logger.info("查询大小区经理返回结果:{}", JSON.toJSONString(userOrg));
        //循环大小区获取大小区经理数据
        companyDetailDTOList1.forEach(item -> {
            if(!userOrg.isFail() && CollectionUtils.isNotEmpty(userOrg.getData())){
                UserOrgInfoDTO orgUserDTO = userOrg.getData().stream().filter(item1 -> item1.getOrgid().equals(item.getAfterSmallAreaId())).findFirst().orElse(null);
                if (orgUserDTO != null){
                    item.setAreaManager(orgUserDTO.getUsername());
                }
            }
        });
        pageDmsResponse.getData().getRecords().forEach(item -> {
            CompanyDetailDTO companyDetailDTO = companyDetailDTOList1.stream().filter(item1 -> item1.getCompanyCode().equals(item.getOwnerCode())).findFirst().orElse(null);
            if (companyDetailDTO != null) {
                item.setProvinceName(companyDetailDTO.getProvinceName());
                item.setCityName(companyDetailDTO.getCityName());
                item.setAreaManager(companyDetailDTO.getAreaManager());
                item.setAfterBigareaName(companyDetailDTO.getAfterBigAreaName());
                item.setAfterSmallareaName(companyDetailDTO.getAfterSmallAreaName());
                item.setGroupCompanyName(companyDetailDTO.getGroupCompanyName());
            }
        });
        logger.info("queryTransparency result:{}", JSON.toJSONString(pageDmsResponse));
        return pageDmsResponse.getData();
    }

    /**
     * 续保线索跟进透明导出接口
     */
    public  void  exportTransparency(InsuranceLeadsTransparencyQueryDto queryDto){
        // 获取导出字段
        List<ExcelExportColumn> exportColumnList = buildExcelColumn(queryDto.getType());
        // 组装下载中心 DTO
        DownloadDto dto = new DownloadDto();
        dto.setQueryParams(BeanMapperUtil.toMap(queryDto));
        dto.setExcelName("续保跟进明细报表.xlsx");
        dto.setSheetName("续保跟进明细报表");
        dto.setServiceUrl("http://application-maintain-management/renewalOfInsurance/v1/exportCallback");
        dto.setExcelExportColumnList(exportColumnList);
        logger.info("export Transparency leads params:{}", dto);
        // 导出
        downloadServiceFeign.downloadExportExcel(dto);
    }

    /**
     * 下载中心回调
     * @param queryDto
     * @return
     */
    public List<InsuranceLeadsTransparencyDto> exportCallback(InsuranceLeadsTransparencyQueryDto queryDto){
        Page<InsuranceLeadsTransparencyDto> page = queryTransparencyPage(queryDto);
        return page.getRecords();
    }

    /**
     * 导出字段
     */
    private List<ExcelExportColumn> buildExcelColumn(String type) {
        List<ExcelExportColumn> exportColumnList = new ArrayList<>();
        if (type.equals("c")) {
            exportColumnList.add(new ExcelExportColumn("afterBigareaName", "大区"));
            exportColumnList.add(new ExcelExportColumn("afterSmallareaName", "小区"));
            exportColumnList.add(new ExcelExportColumn("provinceName", "省份"));
            exportColumnList.add(new ExcelExportColumn("cityName", "城市"));
            exportColumnList.add(new ExcelExportColumn("groupCompanyName", "集团"));
            exportColumnList.add(new ExcelExportColumn("areaManager", "区域经理"));
        }
        exportColumnList.add(new ExcelExportColumn("ownerCode", "经销商代码"));
        exportColumnList.add(new ExcelExportColumn("vin", "VIN"));
        exportColumnList.add(new ExcelExportColumn("licensePlate", "车牌号"));
        exportColumnList.add(new ExcelExportColumn("insuranceExpiryDate", "保险到期日期"));
        exportColumnList.add(new ExcelExportColumn("renewCustomerType", "续保客户类型"));
        exportColumnList.add(new ExcelExportColumn("followUpTime", "跟进时间"));
        exportColumnList.add(new ExcelExportColumn("followUpPersonnel", "跟进人员"));
        exportColumnList.add(new ExcelExportColumn("followUpContent", "跟进内容"));
        exportColumnList.add(new ExcelExportColumn("followUpResult", "跟进结果"));
        exportColumnList.add(new ExcelExportColumn("failReason", "失败原因"));
        exportColumnList.add(new ExcelExportColumn("isAiCall", "是否Ai外呼"));
        exportColumnList.add(new ExcelExportColumn("isConnected", "是否接通"));
        exportColumnList.add(new ExcelExportColumn("aiScore", "AI得分"));
        exportColumnList.add(new ExcelExportColumn("callTime", "通话时间"));
        exportColumnList.add(new ExcelExportColumn("callDuration", "通话时长(秒)"));
        return exportColumnList;
    }
}
