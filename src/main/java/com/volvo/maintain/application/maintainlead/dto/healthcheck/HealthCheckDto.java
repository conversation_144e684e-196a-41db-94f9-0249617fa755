package com.volvo.maintain.application.maintainlead.dto.healthcheck;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HealthCheckDto implements Serializable {

    private String ownerCode;

    private String healthNo;

    private String roNo;

    private List<String> vins;

    private String startTime;

    private String endTime;
}
