package com.volvo.maintain.application.maintainlead.dto.workshop;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UsedCarGetManagerByRoleCodeDto implements Serializable {

    //经销商代码
    private String companyCode;

    //在职状态  10081001:在职 , 10081002:离职
    private Integer isOnjob;

    //角色代码
    private List<String> roleCode;

    private List<String> userIds;

    private List<String> empIds;
}
