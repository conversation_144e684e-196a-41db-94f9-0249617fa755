package com.volvo.maintain.application.maintainlead.emums;

import lombok.Getter;

@Getter
public enum ClueStrategyEnum {

    FAULT_LAMP("100",null, "故障灯"),
    MAINTENANCE_LAMP("101",null, "保养灯"),
    SPARE_PARTS("102",null, "零附件"),
    ACCIDENT_CLUE("103",91111013, "事故线索"),
    RENEWAL_CLUE("104",91111033, "续保线索"),
    ACCIDENT_CLUE_PA("PAIC",91111038, "事故线索-平安"),
    ACCIDENT_CLUE_GS("CLPC",91111039, "事故线索-国寿"),
    ACCIDENT_CLUE_TB("CPIC",91111040, "事故线索-太保");


    private final String code;

    private final Integer modType;

    private final String description;

    ClueStrategyEnum(String code,Integer modType, String description) {
        this.code = code;
        this.modType = modType;
        this.description = description;
    }

    public static ClueStrategyEnum getStrategy(String code) {
        for (ClueStrategyEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
