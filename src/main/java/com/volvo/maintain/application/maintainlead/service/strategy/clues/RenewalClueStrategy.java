package com.volvo.maintain.application.maintainlead.service.strategy.clues;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.volvo.datalake.crypto.DecryptUtil;
import com.volvo.maintain.application.maintainlead.dto.CustomerInfoDto;
import com.volvo.maintain.application.maintainlead.dto.EmpByRoleCodeDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.ClueDataDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadOperationResultDto;
import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueDto;
import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueResDto;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceBillListDto;
import com.volvo.maintain.application.maintainlead.emums.ClueStrategyEnum;
import com.volvo.maintain.infrastructure.enums.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.MidVehicleVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.*;

@Slf4j
@Service
public class RenewalClueStrategy extends AbsClueExecuteStrategy  {

    public static final String  KEY_VERION = "1.2211.1";
    public static final String DATA = "data";
    public static final String PLAIN_TEXT = "plainText";



    @Override
    protected ClueStrategyEnum strategyEnum() {
        return ClueStrategyEnum.RENEWAL_CLUE;
    }

    @Override
    protected boolean validate(LeadOperationResultDto dto) {
        return Objects.nonNull(dto) && StringUtils.isNotBlank(dto.getVehicleVin()) &&
                StringUtils.isNotBlank(dto.getSourceClueId()) &&
                StringUtils.isNotBlank(dto.getLeadsType()) &&
                StringUtils.isNotBlank(dto.getLeadsReceiveTime()) &&
                Objects.nonNull(dto.getData()) && dto.getId() != null &&
                CollectionUtils.isNotEmpty(dto.getData().getContactInfo()) &&
                Objects.nonNull(dto.getData().getCarInfo());
    }

    @Override
    protected void preProcess(LeadOperationResultDto dto) {
        //数据解密 手机号，姓名
        List<ClueDataDto.ContactInfo> contactInfo = dto.getData().getContactInfo();
        //解密开关
        if(baseProperties.getDecryptedRenewalClue()){
            if (CollectionUtils.isNotEmpty(contactInfo)) {
                contactInfo.forEach(info -> {
                    String customerMobile = info.getCustomerMobile();
                    if(StringUtils.isNotEmpty(customerMobile)){
                        Optional.ofNullable(getEvaluate(customerMobile)).ifPresent(info::setCustomerMobile);
                    }
                    String customerName = info.getCustomerName();
                    if(StringUtils.isNotEmpty(customerName)){
                        Optional.ofNullable(getEvaluate(customerName)).ifPresent(info::setCustomerName);
                    }
                });
            }
            dto.setVehicleVin(getEvaluate(dto.getVehicleVin()));
        }
        //续保线索数据源
        ClueDataDto.RenewalInsuranceInfo renewalInsuranceInfo = dto.getData().getRenewalInsuranceInfo();
        if(StringUtils.isNotEmpty(renewalInsuranceInfo.getSourceType())){
            Optional.ofNullable(RenewalClueDataSource.getByDesc(renewalInsuranceInfo.getSourceType())).ifPresent(e -> renewalInsuranceInfo.setSourceType(e.toString()));
        }
        //续保线索判断来源依据
        if(StringUtils.isNotEmpty(renewalInsuranceInfo.getJudgeSourceTypeCriteria())){
            Optional.ofNullable(RenewalClueJudgmentBasis.getByDesc(renewalInsuranceInfo.getJudgeSourceTypeCriteria())).ifPresent(e -> renewalInsuranceInfo.setJudgeSourceTypeCriteria(e.toString()));
        }
        //续保客户类型
        if(StringUtils.isNotEmpty(renewalInsuranceInfo.getRenewalCustomerType())){
            Optional.ofNullable(InsuranceTypeEnum.fromChineseName(renewalInsuranceInfo.getRenewalCustomerType())).ifPresent(e -> renewalInsuranceInfo.setRenewalCustomerType(e.getValue().toString()));
        }

        if(StringUtils.isNotEmpty(renewalInsuranceInfo.getDealerCategory())){
            Optional.ofNullable(RenewalDealerType.getByDesc(renewalInsuranceInfo.getDealerCategory())).ifPresent(e -> renewalInsuranceInfo.setDealerCategory(e.toString()));
        }

        //续保保单来源
        ClueDataDto.InsuranceInfo insuranceInfo = dto.getData().getInsuranceInfo();
        if(StringUtils.isNotEmpty(insuranceInfo.getInsuranceSourceType())){
            Optional.ofNullable(RenewalPolicySource.getByDesc(insuranceInfo.getInsuranceSourceType())).ifPresent(e -> insuranceInfo.setInsuranceSourceType(e.toString()));
        }
    }

    private String getEvaluate(String ciphertext) {
        try {
            log.info("cipher text: {}", ciphertext);
            JSONObject jsonObject = DecryptUtil.evaluate(ciphertext, KEY_VERION);
            log.info("Evaluate cipher text: {}", ciphertext);
            Object data = jsonObject.get(DATA);
            if(Objects.nonNull(data) && data instanceof JSONObject){
                return ((JSONObject) data).get(PLAIN_TEXT).toString();
            }
            return null;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    protected void fillData(LeadOperationResultDto dto) {
        //填充续保客户信息
        fillUserInfo(dto);
        //填充保险专员信息
        DistributeClueDto distributeClueDto = dto.getDistributeClueDto();
        List<EmpByRoleCodeDto> empByRoleCodeDtos = super.selectFWJLByOwnerCode(distributeClueDto.getDealerCode(), ROLE_CODE_BXZY);
        if(CollectionUtils.isNotEmpty(empByRoleCodeDtos)){
            List<Long> saIds = empByRoleCodeDtos.stream().map(EmpByRoleCodeDto::getUserId).collect(Collectors.toList());
            distributeClueDto.setSaIds(saIds);
        }
    }

    //填充线索客户信息
    private void fillUserInfo(LeadOperationResultDto dto) {
        //填充车主数据
        Optional.ofNullable(dto.getData().getDealerInfo()).ifPresent(dealerInfo-> {
            DistributeClueDto distributeClueDto = dto.getDistributeClueDto();
            List<ClueDataDto.ContactInfo> contactInfos = dto.getData().getContactInfo();
            Map<String, ClueDataDto.ContactInfo> contactInfoMap;
            if(CollectionUtils.isEmpty(contactInfos)){
                contactInfoMap = new HashMap<>();
            }else{
                contactInfoMap = contactInfos.stream().collect(Collectors.toMap(ClueDataDto.ContactInfo::getTypeCode, Function.identity()));
            }
            ClueDataDto.DealerInfo info = dealerInfo.get(0);
            String dealerCode = info.getDealerCode();
            ClueDataDto.RenewalInsuranceInfo renewalInsuranceInfo = dto.getData().getRenewalInsuranceInfo();
            RenewalDealerType dealerType = RenewalDealerType.getEnumByType(renewalInsuranceInfo.getDealerCategory());
            if(Objects.isNull(dealerType)){
                log.info("经销商类型异常,无法处理续保客户信息");
                return;
            }
            switch (dealerType){
                case POLICY_DEALER:
                    fillUserInfoByPolicy(dto, contactInfoMap, distributeClueDto, dealerCode);
                    break;
                case WORK_ORDER_DEALER:
                    fillUserInfoByOrder(contactInfoMap, distributeClueDto);
                    break;
                case SALES_DEALER:
                    fillUserInfoBySalesOrder(dto, dealerCode, distributeClueDto);
                    break;
                default:
                    log.info("经销商类型异常,无法处理续保客户信息");
            }
        });
    }

    private void fillUserInfoBySalesOrder(LeadOperationResultDto dto, String dealerCode, DistributeClueDto distributeClueDto) {
        //取自店投保人
        DmsResponse<InsuranceBillListDto> response = domainInsuranceLeadsFeign.lastInsureInfo(dto.getVehicleVin(), dealerCode);
        InsuranceBillListDto insuranceBillListDto = response.getData();
        if(Objects.nonNull(insuranceBillListDto) && StringUtils.isNotEmpty(insuranceBillListDto.getPolicyHolderMobile())){
            log.info("续保线索下发,经销商类型:销售经销商,有自店投保人信息");
            distributeClueDto.setName(insuranceBillListDto.getPolicyHolderName());
            distributeClueDto.setTel(insuranceBillListDto.getPolicyHolderMobile());
            return;
        }else{
            log.info("续保线索下发,经销商类型:销售经销商,无自店投保人信息");
        }
        //取自店车主
        CustomerInfoDto customerInfoDto = customerService.queryOwnerCustomer(dto.getVehicleVin(), dealerCode);
        if(Objects.nonNull(customerInfoDto) && StringUtils.isNotEmpty(customerInfoDto.getMobile())){
            log.info("续保线索下发,经销商类型:保单经销商,有自店车主信息");
            distributeClueDto.setName(customerInfoDto.getCustomerName());
            distributeClueDto.setTel(customerInfoDto.getMobile());
            return;
        }else{
            log.info("续保线索下发,经销商类型:保单经销商,无自店车主信息");
        }

        //取开票联系人
        MidVehicleVo midVehicleVo = super.getMidVehicleVo(dto.getVehicleVin());
        if(Objects.nonNull(midVehicleVo) && StringUtils.isNotEmpty(midVehicleVo.getMobile())){
            log.info("续保线索下发,经销商类型:保单经销商,有开票联系人信息");
            distributeClueDto.setName(midVehicleVo.getName());
            distributeClueDto.setTel(midVehicleVo.getMobile());
        }else{
            log.info("续保线索下发,经销商类型:保单经销商,无开票联系人信息");
        }
    }

    private void fillUserInfoByOrder(Map<String, ClueDataDto.ContactInfo> contactInfoMap, DistributeClueDto distributeClueDto) {
        //取下发送修人
        ClueDataDto.ContactInfo repairContactInfo = contactInfoMap.get(REPAIRER_CODE);
        if(Objects.nonNull(repairContactInfo) && StringUtils.isNotEmpty(repairContactInfo.getCustomerMobile())){
            distributeClueDto.setName(repairContactInfo.getCustomerName());
            distributeClueDto.setTel(repairContactInfo.getCustomerMobile());
            log.info("续保线索下发,经销商类型:工单经销商,存在送修人信息");
        }else{
            log.info("续保线索下发,经销商类型:工单经销商,无送修人信息");
        }
    }

    private void fillUserInfoByPolicy(LeadOperationResultDto dto, Map<String, ClueDataDto.ContactInfo> contactInfoMap, DistributeClueDto distributeClueDto, String dealerCode) {
            //取下发投保人
            ClueDataDto.ContactInfo insuredContactInfo = contactInfoMap.get(INSURED_CODE);
            if(Objects.nonNull(insuredContactInfo) && StringUtils.isNotEmpty(insuredContactInfo.getCustomerMobile())){
                distributeClueDto.setName(insuredContactInfo.getCustomerName());
                distributeClueDto.setTel(insuredContactInfo.getCustomerMobile());
                log.info("续保线索下发,经销商类型:保单经销商,存在投保人信息");
                return;
            }else{
                log.info("续保线索下发,经销商类型:保单经销商,无投保人信息");
            }


            //取送修人
            List<CustomerInfoDto> customerInfoDtos = customerService.queryRepairOwner(dto.getVehicleVin(), dealerCode);
            if(CollectionUtils.isNotEmpty(customerInfoDtos)){
                log.info("续保线索下发,经销商类型:保单经销商,有送修人信息");
                List<CustomerInfoDto> customerInfos = customerInfoDtos.stream().filter(e -> StringUtils.isNotEmpty(e.getMobile())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(customerInfos)){
                    CustomerInfoDto repairOwner = customerInfos.get(0);
                    distributeClueDto.setName(repairOwner.getCustomerName());
                    distributeClueDto.setTel(repairOwner.getMobile());
                    return;
                }else{
                    log.info("续保线索下发,经销商类型:保单经销商,有送修人信息,送修人手机号为空");
                }
            }else{
                log.info("续保线索下发,经销商类型:保单经销商,无送修人信息");
            }

            //取自店投保人
            DmsResponse<InsuranceBillListDto> response = domainInsuranceLeadsFeign.lastInsureInfo(dto.getVehicleVin(), dealerCode);
            InsuranceBillListDto insuranceBillListDto = response.getData();
            if(Objects.nonNull(insuranceBillListDto) && StringUtils.isNotEmpty(insuranceBillListDto.getPolicyHolderMobile())){
                log.info("续保线索下发,经销商类型:保单经销商,有自店投保人信息");
                distributeClueDto.setName(insuranceBillListDto.getPolicyHolderName());
                distributeClueDto.setTel(insuranceBillListDto.getPolicyHolderMobile());
                return;
            }else{
                log.info("续保线索下发,经销商类型:保单经销商,无自店投保人信息");
            }

            //取自店车主
            CustomerInfoDto customerInfoDto = customerService.queryOwnerCustomer(dto.getVehicleVin(), dealerCode);
            if(Objects.nonNull(customerInfoDto) && StringUtils.isNotEmpty(customerInfoDto.getMobile())){
                log.info("续保线索下发,经销商类型:保单经销商,有自店车主信息");
                distributeClueDto.setName(customerInfoDto.getCustomerName());
                distributeClueDto.setTel(customerInfoDto.getMobile());
            }else{
                log.info("续保线索下发,经销商类型:保单经销商,无自店车主信息");
            }
    }

    @Override
    protected DistributeClueResDto distributeClues(LeadOperationResultDto dto) {
        DmsResponse<DistributeClueResDto> domainResponse = domainMaintainLeadFeign.insertClues(dto);
        log.info("续保线索下发响应:{}", JSON.toJSONString(domainResponse));
        DistributeClueResDto data = domainResponse.getData();
        if (domainResponse.isFail() || Objects.nonNull(data) && !data.isSuccess()) {
            log.error("调用领域线索失败");
            return new DistributeClueResDto(false,null,null);
        }
       /* try {
            DmsResponse<List<Long>> inviteIds = domainMaintainLeadFeign.getInsuranceVehicleRecordId(dto.getId());
            log.info("续保跟进透明记录触点 inviteIds:{}", JSON.toJSONString(inviteIds));
            if (inviteIds.isSuccess() && CollectionUtils.isNotEmpty(inviteIds.getData())) {
                fullLeadsService.buildRenewalLeadStaticDto(inviteIds.getData());
            }
        }catch (Exception e){
            log.error("续保线索跟进报表记录失败",e);
        }*/

        return data;
    }

    @Override
    protected boolean handleIdempotency(Long id) {
        // 查询是否存在线索
        DmsResponse<Boolean> cluesResult = domainMaintainLeadFeign.queryCluesExistsByCrmId(ClueStrategyEnum.RENEWAL_CLUE.getCode(),id);
        if (cluesResult.isFail()) {
            throw new ServiceBizException("调用领域线索失败！");
        }
        return Objects.nonNull(cluesResult.getData()) && cluesResult.getData();
    }
}
