package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("事故线索导出数据")
public class AccidentCluesExportDto {
    @ApiModelProperty(value = "售后小区名称")
    private String afterSmallAreaName;

    @ApiModelProperty(value = "售后大区名称")
    private String afterBigAreaName;

    @ApiModelProperty(value = "省份")
    private String provinceName;

    @ApiModelProperty(value = "城市")
    private String cityName;

    @ApiModelProperty("经销商代码")
    private String dealerCode;

    @ApiModelProperty(value = "渠道标签")
    private String channelType;

    @ApiModelProperty("报案号")
    private String registNo;

    @ApiModelProperty("VIN")
    private String vin;

    @ApiModelProperty("车牌")
    private String license;

    @ApiModelProperty("车型名称")
    private String modelName;

    @ApiModelProperty("客户名")
    private String ownerName;

    @ApiModelProperty("客户手机号")
    private String ownerMobile;

    @ApiModelProperty("报案人")
    private String contacts;

    @ApiModelProperty("报案人手机号")
    private String contactsPhone;

    @ApiModelProperty("出险时间")
    private String accidentDate;

    @ApiModelProperty("出险内容")
    private String accidentReason;

    @ApiModelProperty("出险地点")
    private String accidentAddress;

    @ApiModelProperty("报案时间")
    private String reportDate;

    @ApiModelProperty("线索创建/推送时间")
    private String createdAt;

    @ApiModelProperty("首次跟进时间")
    private String firstFollowTime;

    @ApiModelProperty("下次跟进时间")
    private String nextFollowTime;

    @ApiModelProperty("二次跟进时间")
    private String lastFollowTime;

    @ApiModelProperty("跟进次数")
    private String followCount;

    @ApiModelProperty("跟进状态")
    private String followStatus;

    @ApiModelProperty("跟进内容")
    private String followText;

    @ApiModelProperty("跟进失败原因")
    private String followFailWhy;

    @ApiModelProperty("跟进人员")
    private String followPeopleName;

    @ApiModelProperty("保险公司名称")
    private String insuranceCompanyName;

    @ApiModelProperty("送返修标识")

    private String clientType;

    @ApiModelProperty("是否重复线索")
    private String repeatLead;

    @ApiModelProperty("预约单号")
    private String bookingOrderNo;

    @ApiModelProperty("进厂工单号")
    private String intoRoNo;

    @ApiModelProperty("工单状态")
    private String cluesStatus;

    @ApiModelProperty("进厂经销商")
    private String intoDealerCode;

    @ApiModelProperty("进厂时间")
    private String intoDealerDate;

    @ApiModelProperty("留修情况")
    private String repairSituation;

    @ApiModelProperty("预约到店时间")
    private String bookingComeTime;

    @ApiModelProperty("维修类型")
    private String repairTypeCode;

    @ApiModelProperty("维修总金额")
    private BigDecimal repairAmount;

    @ApiModelProperty("维修配件金额")
    private BigDecimal repairPartAmount;

    @ApiModelProperty("维修工时金额")
    private BigDecimal labourAmount;

    @ApiModelProperty("线索来源")
    private String cluesResource;

    @ApiModelProperty("来源渠道")
    private String insuranceSource;
}
