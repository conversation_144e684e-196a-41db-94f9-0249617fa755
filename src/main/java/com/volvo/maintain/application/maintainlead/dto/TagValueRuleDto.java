package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.util.List;


/**
 * 功能描述：值规则解析
 *
 * <AUTHOR>
 * @date 2024/01/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("值规则解析")
public class TagValueRuleDto {
    // 值获取类型
    private Integer getType;
    // 显示类型
    private Integer showType;
    //展示规则
    private ShowRuleDto showRule;


    @Data
    public static class ShowRuleDto{
        // 默认
        private Integer defaul;

        //范围规则
        private List<RangeDto> range;

        //脱敏展示
        private SensitiveDto sensitive;

        //组合规则
        private List<ComposeDto> compose;

        //选择规则
        private SelectDto select;
        //转义规则
        private ConvertDto convert;
    }
    /**
     * 范围规则
     */
    @Data
    public static class RangeDto{
        // 最小
        private Integer min;
        // 最大
        private Integer max;
        //值
        private String value;

    }
    /**
     * 脱敏展示
     */
    @Data
    public static class SensitiveDto{
        // 开始字符
        private Integer start;
        // 长度
        private Integer length;
        //替换符号
        private String symbol;

    }

    /**
     * 组合规则
     */
    @Data
    public static  class ComposeDto{
        // 标签code
        private String tagId;
        //拼接符号
        private String symbol;

    }

    /**
     * 选择规则
     */
    @Data
    public static class SelectDto{
        // 值
        private String value;
        //是否显示
        private String show;

    }
    /**
     * 转义规则
     */
    @Data
    public static class ConvertDto{
        // 或规则
        private List<andOrDto> or;
        //与规则
        private List<andOrDto> and;


    }

    /**
     * 转义规则(or)
     */
    @Data
    public static class andOrDto{
        // 标签code
        private String tagId;
        //操作
        private String operator;

        //值
        private String value;

    }

}
