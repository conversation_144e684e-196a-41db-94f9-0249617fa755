package com.volvo.maintain.application.maintainlead.dto.white;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "VehicleHealthCheckWhiteListDto", description = "白名单参数")
@Builder
public class WhiteListDto {

    /**
    * 经销商code
    */
    private List<String> companyDtoList;

    /**
    * 经销商code
    */
    private List<String> ownerCode;

    /**
    * 集团code
    */
    private List<String> groupCode;

    /**
     * vin
     */
    private List<String> vin;

    /**
     * 手机号
     */
    private List<String> phoneNumber;

    /**
    * 状态
    */
    private Integer state;

    /**
     * 模块类型
     */
    private Integer modType;

    /**
     * vin或手机号
     */
    private String vehicleOrPhone;

    /**
     * vin或手机号
     */
    private String vehicleOrPhoneImport;

    /**
     * 名单类型
     */
    private Integer rosterType;

    /**
     * 业务描述
     */
    private String businessDescription;

    /**
     * 参数代码
     */
    private String itemCode;


    /**
    * 是否删除
    */
    private Integer isDeleted;

    /**
     * 操作人
     */
    private String createdBy;

    private String updatedBy;
}