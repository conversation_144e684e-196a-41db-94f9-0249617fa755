package com.volvo.maintain.application.maintainlead.service.faultLight.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.EmailInfoDto;
import com.volvo.maintain.application.maintainlead.dto.EmpByRoleCodeDto;
import com.volvo.maintain.application.maintainlead.dto.TmVehicleDto;
import com.volvo.maintain.application.maintainlead.dto.faultlight.ClueNotifyDto;
import com.volvo.maintain.application.maintainlead.dto.message.MessageSendDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.util.ObjectUtil;
import com.yonyou.cyx.function.utils.common.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description 故障灯线索通知帮助类
 *
 * <AUTHOR>
 * &#064;date  2023/7/19 19:26
 */
@Slf4j
@EnableAsync
@Component
public class FaultLightClueServiceHelper {

    @Autowired
    private CommonMethodService commonMethodService;

    private static final String UNKNOWN = "无";
    private static final DateTimeFormatter abbFormatter = DateTimeFormatter.ofPattern("yyMMdd");
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String GET = "get";
    private static final String COMMENTS = "comments";

    @Value("${volvo.email.publicFrom:<EMAIL>}")
    private String publicFrom;
    @Value("${fault.notify.subject:故障灯邀约线索提醒}")
    private String subject;
    @Value("${fault.notify.emailText:尊敬的经销商您好：<br>" +
            "&nbsp; &nbsp; 车牌：%s，VIN:%s，车辆于%s发生了%s；于%s经400热线联络确认本店为意向经销商请尽快跟进，400热线备注为：%s。详细信息可登录NEWBIE查看。经销商代码：%s。<br>" +
            "&nbsp; &nbsp; 为确保故障灯提示的故障信息会被及时跟进，烦请及时联系上述联系人并关注后续状态<br>" +
            "&nbsp; &nbsp; 感谢<br>}")
    private String emailText;
    @Value("${fault.notify.role:YYZY,FWJL}")
    private String[] roleCodeArray;
    @Value("${fault.notify.templateCode:60656f6b-239e-4cd1-b075-b518e451d6f3}")
    private String templateCode;

    @Async
    public void doClueNotify(ClueNotifyDto dto) {
        log.info("clueNotify,start");
        if (dto == null) {
            log.info("clueNotify,dto == null");
            return;
        }
        log.info("clueNotify,dto:{}", JSON.toJSONString(dto));
        /*查询数据*/
        String vin = dto.getVehicleVin();
        //查询发送信息车架号,线索生成时间,线索下发时间,400备注,经销商代码
        //根据vin获取车辆信息
        TmVehicleDto vehicleDTO = commonMethodService.queryVehicle(vin);
        if (ObjectUtils.isNotEmpty(vehicleDTO)) {
            String plateNumber = vehicleDTO.getPlateNumber();
            log.info("clueNotify,plateNumber:{}", plateNumber);
            dto.setPlateNumber(plateNumber);
        }
        //查询收件人信息
        EmpByRoleCodeDto empByRoleCodeDto = new EmpByRoleCodeDto();
        empByRoleCodeDto.setIsOnjob(CommonConstant.IS_ON_JOB_IN);
        empByRoleCodeDto.setRoleCode(this.getRoleCode());
        empByRoleCodeDto.setCompanyCode(dto.getDealerCode());
        List<EmpByRoleCodeDto> listDto =  commonMethodService.getEmpByRoleCodeDtos(empByRoleCodeDto);

        if (CollectionUtils.isEmpty(listDto)) {
            log.info("clueNotify,CollectionUtils.isEmpty(listDto)");
            return;
        }
        log.info("clueNotify,listDto:{}", listDto.size());
        //摘除离职人员
        List<EmpByRoleCodeDto> list = listDto.stream().filter(a -> Objects.nonNull(a) && !CommonConstant.IS_ON_JOB_DIMISSION.equals(a.getIsOnjob())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            log.info("clueNotify,CollectionUtils.isEmpty(list)");
            return;
        }
        log.info("clueNotify,list:{}", list.size());
        //转换时间
        convertDate(dto);
        //处理空值/限制外呼备注长度
        setObject(dto);
        //发送邮件
        List<String> emailList = list.stream().map(EmpByRoleCodeDto::getEmail)
                .filter(email -> !StringUtils.isNullOrEmpty(email)).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(emailList)) {
            log.info("clueNotify,emailList:{}", emailList.size());
            try {
                clueEmailsNotify(dto, emailList);
            } catch (Exception e) {
                log.info("clueEmailsNotify,clueEmailsNotify,Exception:", e);
            }
        }
        //发送短信
        List<String> phoneList = list.stream().map(EmpByRoleCodeDto::getPhone)
                .filter(phone -> !StringUtils.isNullOrEmpty(phone)).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(phoneList)) {
            log.info("clueNotify,phoneList:{}", phoneList.size());
            try {
                cluePhoneNotify(dto, phoneList);
            } catch (Exception e) {
                log.info("clueNotify,cluePhoneNotify,Exception:", e);
            }
        }
        log.info("clueNotify,end");
    }
    private List<String> getRoleCode() {
        ArrayList<String> list = new ArrayList<>(roleCodeArray.length);
        Collections.addAll(list, roleCodeArray);
        return list;
    }

    private static void convertDate(ClueNotifyDto dto) {
        //线索生成时间
        String clueGenTime = getDateParam(dto.getClueGenTimeDate());
        log.info("convertDate,clueGenTime:{}", clueGenTime);
        dto.setClueGenTime(clueGenTime);
        //线索下发时间
        String clueDisTime = getDateParam(dto.getClueDisTimeDate());
        log.info("convertDate,clueDisTime:{}", clueDisTime);
        dto.setClueDisTime(clueDisTime);
    }

    private static String getDateParam(Date date) {
        log.info("getDateParam,date:{}", date);
        if (date == null) {
            return UNKNOWN;
        }
        try {
            Instant instant = date.toInstant();
            ZoneId zone = ZoneId.systemDefault();
            LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
            return formatter.format(localDateTime);
        } catch (Exception e) {
            log.info("getDateParam,Exception:", e);
            return UNKNOWN;
        }
    }

    private static void setObject(ClueNotifyDto object) {
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                String name = field.getName();
                String str = name.substring(0, 1).toUpperCase();
                String get = GET + str + field.getName().substring(1);
                Method method = object.getClass().getMethod(get);
                if (!field.getType().equals(String.class)) {
                    continue;
                }
                Object ob = method.invoke(object);
                if (StringUtils.isNullOrEmpty(ob)) {
                    field.setAccessible(true);
                    field.set(object, UNKNOWN);
                } else if (COMMENTS.equals(name)) {
                    field.setAccessible(true);
                    field.set(object, StrUtil.maxLength(ob.toString(), CommonConstant.NOTIFY_LENGTH));
                }
            } catch (Exception e) {
                log.info("setObject:", e);
            }
        }
    }


    private void clueEmailsNotify(ClueNotifyDto dto, List<String> emailList) {
        log.info("clueEmailsNotify,start");
        EmailInfoDto emailInfoDto = new EmailInfoDto();
        emailInfoDto.setFrom(publicFrom);
        emailInfoDto.setSubject(subject + getSubjectTime());
        emailInfoDto.setText(this.getEmailsText(dto));
        emailInfoDto.setTo(emailList.toArray(new String[0]));
        commonMethodService.pushEmail(emailInfoDto);
        log.info("clueEmailsNotify,end");
    }

    private void cluePhoneNotify(ClueNotifyDto dto, List<String> phoneList) {
        log.info("cluePhoneNotify,start");
        MessageSendDto smsPushDTO = new MessageSendDto();
        Map<String, String> stringStringMap = ObjectUtil.emailDtoToMap(dto);
        smsPushDTO.setMobiles(String.join(",", phoneList));
        smsPushDTO.setTemplateId(templateCode);
        smsPushDTO.setParamMap(stringStringMap);
        commonMethodService.pushSms(smsPushDTO);
        log.info("cluePhoneNotify,end");
    }



    private static String getSubjectTime() {
        LocalDate nowDate = LocalDate.now();
        return nowDate.format(abbFormatter);
    }

    private String getEmailsText(ClueNotifyDto dto) {
        log.info("clueNotify,getEmailsText:{}", JSON.toJSONString(dto));
        //赋值
        return String.format(emailText, dto.getPlateNumber(), dto.getVehicleVin(),
                dto.getClueGenTime(), dto.getWarningName(), dto.getClueDisTime(), dto.getComments(), dto.getDealerCode());
    }

}
