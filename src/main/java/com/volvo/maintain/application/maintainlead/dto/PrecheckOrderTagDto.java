package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 环检单tag
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("环检单tag")
public class PrecheckOrderTagDto {

    @ApiModelProperty(value = "vin",name = "vin")
    private String vin;

    @ApiModelProperty(value = "license",name = "license")
    private String license;

    @ApiModelProperty(value = "邀约类型",name = "inviteType")
    private Integer inviteType;

    @ApiModelProperty(value = "待授权",name = "authorization")
    private String authorization;

    @ApiModelProperty(value = "是否vip",name = "vip")
    private boolean vip;

    @ApiModelProperty(value = "vip群组",name = "vipGroup")
    private List<String> vipGroup;

    /**
     * 线索
     */
    private List<String> inviteTypeGroup;

    /**
     * 预约
     */
    private String BookingTag;

    /**
     * 标签
     */
    private List<String> tagList;

    public PrecheckOrderTagDto(String vin,String license){
        this.vin=vin;
        this.license=license;
    }

}
