package com.volvo.maintain.application.maintainlead.dto.clues;

import lombok.Data;

import java.io.Serializable;

@Data
public class InsuranceLeadsTransparencyDto  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 大区ID
     */
    private String afterBigareaId;

    /**
     * 大区名称
     */
    private String afterBigareaName;

    /**
     * 小区ID
     */
    private String afterSmallareaId;

    /**
     * 小区名称
     */
    private String afterSmallareaName;

    /**
     * 集团
     */
    private String  groupCompanyName;

    /**
     * 城市
     */
    private String cityName;

    /**
     * 区域经理
     */
    private String areaManager;

    /**
     * 省份
     */
    private String provinceName;

    /**
     * 经销商代码
     */
    private String  ownerCode;

    /**
     * VIN
     */
    private String vin;

    /**
     * 车牌号
     */
    private String  licensePlate;

    /**
     * 保险到期日期
     */
    private String insuranceExpiryDate;

    /**
     * 续保客户类型
     */
    private String  renewCustomerType;

    /**
     * 跟进时间
     */
    private String followUpTime;

    /**
     * 跟进人员
     */
    private String  followUpPersonnel;

    /**
     * 跟进内容
     */
    private String followUpContent;

    /**
     * 跟进结果
     */
    private String  followUpResult;

    /**
     * 失败原因
     */
    private String  failReason;

    /**
     * 是否Ai外呼
     */
    private String  isAiCall;

    /**
     * 是否接通
     */
    private String  isConnected;

    /**
     * AI得分
     */
    private String  aiScore;

    /**
     * 通话时间
     */
    private String  callTime;

    /**
     * 通话时长(秒)
     */
    private Integer callDuration;


}
