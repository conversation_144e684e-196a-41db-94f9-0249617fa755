package com.volvo.maintain.application.maintainlead.service.customerProfile.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.message.MessageSendDto;
import com.volvo.maintain.application.maintainlead.service.customerProfile.BookingRegisterService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import com.volvo.maintain.infrastructure.gateway.DominAutoFeign;
import com.volvo.maintain.infrastructure.gateway.MaintenanceServiceFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import com.volvo.maintain.infrastructure.util.DateUtil;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;
import com.volvo.maintain.interfaces.vo.booking.BookingEm90Vo;
import com.volvo.maintain.interfaces.vo.booking.BookingOrderResponseVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class BookingRegisterServiceImpl implements BookingRegisterService {
    public static final String BOOKING_ORDER_CONFIRM = "67f15fbf-e232-4c6d-b591-92ade5e3b3d1";
    public static final String BOOKING_ORDER_ENTER = "3693cef9-a6e7-44fc-85f9-4a0d8b9001a2";
    public static final String ORDER_CONFIRM = "bookingOrderConfirm";
    public static final String ORDER_ENTER = "bookingOrderEnter";
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedissonClient redissonClient;
    /**
     * fegin
     */
    @Autowired
    private DmscloudServiceFeign dmscloudServiceFegin;

    @Autowired
    private CommonMethodService commonMethodService;

    @Autowired
    private DominAutoFeign dominAutoFeign;

    @Autowired
    private MaintenanceServiceFeign maintenanceServiceFeign;

    public static final String em90BookingOrderReminder = "EM90_BOOKIN_ORDER_REMINDER";

    @Qualifier("thread360Pool")
    @Autowired
    private ThreadPoolTaskExecutor thread360Pool;

    @Override
    public void em90BookingOrderPush(int beginDate, int endDate) {
        if (ObjectUtils.isEmpty(beginDate)){
            beginDate = 50;
        }
        if (ObjectUtils.isEmpty(endDate)){
            endDate = 15;
        }

        RLock lock = null;
        RBucket<Object> bucket = null;
        try {
            logger.info("Newbie消息通知：发送短信或推送企微任务给预约专员、服务经理 处理Redis锁信息");
            lock = redissonClient.getLock(StringUtils.join("em90BookingOrderReminder", em90BookingOrderReminder));
            bucket = redissonClient.getBucket("em90BookingOrderReminder");

            // 获取锁
            if (!lock.tryLock()) {
                logger.info("执行被拦截，{}", new Date());
                throw new RuntimeException("同车牌同时执行被拦截");
            }
            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();

            // 减去 15 分钟
            LocalDateTime startTime = currentTime.minusMinutes(beginDate);

            LocalDateTime endTime = currentTime.minusMinutes(endDate);

            // 格式化时间输出
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String startTimeStr = startTime.format(formatter);
            String endTimeStr = endTime.format(formatter);

            //获取系统配置
            DmsResponse<CommonConfigDto> configResult = dmscloudServiceFegin.getConfigByKey(CommonConstant.MODEL_CODE_EM90,CommonConstant.EM90_GROUP_TYPE);
            logger.info("configResult response:{}",JSONObject.toJSONString(configResult));
            if (configResult.isFail()){
                throw new ServiceBizException("获取系统配置失败:{}",JSONObject.toJSONString(configResult));
            }

            String configValue = configResult.getData().getConfigValue();

            DmsResponse<BookingOrderResponseVo> bookingListDmsResponse = dmscloudServiceFegin.queryEm90AppointmentOrder(startTimeStr, endTimeStr, Integer.parseInt(configValue));
            logger.info("em90BookingOrderPush bookingListDmsResponse bookingListDmsResponse:{} ", bookingListDmsResponse);
            if(bookingListDmsResponse.isFail()){
                logger.info("em90BookingOrderPush bookingListDmsResponse isnull ");
                return;
            }
            BookingOrderResponseVo bookingOrderResponseVo = bookingListDmsResponse.getData();

            if(ObjectUtils.isEmpty(bookingOrderResponseVo)){
                return;
            }

            // 未确认列表
            List<BookingEm90Vo> bookingOrderConfirm = bookingOrderResponseVo.getBookingOrderConfirm();
            List<BookingEm90Vo> bookingOrderEnter = bookingOrderResponseVo.getBookingOrderEnter();

            getBookingEm90Vos(bookingOrderConfirm, BOOKING_ORDER_CONFIRM, ORDER_CONFIRM);
            getBookingEm90Vos(bookingOrderEnter, BOOKING_ORDER_ENTER, ORDER_ENTER);

        } catch (Exception e) {
            logger.error("发送短信失败", e);
        }finally {
            if (null != bucket) {
                bucket.delete();
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    @Override
    public void em90BookingOrderPushCs(List<BookingEm90Vo> bookingEm90VoList) {
        getBookingEm90Vos(bookingEm90VoList,"67f15fbf-e232-4c6d-b591-92ade5e3b3d1","bookingOrderConfirm");
    }

    /**
     * em90取送车未确认通知
     */
    @Override
    public void em90TakeDeliverCarUnconfirmed(int beginDate, int endDate) {
        try {
            logger.info("em90TakeDeliverCarUnconfirmed begin");
            List<VehicleOrderResponseDtoDto> vehicleOrderResponseDtoDtos = queryVehicleOrder(beginDate, endDate);
            if (CollectionUtils.isEmpty(vehicleOrderResponseDtoDtos)) {
                logger.info("em90TakeDeliverCarUnconfirmed vehicleOrderResponseDtoDtos isEmpty");
                return ;
            }
            //获取系统配置
            DmsResponse<CommonConfigDto> configResult = dmscloudServiceFegin.getConfigByKey(CommonConstant.EM90_TAKE_DELIVER_CAT_CONFIG_KEY,CommonConstant.EM90_GROUP_TYPE);
            logger.info("configResult response:{}",JSONObject.toJSONString(configResult));
            if (configResult.isFail()){
                throw new ServiceBizException("获取系统配置失败:{}",JSONObject.toJSONString(configResult));
            }
            vehicleOrderResponseDtoDtos.stream().forEach(i->{
                MessageSendDto messageSendDto = new MessageSendDto();
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("licenseNo", null == i.getCarNo() ? "" : i.getCarNo());
                paramMap.put("orderCode", null == i.getOrderCode() ? "" : i.getOrderCode());
                paramMap.put("dealerCode", null == i.getDealerCode() ? "" : i.getDealerCode());
                messageSendDto.setParamMap(paramMap);
                messageSendDto.setMobiles(configResult.getData().getConfigValue());
                messageSendDto.setTemplateId("a66447c4-739e-4570-9d90-fdcfcbe49e2c");
                PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
                pushMessageRecordVo.setSinceType(2);
                pushMessageRecordVo.setBizNo(CommonConstant.EM90_VEHICLE_DELIVER_UNCONFIRMED_REMINDER);
                pushMessageRecordVo.setSubBizNo(i.getDealerCode() +"/"+ i.getOrderCode()+"/unconfirmed");
                commonMethodService.pushSms(CommonConstant.EM90_VEHICLE_DELIVER_UNCONFIRMED_REMINDER,pushMessageRecordVo,messageSendDto);
            });
            logger.info("em90TakeDeliverCarUnconfirmed end");
        }catch (Exception e){
            logger.info("em90取送车未确认通知失败:{}", JSONObject.toJSONString(e));
        }
    }

    private List<VehicleOrderResponseDtoDto> queryVehicleOrder(int beginDate, int endDate) {
        VehicleOrderPageDto vehicleOrderPageDto = new VehicleOrderPageDto();
        vehicleOrderPageDto.setPage(CommonConstant.VEHICLE_ORDER_PAGE);
        vehicleOrderPageDto.setPageSize(CommonConstant.VEHICLE_ORDER_PAGE_SIZE);
        VehicleOrderPageDto.VehicleOrderRequestDto vehicleOrderRequestDto = new VehicleOrderPageDto.VehicleOrderRequestDto();
        Integer[] orderStatus = new Integer[CommonConstant.VEHICLE_ORDER_ORDER_STATUS];
        vehicleOrderRequestDto.setOrderStatus(orderStatus);
        vehicleOrderRequestDto.setStartCreateTime(DateUtil.queryNowTime(beginDate));
        vehicleOrderRequestDto.setEndCreateTime(DateUtil.queryNowTime(endDate));
        vehicleOrderPageDto.setVehicleOrderRequestDto(vehicleOrderRequestDto);
        logger.info("查询取送车订单入参:{}",JSONObject.toJSONString(vehicleOrderPageDto));
        MidResponse<Page<VehicleOrderResponseDtoDto>> pageResult = dominAutoFeign.queryVehicleOrder(vehicleOrderPageDto);
        logger.info("查询取送车订单入参:{}",JSONObject.toJSONString(pageResult));
        if (null == pageResult || pageResult.isFail()){
            throw  new ServiceBizException("查询取送车订单失败:{}",JSONObject.toJSONString(pageResult));
        }
        List<VehicleOrderResponseDtoDto> vehicleOrderResponseDtoDtos = new ArrayList<>();
        if (null == pageResult.getData()) {
            return vehicleOrderResponseDtoDtos;
        }

        return pageResult.getData().getRecords();
    }

    private void getBookingEm90Vos(List<BookingEm90Vo> bookingEm90VoList,String templateId,String redisKey) {

        String activityType;

        if(CollectionUtils.isEmpty(bookingEm90VoList)){
            return;
        }

        Map<String, List<BookingEm90Vo>> bookingEm90VoMap = bookingEm90VoList.stream()
                .collect(Collectors.groupingBy(BookingEm90Vo::getOwnerCode));

        EmpByRoleCodeDto empByRoleCodeDto = new EmpByRoleCodeDto();
        empByRoleCodeDto.setIsOnjob(10081001);
        if (Objects.equals(redisKey, ORDER_CONFIRM)){
            empByRoleCodeDto.setRoleCode(Arrays.asList("YYZY1","FWJL"));
            activityType = "1";
        }else {
            empByRoleCodeDto.setRoleCode(Collections.singletonList("YYZY1"));
            activityType = "2";
        }

        String finalActivityType = activityType;

        // 使用forEach方法遍历Map
        bookingEm90VoMap.forEach((key, value) -> {

            empByRoleCodeDto.setCompanyCode(key);
            List<EmpByRoleCodeDto> data = commonMethodService.getEmpByRoleCodeDtos(empByRoleCodeDto);
            if (data == null) {return;}

            String phones = data.stream()
                    .map(EmpByRoleCodeDto::getPhone)
                    .filter(Objects::nonNull) // 过滤掉为null的phone
                    .collect(Collectors.joining(","));

            logger.info("em90BookingOrderPush getEmpByRoleCodeDtos phones ：" +phones);

            value.forEach(bookingEm90Vo -> {

                TmVehicleDto tmVehicleDto = commonMethodService.queryVehicle(bookingEm90Vo.getVin());

                if (tmVehicleDto==null){
                    return;
                }

                if (!"895".equals(tmVehicleDto.getModelCode())){
                    return;
                }

                MessageSendDto messageSendDto = new MessageSendDto();
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("licenseNo",bookingEm90Vo.getLicense());
                paramMap.put("orderCode",bookingEm90Vo.getBookingOrderNo());
                paramMap.put("dealerCode",bookingEm90Vo.getOwnerCode());
                messageSendDto.setParamMap(paramMap);
                messageSendDto.setMobiles(phones);
                messageSendDto.setTemplateId(templateId);

                PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
                pushMessageRecordVo.setSinceType(2);
                pushMessageRecordVo.setBizNo(em90BookingOrderReminder);
                pushMessageRecordVo.setSubBizNo(bookingEm90Vo.getOwnerCode() + bookingEm90Vo.getBookingOrderNo());

                try {
                    commonMethodService.pushSms(redisKey, pushMessageRecordVo , messageSendDto);
                } catch (Exception e) {
                    logger.info("getBookingEm90Vos pushSms 失败 ");
                }

                // 企微预约超时未确认、超时未到店消息
                CompletableFuture.supplyAsync(() -> overtimeMessageSendWechat(bookingEm90Vo, finalActivityType), thread360Pool);

            });


        });

    }



    private Boolean overtimeMessageSendWechat(BookingEm90Vo bookingEm90Vo,String activityType) {
        try {
            logger.info("em90BookingOrderPush,overtimeMessageSendWechat,bookingEm90Vo:{},activityType:{}" ,bookingEm90Vo,activityType);
            OrderMessageRequestDto orderMessageRequestDto = new OrderMessageRequestDto();
            orderMessageRequestDto.setDealerCode(bookingEm90Vo.getOwnerCode());
            orderMessageRequestDto.setVin(bookingEm90Vo.getVin());
            orderMessageRequestDto.setReservationId(bookingEm90Vo.getAppointmentId());
            orderMessageRequestDto.setActivityType(activityType);
            logger.info("em90BookingOrderPush,overtimeMessageSendWechat,orderMessageRequestDto:{}" ,orderMessageRequestDto);
            maintenanceServiceFeign.overtimeMessageSendWechat(orderMessageRequestDto);
        }catch (Exception e){
            logger.info("企微预约超时未确认、超时未到店消息推送失败",e);
            return false;
        }
        // 返回结果
        return true;
    }


}
