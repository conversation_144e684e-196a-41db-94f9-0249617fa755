package com.volvo.maintain.application.maintainlead.dto.part;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 缺料明细导出
 * </p>
 */
@Data
public class ShortPartExportResultDTO implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * 单据号码
     */
    private String SHEET_NO;
    /**
     * 是否急件
     */
    private Integer IS_URGENT;
    /**
     * 是否已结案
     */
    private Integer CLOSE_STATUS;
    /**
     * 缺料类型
     */
    private Integer SHORT_TYPE;
    /**
     * 仓库
     */
    private String STORAGE_NAME;
    /**
     * 库位代码
     */
    private String STORAGE_POSITION_CODE;
    /**
     * 车牌
     */
    private String LICENSE;
    /**
     * 零件号
     */
    private String PART_NO;
    /**
     * 零件名称
     */
    private String PART_NAME;
    /**
     * 当前库存量
     */
    private BigDecimal STOCK_QUANTITY;
    /**
     * 缺件数量
     */
    private BigDecimal SHORT_QUANTITY;
    /**
     * 客户名称
     */
    private String CUSTOMER_NAME;
    /**
     * 电话
     */
    private String PHONE;
    /**
     * 出入库类型
     */
    private Integer IN_OUT_TYPE;
    /**
     * 经手人
     */
    private String HANDLER_NAME;
}
