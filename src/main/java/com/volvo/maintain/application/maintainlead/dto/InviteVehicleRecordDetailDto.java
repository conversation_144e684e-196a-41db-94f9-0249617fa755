/*
 * Copyright (c) Volvo CAR Distribution (SHANGHAI) Co., Ltd. 2023. All rights reserved.
 */

package com.volvo.maintain.application.maintainlead.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.volvo.dto.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 功能描述：车辆邀约记录明细表
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "InviteVehicleRecordDetailDto", description = "车辆邀约记录明细查询参数")
public class InviteVehicleRecordDetailDto extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    @ApiModelProperty(value = "系统ID", name = "appId")
    private String appId;

    /**
     * 所有者代码
     */
    @ApiModelProperty(value = "所有者代码", name = "ownerCode")
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    @ApiModelProperty(value = "所有者的父组织代码", name = "ownerParCode")
    private String ownerParCode;

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID", name = "orgId")
    private Integer orgId;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", name = "id")
    private Long id;

    /**
     * 邀约ID(线索ID)
     */
    @ApiModelProperty(value = "邀约ID(线索ID)", name = "inviteId")
    private Long inviteId;

    /**
     * 跟进内容
     */
    @ApiModelProperty(value = "跟进内容", name = "content")
    private String content;

    /**
     * 客户反馈
     */
    @ApiModelProperty(value = "客户反馈", name = "feedback")
    private String feedback;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    /**
     * 跟进状态
     */
    @ApiModelProperty(value = "跟进状态", name = "status")
    private Integer status;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因", name = "loseReason")
    private Integer loseReason;

    /**
     * 不需跟进原因
     */
    @ApiModelProperty(value = "不需跟进原因", name = "notFollowReason")
    private Integer notFollowReason;

    /**
     * 下次跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "下次跟进日期", name = "planDate")
    private Date planDate;

    /**
     * 实际跟进日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "实际跟进日期", name = "actualDate")
    private Date actualDate;

    /**
     * 跟进方式
     */
    @ApiModelProperty(value = "跟进方式", name = "mode")
    private Integer mode;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源", name = "dataSources")
    private Integer dataSources;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除", name = "isDeleted")
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效", name = "isValid")
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间", name = "createdAt")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间", name = "updatedAt")
    private Date updatedAt;

    /**
     * 跟进服务顾问ID
     */
    @ApiModelProperty(value = "跟进服务顾问ID", name = "saId")
    private String saId;

    /**
     * 跟进服务顾问姓名
     */
    @ApiModelProperty(value = "跟进服务顾问姓名", name = "saName")
    private String saName;

    /**
     * 经销商代码
     */
    @ApiModelProperty(value = "经销商代码", name = "dealerCode")
    private String dealerCode;

    /**
     * 邀约类型
     */
    @ApiModelProperty(value = "邀约类型", name = "inviteType")
    private Integer inviteType;

    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号", name = "vin")
    private String vin;

    /**
     * 建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "建议进厂日期", name = "adviseInDate")
    private Date adviseInDate;

    /**
     * 新建议进厂日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "新建议进厂日期", name = "newAdviseInDate")
    private Date newAdviseInDate;

    /**
     * call_id
     */
    @ApiModelProperty(value = "外呼id", name = "callId")
    private String callId;

    /**
     * 未使用AI原因
     */
    @ApiModelProperty(value = "未使用AI原因", name = "noAIreason")
    private String noAIreason;

    /**
     * 线索异常
     */
    @ApiModelProperty(value = "线索异常", name = "errorStatus")
    private Integer errorStatus;
}
