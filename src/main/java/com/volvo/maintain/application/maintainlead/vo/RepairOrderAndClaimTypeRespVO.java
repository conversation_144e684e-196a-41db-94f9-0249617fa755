package com.volvo.maintain.application.maintainlead.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("RepairOrderAndClaimTypeRespVO")
public class RepairOrderAndClaimTypeRespVO implements Serializable {
	
    private static final long serialVersionUID = 7262488384516099125L;

	@ApiModelProperty(value = "经销商code")
    private String ownerCode;

    @ApiModelProperty(value = "工单号")
    private String roNo;

    @ApiModelProperty(value = "车架号")
    private String vin;
    
    @ApiModelProperty(value = "开单时间")
    private String roCreateDate;
    
    @ApiModelProperty(value = "工单类型")
    private String repairTypeCode;
    
    @ApiModelProperty(value = "工单类型名称")
    private String repairTypeName;
    
    @ApiModelProperty(value = "保修类别")
    private String claimType;
    
    @ApiModelProperty(value = "结算时间")
    private String balanceTime;
    
    @ApiModelProperty(value = "数据创建时间")
    private String createdAt;

    @ApiModelProperty(value = "数据更新时间")
    private String updatedAt;   
}
