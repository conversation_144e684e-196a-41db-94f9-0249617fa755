package com.volvo.maintain.application.maintainlead.dto.workshop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OutboundOrderDto {

    /**
     * 出库单编号
     */
    private String odoNo;

    /**
     * 下游出库单号
     */
    private String exeOdoNo;

    /**
     * ETA时间 预计到货
     * 时间
     */
    private String etaTime;

    /**
     * 妥投时间
     */
    private String signInTime;

    /**
     * 是否延时1是0否
     */
    private Integer isDelayed;

    /**
     * 延时时间
     */
    private Long delayedTime;

    /**
     * 出库单明细
     */
    private List<OdoItemDetails> itemList;

    /**
     * 字符串eta时间
     */
    private String etaTimeStr;

    /**
     * 采购订单明细主键ID;
     */
    private Long id;

}
