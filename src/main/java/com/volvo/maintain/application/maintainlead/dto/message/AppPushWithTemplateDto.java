package com.volvo.maintain.application.maintainlead.dto.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@ApiModel(description = "App消息推送对象(模版化)")
@AllArgsConstructor
@NoArgsConstructor
public class AppPushWithTemplateDto{


    @ApiModelProperty(value = "应用Id")
    private String appId;

    @NotBlank(message = "模板code不能为空")
    @ApiModelProperty(value = "模板code", required = true)
    private String templateCode;

    @NotNull(message = "模版参数不能为空")
    @ApiModelProperty(value = "模板参数", required = true)
    private Map<String, String> templateParams;

    @ApiModelProperty(value = "标题模板参数")
    private Map<String, String> titleParams;

    @ApiModelProperty(value = "推送目标列表，为空时广播")
    private String[] targetCodes;

    @ApiModelProperty(value = "消息点击触发内容")
    private String json;

    @ApiModelProperty(value = "扩展字段变量Map")
    private Map<String, String> pushExtParams;

    private Boolean remind = true;

    private String remindBody = "";


    private String roNo;

    private List<String> partsNo;


    public void setRoNo(String roNo, List<String> roNoList) {
        if (StringUtils.isBlank(roNo)) {
            if (roNoList != null && !roNoList.isEmpty()) {
                roNoList = roNoList.stream().distinct().collect(Collectors.toList());
                this.roNo = roNoList.get(0);
            }
        } else {
            this.roNo = roNo;
        }
    }


}
