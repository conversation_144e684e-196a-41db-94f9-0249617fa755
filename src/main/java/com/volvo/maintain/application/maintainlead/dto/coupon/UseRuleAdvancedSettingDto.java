package com.volvo.maintain.application.maintainlead.dto.coupon;

import com.yonyou.cyx.framework.bean.dto.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * UseRuleAdvancedSettingAdvancedSetting
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-30
 */
@Data
@ApiModel(value = "UseRuleAdvancedSetting", description = "UseRuleAdvancedSetting")
public class UseRuleAdvancedSettingDto extends BaseDTO {


    @ApiModelProperty(value = "限制经销商(空表示全部)")
    private List<String> limitDealer;
    @ApiModelProperty(value = "维修类型(空表示全部)")
    private List<String> limitRepairType;
    @ApiModelProperty(value = "使用门槛（空表示无门槛）")
    private Double useThreshold;
    @ApiModelProperty(value = "可叠加卡券（空表示无可叠加卡券）")
    private List<String> overlapCoupon;
    @ApiModelProperty(value = "使用规则-是否可叠加优惠券（1:不可叠加 2:可无限叠加 3:可有限叠加）")
    private Integer superimposedCoupons;
    @ApiModelProperty(value = "使用规则-是否可叠加同类优惠券（1:不可叠加 2:可叠加）")
    private Integer superimposeSimilarCoupons;
    @ApiModelProperty(value = "需要激活（1：是）")
    private int needActive;
    @ApiModelProperty(value = "车型（空表示全部车型 否则传入车型code列表）")
    private List<String> model;
    @ApiModelProperty(value = "车龄（空表示无限制 否则逗号分隔最小值和最大值）")
    private String vehicleAge;
    @ApiModelProperty(value = "适用车辆范围（车辆id列表）")
    private List<String> vehicleRange;

    @ApiModelProperty(value = "所需分组数")
    private int needGroupCount;
    @ApiModelProperty(value = "授权项")
    private String authorizedItems;
    @ApiModelProperty(value = "分组明细")
    private List<UseRuleGroupItemDto> useRuleGroupItemDto;

}