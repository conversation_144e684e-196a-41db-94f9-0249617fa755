package com.volvo.maintain.application.maintainlead.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 角色信息
 * @Date 2024/11/18 10:43
 */
@Data
@ApiModel("中台角色信息")
public class RoleInfoDto {

    @ApiModelProperty(value = "用戶创建人")
    private String createBy;

    @ApiModelProperty(value = "用戶创建时间")
    private String createTime;

    @ApiModelProperty(value = "数据来源")
    private Integer dataSource;

    @ApiModelProperty(value = "数据类型")
    private Integer dataType;

    @ApiModelProperty(value = "父角色代码")
    private String parentRoleCode;

    @ApiModelProperty(value = "角色代码")
    private String roleCode;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "用戶修改人")
    private String updateBy;

    @ApiModelProperty(value = "用戶修改时间")
    private String updateTime;

    @ApiModelProperty(value = "角色id")
    private Integer id;

    @ApiModelProperty(value = "角色id")
    private Integer roleId;


}
