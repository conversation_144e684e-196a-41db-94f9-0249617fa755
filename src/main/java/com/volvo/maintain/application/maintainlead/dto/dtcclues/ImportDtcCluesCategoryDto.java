package com.volvo.maintain.application.maintainlead.dto.dtcclues;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("线索种类导入")
public class ImportDtcCluesCategoryDto implements Serializable, IExcelModel, IExcelDataModel {
    private static final long serialVersionUID = 1L;

    @Excel(name = "序号")
    protected String serialNumber;

    @Excel(name = "故障类别")
    protected String faultCategory;

    @Excel(name = "ECU")
    protected String ecu;

    @Excel(name = "DTC")
    protected String dtc;

    /**
     * Confirm状态 （0-全部，1-是, 2-否）
     */
    @Excel(name = "Confirm生成线索")
    protected String confirmStatusString;

    /**
     * Indicator状态 （0-全部，1-是, 2-否）
     */
    @Excel(name = "Indicator生成线索")
    protected String indicatorStatusString;

    /**
     * 优先级
     */
    @Excel(name = "优先级")
    protected Integer priority;

    /**
     * Confirm状态 （0-全部，1-是, 2-否）
     */
    protected Integer confirmStatus;

    /**
     * Indicator状态 （0-全部，1-是, 2-否）
     */
    protected Integer indicatorStatus;

    /**
     * remark(失败原因)
     */
    private String errorMsg;

    /**
     * 行号
     */
    private Integer rowNum;
}
