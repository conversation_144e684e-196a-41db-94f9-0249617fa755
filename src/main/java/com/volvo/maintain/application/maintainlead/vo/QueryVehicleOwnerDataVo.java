package com.volvo.maintain.application.maintainlead.vo;

import com.volvo.maintain.application.maintainlead.dto.insurance.InviteInsuranceVehicleOwnerInfoDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 根据vin集合 查询车主车辆信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
public class QueryVehicleOwnerDataVo implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<InviteInsuranceVehicleOwnerInfoDTO> vehicleOwnerInfoList;

}
