package com.volvo.maintain.application.maintainlead.dto.claimapply;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/09/13
 */
@Data
public class ClaimApplyPartDTO implements Serializable {

    /**
     * 系统id
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织id
     */
    private Integer orgId;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 理赔申请id 关联tt_claim_apply_use的id
     */
    private Long applyId;

    /**
     * 配件号
     */
    private String partNo;

    /**
     * 配件名称
     */
    private String partName;

    /**
     * 数量
     */
    private Integer partQuantity;

    /**
     * 单价
     */
    private BigDecimal partSalesPrice;

    /**
     * 总价
     */
    private BigDecimal partSalesAmount;

    /**
     * 主因件 ，1：是，0：否
     */
    private Integer isMainPart;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 授权份额
     */
    private BigDecimal approvalQuota;

    /**
     * 授权金额
     */
    private BigDecimal approvalAmount;

}
