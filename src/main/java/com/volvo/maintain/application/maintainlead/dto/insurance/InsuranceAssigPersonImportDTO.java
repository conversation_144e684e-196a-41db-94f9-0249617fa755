package com.volvo.maintain.application.maintainlead.dto.insurance;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "续保分配导入")
public class InsuranceAssigPersonImportDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * VIN
	 */
	private String vin;
	/**
	 * 跟进人员
	 */
	private String saId;
	/**
	 * 跟进服务顾问姓名
	 */
	private String saName;

	/**
	 * 跟进人员账号
	 */
	private String saAccount;

	/**
	 * 错误信息
	 */
	private String errorMsg;
	/**
	 * 是否导入数据有错误, 1有0否
	 * 0正确  1：vin不存在线索 ：vin在系统线索已完成 2:vin为空或者vin不合法 :跟进人员账号为空 :vin重复 :跟进人员不在跟进维护列表
	 */
	private Integer isError;
	/**
	 * 行数
	 */
	private Integer lineNumber;

}
