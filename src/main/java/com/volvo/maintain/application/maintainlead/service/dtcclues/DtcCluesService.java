package com.volvo.maintain.application.maintainlead.service.dtcclues;

import com.volvo.maintain.application.maintainlead.dto.dtcclues.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.infoInherit.DtcCluesCategoryInheritDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface DtcCluesService {
    Page<DtcCluesCategoryInheritDto> generateCategoryList(DtcCluesCategoryListDto listRequestDto);

    Page<DtcCluesCategoryInheritDto> shieldCategoryList(DtcCluesCategoryListDto listRequestDto);

    List<Integer> queryCategoryPriority();

    Integer insertCluesCategory(DtcCluesCategoryDto dtcCluesCategoryDto);

    List<ImportResultInfoDto> importCluesCategory(MultipartFile importFile);

    Integer updateCluesCategory(DtcCluesCategoryDto dtcCluesCategoryDto);

    Integer deleteCluesCategory(String ecu, String dtc);

    Integer insertShieldCategory(DtcCluesCategoryDto dtcCluesCategoryDto);

    List<ImportResultInfoDto> importShieldCategory(MultipartFile importFile);

    Integer updateShieldCategory(DtcCluesCategoryDto dtcCluesCategoryDto);

    Integer deleteShieldCategory(String ecu, String dtc);

    Map<String, List<EcuDtcRelationDto>> queryRelationEcuDtc(Integer category);

    void exportDtcCluesCategory(DtcCluesCategoryListDto dtcCluesCategoryListDto, Integer category);

    List<ExportDtcCluesCategoryDto> exportCluesCategoryCallback(DtcCluesCategoryListDto dtcCluesCategoryListDto, Integer category);
}
