package com.volvo.maintain.application.maintainlead.dto.faultlight;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ApiModel("故障灯线索MQ消息")
public class FaultLightClueResponseDto {

    @ApiModelProperty("400外呼备注")
    private String comments;

    @ApiModelProperty("时间")
    private Date date;

    @ApiModelProperty("待预约标识")
    private boolean flag;

    @ApiModelProperty("400外呼备注")
    private TtFaultLightCluePo ttFaultLightCluePo;

}
