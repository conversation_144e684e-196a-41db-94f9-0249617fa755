package com.volvo.maintain.application.maintainlead.dto.order;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "店端查询取送车订单", description = "店端查询取送车订单")
public class ShopVehicleDeliverDto implements Serializable {

    private static final long serialVersionUID = -3046251551695546780L;

    /**
     * 取送车中台编号中台生成
     */
    @ApiModelProperty(value = "取送车中台编号")
    private String orderCode;

    /**
     * 取送车中台编号中台生成
     */
    @ApiModelProperty(value = "取送车中台编号IK")
    private String orderCodeIk;

    /**
     * 进厂时间start   -> 工单开单时间
     */
    @ApiModelProperty(value = "进厂时间-end yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date workOrderIssuingTimeStart;
    /**
     * 进厂时间end   -> 工单开单时间
     */
    @ApiModelProperty(value = "进厂时间-end yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date workOrderIssuingTimeEnd;

    /**
     * 下单时间start
     */
    @ApiModelProperty(value = "下单时间-end yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date placeOrderTimeStart;

    /**
     * 下单时间end
     */
    @ApiModelProperty(value = "下单时间-end yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date placeOrderTimeEnd;

    /**
     * 预约时间
     */
    @ApiModelProperty(value = "预约时间 yyyy-MM-dd HH:mm:ss start")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bookingTimeStart;

    /**
     * 预约时间
     */
    @ApiModelProperty(value = "预约时间 yyyy-MM-dd HH:mm:ss end")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bookingTimeEnd;

    /**
     * 创建时间 start
     */
    @ApiModelProperty(value = "创建时间 yyyy-MM-dd HH:mm:ss start")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeStart;

    /**
     * 创建时间 end
     */
    @ApiModelProperty(value = "创建时间 end")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;

    /**
     * 取送车订单类型 82711001 取车, 82711002 送车
     */
    @ApiModelProperty(value = "取送车订单类型 82711001 取车, 82711002 送车")
    private Integer type;

    /**
     * 订单编号   -> E代驾单号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderId;

    /**
     * 申请单号   -> 中台取送车单号
     */
    @ApiModelProperty(value = "申请单号")
    private Long id;

    /**
     * 订单状态：82721001 待确认, 82721002 已下单, 82721003 资金已冻结, 82721004 订单取消, 82721005 等待司机接单, 82721006 司机已接单, 82721007 司机已开启订单, 82721008 司机已就位, 82721009 司机开车中, 82721010 司机到达目的地, 82721011 已收车, 82721012 订单已完成, 82721013 已评价
     */
    @ApiModelProperty(value = "订单状态：82721001 待确认, 82721002 已下单, 82721003 资金已冻结, 82721004 订单取消, 82721005 等待司机接单, 82721006 司机已接单, 82721007 司机已开启订单, 82721008 司机已就位, 82721009 司机开车中, 82721010 司机到达目的地, 82721011 已收车, 82721012 订单已完成, 82721013 已评价")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单状态：82721001 待确认, 82721002 已下单, 82721003 资金已冻结, 82721004 订单取消, 82721005 等待司机接单, 82721006 司机已接单, 82721007 司机已开启订单, 82721008 司机已就位, 82721009 司机开车中, 82721010 司机到达目的地, 82721011 已收车, 82721012 订单已完成, 82721013 已评价")
    private List<Integer> orderStatusList;
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号", example = "001")
    private String carNo;

    /**
     * 车牌号Ik查询
     */
    @ApiModelProperty(value = "车牌号Ik查询 ", example = "001")
    private String carNoIk;
    /**
     * VIN
     */
    @ApiModelProperty(value = "VIN", example = "001")
    private String vin;
    /**
     * vinIk
     */
    @ApiModelProperty(value = "vinIk", example = "001")
    private String vinIk;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", example = "001")
    private String workOrderNo;
    /**
     * 工单号Ik查询
     */
    @ApiModelProperty(value = "工单号Ik查询", example = "001")
    private String workOrderNoIK;
    /**
     * 预约单号
     */
    @ApiModelProperty(value = "预约单号", example = "001")
    private String reservationNo;

    /**
     * 预约单号IK查询
     */
    @ApiModelProperty(value = "预约单号IK查询", example = "001")
    private String reservationNoIk;

    @ApiModelProperty(value = "经销商Code", example = "001", hidden = true)
    private String companyCode;

    /**
     * 车型code
     */
    @ApiModelProperty(value = "车型code", example = "895")
    private String modelCode;
    
    @ApiModelProperty(value = "服务商类型 0:e代驾，1:滴滴")
    private Integer supplierType;
}
