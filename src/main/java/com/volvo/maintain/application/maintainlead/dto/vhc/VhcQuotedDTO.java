package com.volvo.maintain.application.maintainlead.dto.vhc;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * vhc报价-检查项DTO
 */
@Data
public class VhcQuotedDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 是否推送用户10041001是
     */
    private String pushUser;
    /**
     * 检查中：89702001检查完成 ：89702003报价已完成：89702004报价已推送 ：89702005用户已反馈 ：89702006转工单完成 ：89702007
     */
    private String vhcState;
    /**
     * 推送用户时间
     */
    private String pushUserTime;
    /**
     * 一级类目检查集合
     */
    private List<VhcMaintanceDTO> vhcClassList;
    /**
     * 异常检查集合数量
     */
    private Integer exceptionVhcClassCount;
    /**
     * 正常检查集合数量
     */
    private Integer normalVhcClassCount;
    /**
     * 一级类目异常检查集合
     */
    private List<VhcMaintanceDTO> exceptionVhcClassList;
    /**
     * 一级类目正常检查集合
     */
    private List<VhcMaintanceDTO> normalVhcClassList;
    /**
     * 车架号
     */
    private String vin;
    /**
     * 直售PC端图片
     */
    private String colorDsPcImage;
    /**
     * PC图片
     */
    private String colorPcImage;
    /**
     * 外色图片(完整图片)
     */
    private String colorBigImage;
    /**
     * 外色图片(缩略图)
     */
    private String colorSmallImage;
    /**
     * 車型名稱
     */
    private String modelName;
    /**
     * 車型名稱
     */
    private String modelCode;

}
