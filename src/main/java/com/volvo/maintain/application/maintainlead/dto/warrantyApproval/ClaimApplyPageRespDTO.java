package com.volvo.maintain.application.maintainlead.dto.warrantyApproval;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "延保理赔申请列表DTO", description = "延保理赔申请列表DTO")
public class ClaimApplyPageRespDTO {

    @ApiModelProperty("延保理赔申请id")
    private Long id;

    @ApiModelProperty("经销商代码")
    private String ownerCode;

    @ApiModelProperty("经销商名称")
    private String dealerName;

    @ApiModelProperty("车架号")
    private String vin;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "出险日期")
    private Date faultTime;

    @ApiModelProperty(value = "产品名称")
    private String warrantyProductName;

    @ApiModelProperty("索赔金额")
    private BigDecimal claimAmount;

    @ApiModelProperty("易保报案号")
    private String caseNo;

    @ApiModelProperty("保单号")
    private String policyNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "保单起期")
    private Date policyStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "保单止期")
    private Date policyEndDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "首次提交时间")
    private Date firstSubmitDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "经销商二次提交时间")
    private Date secondSubmitDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "易保首次初审通过时间")
    private Date firstReviewDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "易保最后初审通过时间")
    private Date finalReviewDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "终审通过时间")
    private Date finalApprovalDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结案日期")
    private Date closeCaseDate;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("审核状态中文")
    private String approvalStatusStr;

    @ApiModelProperty("审核状态")
    private Integer approvalStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "回款维护时间")
    private Date approvalAmtTime;

    @ApiModelProperty("承保渠道")
    private  Integer underwritingChannel;

    @ApiModelProperty("承保渠道")
    private String underwritingChannelStr;
}


