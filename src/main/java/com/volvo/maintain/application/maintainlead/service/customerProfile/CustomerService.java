package com.volvo.maintain.application.maintainlead.service.customerProfile;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.CustomerInfoDto;
import com.volvo.maintain.application.maintainlead.dto.CustomerTagsDto;
import com.volvo.maintain.infrastructure.gateway.response.CdpResponse;
import com.volvo.maintain.interfaces.vo.RepairOrderHistoryParamsVo;
import com.volvo.maintain.interfaces.vo.RepairOrderHistoryResultVo;

import java.util.List;

public interface CustomerService {

    List<CustomerInfoDto> queryCustomInfoList(String vin, String mobile, String ownerCode);

    List<CustomerInfoDto> queryCustomInfoListByVin(String vin, String ownerCode);

    List<CustomerInfoDto> queryCustomInfoListByMobile(String vin, String mobile, String ownerCode);

    /**
     * 批量手机号查询客户属性和标签
     * @param mobileList
     * @return
     */
    CdpResponse<List<CustomerTagsDto>> queryCdpCustomAndTag(String vin, List<String> mobileList);

    Page<RepairOrderHistoryResultVo> queryCustomerJourney(int current, int size, RepairOrderHistoryParamsVo paramsVo);

    CustomerInfoDto queryDmsDefaultParam(String cdpTagIdKey);

    /**
     * 查询自店车主信息
     * @param vin
     * @param ownerCode
     * @return
     */
    CustomerInfoDto queryOwnerCustomer(String vin, String ownerCode);

    /**
     * 查询最近3个送修人信息
     * @param vin
     * @param ownerCode
     * @return
     */
    List<CustomerInfoDto> queryRepairOwner(String vin, String ownerCode);

    CustomerInfoDto queryCdpCustomer(String vin, String version);
}
