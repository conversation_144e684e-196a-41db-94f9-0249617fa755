package com.volvo.maintain.application.maintainlead.dto.dtcclues;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Objects;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("DTC线索类别")
public class DtcCluesCategoryListDto extends DtcCluesCategoryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "当前页", name = "currentPage")
    private Integer currentPage;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        DtcCluesCategoryListDto that = (DtcCluesCategoryListDto) o;
        return Objects.equals(currentPage, that.currentPage) && Objects.equals(pageSize, that.pageSize);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), currentPage, pageSize);
    }
}
