package com.volvo.maintain.application.maintainlead.dto.accidentclue;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 事故线索-分配规则配置表
 */
@ApiModel(description = "事故线索-分配规则配置表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class OwnerRuleDto implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * ID
	 */
	private Long id;

	/**
	 * 经销商code
	 */
	private String ownerCode;

	/**
	 * 规则类型：1:分配规则, 2:消息规则
	 */
	private Integer ruleType;

	/**
	 * 用户id
	 */
	private Long userId;

	/**
	 * 用户code
	 */
	private String userCode;

	/**
	 * 用户手机号
	 */
	private String userMobile;

	/**
	 * 用户名称
	 */
	private String userName;

	/**
	 * 在职状态 10081001:在职, 10081002:离职
	 */
	private Integer isOnjob;

	/**
	 * 是否选中，是：0041001, 否：10041002
	 */
	private Integer isSelect;

	/**
	 * 分配时间
	 */
	private Date allocationTime;

	/**
	 * 删除标识（0-未删除，1-已删除）
	 */
	private Integer isDeleted;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 更新人
	 */
	private String updateBy;

	/**
	 * 创建sql人
	 */
	private String createSqlby;

	/**
	 * 更新sql人
	 */
	private String updateSqlby;
}