package com.volvo.maintain.application.maintainlead.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 取送车订单详情
 *
 * <AUTHOR>
 * @since 2020-06-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "VehicleDeliverDetailVO", description = "取送车订单详情")
public class VehicleDeliverDetailVo {

    /*基本信息*/
    @ApiModelProperty(value = "vehicleDeliverVO")
    private VehicleDeliverVo vehicleDeliverVO;

    /*订单详情*/
    @ApiModelProperty(value = "detailVO")
    private DetailVo detailVO;

    /*司机信息*/
    @ApiModelProperty(value = "DriverInfoVO")
    private DriverInfoVo driverInfoVO;

    @ApiModelProperty(value = "异议反馈")
    private String reviewReason;
    
    @ApiModelProperty(value = "服务商类型 0:e代驾，1:滴滴")
    private Integer supplierType;
    
    @ApiModelProperty(value = "弹窗文案")
    private String alertMessage;

}