package com.volvo.maintain.application.maintainlead.service.strategy.clues;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.volvo.maintain.application.maintainlead.dto.EmpByRoleCodeDto;
import com.volvo.maintain.application.maintainlead.dto.RequestDto;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.ClueDataDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadOperationResultDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadsLogRecordDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LiteCrmClueResultDTO;
import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueDto;
import com.volvo.maintain.application.maintainlead.dto.clues.DistributeClueResDto;
import com.volvo.maintain.application.maintainlead.emums.ClueStrategyEnum;
import com.volvo.maintain.application.maintainlead.service.FullLeadsService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CustomerService;
import com.volvo.maintain.application.maintainlead.service.strategy.ClueExecuteStrategy;
import com.volvo.maintain.infrastructure.config.BaseProperties;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.MidVehicleVo;
import com.volvo.maintain.interfaces.vo.carebuy.CareBuyedVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

import static com.volvo.maintain.infrastructure.constants.CommonConstant.DEFAULT_STORE;

@Slf4j
public abstract class AbsClueExecuteStrategy implements ClueExecuteStrategy {

    @Autowired
    protected DomainMaintainLeadFeign domainMaintainLeadFeign;

    @Autowired
    protected DomainMaintainAuthFeign domainMaintainAuthFeign;

    @Autowired
    protected MidEndVehicleCenterFeign midEndVehicleCenterFeign;

    @Autowired
    protected MidEndAuthCenterFeign midEndAuthCenterFeign;

    @Autowired
    protected MidEndOrgCenterFeign midEndOrgCenterFeign;

    @Autowired
    protected DomainInsuranceLeadsFeign domainInsuranceLeadsFeign;

    @Autowired
    protected CustomerService customerService;

    @Autowired
    protected BaseProperties baseProperties;

    @Autowired
    protected FullLeadsService fullLeadsService;

    protected boolean validate(LeadOperationResultDto dto){return true;}

    //策略枚举
    protected abstract ClueStrategyEnum strategyEnum();

    //前置处理方法
    protected void preProcess(LeadOperationResultDto dto){}

    //幂等处理
    protected boolean handleIdempotency(Long id){return false;}

    //数据填充
    protected void fillData(LeadOperationResultDto dto){};

    //后置处理方法
    protected boolean postProcess(LeadOperationResultDto dto){return true;}

    protected void errorProcess(LeadOperationResultDto dto){}


    //获取策略枚举
    protected ClueStrategyEnum getStrategyEnum(){
        return Optional.ofNullable(strategyEnum()).orElseThrow(() -> new ServiceBizException("当前策略不能为空"));
    };

    @Override
    public String mark() {
        return getStrategyEnum().name();
    }

    /**
     * 线索下发执行模板方法
     * <p>
     * 定义了一系列线索下发需要执行的步骤，并提供部分实现。线索下发接口兼容不同类型线索，不同类型线索需遵循相同的实现步骤，即
     * <ul>
     * <li>第一步：记录原始日志（记录原始接口日志）</li>
     * <li>第二步：数据预处理(字段转义,解密 针对特定线索类型，如无子类则不需要重写)</li>
     * <li>第三步：白名单检查（提供通用的白名单检查能力，如有特殊需求重写此方法）</li>
     * <li>第四步：幂等处理 查询是否存在线索（提供一个通用的基于LitecrmId查询的幂等实现能力，不同类型线索实现应当不同）</li>
     * <li>第五步：数据填充 （线索下发前部分线索数据缺失或补充则此方法提供填充能力）</li>
     * <li>第六步：线索下发 （具体线索的下发逻辑，不同类型线索实现应当不同）</li>
     * <li>第七步：更新线索日志状态 （提供了通用的日志更新实现）</li>
     * <li>第八步：数据后处理 （线索下发完成后的后置处理）</li>
     * <li>第九步：异常处理 （线索下发异常后的通用处理）</li>
     * </ul>
     * <p>
     */
    @Override
    public LiteCrmClueResultDTO executeResp(LeadOperationResultDto dto) {
        LiteCrmClueResultDTO resultDTO = new LiteCrmClueResultDTO();
        ClueStrategyEnum strategyEnum = getStrategyEnum();
        try {

            boolean validate = this.validate(dto);
            if(!validate){
                throw new ServiceBizException("参数校验不通过!");
            }

            String dealer = this.getDealer(dto);
            //1.记录原始日志
            Long logId = this.logClue(dto, dealer, strategyEnum);
            DistributeClueDto clueDto = new DistributeClueDto();
            dto.setDistributeClueDto(clueDto);
            clueDto.setDealerCode(dealer);
            clueDto.setLogId(logId);

            //2.数据预处理(字段转义,解密)
            this.preProcess(dto);

            //3.白名单检查
            boolean flag  = this.checkWhitelist(dto,strategyEnum, dealer);
            if (!flag) {
                resultDTO.setRejectionCodes(Lists.newArrayList(dealer));
                return resultDTO;
            }
            //4.幂等处理 查询是否存在线索
            flag = handleIdempotency(dto.getId());
            if (flag) {
                throw new ServiceBizException("线索已经下发,请勿重复下发!");
            }

            //5.数据填充
            this.fillData(dto);

            //6.线索下发
            DistributeClueResDto resDto = this.distributeClues(dto);
            if (!resDto.isSuccess()) {
                throw new ServiceBizException("线索下发失败!");
            }else{
                dto.setDistributeClueResDto(resDto);
            }

            //7.更新线索日志状态
            this.updateLogClue(logId,resDto,strategyEnum);

            //8.数据后处理
            flag = postProcess(dto);
            if (!flag) {
                throw new ServiceBizException("线索下发失败!");
            }
        } catch (Exception e) {
            log.error("线索下发异常",e);
            //9.异常处理
            errorProcess(dto);
            throw e;
        }
        return resultDTO;
    }

    private void updateLogClue(Long id,DistributeClueResDto resDto,ClueStrategyEnum strategyEnum){
        // 更新下发日志
        LeadsLogRecordDto leadsLogRecordDto = new LeadsLogRecordDto();
        leadsLogRecordDto.setId(id);
        leadsLogRecordDto.setSinceType(Integer.valueOf(strategyEnum.getCode()));
        leadsLogRecordDto.setTaskStatus(1);
        String message = JSONObject.toJSONString(resDto);
        leadsLogRecordDto.setRespContent(StringUtils.left(message,200));
        domainMaintainLeadFeign.commUpdateLog(leadsLogRecordDto);
    }


    protected DistributeClueResDto distributeClues(LeadOperationResultDto dto){
        DmsResponse<DistributeClueResDto> domainResponse = domainMaintainLeadFeign.insertClues(dto);
        log.info("crmToNewbieClueDistribute Whether successful:{}", domainResponse);
        boolean res = domainResponse.getData().isSuccess();
        if (domainResponse.isFail() || Objects.nonNull(res) && res) {
            log.error("调用领域线索失败");
            return null;
        }
        //fullLeadsService.buildRenewalLeadStaticDto(dto.getId());
        return new DistributeClueResDto(false,null,null);
    }


    protected boolean checkWhitelist(LeadOperationResultDto dto,ClueStrategyEnum strategyEnum,String dealer){
        return checkWhitelist(strategyEnum,dealer);
    }

    protected boolean checkWhitelist(ClueStrategyEnum strategyEnum,String dealer){
        Integer modType = strategyEnum.getModType();
        if (StringUtils.isNotBlank(dealer) && Objects.nonNull(modType)) {
            log.info("crmToNewbieClueDistribute dealer isNotBlank");
            if (!isWhite(dealer,modType)) {
                log.info("crmToNewbieClueDistribute dealer isNotBlank and not isWhite dealer:{}", dealer);
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    private boolean isWhite(String dealer,Integer modType) {
        log.info("crmToNewbieClueDistribute isWhite dealer:{}", dealer);
        DmsResponse<Object> response = domainMaintainAuthFeign.checkWhitelist(dealer, modType, CommonConstant.WECOM_ACCIDENT_ROSTER_TYPE_WHITE, "");
        log.info("crmToNewbieClueDistribute isWhite response:{}",response);
        if (Objects.isNull(response) || response.isFail()){
            log.info("crmToNewbieClueDistribute isWhite error");
            return false;
        }
        Object data = response.getData();
        if (null == data) {
            log.info("crmToNewbieClueDistribute isWhite data isnull");
            return false;
        }
        try {
            return Boolean.parseBoolean(data.toString());
        } catch (Exception e) {
            log.info("crmToNewbieClueDistribute isWhite e:{}", e);
            return false;
        }
    }


    protected String getDealer(LeadOperationResultDto dto) {
        List<ClueDataDto.DealerInfo> dealerInfo = dto.getData().getDealerInfo();
        if (CollectionUtils.isEmpty(dealerInfo)) {
            return null;
        }
        // 经销商信息集合，DVR线索下发的时候，如果到店经销商和DVR销售门店都有的时候，都会存在这里，到店经销商dealerType="DEFAULT"；DVR销售门店 dealerType="DVR售卖经销商代码"
        List<String> dealerList = dealerInfo.stream()
                .filter(Objects::nonNull)
                .filter(item -> Objects.equals(item.getDealerType(), DEFAULT_STORE))
                .map(ClueDataDto.DealerInfo::getDealerCode).collect(Collectors.toList());
        String dealer = dealerList.stream().filter(StringUtils::isNotBlank).findFirst().orElse(null);
        log.info("crmToNewbieClueDistributeQueryDealerResponse:{}" , dealer);
        return dealer;
    }

    protected Long logClue(LeadOperationResultDto dto,String dealerCode,ClueStrategyEnum strategyEnum){
        LeadsLogRecordDto leadsLogRecordDto = new LeadsLogRecordDto();
        leadsLogRecordDto.setSinceType(Integer.valueOf(strategyEnum.getCode()));
        leadsLogRecordDto.setReqParams(JSONObject.toJSONString(dto));
        leadsLogRecordDto.setBizNo(dto.getVehicleVin()+ dealerCode);
        leadsLogRecordDto.setSubBizNo(dto.getId().toString());
        leadsLogRecordDto.setSubSinceType(1);
        leadsLogRecordDto.setTaskStatus(0);
        DmsResponse<Long> response = domainMaintainLeadFeign.commInsertLog(leadsLogRecordDto);
        if (response.isFail() || null == response.getData()){
            log.error("litecrm线索下发日志记录失败:{}", JSONObject.toJSONString(response));
        }else{
            log.info("litecrm线索下发日志记录成功");

        }
        return response.getData();
    }

    /**
     * 中台查询车主信息
     * @param vin
     * @return
     */
    protected MidVehicleVo getMidVehicleVo(String vin) {
        RequestDto<CareBuyedVo> objectRequestDto = new RequestDto<>();
        CareBuyedVo careBuyedVo = new CareBuyedVo();
        careBuyedVo.setVin(vin);
        objectRequestDto.setData(careBuyedVo);
        objectRequestDto.setPage(1L);
        objectRequestDto.setPageSize(1L);
        DmsResponse<Page<MidVehicleVo>> listDmsResponse = midEndVehicleCenterFeign.listOwnerVehiclePage(objectRequestDto);
        Page<MidVehicleVo> page = listDmsResponse.getData();
        if(Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())){
            return null;
        }
        return page.getRecords().get(0);
    }

    /**
     * 根据经销商查询服务经理
     */
    protected List<EmpByRoleCodeDto> selectFWJLByOwnerCode(String ownerCode,String roleCode) {
        log.info("selectFWJLByOwnerCode start ownerCode:{}", ownerCode);
        ResponseDto<EmpByRoleCodeDto> reqDos = new ResponseDto<>();
        EmpByRoleCodeDto empDto = new EmpByRoleCodeDto();
        // 经销商
        empDto.setCompanyCode(ownerCode);
        // 在职状态
        empDto.setIsOnjob(CommonConstant.IS_ON_JOB_IN);
        // 角色code
        empDto.setRoleCode(Collections.singletonList(roleCode));
        reqDos.setData(empDto);
        DmsResponse<List<EmpByRoleCodeDto>> response = midEndAuthCenterFeign.queryDealerUser(reqDos);
        if (null == response || response.isFail()) {
            log.info("selectFWJLByOwnerCode response isfail");
            return null;
        }
        log.info("selectFWJLByOwnerCode end");
        return response.getData();
    }

}
