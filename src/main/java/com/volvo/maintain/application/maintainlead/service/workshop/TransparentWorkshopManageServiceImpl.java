package com.volvo.maintain.application.maintainlead.service.workshop;

import cn.hutool.core.lang.Validator;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lop.open.api.sdk.DefaultDomainApiClient;
import com.lop.open.api.sdk.LopException;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.OpenOrderTraceService.commonQueryOrderTrace.CommonOrderTraceRequest;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.OpenOrderTraceService.commonQueryOrderTrace.CommonOrderTraceResponse;
import com.lop.open.api.sdk.plugin.LopPlugin;
import com.lop.open.api.sdk.plugin.factory.OAuth2PluginFactory;
import com.lop.open.api.sdk.request.IntegratedSupplyChain.IntegratedsupplychainOrderTraceQueryV2LopRequest;
import com.lop.open.api.sdk.response.IntegratedSupplyChain.IntegratedsupplychainOrderTraceQueryV2LopResponse;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderDto;
import com.volvo.maintain.application.maintainlead.dto.RequestDto;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.UserInfoByUserIdsDto;
import com.volvo.maintain.application.maintainlead.dto.bookingOrder.BookingOrderDto;
import com.volvo.maintain.application.maintainlead.dto.message.AppPushWithTemplateDto;
import com.volvo.maintain.application.maintainlead.dto.order.RepairOrderBatchQueryDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.*;
import com.volvo.maintain.application.maintainlead.mq.producer.PartDeliveryMQSendProducer;
import com.volvo.maintain.application.maintainlead.mq.producer.TransparentTocProducer;
import com.volvo.maintain.application.maintainlead.mq.producer.WorkshopMessageReminderProducer;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import com.volvo.maintain.application.maintainlead.vo.UserInfoVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.*;
import com.volvo.maintain.infrastructure.config.JdConfig;
import com.volvo.maintain.infrastructure.config.OSCCProperties;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.*;
import com.volvo.maintain.infrastructure.gateway.request.ETAOperationParamsDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ImResponse;
import com.volvo.maintain.infrastructure.gateway.response.PartPurchaseOrderDetaileDTO;
import com.volvo.maintain.infrastructure.gateway.response.ShortPartDto;
import com.volvo.maintain.infrastructure.util.ConvertRoleCodeToEmployeeIdUtils;
import com.volvo.maintain.infrastructure.util.OSCCRequestHelper;
import com.volvo.utils.BeanMapperUtil;
import com.volvo.utils.DateUtils;
import com.volvo.utils.LoginInfoUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

@Service
public class TransparentWorkshopManageServiceImpl implements TransparentWorkshopManageService{

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private DomainPartsFeign domainPartsFeign;

    @Autowired
    private DomainPurchaseFeign domainPurchaseFeign;

    @Autowired
    private DomainMaintainOrdersFeign domainMaintainOrdersFeign;
    
    @Autowired
    private OSCCRequestHelper helper;

    @Autowired
    private OSCCProperties properties;

    @Autowired
    private JdConfig jdConfig;

    @Autowired
    private ConvertRoleCodeToEmployeeIdUtils convertRoleCodeToEmployeeIdUtils;
    
    @Autowired
    private TransparentTocProducer transparentTocProducer;

    @Autowired
    private CommonMethodService commonMethodService;

    @Autowired
    private MidEndBasicdataCenterFeign midEndBasicdataCenterFeign;

    @Autowired
    private WorkshopMessageReminderProducer workshopMessageReminderProducer;

    @Autowired
    private DmscloudReportFeign dmscloudReportFeign;

    @Autowired
    private ApplicationMaintenanceentertainFeign applicationMaintenanceentertainFeign;

    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;
    
    @Autowired
    private MidEndAuthCenterFeign midEndAuthCenterFeign;
    
    @Resource
    private PartDeliveryMQSendProducer partDeliveryMQSendProducer;
    
    @Override
    public IPage<ShortPartItemDto> queryShortPart(ShortPartItemVo shortPartItemVo) {
        logger.info("query short part list");
        Page<ShortPartItemDto> shortPartItemDtoPage = queryShortParts(shortPartItemVo);
        // 检查 shortPartItemDtoPage 和其记录是否为空
        if (shortPartItemDtoPage == null || shortPartItemDtoPage.getRecords() == null || shortPartItemDtoPage.getRecords().isEmpty()) {
            return shortPartItemDtoPage;
        }
        // 提取 采购明细主键ID 并查询相关数据
        Map<Long, PartPurchaseOrderDetaileDTO> detaileDTOMap = queryPurchaseOrderById(
                shortPartItemDtoPage.getRecords().stream()
                        .map(ShortPartItemDto::getPurchaseOrderDetailId)
                        .collect(Collectors.toSet())
        ).stream().collect(Collectors.toMap(
                PartPurchaseOrderDetaileDTO::getId,
                Function.identity(),
                (k1, k2) -> k2
        ));

        // 提取 采购主键ID 并查询相关数据
        Map<Long, PartPurchaseOrderDTO> orderDTOMap = queryPurchaseOrderByIds(
                detaileDTOMap.values().stream()
                        .map(PartPurchaseOrderDetaileDTO::getPurchaseOrderId)
                        .collect(Collectors.toSet())
        ).stream().collect(Collectors.toMap(
                PartPurchaseOrderDTO::getId,
                Function.identity(),
                (k1, k2) -> k2
        ));
        // 数据聚合，将信息填充到 ShortPartItemDto 中
        shortPartItemDtoPage.getRecords().forEach(item -> Optional.ofNullable(detaileDTOMap.get(item.getPurchaseOrderDetailId()))
                .ifPresent(detailDTO -> {
                    item.setMissingPartsStatus(detailDTO.getMissingPartsStatus());
                    item.setExpectedDeliveryTime(detailDTO.getExpectedDeliveryTime());
                    String partNo = item.getPartNo();
                    String replaceParts = detailDTO.getReplaceParts();
                    Integer closeStatus = item.getCloseStatus();
                    if (StringUtils.isNotBlank(partNo) && StringUtils.isNotBlank(replaceParts) && !partNo.equals(replaceParts)
                            && Integer.valueOf(10041002).equals(closeStatus)) {
                        item.setPartsStatus(detailDTO.getPartsStatus());
                        item.setReplaceParts(replaceParts);
                    }
                    // 设置采购订单号
                    Optional.ofNullable(orderDTOMap.get(detailDTO.getPurchaseOrderId()))
                            .ifPresent(orderDTO -> item.setPurchaseNo(orderDTO.getPurchaseNo()));
                }));
        // 存在替换件/block  expectedDeliveryTime 置空
        shortPartItemDtoPage.getRecords().forEach(item -> {
            if (Objects.equals(item.getPartsStatus(), 81181002L) || Objects.equals(item.getPartsStatus(), 81181001L)) {
                item.setExpectedDeliveryTime(null);
            }
        });
        // 排序  存在替换件和block 的在上面并且按照缺料时间排序
        shortPartItemDtoPage.getRecords().sort(
                Comparator.comparing((ShortPartItemDto item) -> {
                            if (Objects.equals(item.getPartsStatus(), 81181002L)) {
                                return 2;
                            } else if (Objects.equals(item.getPartsStatus(), 81181001L)) {
                                return 1;
                            } else {
                                return 0;
                            }
                        }).reversed()
                        .thenComparing(ShortPartItemDto::getCreatedAt, Comparator.reverseOrder()) // 按 createdAt 时间倒序排列
        );
        // 填充服务顾问及技术
        this.setFWGWandJS(shortPartItemDtoPage.getRecords());
        return shortPartItemDtoPage;
    }

    /**
     * 填充服务顾问及技师
     * @param ShortPartItemDtoList
     */
    private void setFWGWandJS(List<ShortPartItemDto> ShortPartItemDtoList){
        // 处理服务顾问
        List<Long> serviceAdvisorIds = ShortPartItemDtoList.stream()
                .filter(dto -> Validator.isNumber(dto.getServiceAdvisor()))
                .map(config -> Long.parseLong(config.getServiceAdvisor())).collect(Collectors.toList());
        logger.info("setFWGWandJS:{}", JSONObject.toJSONString(serviceAdvisorIds));
        if(CollectionUtils.isNotEmpty(serviceAdvisorIds)){
            // 2. 使用midEndAuthCenterFeign.queryUserInfoByIds查询用户信息
            UserInfoByUserIdsDto buildUserInfoIds = UserInfoByUserIdsDto.builder().userIds(serviceAdvisorIds).build();
            RequestDto<UserInfoByUserIdsDto> requestDto = new RequestDto<>();
            requestDto.setData(buildUserInfoIds);
            ResponseDto<List<UserInfoVo>> userInfoList = midEndAuthCenterFeign.queryUserInfoByIds(requestDto);
            if (userInfoList.isFail()) {
                throw new ServiceBizException("获取组织中心用户信息失败！");
            }
            // 3. 将查询回来的用户信息赋值给 shortPartResultDTOList02 中的每一个对象
            Map<Integer, UserInfoVo> userInfoMap = userInfoList.getData().stream()
                    .collect(Collectors.toMap(UserInfoVo::getUserId, Function.identity(), (k1, k2) -> k1));

            ShortPartItemDtoList.stream()
                    .filter(dto -> Validator.isNumber(dto.getServiceAdvisor()))
                    .forEach(config -> {
                        UserInfoVo userInfo = userInfoMap.get(Integer.parseInt(config.getServiceAdvisor()));
                        if (userInfo != null) {
                            config.setServiceAdvisor(userInfo.getEmployeeName());
                        }
                        if (config.getServiceAdvisor().equals("-1")) {
                            config.setServiceAdvisor(null);
                        }
                    });
        }
        // 处理技师
        List<Long> technicianIds = new ArrayList<>();
        ShortPartItemDtoList.stream().filter(item -> StringUtils.isNotEmpty(item.getTechnician())).forEach(item -> {
            String technician = item.getTechnician();
            String[] technicianSplit = technician.split(",");
            for (String technicianId: technicianSplit) {
                technicianIds.add(Long.valueOf(technicianId));
            }
        });
        if(CollectionUtils.isNotEmpty(technicianIds)){
            // 2. 使用midEndAuthCenterFeign.queryUserInfoByIds查询用户信息
            UserInfoByUserIdsDto buildUserInfoIds = UserInfoByUserIdsDto.builder().userIds(technicianIds).build();
            RequestDto<UserInfoByUserIdsDto> requestDto = new RequestDto<>();
            requestDto.setData(buildUserInfoIds);
            ResponseDto<List<UserInfoVo>> userInfoList = midEndAuthCenterFeign.queryUserInfoByIds(requestDto);
            if (userInfoList.isFail()) {
                throw new ServiceBizException("获取组织中心用户信息失败！");
            }
            // 3. 将查询回来的用户信息赋值给 shortPartResultDTOList02 中的每一个对象
            Map<Integer, UserInfoVo> userInfoMap = userInfoList.getData().stream()
                    .collect(Collectors.toMap(UserInfoVo::getUserId, Function.identity(), (k1, k2) -> k1));

            ShortPartItemDtoList.stream().filter(item -> StringUtils.isNotEmpty(item.getTechnician())).forEach(item -> {
                String technician = item.getTechnician();
                String[] technicianSplit = technician.split(",");
                StringBuilder str = new StringBuilder();
                for (String technicianId: technicianSplit) {
                    UserInfoVo userInfo = userInfoMap.get(Integer.parseInt(technicianId));
                    if (userInfo != null) {
                        str.append(userInfo.getEmployeeName()+"/");
                    }
                }
                item.setTechnician(str.length() > 0 ? str.substring(0, str.length() - 1) : str.toString());
            });
        }
    }

    /**
     * 查看单据ETA时间接口
     * 因经销商会在OG中查看商品的基本信息和价格，以及希望能
     * 看到本地仓库库存和全国仓库的可用库存，一个经销商一次
     * 只能查一个商品的数据
     * 支持一个一个查询，for调用收集结果集。
     */
    public void queryETADocumentDetails() {
        logger.info("query eta document details");
        // 更新零件状态block 并发送站内信
        updatePartStatusAndSendMail();
        //  获取采购单数据并映射为请求参数
        List<PartPurchaseOrderDetaileDTO> purchaseOrderList = queryPartPurchaseOrderDetailList();
        //  如果为空则结束方法
        if (CollectionUtils.isEmpty(purchaseOrderList)) {
            logger.info("Purchase order list is empty. Terminating the process.");
            return;
        }
        List<OSCCRequestParamsDto> paramsDtoList = BeanMapperUtil.copyList(purchaseOrderList, OSCCRequestParamsDto.class);
        // 查询ETA时间
        List<ETADocumentDetails> etaDocumentDetails = queryETATimeToOSCC(paramsDtoList);
        // 处理ETA文档详情，生成结果列表
        List<ETADocumentParamsVo> resultList = processETADocumentDetails(etaDocumentDetails);
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }
        // 发送发送站内信
        sendMailNotifications(etaDocumentDetails, purchaseOrderList);
        // 更新ETA时间
        domainPurchaseFeign.updateERATime(resultList);
        // 同步ETA时间
        this.synchronousEta(purchaseOrderList, resultList);
        
        etaDocumentDetailsDataHandle(etaDocumentDetails);
    }

    /**
     * 同步预计到货时间到 cyx_repair.tt_part_buy_ext
     */
    private void synchronousEta(List<PartPurchaseOrderDetaileDTO> purchaseOrderList, List<ETADocumentParamsVo> resultList){
        logger.info("synchronousEta start:{}", JSON.toJSONString(resultList));
        Map<Long, PartPurchaseOrderDetaileDTO> purchaseOrder = purchaseOrderList.stream().collect(Collectors.toMap(PartPurchaseOrderDetaileDTO::getId, Function.identity(), (k1, k2) -> k2));
        resultList.stream().forEach(item -> {
            if(Objects.nonNull(item.getId()) && Objects.nonNull(purchaseOrder.get(item.getId()))){
                // 如果替换件号不为空的情况下，是否就取替换件号即可？？
                PartPurchaseOrderDetaileDTO dto = purchaseOrder.get(item.getId());
                item.setPartsNo(dto.getPartNo());
            }
        });
        List<ETADocumentParamsVo> paramsVoList = resultList.stream()
                .filter(item -> Objects.nonNull(item.getId()) && Objects.nonNull(item.getExpectedDeliveryTime()))
                .collect(Collectors.toList());
        // 调用domain-maintain-orders服务同步eta时间
        domainMaintainOrdersFeign.synchronousEta(paramsVoList);
        logger.info("synchronousEta end");

    }
	private void etaDocumentDetailsDataHandle(List<ETADocumentDetails> etaDocumentDetails) {
        logger.info("eta documents details data handle");
		Map<String, List<String>> oldSpGoodsNoList = getOldSpGoodsNoList(etaDocumentDetails);
		logger.info("oldSpGoodsNoList : {}", JSON.toJSONString(oldSpGoodsNoList));
		for (Map.Entry<String, List<String>> entry : oldSpGoodsNoList.entrySet()) {
			String key = entry.getKey();
			String[] split = key.split("#");
			List<String> val = entry.getValue();        	
			if(CollectionUtils.isEmpty(val)) {
				continue;
			}
			String ownerCode = split[0];
			String purchaseNo = split[1];
			String sceneCode = "7";
			etaDataMateHandle(val, ownerCode, purchaseNo, sceneCode);
		}
	}
	
	private void etaDataMateHandle(List<String> val, String ownerCode, String purchaseNo, String sceneCode) {
		partDeliveryMQSendProducer.sendOsccPartDeliveryMQMsg(val, ownerCode, purchaseNo);
		try {
			logger.info("获取缺料明细数据：ownerCode:{}, roNo : {}", ownerCode, purchaseNo);
			DmsResponse<List<PartPurchaseOrderDetaileDTO>> queryPurchasePart = domainPurchaseFeign.queryPurchasePart(ownerCode, purchaseNo);
			if(queryPurchasePart==null || queryPurchasePart.isFail()) {
				return;
			}
			
        	List<PartPurchaseOrderDetaileDTO> data = queryPurchasePart.getData();
			List<Long> ids = data.stream().filter(Objects::nonNull).map(PartPurchaseOrderDetaileDTO::getId).collect(Collectors.toList());        	
        	//触发触点消息
        	DmsResponse<List<ShortPartDto>> shortInfoByPurchaseDetaileIds = domainMaintainOrdersFeign.queryShortInfoByPurchaseDetaileIds(ids);
        	if(Objects.isNull(shortInfoByPurchaseDetaileIds) || shortInfoByPurchaseDetaileIds.isFail()) {
        		return;
        	}
        	List<ShortPartDto> shortPartList = shortInfoByPurchaseDetaileIds.getData();
        	Map<String, List<ShortPartDto>> shortPartMap = shortPartList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(ShortPartDto::getPartNo));
			data.stream().filter(Objects::nonNull).filter(obj->val.contains(obj.getPartNo())).forEach(obj->purchasePartReplaceEtaDataMate(obj, sceneCode, shortPartMap));
		} catch (Exception e) {
			logger.info("处理替换件消息发送失败：", e);
		}
	}

    /**
     * ETA替换件发送消息
     */
	private void purchasePartReplaceEtaDataMate(PartPurchaseOrderDetaileDTO obj, String sceneCode, Map<String, List<ShortPartDto>> shortPartMap) {
		try {
            logger.info("purchasePartReplaceEtaDataMate is sceneCode:{}", sceneCode);
			List<ShortPartDto> list = shortPartMap.get(obj.getPartNo());
			List<Long> shortIds = list.stream().filter(Objects::nonNull).map(ShortPartDto::getShortId).collect(Collectors.toList());
			ETAOperationParamsDTO etaOperationParams = new ETAOperationParamsDTO();
			etaOperationParams.setOwnerCode(obj.getOwnerCode());
			etaOperationParams.setPartNo(obj.getPartNo());
			etaOperationParams.setPartQuantity(obj.getOrderQuantity());
			etaOperationParams.setSheetNo(obj.getLinkedNumber());
			etaOperationParams.setReplacePartNo(obj.getReplaceParts());
			etaOperationParams.setSceneCode(sceneCode);
			etaOperationParams.setPurchaseOrderDetailId(obj.getId());
			etaOperationParams.setShortIds(shortIds);
			long currentTimeMillis = System.currentTimeMillis();
			etaOperationParams.setMsgTimeStamp(currentTimeMillis);
			String id = String.join("", etaOperationParams.getOwnerCode(), etaOperationParams.getPartNo());
			transparentTocProducer.sendOrderMsg(etaOperationParams, id);
		} catch (Exception e) {
			logger.info("数据转换/消息发送失败：", e);
		}
	}

    /**
     * 处理eta结果 eta时间 odo  bo
     * @param etaDocumentDetails  eta原始结果集
     * @return 处理后的结果
     */
    private List<ETADocumentParamsVo> processETADocumentDetails(List<ETADocumentDetails> etaDocumentDetails) {
        Map<String, ETADocumentDetails> ETAMap = etaDocumentDetails.stream()
                .collect(Collectors.toMap(eta -> eta.getSalesMainDetails().getOwnerCode() +"#"+ eta.getSalesMainDetails().getPurchaseOrder(), Function.identity(), (k1, k2) -> k2));

        List<ETADocumentParamsVo> resultList = new ArrayList<>();
        ETAMap.forEach((k, value) -> {
            List<ETADocumentParamsVo> odoETAList = createOdoETAList(k, value);
            resultList.addAll(odoETAList);
        });
        logger.info("processETADocumentDetails:{}", JSONObject.toJSONString(resultList));
        return resultList;
    }

    /**
     * 区分odo出库单有无eta时间 没有从bo取eta时间并替换。
     * k 经销商+采购单 拼接符#
     * @param ownerCodeAndPurchase 经销商
     * @param value ETADocumentDetails
     * @return 过滤之后结果集
     */
    private List<ETADocumentParamsVo> createOdoETAList(String ownerCodeAndPurchase, ETADocumentDetails value) {
        List<ETADocumentParamsVo> boETAList = new ArrayList<>();
        if (CollectionUtils.isEmpty(value.getBoList()) && CollectionUtils.isEmpty(value.getOdoList())) {
            return boETAList;
        }
        if (CollectionUtils.isEmpty(value.getOdoList())) {
            value.getBoList().stream().filter(time -> StringUtils.isNotBlank(time.getEtaTime())).forEach(bo -> {
                String etaTimeStr = bo.getEtaTime()+"@";
                String ownerCodeAndETAString = ownerCodeAndPurchase + "#" + etaTimeStr;
                String spGoodsNos = bo.getSpGoodsNo();
                Long id = bo.getId();
                boETAList.add(createETADocumentParamsVo(ownerCodeAndETAString.split("#"), spGoodsNos, id));
            });
        }
        logger.info("odoList为空，从boList获取ETA时间并构建参数: {}", boETAList.size());
        List<Triple<String, String, Long>> odoDetails = value.getOdoList().stream()
                .flatMap(odo -> odo.getItemList().stream().map(item ->
                        Triple.of(
                                ownerCodeAndPurchase + "#" + (StringUtils.isBlank(odo.getEtaTimeStr()) ? "null" : odo.getEtaTimeStr()),
                                item.getIsReplace() == 1 ? item.getSpGoodsNo() : null,
                                item.getId()
                        )))
                .collect(Collectors.toList());
        Map<Boolean, List<Triple<String, String, Long>>> partitionedMap = odoDetails.stream()
                .collect(Collectors.partitioningBy(dto -> "null".equals(dto.getLeft().split("#")[2])));
        List<ETADocumentParamsVo> odoETAList = partitionedMap.get(false).stream()
                .map(v -> createETADocumentParamsVo(v.getLeft().split("#"), v.getMiddle(), v.getRight()))
                .collect(Collectors.toList());
        Map<Long, Triple<String, String, Long>> allMap = partitionedMap.get(true).stream()
                .collect(Collectors.toMap(Triple::getRight, Function.identity(), (k1, k2) -> k1));



        allMap.forEach((k1, v1) -> {
            String[] ownerCodeAndETA = v1.getLeft().split("#");
            updateETATimeFromBoList(value.getBoList(), ownerCodeAndETA, v1.getMiddle());
            // 找到对应的 Triple 来获取 Odo 的 ID
            Triple<String, String, Long> matchingTriple = partitionedMap.get(true).stream()
                    .filter(triple -> triple.getLeft().equals(v1.getLeft()))
                    .findFirst()
                    .orElse(null);
            if (matchingTriple != null) {
                Long id = matchingTriple.getRight();
                boETAList.add(createETADocumentParamsVo(ownerCodeAndETA, v1.getMiddle(), id));
            }
        });
        logger.info("odoETAList result区分odo出库单有无eta时间 没有从bo取eta时间并替换:{}", odoETAList.size());
        boETAList.addAll(odoETAList);
        logger.info("boETAList result: {}", boETAList.size());
        return boETAList;
    }

    /**
     * bo单 中的eta时间替换 odo单中的eta时间
     * 添加@ 是为区分eta时间做更新的时候区分更新的是bo单中的还是odo单中的
     * @param boList bo单
     * @param ownerCodeAndETA newbie接受eta时间的属性
     * @param partsNo 零件
     */
    private void updateETATimeFromBoList(List<SalesItemOutStockDetailsDto> boList, String[] ownerCodeAndETA, String partsNo) {
        logger.info("updateETATimeFromBoList is ownerCodeAndETA:{}", JSONObject.toJSONString(ownerCodeAndETA));
        Optional.ofNullable(boList).ifPresent(list ->
                list.forEach(bo -> {
                    if (StringUtils.isNotBlank(partsNo) && partsNo.equals(bo.getSpGoodsNo()) && StringUtils.isNotBlank(bo.getEtaTime())) {
                        ownerCodeAndETA[2] = bo.getEtaTime() + "@";
                    }
                })
        );
    }

    /**
     *
     * @param ownerCodeAndETA 下标0为经销商，1时间，
     * @param partsNo 零件号
     * @param id 主键
     * @return 文档信息
     */
    private ETADocumentParamsVo createETADocumentParamsVo(String[] ownerCodeAndETA, String partsNo, Long id) {
        logger.info("createETADocumentParamsVo is id:{},{},{}", ownerCodeAndETA.toString(), partsNo, id);
        ETADocumentParamsVo vo = new ETADocumentParamsVo();
        vo.setExpectedDeliveryTime(ownerCodeAndETA[2]);
        vo.setReplaceParts(partsNo);
        vo.setOwnerCode(ownerCodeAndETA[0]);
        vo.setPurchaseNo(ownerCodeAndETA[1]);
        vo.setId(id);
        return vo;
    }

    /**
     *
     * @param etaDocumentDetails ETA原始结果
     * @param purchaseOrderList NEWBIE原始结果
     */
    private void sendMailNotifications(List<ETADocumentDetails> etaDocumentDetails, List<PartPurchaseOrderDetaileDTO> purchaseOrderList) {
        logger.info("sendMailNotifications is begin");
        // key 经销商 value 替换件集合
        Map<String, List<String>> oldSpGoodsNoMap = getOldSpGoodsNoList(etaDocumentDetails);
        // 跟newbie 原始结果过滤 保留存在的数据 key 采购明细主键id value 命中的替换件零件号list
        Map<Long, List<String>> detailMap = getDetailMap(purchaseOrderList, oldSpGoodsNoMap);
        List<PartPurchaseOrderDetaileDTO> roNoList = queryPurchaseOrderById(detailMap.keySet());
        updatePurchaseOrderById(detailMap.keySet());
        List<PurchaseOrderDetailsDto> roNoDetails = getRoNoDetails(detailMap, roNoList);
        Map<String,Object> map = Maps.newHashMap();
        // 发送邮件通知 主键 ID  工单号， 替换件集合
        roNoDetails.stream().distinct().collect(Collectors.toMap(
                PurchaseOrderDetailsDto::getId,
                detail -> detail,
                (k1, k2) -> k1)).values().forEach(detail -> {
            Long id = detail.getId();
            String roNo = detail.getRoNo();
            List<String> partList = detail.getPartList();
            // 查找对应的 OwnerCode
            String ownerCode = findOwnerCodeByPartList(oldSpGoodsNoMap, partList);
            if (ownerCode != null) {
                sendMailNotice(id, roNo, partList, ownerCode, false, map);
            }
        });
    }

    private String findOwnerCodeByPartList(Map<String, List<String>> oldSpGoodsNoMap, List<String> partList) {
        return oldSpGoodsNoMap.entrySet().stream()
                .filter(entry -> partList.stream().anyMatch(entry.getValue()::contains))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);
    }

    private Map<String, List<String>> getOldSpGoodsNoList(List<ETADocumentDetails> etaDocumentDetails) {
        return etaDocumentDetails.stream()
                .collect(Collectors.groupingBy(
                        eta -> eta.getSalesMainDetails().getOwnerCode() + "#" + eta.getSalesMainDetails().getPurchaseOrder(),
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .flatMap(etaDoc -> CollectionUtils.isNotEmpty(etaDoc.getOdoList()) ? etaDoc.getOdoList().stream() : Stream.empty())
                                        .flatMap(odo -> CollectionUtils.isNotEmpty(odo.getItemList()) ? odo.getItemList().stream() : Stream.empty())
                                        .filter(item -> item.getIsReplace() == 1)
                                        .map(OdoItemDetails::getOldSpGoodsNo)
                                        .collect(Collectors.toList())
                        )
                ));
    }

    private Map<Long, List<String>> getDetailMap(List<PartPurchaseOrderDetaileDTO> purchaseOrderList, Map<String, List<String>> oldSpGoodsNoMap) {
        if (MapUtils.isEmpty(oldSpGoodsNoMap)) {
            return Collections.emptyMap();
        }
        return purchaseOrderList.stream()
                .filter(order -> {
                    String partNo = order.getPartNo();
                    String ownerCode = order.getOutCustomerNo();
                    String purchaseNo = order.getNbCode();
                    return oldSpGoodsNoMap.entrySet().stream()
                            .anyMatch(entry -> {
                                // 将键按照 "#" 分割为 ownerCode 和 purchaseNo
                                String[] keyParts = entry.getKey().split("#");
                                if (keyParts.length == 2) {
                                    String entryOwnerCode = keyParts[0];
                                    String entryPurchaseNo = keyParts[1];
                                    // 检查 ownerCode, purchaseNo 和 partNo 是否匹配
                                    return entryOwnerCode.equals(ownerCode) &&
                                            entryPurchaseNo.equals(purchaseNo) &&
                                            entry.getValue().contains(partNo);
                                }
                                return false;
                            });
                })
                .collect(Collectors.groupingBy(PartPurchaseOrderDetaileDTO::getId,
                        Collectors.mapping(PartPurchaseOrderDetaileDTO::getPartNo, Collectors.toList())));
    }

    private List<PurchaseOrderDetailsDto> getRoNoDetails(Map<Long, List<String>> detailMap, List<PartPurchaseOrderDetaileDTO> roNoList) {
        return detailMap.entrySet().stream()
                .map(entry -> new PurchaseOrderDetailsDto(
                        entry.getKey(),
                        findRoNoById(entry.getKey(), roNoList),
                        entry.getValue()
                ))
                .collect(Collectors.toList());
    }

    /**
     * 公共方法查询 eta时间
     * @param paramsDtoList 请求参数
     * @return finalResult
     */
    private List<ETADocumentDetails> queryETATimeToOSCC(List<OSCCRequestParamsDto> paramsDtoList) {
        ObjectMapper mapper = new ObjectMapper();
        List<ETADocumentDetails> finalResult = new ArrayList<>();
        Lists.partition(paramsDtoList, 20).forEach(list -> {
            Map<String, Object> param = new HashMap<>();
            param.put("orderParamList", list);
            Map<String, Object> result = helper.sendPost(properties.getPath(), param);
            logger.info("queryETATimeToOSCC result :{}", result.get("code"));
            if (properties.getUnThrowError() == null || properties.getUnThrowError() != 10041001) {
                if (!"200".equals(String.valueOf(result.get("code")))) {
                    logger.info("queryETATimeToOSCC result:{}, param :{}", result, param);
                    return;
                }
            }
            // 将每次返回的部分结果转换为 List<ETADocumentDetails> 并加入最终结果列表
            List<ETADocumentDetails> partialResult = mapper.convertValue(result.get("data"), new TypeReference<List<ETADocumentDetails>>() {
            });
            if (CollectionUtils.isEmpty(partialResult)) {
                return;
            }
            finalResult.addAll(partialResult);
        });
        finalResult.forEach(etaDetail -> {
            SalesMainDetailsDto salesMainDetails = etaDetail.getSalesMainDetails();
            List<OutboundOrderDto> odoList = etaDetail.getOdoList();
            List<SalesItemOutStockDetailsDto> boList = etaDetail.getBoList();
            paramsDtoList.stream()
                    .filter(dto -> dto.getNbCode().equals(salesMainDetails.getSellerSalesNo()) &&
                            dto.getOutCustomerNo().equals(salesMainDetails.getOutCustomerNo()))
                    .forEach(matchingDto -> {
                        salesMainDetails.setOwnerCode(matchingDto.getOutCustomerNo());
                        salesMainDetails.setPurchaseOrder(matchingDto.getNbCode());
                        Optional.ofNullable(odoList).ifPresent(odoItems -> {
                            odoItems.forEach(odo -> {
                                Optional.ofNullable(odo.getItemList()).ifPresent(itemList -> {
                                    itemList.stream()
                                            .filter(item -> item.getOldSpGoodsNo().equals(matchingDto.getPartNo()))
                                            .forEach(matchingItem -> matchingItem.setId(matchingDto.getId()));
                                });
                            });
                        });
                        Optional.ofNullable(boList).ifPresent(boItems -> {
                            boItems.forEach(bo -> {
                                if (bo.getSpGoodsNo().equals(matchingDto.getPartNo())) {
                                    bo.setId(matchingDto.getId());
                                }
                            });
                        });
                    });
        });
        logger.info("queryETATimeToOSCC finalResult: {}", JSONObject.toJSONString(finalResult));
        return finalResult;
    }

    /**
     * 找到需要发消息数据的id ，在找到采购单获取工单号 发送消息 这里  (可以加参数userId)
     * @param id 采购明细 id
     * @param roNo 工单号
     * @param partList 发送消息的零件号
     * @param ownerCode 经销商
     * @param flag 标识
     */
    public void sendMailNotice(Long id, String roNo, List<String> partList, String ownerCode, boolean flag, Map<String,Object> map) {
        logger.info("send mail notice flag is :{}, id :{}, roNo :{}, ownerCode :{}, partList :{}", flag, id, roNo, ownerCode,JSONObject.toJSONString(partList));
        List<String> roNoList = queryServiceAdvisorById(id);
        List<String> userIdList = queryServiceAdvisor(ownerCode, roNoList);
        List<String> uniqueRoNoList = new ArrayList<>();
        for (String ro : roNoList) {
            if (!map.containsKey(ro)) {
                uniqueRoNoList.add(ro);
                map.put(ro, true);
            }
        }
        AppPushWithTemplateDto appPushWithTemplateDto = new AppPushWithTemplateDto();
        appPushWithTemplateDto.setRoNo(roNo, uniqueRoNoList);
        appPushWithTemplateDto.setPartsNo(partList);
        String dealer = ownerCode.contains("#") ? ownerCode.split("#")[0] : ownerCode;
        convertRoleCodeToEmployeeIdUtils.sendAppMessage(appPushWithTemplateDto, dealer, userIdList, flag);
    }

    /**
     * 基于采购单id 经销商 获取
     * @param purchaseOrderId 采购明细id
     * @return 工单号
     */
    public List<String> queryServiceAdvisorById(Long purchaseOrderId) {
    	logger.info("queryServiceAdvisorById: {}", purchaseOrderId);
        List<ShortPartItemDto> shortPartItemDtoPage = queryShortItemByDetailIds(Collections.singletonList(purchaseOrderId));
        if (CollectionUtils.isEmpty(shortPartItemDtoPage)) {
            return Collections.emptyList();
        }
        return shortPartItemDtoPage.stream()
                .filter(this::isShortPartStatusMatched)
                .map(ShortPartItemDto::getSheetNo)
                .collect(Collectors.toList());
    }

    /**
     * 查询已上传未入库的采购订单查询ETA
     *
     * @return 符合条件的采购订单
     */
    public List<PartPurchaseOrderDetaileDTO> queryPartPurchaseOrderDetailList() {
    	logger.info("queryPartPurchaseOrderDetailList");
        DmsResponse<List<PartPurchaseOrderDetaileDTO>> purchaseOrderResponse = domainPurchaseFeign.queryPartPurchaseOrderDetailList();
        if (ObjectUtils.isEmpty(purchaseOrderResponse)) {
            throw new ServiceBizException("feign domain parts error");
        }
        if (purchaseOrderResponse.isFail() || null == purchaseOrderResponse.getData()) {
            throw new ServiceBizException(purchaseOrderResponse.getErrMsg());
        }
        return purchaseOrderResponse.getData();
    }

    /**
     * 根据采购单明细主键Id 查询明细数据
     * @param idList 主键ID
     * @return 工单号集合
     */
    public List<PartPurchaseOrderDetaileDTO> queryPurchaseOrderById(Set<Long> idList) {
       logger.info("queryPurchaseOrderById");
       DmsResponse<List<PartPurchaseOrderDetaileDTO>> response = domainPurchaseFeign.queryPurchaseOrderById(idList);
       logger.info("根据采购单明细主键Id 查询明细数据 queryPurchaseOrderById return :{}", JSONObject.toJSONString(response));
       if (ObjectUtils.isEmpty(response)) {
           throw new ServiceBizException("feign domain parts error");
       }
       if (response.isFail() || null == response.getData()) {
           throw new ServiceBizException(response.getErrMsg());
       }
       return response.getData();
    }

    /**
     * 根据采购单明细主键Id 更新零件为替换件
     * @param idList 主键ID
     */
    public void updatePurchaseOrderById(Set<Long> idList) {
        logger.info("updatePurchaseOrderById");
        DmsResponse<Void> response = domainPurchaseFeign.updatePurchaseOrderById(idList);
        if (ObjectUtils.isEmpty(response)) {
            throw new ServiceBizException("feign domain parts error");
        }
        if (response.isFail()) {
            throw new ServiceBizException(response.getErrMsg());
        }
    }

    /**
     * 根据采购单明细中的采购单主键id（purchaseOrderId） 查询采购单详情，
     * @param idList 主键ID
     * @return 工单号集合
     */
    public List<PartPurchaseOrderDTO> queryPurchaseOrderByIds(Set<Long> idList) {
        logger.info("queryPurchaseOrderByIds");
        DmsResponse<List<PartPurchaseOrderDTO>> response = domainPurchaseFeign.queryPurchaseOrderByIds(idList);
        if (ObjectUtils.isEmpty(response)) {
            throw new ServiceBizException("feign domain parts error");
        }
        if (response.isFail() || null == response.getData()) {
            throw new ServiceBizException(response.getErrMsg());
        }
        return response.getData();
    }

    public static String findRoNoById(Long id, List<PartPurchaseOrderDetaileDTO> purchaseOrderList) {
        // 找到与给定 id 对应的 roNo
        return purchaseOrderList.stream()
                .filter(order -> order.getId().equals(id))
                .map(PartPurchaseOrderDetaileDTO::getLinkedNumber)
                .filter(StringUtils::isNotBlank)
                .findFirst()
                .orElse(null); // 如果没有找到对应的 roNo，返回 null 或其他默认值
    }

    /**
     * 查询ETA时间 （C端使用）
     *
     * @return ETA时间集合
     */
    @Override
    public List<OutboundOrderETAResponseDto> queryETADocumentDetailsToC(List<ToCRequestParamsDto> toCRequestParamsDto) {
        logger.info("queryETADocumentDetailsToC");
        if (CollectionUtils.isEmpty(toCRequestParamsDto)) {
            return Collections.emptyList();
        }
        List<OutboundOrderToCResponseDto> shortOrder = queryPartsShortPart(toCRequestParamsDto);
        if (CollectionUtils.isEmpty(shortOrder)) {
            logger.info("No orders found in queryPartsShortPart");
            return Collections.emptyList();
        }
        // 封装返回数据
        List<OutboundOrderETAResponseDto> resultData = shortOrder.stream().map(item01 -> {
            OutboundOrderETAResponseDto responseDto = new OutboundOrderETAResponseDto();
            BeanUtils.copyProperties(item01, responseDto);
            responseDto.setOwnerCode(item01.getOwnerCode());
            responseDto.setRoNo(item01.getSheetNo());
            responseDto.setPartsNo(item01.getPartNo());
            return responseDto;
        }).collect(Collectors.toList());

        Set<Long> purchaseOrderIds = shortOrder.stream()
                .map(OutboundOrderToCResponseDto::getPurchaseOrderDetailId)
                .filter(ObjectUtils::isNotEmpty)
                .collect(Collectors.toSet());
        // 根据缺料中的purchase_order_detail_id 查询采购明细
        List<PartPurchaseOrderDetaileDTO> partPurchaseOrderDetaileList = queryPurchaseOrderById(purchaseOrderIds);
        if (CollectionUtils.isEmpty(partPurchaseOrderDetaileList)) {
            logger.info("No purchase order details found for the given IDs");
            return resultData;
        }

        Map<Long, PartPurchaseOrderDetaileDTO> purchaseOrderDetaileList = partPurchaseOrderDetaileList.stream().collect(Collectors.toMap(PartPurchaseOrderDetaileDTO::getId, Function.identity(), (p1, p2) -> p1));
        resultData.stream().filter(item -> Objects.nonNull(item.getPurchaseOrderDetailId())).forEach(item -> {
            PartPurchaseOrderDetaileDTO partPurchaseOrderDetaileDTO = purchaseOrderDetaileList.get(item.getPurchaseOrderDetailId());
            if(null != partPurchaseOrderDetaileDTO){
                BeanUtils.copyProperties(partPurchaseOrderDetaileDTO, item);
                item.setEtaTime(Objects.isNull(partPurchaseOrderDetaileDTO.getExpectedDeliveryTime()) ? null : partPurchaseOrderDetaileDTO.getExpectedDeliveryTime());
            }
        });
        // 使用 partitioningBy 将含 ETA 和不含 ETA 的分开处理
        Map<Boolean, List<PartPurchaseOrderDetaileDTO>> partitionedList = partPurchaseOrderDetaileList.stream()
//                .filter(s -> !Objects.equals(81181002L,s.getPartsStatus()))
                .collect(Collectors.partitioningBy(
                        detail -> StringUtils.isNotBlank(detail.getExpectedDeliveryTime())
                ));
        logger.info("partitionedList result :{}", JSONObject.toJSONString(partitionedList));

        // 处理没有 ETA 的条目
        List<PartPurchaseOrderDetaileDTO> noEtaDetails = partitionedList.get(false);
        if (CollectionUtils.isEmpty(noEtaDetails)) {
            return resultData;
        }

        Set<Long> orderIds = noEtaDetails.stream()
                .map(PartPurchaseOrderDetaileDTO::getPurchaseOrderId)
                .collect(Collectors.toSet());

        List<PartPurchaseOrderDTO> purchaseOrderList = queryPurchaseOrderByIds(orderIds);
        if (CollectionUtils.isEmpty(purchaseOrderList)) {
            logger.info("No purchase orders found by IDs");
            return resultData;
        }

        // 将 purchaseOrderList 转换为 Map，便于查找
        Map<Long, PartPurchaseOrderDTO> purchaseOrderMap = purchaseOrderList.stream()
                .collect(Collectors.toMap(PartPurchaseOrderDTO::getId, Function.identity()));

        Map<Long, PartPurchaseOrderDetaileDTO> map = partitionedList.get(false).stream()
                .collect(Collectors.toMap(PartPurchaseOrderDetaileDTO::getId, Function.identity()));

        // 遍历 shortOrder，并根据 purchaseOrderDetailId 填充对应的 purchaseOrder 信息
        shortOrder.forEach(outboundOrder -> {
            Long purchaseOrderDetailId = outboundOrder.getPurchaseOrderDetailId();
            Optional.ofNullable(map.get(purchaseOrderDetailId)).ifPresent(purchaseOrderDetail -> {
                Long purchaseOrderId = purchaseOrderDetail.getPurchaseOrderId();
                Optional.ofNullable(purchaseOrderMap.get(purchaseOrderId)).ifPresent(partPurchaseOrderDTO -> {
                    if (outboundOrder.getPurchaseNoList() == null) {
                        outboundOrder.setPurchaseNoList(new ArrayList<>());
                    }
                    outboundOrder.getPurchaseNoList().add(partPurchaseOrderDTO.getPurchaseNo());
                });
            });
        });
        logger.info("回写采购单号到缺料对象:{}", JSONObject.toJSONString(shortOrder));
        List<OSCCRequestParamsDto> paramsDtoList = purchaseOrderList.stream()
                .map(this::createOSCCRequestParams)
                .collect(Collectors.toList());
        //OSCC补偿
        this.exceOSCC(paramsDtoList, resultData, shortOrder);
        return resultData;
    }

    /**
     *
     */
    private void exceOSCC(List<OSCCRequestParamsDto> paramsDtoList,List<OutboundOrderETAResponseDto> resultData, List<OutboundOrderToCResponseDto> shortOrder){
       try {
           List<ETADocumentDetails> etaDocumentDetails = queryETATimeToOSCC(paramsDtoList);
           if (CollectionUtils.isEmpty(etaDocumentDetails)) {
               return;
           }

           // 处理 OSCC 查询结果
           logger.info("build etaDocumentDetails result :{}", JSONObject.toJSONString(etaDocumentDetails));
           List<OutboundOrderETAResponseDto> responseList = etaDocumentDetails.stream()
                   .flatMap(etaDetail -> matchShortOrderWithETA(shortOrder, etaDetail))
                   .collect(Collectors.toList());
           logger.info("处理 OSCC 查询结果 responseList result :{}", JSONObject.toJSONString(responseList));
           if(!CollectionUtils.isEmpty(responseList)){
               Map<Long, OutboundOrderETAResponseDto> eTAResponseList = responseList.stream().collect(Collectors.toMap(OutboundOrderETAResponseDto::getPurchaseOrderDetailId, Function.identity(), (p1, p2) -> p1));
               resultData.stream().filter(item -> Objects.nonNull(item.getPurchaseOrderDetailId())).forEach(item -> {
                   OutboundOrderETAResponseDto dto = eTAResponseList.get(item.getPurchaseOrderDetailId());
                   if(null != dto){
                       BeanUtils.copyProperties(dto, item);
                   }
               });
           }
       }catch (Exception e){
           logger.info("exceOSCC IS FAIL", e);
       }
    }
    /**
     * 构建返回C端对象
     * @param outboundOrder 出库单
     * @param detailDTO 采购明细
     * @param etaTime 时间
     * @return 对象
     */
    private OutboundOrderETAResponseDto createETAResponseDto(OutboundOrderToCResponseDto outboundOrder,
                                                             PartPurchaseOrderDetaileDTO detailDTO,
                                                             String etaTime) {
        logger.info("createETAResponseDto");
        OutboundOrderETAResponseDto responseDto = new OutboundOrderETAResponseDto();

        BeanUtils.copyProperties(outboundOrder, responseDto);
        BeanUtils.copyProperties(detailDTO, responseDto);

        responseDto.setOwnerCode(outboundOrder.getOwnerCode());
        responseDto.setPurchaseOrderDetailId(outboundOrder.getPurchaseOrderDetailId());
        responseDto.setRoNo(outboundOrder.getSheetNo());
        responseDto.setPartsNo(detailDTO.getPartNo());
        responseDto.setEtaTime(etaTime);
        return responseDto;
    }

    /**
     * 构建 OSCC 请求参数
     * @param purchaseOrder 采购单
     * @return 请求对象
     */
    private OSCCRequestParamsDto createOSCCRequestParams(PartPurchaseOrderDTO purchaseOrder) {
        logger.info("createOSCCRequestParams");
        OSCCRequestParamsDto dto = new OSCCRequestParamsDto();
        dto.setOutCustomerNo(purchaseOrder.getDealerCode());
        dto.setNbCode(purchaseOrder.getPurchaseNo());
        return dto;
    }

    /**
     * 匹配 shortOrder 和 ETA 详情的辅助方法
     * @param shortOrder 缺料
     * @param etaDetail eta明细
     * @return 返回结果
     */
    private Stream<OutboundOrderETAResponseDto> matchShortOrderWithETA(List<OutboundOrderToCResponseDto> shortOrder,
                                                                       ETADocumentDetails etaDetail) {
        logger.info("matchShortOrderWithETA");
        String etaOwnerCode = etaDetail.getSalesMainDetails().getOwnerCode();
        String etaPurchaseOrder = etaDetail.getSalesMainDetails().getPurchaseOrder();
        return shortOrder.stream()
                .filter(shortOrderDto -> shortOrderDto.getPurchaseNoList() != null &&
                        shortOrderDto.getPurchaseNoList().contains(etaPurchaseOrder) &&
                        etaOwnerCode.equals(shortOrderDto.getOwnerCode()))
                .map(shortOrderDto -> {
                    OutboundOrderETAResponseDto responseDto = new OutboundOrderETAResponseDto();
                    responseDto.setOwnerCode(shortOrderDto.getOwnerCode());
                    responseDto.setRoNo(shortOrderDto.getSheetNo());
                    responseDto.setPurchaseOrderDetailId(shortOrderDto.getPurchaseOrderDetailId());
                    responseDto.setPartsNo(etaDetail.getOdoList().stream()
                            .flatMap(odo -> odo.getItemList().stream())
                            .map(OdoItemDetails::getOldSpGoodsNo)
                            .filter(oldSpGoodsNo -> oldSpGoodsNo.equals(shortOrderDto.getPartNo()))
                            .findFirst()
                            .orElse(null));
                    responseDto.setEtaTime(etaDetail.getOdoList().stream()
                            .filter(odo -> odo.getItemList().stream()
                                    .anyMatch(item -> item.getOldSpGoodsNo().equals(shortOrderDto.getPartNo())))
                            .map(OutboundOrderDto::getEtaTimeStr)
                            .findFirst()
                            .orElse(null));
                    return responseDto;
                });
    }

    /**
     * 物流节点 状态查询
     * @param requestParamsDtoList 入参为 SCOU49201676243762
     * @return 对应单据物流节点
     */
    @Override
    public List<CommonOrderTraceResponse> queryLogisticsNode(List<ToJDRequestParamsDto> requestParamsDtoList) {
        logger.info("queryLogisticsNode");
        if (ObjectUtils.isEmpty(requestParamsDtoList)) {
            return Lists.newArrayList();
        }
        List<CommonOrderTraceResponse> responses = new ArrayList<>();
        requestParamsDtoList.forEach(v -> {
            try {
                IntegratedsupplychainOrderTraceQueryV2LopRequest request = buildParams();
                CommonOrderTraceRequest requestDto = buildRequestDto(v);
                request.setRequest(requestDto);
                request.setUseJosAuth(true);
                IntegratedsupplychainOrderTraceQueryV2LopResponse response = this.init().execute(request);
                if (ObjectUtils.isEmpty(response)) {
                    return;
                }
                String responseString = new ObjectMapper().writeValueAsString(response);
                logger.info("Response for enterpriseOrderNo {}: {}", v.getEnterpriseOrderNo(), responseString);
                JSONObject jsonResponse = JSONObject.parseObject(responseString);
                if (jsonResponse == null || jsonResponse.getJSONObject("result") == null) {
                    logger.info("Failed to parse response for enterpriseOrderNo: {}", v.getEnterpriseOrderNo());
                    return;
                }
                String code = jsonResponse.getJSONObject("result").getString("code");
                if (!"1000".equals(code)) {
                    logger.info("Invalid response code {} for enterpriseOrderNo: {}", code, v.getEnterpriseOrderNo());
                    return;
                }
                CommonOrderTraceResponse data = jsonResponse.getJSONObject("result").getJSONObject("data").toJavaObject(CommonOrderTraceResponse.class);
                if (ObjectUtils.isEmpty(data)) {
                    logger.warn("No order trace found for enterpriseOrderNo: {}", v.getEnterpriseOrderNo());
                    return;
                }
                responses.add(data);
            } catch (LopException | JsonProcessingException e) {
                logger.error("Error occurred while processing enterpriseOrderNo: {}", v.getEnterpriseOrderNo(), e);
            }
        });
        return responses;
    }


    private CommonOrderTraceRequest buildRequestDto(ToJDRequestParamsDto v) {
        logger.info("buildRequestDto");
        CommonOrderTraceRequest requestDto = new CommonOrderTraceRequest();
        requestDto.setCustomerCode(jdConfig.getCustomerCode());
        requestDto.setEnterpriseOrderNo(v.getEnterpriseOrderNo());
        return requestDto;
    }

    /**
     * 同步零件状态 到 更新采购明细  更新明细中的 是否入库 零件数量
     * @param listPartBuyItemDto 采购入库 原厂弹窗入库
     */
    @Override
    public void syncPartStatus(ListPartBuyItemDto listPartBuyItemDto) {
        logger.info("syncPartStatus");
        if (ObjectUtils.isEmpty(listPartBuyItemDto) || CollectionUtils.isEmpty(listPartBuyItemDto.getDms_table())) {
            return;
        }
        try {
            CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
            String ownerCode = currentLoginInfo.getOwnerCode();
            logger.info("从登录信息补充进销商：{}", ownerCode);
            listPartBuyItemDto.setOwnerCode(ownerCode);
        } catch (Exception e) {
			logger.info("补充经销商失败：", e);
		}
        
        DmsResponse<List<SynPurchaseDetailsDto>> response = domainPartsFeign.queryPurchaseDetail(listPartBuyItemDto);
        logger.info("syncPartStatus begin requestParams:{}", JSONObject.toJSONString(response.getData()));
        partDeliveryMQSendProducer.sendEnterRecordPartDeliveryMQMsg(response, listPartBuyItemDto.getOwnerCode(), listPartBuyItemDto.getStockInNo(), listPartBuyItemDto.getDms_table(), String.valueOf(listPartBuyItemDto.getStockInType()));
        if (response.isFail() || CollectionUtils.isEmpty(response.getData()) ) {
            return;
        }
        // 更新采购明细  更新明细中的 是否入库 零件数量
        domainPurchaseFeign.syncPartStatus(response.getData());

        purchaseInStockEtaDataMateHandle(response.getData());

        initializeMissingPartsArrivalMessaging(response.getData());
    }

    private void initializeMissingPartsArrivalMessaging(List<SynPurchaseDetailsDto> synPurchaseDetailsList) {
        logger.info("initializeMissingPartsArrivalMessaging");
        try {
            // 经销商+采购单号， 查询满足 已签收 的采购单明细id。 只是当前的采购单，
            SynPurchaseDetailsDto synPurchaseDetailsDto = synPurchaseDetailsList.get(0);
            List<PartPurchaseOrderDetaileDTO> purchaseDetails = queryPurchaseDetail(synPurchaseDetailsDto.getOwnerCode(), synPurchaseDetailsDto.getPurchaseNo());
            if (CollectionUtils.isEmpty(purchaseDetails)) {
                return;
            }
            Map<Boolean, List<PartPurchaseOrderDetaileDTO>> detailMap = purchaseDetails.stream()
                    .collect(Collectors.partitioningBy(detail -> CommonConstant.MISSING_PART_STATUS_8.equals(detail.getMissingPartsStatus())));
            logger.info("give 81181005 group by initializeMissingPartsArrivalMessaging result : {}", JSONObject.toJSONString(detailMap));
            List<Long> ids = detailMap.get(true).stream()
                    .filter(detail -> CommonConstant.MISSING_PART_STATUS_8.equals(detail.getMissingPartsStatus()))
                    .map(PartPurchaseOrderDetaileDTO::getId)
                    .distinct()
                    .collect(Collectors.toList());
            List<ShortPartItemDto> shortItems = queryShortItemByDetailIds(ids);
            // 对sheetNo 去重
            Map<Pair<String, String>, ShortPartItemDto> shortItemMap = shortItems.stream()
                    .collect(Collectors.toMap(item -> Pair.of(item.getOwnerCode(),item.getSheetNo()), Function.identity(), (k, v) -> v));
            List<ToCRequestParamsDto> tocRequestParamsList = shortItemMap.keySet().stream()
                    .map(pair -> {
                        ToCRequestParamsDto paramsDto = new ToCRequestParamsDto();
                        paramsDto.setOwnerCode(pair.getLeft());
                        paramsDto.setRoNo(pair.getRight());
                        return paramsDto;
                    })
                    .collect(Collectors.toList());
            // 得到的所有缺料 （ownerCode，roNo）in （）;
            List<OutboundOrderToCResponseDto> shortPartList = this.queryPartsShortPart(tocRequestParamsList);
            Map<Pair<String, String>, List<Long>> partOwnerSheetMap = shortPartList.stream().filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(item -> Pair.of(item.getOwnerCode(), item.getSheetNo()), Collectors.mapping(OutboundOrderToCResponseDto::getPurchaseOrderDetailId, Collectors.toList())));
            logger.info("partOwnerSheetMap result : {}", JSONObject.toJSONString(partOwnerSheetMap));
            Map<Pair<String, String>, String> licenseMap = shortPartList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            item -> Pair.of(item.getOwnerCode(), item.getSheetNo()),
                            OutboundOrderToCResponseDto::getLicense,
                            (license1, license2) -> license1
                    ));
            // 所有的采购明细。
            Set<Long> purchaseOrderDetailIds = shortPartList.stream().filter(Objects::nonNull).map(OutboundOrderToCResponseDto::getPurchaseOrderDetailId).collect(Collectors.toSet());
            List<PartPurchaseOrderDetaileDTO> purchaseOrderItems = this.queryPurchaseOrderById(purchaseOrderDetailIds);
            // 这里，所有没有完成的单据
            List<Long> purchaseOrderIds = purchaseOrderItems.stream()
                    .filter(detail -> !CommonConstant.MISSING_PART_STATUS_8.equals(detail.getMissingPartsStatus())).map(PartPurchaseOrderDetaileDTO::getId).collect(Collectors.toList());
            logger.info("final result satisfy 81181005L purchaseOrderIds is:{}", JSONObject.toJSONString(purchaseOrderIds));
            partOwnerSheetMap.forEach((k1, v1) -> {
                String license = licenseMap.get(k1);
                // 获取服务顾问
                RepairOrderBatchQueryDto dto = new RepairOrderBatchQueryDto();
                dto.setOwnerCode(k1.getLeft());
                dto.setRoNo(k1.getRight());
                List<RepairOrderBatchQueryDto> repairOrderList = Collections.singletonList(dto);
                List<RepairOrderDto> orderDtos = this.queryRepairOrderInfo(repairOrderList);
                // 校验是否包含所有采购订单
                if (!v1.contains(null) && (!new HashSet<>(v1).containsAll(purchaseOrderIds) || CollectionUtils.isEmpty(purchaseOrderIds))) {
                    try {
                        buildMsgDto(k1, license, orderDtos);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            });
        } catch (Exception e) {
            logger.info("Trigger contact message remind workshop: ", e);
        }
    }

    private void buildMsgDto(Pair<String, String> pair, String licenses, List<RepairOrderDto> orderDtos) {
        RepairOrderDto orderDto = Optional.ofNullable(orderDtos)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .orElse(null);
        String vin = Optional.ofNullable(orderDto).map(RepairOrderDto::getVin).orElse(StringUtils.EMPTY);
        String serviceAdvisor = Optional.ofNullable(orderDto).map(RepairOrderDto::getServiceAdvisor).orElse(StringUtils.EMPTY);
        try {
            SceneMessageRemindDto sceneMessageRemindDto = new SceneMessageRemindDto();
            sceneMessageRemindDto.setOwnerCode(pair.getLeft());
            sceneMessageRemindDto.setSceneType(CommonConstant.MISSING_PARTS_REMIND);
            sceneMessageRemindDto.setBusinessId(pair.getRight());
            TemplateParameterDto templateDto = TemplateParameterDto.builder().license(licenses).serviceAdvisor(serviceAdvisor).vin(vin).roNo(pair.getRight()).build();
            BusinessParameterDto businessTemplate = BusinessParameterDto.builder().license(licenses).serviceAdvisor(serviceAdvisor).vin(vin).roNo(pair.getRight()).build();
            sceneMessageRemindDto.setBusinessParameter(JSONObject.parseObject(JSONObject.toJSONString(businessTemplate)));
            sceneMessageRemindDto.setTemplateParameter(templateDto);
            sceneMessageRemindDto.setExtend(businessTemplate);
            // 发送消息
            workshopMessageReminderProducer.sendOrderMsg(sceneMessageRemindDto, pair.getRight());
        } catch (Exception e) {
            logger.info("send msg fail sheet :{}, ownerCode:{}, e", pair.getRight(), pair.getLeft(), e);
        }
    }

    /**
     * 采购订单入库 触点消息
     * @param synPurchaseDetailsList
     */
	private void purchaseInStockEtaDataMateHandle(List<SynPurchaseDetailsDto> synPurchaseDetailsList) {
        logger.info("purchaseInStockEtaDataMateHandle");
		try {
	        SynPurchaseDetailsDto synPurchaseDetails = synPurchaseDetailsList.get(0);
	        List<String> partNos = synPurchaseDetailsList.stream().filter(Objects::nonNull).map(SynPurchaseDetailsDto::getPartNo).collect(Collectors.toList());
        	// 查询从采购单数据
        	DmsResponse<List<PartPurchaseOrderDetaileDTO>> queryPurchasePart = domainPurchaseFeign.queryPurchasePart(synPurchaseDetails.getOwnerCode(), synPurchaseDetails.getPurchaseNo());
        	if(Objects.isNull(queryPurchasePart) || queryPurchasePart.isFail()) {
        		return;
        	}
        	List<PartPurchaseOrderDetaileDTO> partPurchaseOrderDetaileList = queryPurchasePart.getData();
        	List<Long> ids = partPurchaseOrderDetaileList.stream().filter(Objects::nonNull).map(PartPurchaseOrderDetaileDTO::getId).collect(Collectors.toList());        	
        	//触发触点消息
        	DmsResponse<List<ShortPartDto>> shortInfoByPurchaseDetaileIds = domainMaintainOrdersFeign.queryShortInfoByPurchaseDetaileIds(ids);
        	if(Objects.isNull(shortInfoByPurchaseDetaileIds) || shortInfoByPurchaseDetaileIds.isFail()) {
        		return;
        	}
        	String sceneCode = "8";
        	List<PartPurchaseOrderDetaileDTO> data = queryPurchasePart.getData();
        	List<ShortPartDto> shortPartList = shortInfoByPurchaseDetaileIds.getData();
        	Map<String, List<ShortPartDto>> shortPartMap = shortPartList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(ShortPartDto::getPartNo));
        	data.stream().filter(Objects::nonNull).forEach(obj->dataConvertSendMsg(partNos, sceneCode, shortPartMap, obj));
		} catch (Exception e) {
			logger.info("触发触点消息: ", e);
		}
	}

	/**
	 * 数据转换处理急消息发送
	 * @param partNos
	 * @param sceneCode
	 * @param shortPartMap
	 * @param obj
	 */
	private void dataConvertSendMsg(List<String> partNos, String sceneCode,
			Map<String, List<ShortPartDto>> shortPartMap, PartPurchaseOrderDetaileDTO obj) {
        logger.info("dataConvertSendMsg");
		try {
			String partNo = StringUtils.isBlank(obj.getReplaceParts())?obj.getPartNo():obj.getReplaceParts();
			if(!partNos.contains(partNo)) {
				return;
			}
			List<ShortPartDto> list = shortPartMap.get(obj.getPartNo());
			List<Long> shortIds = list.stream().filter(Objects::nonNull).map(ShortPartDto::getShortId).collect(Collectors.toList());
			ETAOperationParamsDTO etaOperationParams = new ETAOperationParamsDTO();
			etaOperationParams.setOwnerCode(obj.getOwnerCode());
			etaOperationParams.setPartNo(obj.getPartNo());
			etaOperationParams.setPartQuantity(obj.getOrderQuantity());
			etaOperationParams.setSheetNo(obj.getLinkedNumber());
			etaOperationParams.setReplacePartNo(obj.getReplaceParts());
			etaOperationParams.setSceneCode(sceneCode);
			etaOperationParams.setShortIds(shortIds);
			etaOperationParams.setPurchaseOrderDetailId(obj.getId());
			long currentTimeMillis = System.currentTimeMillis();
			etaOperationParams.setMsgTimeStamp(currentTimeMillis);
			String id = String.join("", etaOperationParams.getOwnerCode(), etaOperationParams.getPartNo());
			transparentTocProducer.sendOrderMsg(etaOperationParams, id);
		} catch (Exception e) {
			logger.info("数据转换/消息发送失败：", e);
		}
	}

    

    private DefaultDomainApiClient init() {
        logger.info("init");
        return new DefaultDomainApiClient(jdConfig.getServerUrl(),500,15000);
    }

    private IntegratedsupplychainOrderTraceQueryV2LopRequest buildParams() {
        logger.info("buildParams");
        IntegratedsupplychainOrderTraceQueryV2LopRequest request = new IntegratedsupplychainOrderTraceQueryV2LopRequest();
        LopPlugin lopPlugin = OAuth2PluginFactory.produceLopPlugin(jdConfig.getAppKey(), jdConfig.getAppSecret(), jdConfig.getAccessToken());
        request.addLopPlugin(lopPlugin);
        return request;
    }

    /**
     * 更新零件状态block 并发送站内信
     */
    public void updatePartStatusAndSendMail() {
        logger.info("updatePartStatusAndSendMail");
        DmsResponse<List<PartPurchaseOrderDetaileDTO>> response = domainPurchaseFeign.updatePartStatus();
        if (response.isFail() || CollectionUtils.isEmpty(response.getData())) {
            return;
        }
        // 补充缺料明细工单号到dto
        List<PartPurchaseOrderDetaileDTO> partPurchaseOrderDetaileDTOList = supplementRoNo(response.getData());
        Map<String,Object> map = Maps.newHashMap();
        partPurchaseOrderDetaileDTOList.stream()
                .collect(Collectors.toMap(
                        PartPurchaseOrderDetaileDTO::getLinkedNumber,
                        dto -> dto,
                        (k1, k2) -> k1))
                .values()
                .forEach(dto -> {
                    String roNo = dto.getLinkedNumber();
                    String ownerCode = dto.getOwnerCode();
                    List<String> partList = Collections.singletonList(dto.getPartNo());
                    Long id = dto.getId();
                    sendMailNotice(id, roNo, partList, ownerCode, true, map);
                });
    }

    /**
     * 处理 无效零件的采购关联单号 用缺料工单号代替
     * @param data 采购明细
     * @return 替换过的采购明细
     */
    private List<PartPurchaseOrderDetaileDTO> supplementRoNo(List<PartPurchaseOrderDetaileDTO> data) {
        logger.info("supplementRoNo");
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        data.stream()
                .filter(roNo -> roNo.getId() != null)
                .forEach(roNo -> {
                    List<ShortPartItemDto> page = queryShortItemByDetailIds(Collections.singletonList(roNo.getId()));
                    if (CollectionUtils.isNotEmpty(page)) {
                        Optional<ShortPartItemDto> matchedItem = page.stream()
                                .filter(this::isShortPartStatusMatched)
                                .findFirst();
                        matchedItem.ifPresent(shortPartItem -> roNo.setLinkedNumber(shortPartItem.getSheetNo()));
                    }
                });
        return data;
    }

    /**
     *  零附件缺料列表
     * @param shortPartItemVo 入参对象
     * @return 返回查询数据
     */
    public Page<ShortPartItemDto> queryShortParts(ShortPartItemVo shortPartItemVo) {
        DmsResponse<Page<ShortPartItemDto>> response = domainPartsFeign.queryShortPart(shortPartItemVo);
        logger.info("queryShortParts response: {}", JSONObject.toJSONString(response));
        if (ObjectUtils.isEmpty(response)) {
            throw new ServiceBizException("feign domain parts error");
        }
        if (response.isFail() || null == response.getData()) {
            throw new ServiceBizException(response.getErrMsg());
        }
        return response.getData();
    }

    /**
     * 根据经销商和 单号凭据查询 服务顾问List
     * @param ownerCode 经销商
     * @param sheetNoList 缺料明细 中的sheetNo
     * @return 返回userIDList
     */
    public List<String> queryServiceAdvisor(String ownerCode, List<String> sheetNoList) {
        logger.info("queryServiceAdvisor is sheetNoList {}", sheetNoList);
        DmsResponse<List<String>> response = domainMaintainOrdersFeign.queryServiceAdvisorList(ownerCode, sheetNoList);
        logger.info("queryServiceAdvisor response: {}", JSONObject.toJSONString(response));
        if (response.isFail()) {
            throw new ServiceBizException("feign domain maintain orders error");
        }
        if (response.getData() == null) {
            return null;
        }
        return response.getData();
    }

    private boolean isShortPartStatusMatched(ShortPartItemDto shortPartItemDto) {
        logger.info("isShortPartStatusMatched");
        return CommonConstant.SHORT_PART_STATUS_2.equals(shortPartItemDto.getShortType())
                || CommonConstant.SHORT_PART_STATUS_8.equals(shortPartItemDto.getShortType());
    }

    /**
     * 工单号经销商查询缺料数据
     * @param toCRequestParamsDto 入参对象
     * @return 需要的数据
     */
    public List<OutboundOrderToCResponseDto> queryPartsShortPart(List<ToCRequestParamsDto> toCRequestParamsDto) {
        logger.info("queryPartsShortPart");
        DmsResponse<List<OutboundOrderToCResponseDto>> response = domainPartsFeign.toc(toCRequestParamsDto);
        logger.info("工单号经销商查询缺料数据 queryPartsShortPart result: {}", JSONObject.toJSONString(response));
        if (response.isFail() || null == response.getData()) {
            throw new ServiceBizException("feign domain maintain orders error");
        }
        return response.getData();
    }

    /**
     *
      * @param shortPartItemVo 入参
     * @return 数据集
     */
    @Override
    public IPage<ShortPartItemWeComDto> queryShortPartWeCom(ShortPartItemVo shortPartItemVo) {
       DmsResponse<Page<ShortPartItemWeComDto>> response = dmscloudReportFeign.weComQueryShortPartItem(shortPartItemVo);
       logger.info("queryShortPartWeCom response: {}", JSONObject.toJSONString(response));
       if (response.isFail() || null == response.getData()) {
           throw new ServiceBizException(response.getErrMsg());
       }
        // 转车型名称
        convertModelName(response.getData());
        return response.getData();
    }

    /**
     * 获取 已签收，部分签收，未签收的数量
     * @return 封装对象
     */
    @Override
    public MissingPartsStatusDto getShortPartStatus(ShortPartItemVo shortPartItemVo) {
        DmsResponse<List<MissingPartsStatusDto>> response = dmscloudReportFeign.weComQueryShortPartStatusCount(shortPartItemVo);
        logger.info("getShortPartStatus response: {}", JSONObject.toJSONString(response));
        if (response.isFail() || null == response.getData()) {
            throw new ServiceBizException(response.getErrMsg());
        }
        MissingPartsStatusDto summary = new MissingPartsStatusDto();
        response.getData().stream()
                .filter(Objects::nonNull)
                .forEach(dto -> {
                    switch (dto.getDeliveredCountStatus()) {
                        case "deliveredCount":
                            summary.setDeliveredCount(dto.getDeliveredCountStatusCount());
                            break;
                        case "undeliveredCount":
                            summary.setUndeliveredCount(dto.getDeliveredCountStatusCount());
                            break;
                        case "partiallyDeliveredCount":
                            summary.setPartiallyDeliveredCount(dto.getDeliveredCountStatusCount());
                            break;
                        default:
                           logger.info("exist type is :{}", dto.getDeliveredCountStatus());
                            break;
                    }
                });
        return summary;
    }

    /**
     * 根据缺料主键查询 明细
     * @param shortPartItemVo 入参
     * @return 明细数据
     */
    @Override
    public ShortPartItemDto queryShortPartItem(ShortPartItemVo shortPartItemVo) {
        if (ObjectUtils.isEmpty(shortPartItemVo) || StringUtils.isEmpty(shortPartItemVo.getOwnerCode()) || StringUtils.isEmpty(shortPartItemVo.getSheetNo())) {
            throw new ServiceBizException("缺少参数");
        }
        DmsResponse<List<ShortPartItemDto>> response = dmscloudReportFeign.weComQueryShortPartDetail(shortPartItemVo.getOwnerCode(), shortPartItemVo.getSheetNo());
        logger.info("queryShortPartItem response: {}", JSONObject.toJSONString(response));
        if (response.isFail() || null == response.getData()) {
            throw new ServiceBizException(response.getErrMsg());
        }
        this.getModelCodeList(response.getData());
        return Optional.of(response)
                .map(DmsResponse::getData)
                .filter(data -> !data.isEmpty())
                .map(data -> {
                    ShortPartItemDto firstItem = data.get(0);
                    ShortPartItemDto result = new ShortPartItemDto();
                    result.setCustomerName(firstItem.getCustomerName());
                    result.setLicense(firstItem.getLicense());
                    result.setModelName(firstItem.getModelName());
                    result.setServiceAdvisor(firstItem.getServiceAdvisor());
                    result.setEndTimeSupposed(firstItem.getEndTimeSupposed());
                    result.setPurchaseOrderDetailsDtoList(BeanMapperUtil.copyList(data, PartsStockDetailDto.class));
                    return result;
                })
                .orElseGet(ShortPartItemDto::new);
    }

    /**
     * 记录通话记录
     * @param workshopCallRecordDto 通话记录
     * @return true false
     */
    @Override
    public Boolean addCallLog(WorkshopCallRecordDto workshopCallRecordDto) {
        DmsResponse<Boolean> response = domainPartsFeign.addCallLog(workshopCallRecordDto);
        if (response.isFail()) {
            throw new ServiceBizException(response.getErrMsg());
        }
        if (response.getData()) {
            try {
                // 发送消息
                RepairOrderBatchQueryDto dto = new RepairOrderBatchQueryDto();
                dto.setOwnerCode(workshopCallRecordDto.getOwnerCode());
                dto.setRoNo(workshopCallRecordDto.getRoNo());
                List<RepairOrderBatchQueryDto> repairOrderList = Collections.singletonList(dto);
                List<RepairOrderDto> orderList = this.queryRepairOrderInfo(repairOrderList);
                if (CollectionUtils.isNotEmpty(orderList)) {
                    RepairOrderDto orderDto = orderList.get(0);
                    SceneMessageRemindDto sceneMessageRemindDto = new SceneMessageRemindDto();
                    sceneMessageRemindDto.setOwnerCode(workshopCallRecordDto.getOwnerCode());
                    sceneMessageRemindDto.setSceneType(CommonConstant.PHONE_PARTS_REMIND);
                    sceneMessageRemindDto.setBusinessId(workshopCallRecordDto.getRoNo());
                    TemplateParameterDto templateDto = TemplateParameterDto.builder().license(orderDto.getLicense()).serviceAdvisor(workshopCallRecordDto.getServiceAdvisor()).vin(orderDto.getVin()).roNo(workshopCallRecordDto.getRoNo()).build();
                    BusinessParameterDto businessTemplate = BusinessParameterDto.builder().license(orderDto.getLicense()).serviceAdvisor(workshopCallRecordDto.getServiceAdvisor()).vin(orderDto.getVin()).roNo(workshopCallRecordDto.getRoNo()).build();
                    sceneMessageRemindDto.setBusinessParameter(JSONObject.parseObject(JSONObject.toJSONString(businessTemplate)));
                    sceneMessageRemindDto.setTemplateParameter(templateDto);
                    sceneMessageRemindDto.setExtend(businessTemplate);
                    workshopMessageReminderProducer.sendOrderMsg(sceneMessageRemindDto, workshopCallRecordDto.getRoNo());
                }
            } catch (Exception e) {
                logger.info("send msg fail sheet :{}, ownerCode:{}, e", workshopCallRecordDto.getRoNo(), workshopCallRecordDto.getOwnerCode(), e);
            }
        }
        return response.getData();
    }

    /**
     * 查询通话记录
     * @param ownerCode 经销商
     * @param roNo 工单号
     * @param serviceAdvisor 服务顾问
     * @return 详情
     */
    @Override
    public Page<WorkshopCallRecordDto> queryCallItem(String ownerCode, String roNo, String serviceAdvisor, Integer pageNum, Integer pageSize) {
        DmsResponse<Page<WorkshopCallRecordDto>> response = domainPartsFeign.queryCallItem(ownerCode, roNo, serviceAdvisor, pageNum, pageSize);
        if (response.isFail()) {
            throw new ServiceBizException(response.getErrMsg());
        }
        return response.getData();
    }

    /**
     * 修改预计交车时间
     * @param roNo 工单号
     * @param endTimeSupposed 预交车时间
     */
    @Override
    public void updateRepairOrderStatus(String roNo, String endTimeSupposed) {
        // 检查登录信息
        String ownerCode = checkLoginInfo();
        DmsResponse<Void> response = domainMaintainOrdersFeign.updateRepairOrderStatus(ownerCode, roNo, endTimeSupposed);
        if (response.isFail()) {
            throw new ServiceBizException(response.getErrMsg());
        }
        dmscloudServiceFeign.endTimeSupposedMsg(ownerCode,roNo,endTimeSupposed);
    }

    /**
     * 检查当前登录信息
     * @return 登录经销商代码
     */
    private String checkLoginInfo() {
        CurrentLoginInfoDto currentLoginInfo = LoginInfoUtil.getCurrentLoginInfo();
        if (currentLoginInfo == null) {
            throw new IllegalStateException("当前登录信息为空");
        }
        String ownerCode = currentLoginInfo.getOwnerCode();
        if (ownerCode == null) {
            throw new IllegalStateException("当前登录用户的 ownerCode 为空");
        }
        return ownerCode;
    }

    /**
     * try 因为接口文档入参 modelCode 入参为int 返回String
     * @param shortPartItems 缺料mqp
     * @return 所有的modelIds
     */
    private List<Long> getModelCodes(IPage<ShortPartItemWeComDto> shortPartItems) {
        return shortPartItems.getRecords().stream()
                .map(ShortPartItemWeComDto::getModelCode)
                .filter(ObjectUtils::isNotEmpty)
                .map(code -> {
                    try {
                        return Long.parseLong(code);
                    } catch (NumberFormatException e) {
                        logger.info("getModelCodes parseLong error: {},{}", code,e);
                        return null;
                    }
                })
                .filter(code -> code != null)
                .distinct()
                .collect(Collectors.toList());
    }


    /**
     * 讲modelCode 转换为 中文名称
     * @param shortPartItems 缺料map 基础数据源
     */
    private void convertModelName(IPage<ShortPartItemWeComDto> shortPartItems) {
        List<Long> modelCodes = getModelCodes(shortPartItems);
        if (CollectionUtils.isEmpty(modelCodes)) {
            logger.info("convertModelName params is empty");
            return;
        }
        // 调用 queryModelNameByIds 方法获取中文名称
        List<ModelInfoDto> modelInfoList = queryModelNameByIds(modelCodes);
        Map<String, String> modelCodeNameMap = modelInfoList.stream()
                .collect(Collectors.toMap(ModelInfoDto::getModelId, ModelInfoDto::getModelName));
        shortPartItems.getRecords().forEach(shortPartItemDto -> {
            String modelCode = shortPartItemDto.getModelCode();
            if (modelCode != null && modelCodeNameMap.containsKey(modelCode)) {
                shortPartItemDto.setModelName(modelCodeNameMap.get(modelCode));
            }
        });
    }

    /**
     * 查询工单明细 条件 where （owner_code，ro_no） in （）
     * @param ownerCodeAndRoNoList 经销商和工单号集合
     * @return 返回数据集
     */
    private List<RepairOrderDto> queryRepairOrderInfo(List<RepairOrderBatchQueryDto> ownerCodeAndRoNoList) {
        DmsResponse<List<RepairOrderDto>> response = domainMaintainOrdersFeign.queryRepairOrderInfo(ownerCodeAndRoNoList);
        logger.info("queryRepairOrderInfo response: {}", JSONObject.toJSONString(response));
        if (response.isFail() || null == response.getData()) {
            throw new ServiceBizException("feign domain maintain orders error");
        }
        return response.getData();
    }


    private List<ModelInfoDto> queryModelNameByIds(List<Long> ids) {
        ModelIdDto modelDto = new ModelIdDto();
        modelDto.setModeIds(ids);
        ResponseDto<List<ModelInfoDto>> response = midEndBasicdataCenterFeign.queryModelNameById(modelDto);
        logger.info("queryModelNameByIds response: {}", JSONObject.toJSONString(response));
        if (response.isFail() || null == response.getData()) {
            throw new ServiceBizException(response.getReturnMessage());
        }
        return response.getData();
    }

    /**
     * 经销商 采购单 查询 明细数据
     * @param ownerCode 经销商
     * @param purchaseOrder 采购单
     * @return 采购明细数据
     */
    private List<PartPurchaseOrderDetaileDTO> queryPurchaseDetail(String ownerCode, String purchaseOrder) {
        DmsResponse<List<PartPurchaseOrderDetaileDTO>> purchaseOrderDetailsResponse = domainPurchaseFeign.queryPurchasePart(ownerCode, purchaseOrder);
        logger.info("queryPurchaseDetail response: {}", JSONObject.toJSONString(purchaseOrderDetailsResponse));
        if (purchaseOrderDetailsResponse.isFail() || null == purchaseOrderDetailsResponse.getData()) {
            throw new ServiceBizException("feign domain purchase error");
        }
        return purchaseOrderDetailsResponse.getData();
    }

    /**
     * 根据采购明细id 查询缺料明细
     * @param ids 采购明细id
     * @return 缺料明细数据
     */
    private List<ShortPartItemDto> queryShortItemByDetailIds(List<Long> ids) {
        logger.info("queryShortItemByDetailIds request params is :{}", ids);
        DmsResponse<List<ShortPartItemDto>> response = domainPartsFeign.queryShortPartItem(ids);
        logger.info("queryShortItemByDetailIds response: {}", JSONObject.toJSONString(response));
        if (response.isFail() || null == response.getData()) {
            throw new ServiceBizException("feign domain parts error");
        }
        return response.getData();
    }

    /**
     * 查询预约单（weCom）
     * @param bookingOrderVo 查询参数
     * @return 分页集
     */
    @Override
    public IPage<BookingOrderDto> queryBookingWeCom(BookingOrderVo bookingOrderVo) {
        DmsResponse<Page<BookingOrderDto>> response = dmscloudReportFeign.queryBookingWeCom(bookingOrderVo);
        validateFeignResponse(response);
        return response.getData();
    }

    @Override
    public BookingStatusDto queryBookingStatus(BookingOrderVo bookingOrderVo) {
        DmsResponse<BookingOrderParamDto> bookingResponse = dmscloudReportFeign.queryBookingOrderCount(bookingOrderVo);
        validateFeignResponse(bookingResponse);
        BookingOrderParamDto bookingInfo = bookingResponse.getData();
        return BookingStatusDto.builder()
                .bookingArrived(bookingInfo.getBookingArrived())
                .bookingNotArrived(bookingInfo.getBookingNotArrived())
                .bookingAll(bookingInfo.getBookingAll())
                .build();
    }

    private <T> void validateFeignResponse(DmsResponse<T> response) {
        logger.info("validateFeignResponse response: {}", JSONObject.toJSONString(response));
        if (response.isFail() || response.getData() == null) {
            throw new ServiceBizException(response.getErrMsg());
        }
    }

    private void getModelCodeList(List<ShortPartItemDto> shortPartItemDtoList) {
        List<Long> modelIds = shortPartItemDtoList.stream()
                .map(ShortPartItemDto::getModelCode)
                .filter(ObjectUtils::isNotEmpty)
                .map(code -> {
                    try {
                        return Long.parseLong(code);
                    } catch (NumberFormatException e) {
                        throw new IllegalStateException("无法将 modelCode 转换为 Long: " + code, e);
                    }
                })
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(modelIds)) {
            logger.info("getModelCodeList detail params is empty");
            return;
        }
        List<ModelInfoDto> modelInfoList = queryModelNameByIds(modelIds);
        Map<String, String> modelCodeNameMap = modelInfoList.stream()
                .collect(Collectors.toMap(ModelInfoDto::getModelId, ModelInfoDto::getModelName));
        shortPartItemDtoList.forEach(shortPartItemDto -> {
            String modelCode = shortPartItemDto.getModelCode();
            if (modelCode != null && modelCodeNameMap.containsKey(modelCode)) {
                shortPartItemDto.setModelName(modelCodeNameMap.get(modelCode));
            }
        });
    }

    /**
     * 记得传经销商
     * 获取所有当日的数据量
     * @return 构建的对象
     */
    @SneakyThrows
    @Override
    public BadgeCountSummaryDto buildPartStatusCount(String ownerCode) {
        String todayMin = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String todayMax = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        VehicleEntranceParamsVo vo = new VehicleEntranceParamsVo();
        vo.setEntryTimeBegin(todayMin);
        vo.setEntryTimeEnd(todayMax);
        vo.setDealerCode(ownerCode);
        // 获取分拨
        CompletableFuture<VehicleEntranceCountDto> responseFuture = CompletableFuture.supplyAsync(() -> {
            DmsResponse<VehicleEntranceCountDto> response = dmscloudReportFeign.queryAllocatedCount(vo);
            return response.getData();
        });

        // 预约
        BookingOrderVo orderVo = new BookingOrderVo();
        orderVo.setOwnerCode(ownerCode);
        orderVo.setBookingComeTimeBegin(todayMin);
        orderVo.setBookingComeTimeEnd(todayMax);
        CompletableFuture<BookingStatusDto> bookingStatusDtoFuture = CompletableFuture.supplyAsync(() -> {
            BookingStatusDto bookingStatusDto = this.queryBookingStatus(orderVo);
            return bookingStatusDto;
        });

        //缺件
        ShortPartItemVo itemVo = new ShortPartItemVo();
        itemVo.setOwnerCode(ownerCode);
        CompletableFuture<MissingPartsStatusDto> shortPartStatusDtoFuture = CompletableFuture.supplyAsync(() -> {
            MissingPartsStatusDto shortPartStatus = this.getShortPartStatus(itemVo);
            return shortPartStatus;
        });
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(responseFuture, bookingStatusDtoFuture,shortPartStatusDtoFuture);
        combinedFuture.join();

        return BadgeCountSummaryDto.builder()
                .vehicleEntranceCount(responseFuture.get())
                .bookingStatus(bookingStatusDtoFuture.get())
                .missingPartsStatus(shortPartStatusDtoFuture.get())
                .build();
    }
}
