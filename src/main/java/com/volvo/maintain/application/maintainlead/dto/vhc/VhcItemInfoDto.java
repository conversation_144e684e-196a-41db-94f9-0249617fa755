package com.volvo.maintain.application.maintainlead.dto.vhc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description VHC车辆健康检查小类信息
 * @Date 2024/9/26 17:14
 */
@Data
@ApiModel("VHC车辆健康检查小类信息")
public class VhcItemInfoDto {

    @ApiModelProperty("小类id")
    private Integer id;

    @ApiModelProperty("vhc单号")
    private String vhcNo;

    @ApiModelProperty("检查项名称")
    private String itemName;

    @ApiModelProperty("检查项大类id")
    private String itemClassId;

    @ApiModelProperty("检查项备注")
    private String itemContent;

    @ApiModelProperty("检查项照片")
    private String itemPhotograph;

    @ApiModelProperty("检查项数值")
    private String itemNumerical;

    @ApiModelProperty("检查项故障现象代码")
    private String itemMalfunctionCode;

    @ApiModelProperty("检查项故障现象")
    private String itemMalfunction;

    @ApiModelProperty("检查详细备注")
    private String itemTxt;

    @ApiModelProperty("配置表配置项id")
    private String configItemId;

    @ApiModelProperty("预计服务时长")
    private String workDate;

    @ApiModelProperty("确认状态")
    private String confirmState;

    @ApiModelProperty("不修原因反馈")
    private String noRepairCause;

    @ApiModelProperty("维修结果")
    private String classResult;

    @ApiModelProperty("不修原因反馈是否有效（10011001有效，10011002无效）")
    private String noRepairCauseValid;

    @ApiModelProperty("不修原因来源渠道（b端，c端）")
    private String noRepairCauseChannel;

    @ApiModelProperty("是否删除")
    private Integer isDeleted;
}
