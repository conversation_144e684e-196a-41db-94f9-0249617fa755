package com.volvo.maintain.application.maintainlead.dto;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <p>
 * 零附件主数据
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */

@ApiModel(value = "零附件主数据")
@Data
public class PartsInfoDTO {

    /**
     * 自增序列
     */
    private Long partsId;

    /**
     * 零件号
     */
    private String partsNo;
    
    /**
     * 零件list
     */
    private List<String> partsNos;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 零件名称
     */
    private String partsName;

    /**
     * 零件英文名称
     */
    private String partsEname;

    /**
     * AGE Code
     */
    private String ageCode;

    /**
     * CLS12成本价
     */
    private BigDecimal cls12;

    /**
     * CLS4成本价
     */
    private BigDecimal cls4;

    /**
     * 折扣码
     */
    private String discountCode;

    /**
     * 功能码
     */
    private String functionCode;

    /**
     * 采购束包装
     */
    private BigDecimal purchasePackage;

    /**
     * 国别码
     */
    private String countryCode;

    /**
     * 产品类别
     */
    private String productCode;

    /**
     * 销售价
     */
    private BigDecimal unitPrice;

    /**
     * ABC
     */
    private String abcCode;

    /**
     * 被取代码
     */
    private String replacedCode;

    /**
     * 取代码
     */
    private String replaceCode;

    /**
     * EUC
     */
    private String euc;

    /**
     * 配件分类
     */
    private String ddls;

    /**
     * 库存
     */
    private BigDecimal stockQty;

    /**
     * weight
     */
    private String weight;

    /**
     * volume
     */
    private BigDecimal volume;

    /**
     * unit
     */
    private String unit;

    /**
     * BSPR_code
     */
    private String bsprCode;

    /**
     * PAG_price
     */
    private BigDecimal pagPrice;

    /**
     * 保修成本
     */
    private BigDecimal warCost;

    /**
     * 是否原厂件
     */
    private String partsKind;

    /**
     * CCC
     */
    private String cccFlag;

    /**
     * DFS
     */
    private String dfsFlag;

    /**
     * SurCharge
     */
    private String surchargehge;

    /**
     * define1
     */
    private String define1;

    /**
     * define2
     */
    private BigDecimal define2;

    /**
     * min_order_qty
     */
    private BigDecimal minOrderQty;

    /**
     * 是否删除，1：删除，0：未删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateBy;

    private String binding;

    /**
     * vat 税率
     */
    private Double vat;

    /**
     * 返利类型
     */
    private Integer rebateType;

    /**
     * 返利比例
     */
    private Integer rebatePercent;

    /**
     * 是否可辅料批量出库
     */
    private String isSubmaterialOut;

    /**
     * tax_code
     */
    private String taxCode;

    /**
     * Unit_of_measure
     */
    private String unitOfMeasure;

    /**
     * Supplier_number
     */
    private String supplierNumber;

    /**
     * passive
     */
    private String passive;

    /**
     * Sales_blocking_code
     */
    private String salesBlockingCode;

    /**
     * DDLS_code
     */
    private String ddlsCode;

    /**
     * replacing
     */
    private String replacing;

    /**
     * replaced
     */
    private String replaced;


    /**
     * 产品类别 名称
     */
    private String productName;


    /**
     * 功能码 名称
     */
    private String functionName;

    /**
     * 采购中数量
     */
    private Double purchaseQuantity;

    /**
     * 原厂库账面库存
     */
    private Double oemstockQuantity;

    /**
     * 外卖库账面库存
     */
    private Double takeawayStockQuantity;

    /**
     * 原厂库可用库存
     */
    private Double oemUsableStock;

    /**
     * 外卖库可用库存
     */
    private Double takeawayUsableStock;

    /**
     * 在途数量
     */
    private Double deliveryQuantity;

    /**
     * 导入的采购配件数量
     */
    private Double orderQuantity;

    /**
     * 采购订单类型
     */
    private String orderLevel;


    /**
     * 需求日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;


    /**
     * 页面类型 1：原采购单 2：动力电池
     */
    private int pageType;

    private String ownerCode;

    /**
     * 精品件图片url
     */
    private String imageUrl;
}
