package com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop;

import lombok.Data;

import java.util.List;

@Data
public class QueryParamDto {
    //经销商code
    private String ownerCode;
    //车牌号
    private String license;
    //vin
    private String vin;
    //工单号
    private String roNo;
    //是否质检
    private String completeTag;
    //工单类型
    private String repairTypeCode;
    //服务顾问
    private String serviceAdvisor;
    //标签
    private String label;
    //工单类型
    private List<String>repairTypeCodeList;
    //服务顾问
    private List<String> serviceAdvisorList;
    //开单时间 开始
    private String beginRoCreateDate;
    //开单时间 结束
    private String endRoCreateDate;
    //预交车时间 开始
    private String beginEndTimeSupposed;
    //预交车时间 结束
    private String endEndTimeSupposed;
    //派工状态
    private String assignStatus;
    //分页参数 页数
    private Long pageNum;
    //分页参数 行数
    private Long pageSize;
    //派工
    private String isPunch;
    //交车状态
    private String deliveryTag;
    //标签list
    private List<String> labelList;
    //查询车牌号类型
    private String businessType;

}
