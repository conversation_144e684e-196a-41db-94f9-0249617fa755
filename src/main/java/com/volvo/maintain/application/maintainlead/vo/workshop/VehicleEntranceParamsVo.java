package com.volvo.maintain.application.maintainlead.vo.workshop;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * tt_vehicle_entrance
 * 
 * <AUTHOR>
 * @version 1.0.0 2023-04-17
 */
@Data
public class VehicleEntranceParamsVo implements Serializable {
	
    private static final long serialVersionUID = -8739684651915893874L;

    /** 类型 */
    private String type;
	
    /** 主键 */
    private Integer id;

    /** 进场车辆上报流水id */
    private String entranceId;

    /** 车牌号 */
    private String licensePlate;

    /** 经销商code */
    private String dealerCode;

    /** 进场时间 */
    @JsonFormat(pattern="yyyy/MM/dd HH:mm:ss",timezone="GMT+8")
    private Date entryTime;

    /** 置顶状态(0-非置顶，1-置顶) */
    private Integer topStatus;

    /** 接待状态(待接待:0、接待中:1、已建单:2) */
    private Integer receiveStatus;

    /** 接待人 */
    private Integer receiveId;

    /** 接待时间 */
    @JsonFormat(pattern="yyyy/MM/dd HH:mm:ss",timezone="GMT+8")
    private Date receiveTime;

    /** 预约单号 */
    private String appointmentId;

    /** 预约时间 */
    private Date appointmentTime;

    /** 环检单号 */
    private String esrsId;

    /** 工单号 */
    private String workOrderId;
    
    /** 是否存在进行中的订单（不存在-0，存在-1） */
    private Integer isExistOrder;

    /** 特殊车辆标识（非特殊车辆-0，特殊车辆-1） */
    private Integer isSpecialVehicle;

    /** 车架号 */
    private String vin;

    /** 是否有效（0-有效，1-无效） */
    private Integer isEffective;
    
    /** 是否进场15分钟内 */
    private Boolean limitedTime;

    //邀约类型
    private Integer inviteType;

    //待授权
    private String authorization;

    //是否vip
    private boolean vip;

    private Integer currentPage;

    private Integer pageSize;

    // 进场时间查询条件
    private String entryTimeBegin;

    // 进场时间查询条件
    private String entryTimeEnd;

    private String entryTimeStr;

    /**
     * 分拨类型 （Unallocated，allocated，allCount）
     */
    private String allocatedType;

    /**
     * 筛选条件，分拨类型，
     */
    private List<Integer> receiveStatusList;

    /**
     * 分拨方式
     */
    private String serviceSourcesType;

    // 分拨方式
    private List<Integer> serviceSources;

    /**
     * 分拨类型
     */
    private String serviceTypesType;

    // 分拨类型
    private List<Integer> serviceTypes;

    private String serviceSource;

    private String serviceType;

    /** 接待人 */
    private String receiveName;




}