package com.volvo.maintain.infrastructure.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 京东相关配置
 */
@Component
@Data
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "default.jd")
@RefreshScope
public class JdConfig {
    /**
     * 服务地址
     */
    private String serverUrl;
    /**
     * 鉴权token
     */
    private String accessToken;
    /**
     * 应用标识
     */
    private String appKey;
    /**
     * 应用密钥
     */
    private String appSecret;

    private String customerCode;
}
