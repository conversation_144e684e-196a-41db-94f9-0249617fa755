package com.volvo.maintain.infrastructure.config;


import java.util.Enumeration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.google.common.collect.Maps;
import com.volvo.interceptor.LoginInfoThreadLocal;

import lombok.extern.slf4j.Slf4j;

@Configuration
@EnableAsync
public class ThreadPoolBeanConfig {
    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolBeanConfig.class);

    private final Integer coreSize = Runtime.getRuntime().availableProcessors() + 1;
    private final Integer maxSize = Runtime.getRuntime().availableProcessors() * 2;

    private Integer aliveTime = 0;

    @Value("${framework.threadPool.capacity:500}")
    private Integer capacity;

    @Value("${framework.threadPool.aliveTime:60}")
    private Integer aliveTimeNew;

    @Value("${framework.threadPool.ioCoreSize:10}")
    private Integer ioCoreSize;

    @Value("${framework.threadPool.ioMaxSize:60}")
    private Integer ioMaxSize;
    
    private static final Integer corePoolSize = 20;
    private static final Integer maxPoolSize = 50;
    private static final Integer queueCapacity = 500;

    @Bean("thread360PoolNew")
    public ThreadPoolTaskExecutor thread360PoolNew() {
        logger.info("队列长度:{}",capacity);
        ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
        pool.setCorePoolSize(ioCoreSize);
        pool.setMaxPoolSize(ioMaxSize);
        pool.setKeepAliveSeconds(aliveTimeNew);
        pool.setQueueCapacity(capacity);
        pool.setTaskDecorator(new AsyncTaskDecorator());
        // 手动设置饱和策略
        pool.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        logger.info("thread360PoolNew init,ioCoreSize:{},ioMaxSize:{},aliveTimeNew:{},capacity:{}",ioCoreSize,ioMaxSize,aliveTimeNew,capacity);
        return pool;
    }

    @Bean("thread360Pool")
    public ThreadPoolTaskExecutor threadPoolExecutor() {
        logger.info("队列长度:{}",capacity);
        ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
        pool.setCorePoolSize(coreSize);
        pool.setMaxPoolSize(maxSize);
        pool.setKeepAliveSeconds(aliveTime);
        pool.setQueueCapacity(capacity);
        //设置线程包装处理登陆信息请求头等
        pool.setTaskDecorator(new AsyncTaskDecorator());
        // 手动设置饱和策略
        pool.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        return pool;
    }
    
    @Bean("threadEBao")
    public ThreadPoolTaskExecutor threadEBaoExecutor() {
        logger.info("队列长度:{}",capacity);
        ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
        pool.setCorePoolSize(coreSize);
        pool.setMaxPoolSize(maxSize);
        pool.setKeepAliveSeconds(aliveTime);
        pool.setQueueCapacity(capacity);
        //设置线程包装处理登陆信息请求头等
        pool.setTaskDecorator(new AsyncTaskDecorator());
        // 手动设置饱和策略
        pool.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        return pool;
    }
    
    @Bean("threadSignQuantity")
    public ThreadPoolTaskExecutor threadSignQuantityPool() {
        logger.info("队列长度:{}",capacity);
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(maxPoolSize);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setKeepAliveSeconds(aliveTime);
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        return taskExecutor;
    }
}

@Slf4j
class AsyncTaskDecorator implements TaskDecorator {


    @Override
    public Runnable decorate(Runnable runnable) {
        try {
            //设置线程attributes
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            RequestContextHolder.setRequestAttributes(attributes, true);
            Map<String,String> header = Maps.newHashMap();
            if(Objects.nonNull(attributes)){
                //resuest为空情况下补偿header
                HttpServletRequest request = attributes.getRequest();
                Enumeration<String> headerNames = request.getHeaderNames();
                while (headerNames.hasMoreElements()) {
                    String key = headerNames.nextElement();
                    header.put(key, request.getHeader(key));
                }
            }

            return () -> {
                //传递登录信息
                try {
                    LoginInfoThreadLocal.HEADER_INFO.set(header);
                    runnable.run();
                } finally {
                    //执行结束移除登录信息
                    LoginInfoThreadLocal.HEADER_INFO.remove();
                    RequestContextHolder.resetRequestAttributes();
                }
            };
        } catch (IllegalStateException e) {
            return runnable;
        }
    }
}