package com.volvo.maintain.infrastructure.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "oscc")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OSCCProperties {

    /**
    *域名
     * **/
    private String baseUrl;
    /*
    * 零件退货接口
    * */
    private String path;
    
    /*
    * 经销商库存查询接口
    * */
    private String dealerStock;

    /*
    *租户  固定值 不同环境不同
    * */
    private String tenantId;
    /*
    * 企业编码 固定值 不同环境不同
    * */
    private String enterpriseNo;

    /*退货完成上传路径*/
    private String pin;

    private String ownerNo;

    private String channelNo;

    private String appKey;

    private String domain;

    private String appSecret;

    private Integer unThrowError;
}
