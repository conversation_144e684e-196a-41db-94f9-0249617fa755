package com.volvo.maintain.infrastructure.config;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.trace.AsyncTraceDispatcher;
import org.apache.rocketmq.client.trace.TraceDispatcher;
import org.apache.rocketmq.client.trace.hook.SendMessageTraceHookImpl;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RocketMQConfig {
	
	@Value("${rocketmq.tocTransparent.name-server}")
    private String nameServer;

    @Value("${rocketmq.tocTransparent.producer.group}")
    private String producerGroup;
    
    @Value("${rocketmq.tocTransparent.producer.access-key}")
    private String accessKey;

    @Value("${rocketmq.tocTransparent.producer.secret-key}")
    private String secretKey;
    
    @Value("${rocketmq.tocTransparent.producer.enable-msg-trace}")
    private boolean enableMsgTrace;

    @Value("${rocketmq.tocTransparent.producer.tls-enable}")
    private boolean tlsEnable;
    
    
	@Bean(name = "tocTransparent")
    @ConditionalOnProperty(prefix = "rocketmq.tocTransparent", name = "name-server")
    RocketMQTemplate rocketMQTemplate1() {
		// 配置 RPCHook
        AclClientRPCHook rpcHook = new AclClientRPCHook(new SessionCredentials(accessKey, secretKey));

        // 使用 RPCHook 创建生产者
        DefaultMQProducer producer = new DefaultMQProducer(producerGroup, rpcHook);

        // 配置消息轨迹
        if (enableMsgTrace) {
            AsyncTraceDispatcher traceDispatcher = new AsyncTraceDispatcher(producerGroup, TraceDispatcher.Type.PRODUCE, "RMQ_SYS_TRACE_TOPIC", rpcHook);
            producer.getDefaultMQProducerImpl().registerSendMessageHook(new SendMessageTraceHookImpl(traceDispatcher));

        }
        // 配置TLS
        if (tlsEnable) {
            System.setProperty("rocketmq.client.tls.enable", "true");
        } else {
            System.setProperty("rocketmq.client.tls.enable", "false");
        }
        producer.setNamesrvAddr(nameServer);
        
		RocketMQTemplate rocketMQTemplate = new RocketMQTemplate();
		rocketMQTemplate.setProducer(producer);
        return rocketMQTemplate;
    }
}
