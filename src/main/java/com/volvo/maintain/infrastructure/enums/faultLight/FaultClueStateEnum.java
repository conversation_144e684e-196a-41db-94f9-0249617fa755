package com.volvo.maintain.infrastructure.enums.faultLight;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**线索状态枚举类*/
@Getter
@AllArgsConstructor
public enum FaultClueStateEnum {
    WAITING_FOR_CONTACT(10541001,"待联络"),
    WAITING_FOR_APPOINTMENT(10541002,"待预约"),
    WAITING_ENTER_STORE(10541003,"待进店"),
    ENTERED_STORE(10541004,"已进店"),
    WAITING_FOR_VERIFICATION(10541005,"待验证"),
    WAITING_FOR_CONFIRMATION(10541006,"待确认"),
    ALREADY_COMPLETED(10541007,"已完成"),
    ENTERING_STORE_NATURALLY(10541008,"自然进店"),
    CLOSE_CLUES(10541009,"已关闭");

    private final Integer code;
    private final String name;

    public static String getNameByCode(Integer code){
        for(FaultClueStateEnum nodeEnum : FaultClueStateEnum.values()){
            if (nodeEnum.code.equals(code)){
                return nodeEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name){
        for(FaultClueStateEnum nodeEnum : FaultClueStateEnum.values()){
            if (StringUtils.equals(nodeEnum.name, name)){
                return nodeEnum.code;
            }
        }
        return null;
    }

    private static Map<Integer, FaultClueStateEnum> map = Arrays.stream(FaultClueStateEnum.values()).collect(Collectors.toMap(FaultClueStateEnum::getCode, e -> e));
    private static Map<Integer,String> valueMap = Arrays.stream(FaultClueStateEnum.values()).collect(Collectors.toMap(FaultClueStateEnum::getCode, e -> e.getName()));
}
