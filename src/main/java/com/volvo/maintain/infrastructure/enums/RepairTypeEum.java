package com.volvo.maintain.infrastructure.enums;

/**
 * 维修工单类型
 */
public enum RepairTypeEum {

    /**
     * 保修
     */
    G_TYPE("保修", "G"),
    /**
     * 事故
     */
    I_TYPE("事故", "I"),
    /**
     * 保养
     */
    M_TYPE("保养", "M"),

    /**
     * 机电维修
     */
    N_TYPE("机电维修", "N"),
    /**
     * PDS
     */
    P_TYPE("PDS", "P"),
    /**
     * 零售
     */
    S_TYPE("零售", "S"),

    ;

    /**
     * 初始化默认设置属性
     */
    private final String code;
    /**
     * 初始化默认设置属性
     */
    private final String value;

    RepairTypeEum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }


    /**
     * 根据value获取去code
     *
     * @param value 11
     * @return
     */
    public static String getCodeByValue(String value) {
        for (RepairTypeEum saleModel : RepairTypeEum.values()) {
            if (value.equals(saleModel.getValue())) {
                return saleModel.getCode();
            }
        }
        return null;
    }


    /**
     * 根据维修类型代码，获取维修类型
     */
    public static String getValueByCode(String code) {
        for (RepairTypeEum saleModel : RepairTypeEum.values()) {
            if (code.equals(saleModel.getCode())) {
                return saleModel.getValue();
            }
        }
        return null;
    }
}
