package com.volvo.maintain.infrastructure.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Getter
public enum RenewalClueJudgmentBasis{

    LAST_YEAR_COMMERCIAL_INSURANCE(96211001, "最近一年商业险"),
    LAST_POLICY(96211002, "最近一次保单"),
    SALES_DATE(96211003, "销售日期");


    private static final Map<String, RenewalClueJudgmentBasis> lookup = new HashMap<>();

    static {
        for (RenewalClueJudgmentBasis e : RenewalClueJudgmentBasis.values()) {
            lookup.put(e.getDesc(), e);
        }
    }

    private final int code;
    private final String desc;

    RenewalClueJudgmentBasis(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer getByDesc(String desc) {
        RenewalClueJudgmentBasis renewalClueJudgmentBasis = lookup.get(desc);
        return Objects.nonNull(renewalClueJudgmentBasis) ? renewalClueJudgmentBasis.code : null;
    }
}
