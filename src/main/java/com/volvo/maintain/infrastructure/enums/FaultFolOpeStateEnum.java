package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**操作状态枚举类*/
@Getter
@AllArgsConstructor
public enum FaultFolOpeStateEnum {

    APPOINTMENT_FAILED(10541002,10551012,"预约失败",0),
    WAITING_ENTER_STORE(10541002,10551003,"预约成功",1),
    SECOND_APPOINTMENT(10541002,10551022,"二次预约",1),
    APPOINTMENT_FAILED_VEHICLE_NO_ALARM(10541002,10551018,"失败-车辆无报警",1),
    APPOINTMENT_FAILED_ALARM_NOT_REPRODUCED(10541002,10551019,"失败-报警未再现",1),
    APPOINTMENT_FAILED_CUSTOMER_REASONS(10541002,10551020,"失败-客户原因",1),
    APPOINTMENT_FAILED_VEHICLE_PROCESSED(10541002,10551017,"失败-车辆已处理",1),
    APPOINTMENT_FAILED_OTHER(10541002,10551021,"失败-其他",1),
    APPOINTMENT_FAILED_SECONDARY_FAILURE_TO_CONNECT(10541002,10551016,"失败-二次未接通",1),
    WAITING_FOR_APPOINTMENT(10541003,9998,"待进店",1),

    ENTERED_STORE(10541003,10551005,"已进店",1),

    NO_ASSOCIATED_WORK_ORDER(null,10551004,"未关联工单",1),

    ENTERED_STORE_NOW(10541004,9999,"已进店",1),
    ENTERED_MISSING_PARTS(10541004,10551006,"缺零件",1),
    ALREADY_COMPLETED(10541004,10551007,"已交车",1),
    WARRANTY_EXTENSION(10541004,10551023,"保修/延保",1),

    OTHER(10541004,10551024,"其他",1),
    NOT_REPAIR(10541004,10551014,"车主不修",1);
   /*
   待预约    预约失败、预约成功(必须填预约时间)
   待进店	已进店(必须关联工单)
   已进店	未关联工单(必须关联工单) ,缺零件、已交车 、车主不修
   已进店	缺零件 、已交车 、车主不修
   */

    private final Integer parentCode;
    private final Integer code;
    private final String name;
    private final Integer state;

    private static final Map<Integer, FaultFolOpeStateEnum> map = Arrays.stream(FaultFolOpeStateEnum.values()).collect(Collectors.toMap(FaultFolOpeStateEnum::getCode, e -> e));
    private static final Map<Integer,String> valueMap = Arrays.stream(FaultFolOpeStateEnum.values()).collect(Collectors.toMap(FaultFolOpeStateEnum::getCode, FaultFolOpeStateEnum::getName));
}
