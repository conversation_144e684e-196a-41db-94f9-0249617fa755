package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description 客户画像一级板块
 *
 * <AUTHOR>
 * @date 2024/01/17 14:44
 */
@Getter
@AllArgsConstructor
public enum FirstPlateEnum {
    FIRST_IDENTITY_ID(35221001, "身份ID板块"),
    FIRST_HUMAN_VEHICLE(35221002, "人车关系板块");

    private final Integer code;
    private final String name;
    public static Integer getCodeByName(String name) {
        for (FirstPlateEnum clueType : FirstPlateEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        for (FirstPlateEnum clueType : FirstPlateEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }
}
