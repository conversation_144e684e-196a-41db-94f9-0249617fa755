package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
/**
 * 功能描述：导出字典翻译
 *
 * <AUTHOR>
 * @since 2024/01/15
 */
@Getter
@AllArgsConstructor
public enum FullLeadExportDictEnum {

    /**
     * 邀约类型
     */
    INVITE_TYPE_FIRST(82381001, "首保"),
    INVITE_TYPE_REGULAR(82381002, "定保"),
    INVITE_TYPE_INSURE(82381003, "续保"),
    INVITE_TYPE_AC(82381004, "VOC事故"),
    INVITE_TYPE_LOSS(82381006, "流失客户"),
    INVITE_TYPE_CALL(82381007, "召回"),
    INVITE_TYPE_ACTIVITY(82381008, "服务活动"),
    INVITE_TYPE_MAINTENANCE(82381009, "保修"),
    INVITE_TYPE_DEALER(82381010, "店端自建"),
    INVITE_TYPE_OEM(82381011, "厂端自建"),
    INVITE_TYPE_WARRING(82381012, "流失预警"),
    INVITE_TYPE_PART(82381013, "商城零附件"),
    INVITE_TYPE_FL(82381014, "故障灯"),

    /**
     * 续保客户类型
     */
    INSURANCE_TYPE_1(81761001, "新保客户"),
    INSURANCE_TYPE_2(81761002, "新转续"),
    INSURANCE_TYPE_3(81761003, "续转续"),
    INSURANCE_TYPE_4(81761004, "在修不在保"),
    INSURANCE_TYPE_5(81761005, "不在修不在保"),

    /**
     * 邀约跟进状态
     */
    FOLLOW_STATUS_FOLLOW_PENDING(82401001, "未跟进"),
    FOLLOW_STATUS_FOLLOW_SUCCESS(82401002, "跟进成功"),
    FOLLOW_STATUS_FOLLOW_FAIL(82401003, "跟进失败"),
    FOLLOW_STATUS_FOLLOW_UP(82401004, "继续跟进"),
    FOLLOW_STATUS_FOLLOW_NOT(82401005, "不需跟进"),
    FOLLOW_STATUS_FOLLOW_OUT(82401006, "逾期未跟进"),

    /**
     * 故障灯跟进状态
     */
    FL_STATUS_1(10551001, "待联络"),
    FL_STATUS_2(10551002, "待预约"),
    FL_STATUS_3(10551003, "待进店"),
    FL_STATUS_4(10551004, "未关联工单"),
    FL_STATUS_5(10551005, "已进店"),
    FL_STATUS_6(10551006, "缺零件"),
    FL_STATUS_7(10551007, "待验证"),
    FL_STATUS_8(10551008, "待确认"),
    FL_STATUS_9(10551009, "已完成"),
    FL_STATUS_10(10551010, "自然进店"),
    FL_STATUS_11(10551011, "联络失败"),
    FL_STATUS_12(10551012, "预约失败"),
    FL_STATUS_13(10551013, "超时未进店"),
    FL_STATUS_14(10551014, "车主不修"),
    FL_STATUS_15(10551015, "线索超时"),
    FL_STATUS_16(10551016, "失败-二次未接通"),
    FL_STATUS_17(10551017, "失败-车辆已处理"),
    FL_STATUS_18(10551018, "失败-车辆无报警"),
    FL_STATUS_19(10551019, "失败-报警未再现"),
    FL_STATUS_20(10551020, "失败-客户原因"),
    FL_STATUS_21(10551021, "失败-其他"),
    FL_STATUS_22(10551022, "二次预约"),
    FL_STATUS_23(10551023, "保修/延保"),
    FL_STATUS_24(10551024, "其他"),

    /**
     * 未使用AI原因
     */
    REASON_TYPE_ENTER(83841001, "车已进厂"),
    REASON_TYPE_CUS_DEFICIENCY(83841002, "联系方式缺失"),
    REASON_TYPE_NUM_DEFICIENCY(83841003, "工作号故障/缺失"),
    REASON_TYPE_REPEAT(83841004, "集团线索重复"),
    REASON_TYPE_ERROR(83841005, "历史工单里程错误/开票交车不符"),
    REASON_TYPE_APPOINTED(83841006, "客户已主动预约（有预约单）"),
    REASON_TYPE_UNUSUAL(83841007, "线索异常（有异常状态）"),
    REASON_TYPE_NOT(83841008, "线索类型不要求AI"),

    /**
     * 异常类型
     */
    UNUSUAL_TYPE_REFUSE(83851001, "客户拒绝邀约"),
    UNUSUAL_TYPE_SCOOTER(83851002, "代步车"),
    UNUSUAL_TYPE_SCRAP(83851003, "车已报废"),
    UNUSUAL_TYPE_CUSTOMER(83851004, "大客户"),
    UNUSUAL_TYPE_DRIVE(83851005, "试驾车"),
    UNUSUAL_TYPE_NORMAL(83851006, "无异常"),
    UNUSUAL_TYPE_OTHER(83851007, "他店客户"),

    /**
     * 流失客户类型
     */
    LOSS_TYPE_1(87901001, "18个月未保养"),
    LOSS_TYPE_2(87901002, "流失客户"),

    /**
     * 失败原因
     */
    LOSE_REASON_1(82431001, "多次电话未接通"),
    LOSE_REASON_2(82431002, "无人接听"),
    LOSE_REASON_3(82431003, "电话关机"),
    LOSE_REASON_4(82431004, "号码错误"),
    LOSE_REASON_5(82431005, "客户拒绝"),
    LOSE_REASON_6(82431006, "他店进厂"),
    LOSE_REASON_7(82431007, "其他"),
    LOSE_REASON_8(82431008, "外地使用"),
    LOSE_REASON_9(82431009, "车辆已卖"),
    LOSE_REASON_10(82431010, "无正确联系方式"),
    LOSE_REASON_11(82431011, "同城他店客户"),
    LOSE_REASON_12(82431012, "异地用车"),
    LOSE_REASON_13(82431013, "修理厂保养"),
    LOSE_REASON_14(82431014, "报废"),
    LOSE_REASON_15(82431015, "里程差异超过3个月"),
    LOSE_REASON_16(82431016, "客户拒绝，未说原因"),
    ;

    private final Integer code;
    private final String desc;
}
