package com.volvo.maintain.infrastructure.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Getter
public enum RenewalPolicySource {

    ONLINE_POLICY(96191001, "在线出单"),
    SINGLE_MANUAL_ENTRY(96191002, "单个补录"),
    BATCH_MANUAL_ENTRY(96191003, "批量补录"),
    BATCH_UPLOAD_INSURANCE_IMPORT(96191004, "批量上传-保司导入"),
    BATCH_UPLOAD_MANUFACTURER_UPLOAD(96191005, "批量上传-厂家上传");

    private static final Map<String, RenewalPolicySource> lookup = new HashMap<>();

    static {
        for (RenewalPolicySource e : RenewalPolicySource.values()) {
            lookup.put(e.getDesc(), e);
        }
    }

    private final int code;
    private final String desc;

    RenewalPolicySource(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer getByDesc(String desc) {
        RenewalPolicySource renewalPolicySource = lookup.get(desc);
        return Objects.nonNull(renewalPolicySource) ? renewalPolicySource.code : null;
    }
}
