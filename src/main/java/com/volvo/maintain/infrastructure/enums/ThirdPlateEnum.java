package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * description 客户画像三级板块
 *
 * <AUTHOR>
 * @date 2024/01/17 14:44
 */
@Getter
@AllArgsConstructor
public enum ThirdPlateEnum {
    THIRD_MAINTENANCE(35231005,35261001, "保养套餐"),
    THIRD_EXTENDED(35231005,35261002, "延保"),
    THIRD_RENEWAL(35231005,35261003, "续保"),
    THIRD_ORIGINAL_COUPON(35231004,35261004, "原厂券"),
    THIRD_PARTY_COUPON(35231004,35261005, "经销商券");
    private final Integer parentcode;
    private final Integer code;
    private final String name;
    public static Integer getCodeByName(String name) {
        for (ThirdPlateEnum clueType : ThirdPlateEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        for (ThirdPlateEnum clueType : ThirdPlateEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }
    public static List<ThirdPlateEnum> getPlateInfo(Integer parentcode) {
        List<ThirdPlateEnum> thirdPlateEnums = new ArrayList<>();
        for (ThirdPlateEnum clueType : ThirdPlateEnum.values()) {
            if (clueType.getParentcode().equals(parentcode)) {
                thirdPlateEnums.add(clueType);
            }
        }
        return thirdPlateEnums;
    }
}
