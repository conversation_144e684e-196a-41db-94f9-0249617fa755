package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * description 客户画像一级板块
 *
 * <AUTHOR>
 * @date 2024/01/17 14:44
 */
@Getter
@AllArgsConstructor
public enum SecondPlateEnum {
    SECOND_CUSTOMER_LABEL(35221002,35231001, "车辆信息"),
    SECOND_MAINTENANCE_LABEL(35221002,35231002, "保养标签"),
    SECOND_AFTER_SALES(35221002,35231003, "售后行为"),
    SECOND_COUPON_INFO(35221002,35231004, "卡劵信息"),
    SECOND_CUSTOMER_RIGHTS(35221002,35231005, "权益信息");

    private final Integer parentcode;
    private final Integer code;
    private final String name;
    public static Integer getCodeByName(String name) {
        for (SecondPlateEnum clueType : SecondPlateEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        for (SecondPlateEnum clueType : SecondPlateEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }
    public static List<SecondPlateEnum> getPlateInfo(Integer parentcode) {
        List<SecondPlateEnum> secondPlateEnums = new ArrayList<>();
        for (SecondPlateEnum clueType : SecondPlateEnum.values()) {
            if (clueType.getParentcode().equals(parentcode)) {
                secondPlateEnums.add(clueType);
            }
        }
        return secondPlateEnums;
    }
}
