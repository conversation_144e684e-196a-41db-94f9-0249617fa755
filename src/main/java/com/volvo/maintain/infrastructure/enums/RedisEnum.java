package com.volvo.maintain.infrastructure.enums;

import org.apache.commons.lang3.ObjectUtils;

public enum RedisEnum {
	// 一级目录为：模块名称
	// 二级目录为：操作类型
	// 三级目录及以下：自己命名

	// BASE NX
	FAULT_NX("FAULT:NX:"),

	CLUE_DATA_SYNCHRO(RedisEnum.FAULT_NX, "CLUE_DATA_SYNCHRO_", 10000),
	DO_FOLLOW_SUB(RedisEnum.FAULT_NX, "DO_FOLLOW_SUB", 10000),

	// WORKSHOP 透明车间相关
	WORKSHOP_NX("WORKSHOP:NX:"),
	SIGN_QUANTITY_CACHE(RedisEnum.WORKSHOP_NX, "SIGN_QUANTITY_CACHE_", 30000),
	SIGN_QUANTITY_LOCK(RedisEnum.WORKSHOP_NX, "SIGN_QUANTITY_LOCK_", 30000),

	// 结尾符，不可用
	END("END:");

	private String key;
	private int timeoutMsecs;
	private int expireMsecs;

	public String getKey() {
		return this.key;
	}

	public String getKey(Object key_) {
		return this.key + key_;
	}

	public String getKey(Object... key_) {
		if (ObjectUtils.isEmpty(key_)) {
			return this.key;
		}

		StringBuffer buffer = new StringBuffer(this.key);
		for (Object obj : key_) {
			buffer.append(null == obj ? "_" : obj.toString());
		}

		return buffer.toString();
	}

	public int getTimeoutMsecs() {
		return this.timeoutMsecs;
	}

	public int getExpireMsecs() {
		return this.expireMsecs;
	}

	RedisEnum(String pk) {
		if (!pk.contains(":")) {
			throw new RuntimeException("请定义包名称！");
		}

		this.key = pk;
	}

	RedisEnum(RedisEnum pk, String key) {
		this.key = pk.key.concat(key);
	}

	RedisEnum(RedisEnum pk, String key, int timeoutMsecs) {
		this.key = pk.key.concat(key);
		this.timeoutMsecs = timeoutMsecs;
	}

	RedisEnum(RedisEnum pk, String key, int timeoutMsecs, int expireMsecs) {
		this.key = pk.key.concat(key);
		this.timeoutMsecs = timeoutMsecs;
		this.expireMsecs = expireMsecs;
	}
}
