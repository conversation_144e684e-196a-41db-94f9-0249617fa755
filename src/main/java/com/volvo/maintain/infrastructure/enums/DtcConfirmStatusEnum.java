package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DtcConfirmStatusEnum {
    CONFIRM_STATUS_0("全部", 0),
    CONFIRM_STATUS_1("是", 1),
    CONFIRM_STATUS_2("否", 2);

    private final String name;
    private final Integer code;

    public static Integer getCodeByName(String name) {
        for (DtcConfirmStatusEnum clueType : DtcConfirmStatusEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        for (DtcConfirmStatusEnum clueType : DtcConfirmStatusEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }
}
