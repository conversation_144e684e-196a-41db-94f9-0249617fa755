/*
 * Copyright (c) Volvo CAR Distribution (SHANGHAI) Co., Ltd. 2023. All rights reserved.
 */

package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 功能描述： 邀约类型枚举
 *
 * <AUTHOR>
 * @since 2023-11-03
 */
@Getter
@AllArgsConstructor
public enum InvitationTypeEnum {
    /**
     * 首保
     */
    FIRST_MAINTENANCE(82381001, "首保"),
    /**
     * 定保
     */
    REGULAR_MAINTENANCE(82381002, "定保"),
    /**
     * 易损件
     */
    LOST_CUSTOMER(82381003, "易损件"),
    /**
     * VOC事故
     */
    VOC_ACCIDENT(82381004, "VOC事故"),
    /**
     * 流失客户
     */
    P_TYPE(82381006, "流失客户"),
    /**
     * 召回
     */
    RECALL_TYPE(82381007, "召回"),
    /**
     * 保修
     */
    MAINTENANCE_TYPE(82381009, "保修"),
    /**
     * 流失预警
     */
    LOST_WARNING(82381012, "流失预警"),
    /**
     * 零附件线索
     */
    INVITE_TYPE_XIII(82381013, "零附件线索"),
    /**
     * 故障灯
     */
    INVITE_TYPE_FAULT_LIGHT(82381014, "故障灯线索"),
    /**
     * 续保
     */
    INVITE_TYPE_INSURANCE(82381003, "续保线索"),
    /**
     * 店端自建
     */
    DEL_CREATED(82381010, "店端自建"),
    /**
     * 厂端自建
     */
    OEM_CREATED(82381011, "厂端自建"),
    ;

    /**
     * 初始化默认设置属性
     */
    private final Integer code;

    /**
     * 初始化默认设置属性
     */
    private final String value;

    /**
     * 功能描述：邀约类型枚举
     *
     * @param code 对应的代码
     * @return boolean 查询当前的code是否在里面
     */
    public static boolean pushMqContainsCode(Integer code) {
        return Arrays.asList(FIRST_MAINTENANCE.getCode(), REGULAR_MAINTENANCE.getCode(), P_TYPE.getCode(),
                LOST_WARNING.getCode(), INVITE_TYPE_XIII.getCode()).contains(code);
    }

    /**
     * 功能描述：获取邀约线索类型列表
     *
     * @return List<Integer> 邀约线索类型列表
     */
    public static List<Integer> findInviteTypeList() {

        return Arrays.asList(FIRST_MAINTENANCE.getCode(), REGULAR_MAINTENANCE.getCode(), P_TYPE.getCode(),
                LOST_WARNING.getCode(), RECALL_TYPE.getCode(), MAINTENANCE_TYPE.getCode(), VOC_ACCIDENT.getCode()
                ,DEL_CREATED.getCode(),OEM_CREATED.getCode(), INVITE_TYPE_XIII.getCode());
    }
}
