package com.volvo.maintain.infrastructure.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Getter
public enum ClueIssuanceType {

    FACTORY_ISSUANCE(96171001, "厂端下发"),
    SELF_STORE_IMPORT(96171002, "自店导入"),
    MIGRATION_IMPORT(96171003, "迁移导入");

    private static final Map<String, ClueIssuanceType> lookup = new HashMap<>();

    static {
        for (ClueIssuanceType e : ClueIssuanceType.values()) {
            lookup.put(e.getDesc(), e);
        }
    }

    private final int code;
    private final String desc;

    ClueIssuanceType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer getByDesc(String desc) {
        ClueIssuanceType clueIssuanceType = lookup.get(desc);
        return Objects.nonNull(clueIssuanceType) ? clueIssuanceType.code : null;
    }
}
