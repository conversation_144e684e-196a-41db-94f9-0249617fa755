package com.volvo.maintain.infrastructure.enums;


import java.util.Objects;

/**
 * redis
 */
public enum CustomizedKeyEnum {

    /** CDP token缓存key 3分钟*/
    cdp_token_key("newbei:cdp:token:", 1000 * 60 * 3),
    cdp_tag_key("DTCC:DMSCUS_CUSTOMER:CDP_TAG:TASK_NYSC:", 1000 * 60 * 30),

    FAULT_LIGHT_SYNC_KEY("DTCC:DMSCUS_CUSTOMER:FAULT_LIGHT:SYNC_TASK:", 1000 * 60 * 30),

    FAULT_LIGHT_UPDATE_KEY("DTCC:DMSCUS_CUSTOMER:FAULT_LIGHT:UPDATE_TASK:", 1000 * 60 * 30),
    updateHighlightFlag("updateHighlightFlag"),
    queryHighlightFlag("queryHighlightFlag")
    ;
    private String key;

    private long expire;

    CustomizedKeyEnum(String key, long expire) {
        this.key = key;
        this.expire = expire;
    }

    CustomizedKeyEnum(String pk) {
        this.key = pk;
    }
    public String getKey() {
        return key;
    }
    public String getKey(Object key_) {
        return this.key + key_;
    }
    public long getExpire() {
        return expire;
    }

    public static long getExpireByName(String key) {
        for (CustomizedKeyEnum valueEnum : CustomizedKeyEnum.values()) {
            if (Objects.equals(key,valueEnum.getKey())) {
                return valueEnum.getExpire();
            }
        }
        return 1;
    }
}
