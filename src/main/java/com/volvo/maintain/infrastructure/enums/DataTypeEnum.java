package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @since 2023/12/25
 */
@Getter
@AllArgsConstructor
public enum DataTypeEnum {

    /**
     * 店端
     */
    DATA_TYPE_DEALER(10461001, "店端类型"),
    /**
     * 厂端
     */
    DATA_TYPE_OEM(10461003, "厂端类型");

    private final Integer dataType;

    private final String desc;
}
