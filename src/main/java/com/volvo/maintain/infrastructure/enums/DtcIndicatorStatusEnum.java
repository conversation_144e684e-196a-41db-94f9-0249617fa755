package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DtcIndicatorStatusEnum {
    INDICATOR_STATUS_0("全部", 0),
    INDICATOR_STATUS_1("是", 1),
    INDICATOR_STATUS_2("否", 2);

    private final String name;
    private final Integer code;

    public static Integer getCodeByName(String name) {
        for (DtcIndicatorStatusEnum clueType : DtcIndicatorStatusEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        for (DtcConfirmStatusEnum clueType : DtcConfirmStatusEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }
}
