package com.volvo.maintain.infrastructure.enums;

import lombok.Getter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Getter
public enum RenewalDealerType {

    POLICY_DEALER(96181001, "保单经销商"),
    WORK_ORDER_DEALER(96181002, "工单经销商"),
    SALES_DEALER(96181003, "销售经销商"),
    SELF_STORE_IMPORT_DEALER(96181004, "自店导入经销商");

    private static final Map<String, RenewalDealerType> descMap = new HashMap<>();

    private static final Map<String, RenewalDealerType> typeMap = new HashMap<>();


    static {
        for (RenewalDealerType e : RenewalDealerType.values()) {
            descMap.put(e.getDesc(), e);
            typeMap.put(String.valueOf(e.getCode()), e);
        }
    }

    private final int code;
    private final String desc;

    RenewalDealerType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer getByDesc(String desc) {
        RenewalDealerType renewalDealerType = descMap.get(desc);
        return Objects.nonNull(renewalDealerType) ? renewalDealerType.code : null;
    }

    public static RenewalDealerType getEnumByType(String type) {
        return typeMap.get(type);
    }
}
