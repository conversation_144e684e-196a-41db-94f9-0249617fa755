/*
 * Copyright (c) Volvo CAR Distribution (SHANGHAI) Co., Ltd. 2023. All rights reserved.
 */

package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 功能描述：标签枚举
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@Getter
@AllArgsConstructor
public enum TagEnum {
    /**
     * 其它标签数据
     */
    OTHER_TAG(35281001, "其它标签数据"),


    /**
     * 本地接口查询
     */
    LOCAL_SELECT(35281002, "本地接口查询"),

    /**
     * 外部接口查询
     */
    EXTERNAL_SELECT(35281003, "外部接口查询");

    /**
     * 初始化默认设置属性
     */
    private final Integer code;

    /**
     * 初始化默认设置属性
     */
    private final String name;

    /**
     * 功能描述：标签枚举
     *
     * @param code 对应的代码
     * @return String 查询当前的code的值
     */
    public static String queryNameByCode(Integer code) {
        Map<Integer, String> map = Arrays.stream(TagEnum.values())
            .collect(Collectors.toMap(TagEnum::getCode, TagEnum::getName));
        return map.get(code);
    }
}
