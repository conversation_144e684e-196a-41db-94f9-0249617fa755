package com.volvo.maintain.infrastructure.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Getter
public enum  RenewalClueDataSource {

    COMMERCIAL_INSURANCE(96201001, "商业险"),
    COMPULSORY_INSURANCE(96201002, "交强险"),
    SALES_DATE(96201003, "销售日期"),
    DEALER_IMPORT(96201004, "自店导入");

    private static final Map<String, RenewalClueDataSource> lookup = new HashMap<>();

    static {
        for (RenewalClueDataSource e : RenewalClueDataSource.values()) {
            lookup.put(e.getDesc(), e);
        }
    }

    private final int code;
    private final String desc;

    RenewalClueDataSource(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer getByDesc(String desc) {
        RenewalClueDataSource renewalClueDataSource = lookup.get(desc);
        return Objects.nonNull(renewalClueDataSource) ? renewalClueDataSource.code : null;
    }
}
