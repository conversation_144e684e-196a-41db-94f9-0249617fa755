package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 取送车订单状态
 */
@Getter
@AllArgsConstructor
public enum PickDeliverCarOrderStatusEnum {
    /**
     * 待确认
     */
    PENDING_CONFIRMATION(82721001, "待确认"),
    /**
     * 已下单
     */
    ORDER_PLACED(82721002, "已下单"),
    /**
     * 资金已冻结
     */
    FUNDS_FROZEN(82721003, "资金已冻结"),
    /**
     * 订单取消
     */
    ORDER_CANCELLED(82721004, "订单取消"),
    /**
     * 等待司机接单
     */
    WAITING_FOR_DRIVER(82721005, "等待司机接单"),
    /**
     * 司机已接单
     */
    DRIVER_ACCEPTED(82721006, "司机已接单"),
    /**
     * 司机已开启订单
     */
    DRIVER_STARTED_ORDER(82721007, "司机已开启订单"),
    /**
     * 司机已就位
     */
    DRIVER_IN_POSITION(82721008, "司机已就位"),
    /**
     * 司机开车中
     */
    DRIVER_ON_THE_WAY(82721009, "司机开车中"),
    /**
     * 司机到达目的地
     */
    DRIVER_ARRIVED(82721010, "司机到达目的地"),
    /**
     * 已收车
     */
    CAR_RECEIVED(82721011, "已收车"),
    /**
     * 订单已完成
     */
    ORDER_COMPLETED(82721012, "订单已完成"),
    /**
     * 已评价
     */
    REVIEWED(82721013, "已评价"),
    /**
     * 修改待审核
     */
    MODIFY_WAIT_AUDIT(82721014, "修改待审核"),
    /**
     * 已关闭
     */
    ORDER_CLOSED(82721016, "已关闭");

    private final Integer code;
    private final String desc;

    /**
     * 获取所有订单状态的code列表
     */
    public static List<Integer> getAllCodes() {
        return Arrays.stream(PickDeliverCarOrderStatusEnum.values())
                .map(PickDeliverCarOrderStatusEnum::getCode)
                .collect(Collectors.toList());

    }
}