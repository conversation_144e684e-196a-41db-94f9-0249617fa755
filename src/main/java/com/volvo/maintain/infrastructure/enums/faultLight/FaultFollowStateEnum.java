package com.volvo.maintain.infrastructure.enums.faultLight;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**跟进状态枚举类*/
@Getter
@AllArgsConstructor
public enum FaultFollowStateEnum {

    WAITING_FOR_CONTACT(10541001,10551001,"待联络",1),
    WAITING_FOR_APPOINTMENT(10541002,10551002,"待预约",1),
    WAITING_ENTER_STORE(10541003,10551003,"待进店",1),

    NO_ASSOCIATED_WORK_ORDER(10541004,10551004,"未关联工单",1),
    ENTERED_STORE(10541004,10551005,"已进店",1),
    ENTERED_MISSING_PARTS(10541004,10551006,"缺零件",1),

    WAITING_FOR_VERIFICATION(10541005,10551007,"待验证",1),
    WAITING_FOR_CONFIRMATION(10541006,10551008,"待确认",1),
    ALREADY_COMPLETED(10541007,10551009,"已完成",1),
    ENTERING_STORE_NATURALLY(10541008,10551010,"自然进店",1),

    CONTACT_FAILURE(10541009,10551011,"联络失败",1),
    APPOINTMENT_FAILED(10541009,10551012,"预约失败",0),
    OVERRUN_ENTERING_STORE(10541009,10551013,"超时未进店",1),
    NOT_REPAIR(10541009,10551014,"车主不修",1),
    CLUES_OVERRUN(10541009,10551015,"线索超时",1),
    APPOINTMENT_FAILED_SECONDARY_FAILURE_TO_CONNECT(10541009,10551016,"失败-二次未接通",1),
    APPOINTMENT_FAILED_VEHICLE_PROCESSED(10541009,10551017,"失败-车辆已处理",1),
    APPOINTMENT_FAILED_VEHICLE_NO_ALARM(10541009,10551018,"失败-车辆无报警",1),
    APPOINTMENT_FAILED_ALARM_NOT_REPRODUCED(10541009,10551019,"失败-报警未再现",1),
    APPOINTMENT_FAILED_CUSTOMER_REASONS(10541009,10551020,"失败-客户原因",1),
    APPOINTMENT_FAILED_OTHER(10541009,10551021,"失败-其他",1),
    SECOND_APPOINTMENT(10541002,10551022,"二次预约",1),
    WARRANTY_EXTENSION(10541004,10551023,"保修/延保",1),

    OTHER(10541004,10551024,"其他",1);

    private final Integer parentCode;
    private final Integer code;
    private final String name;
    private final Integer state;

    public static String getNameByCode(Integer code){
        for(FaultFollowStateEnum nodeEnum : FaultFollowStateEnum.values()){
            if (nodeEnum.code.equals(code)){
                return nodeEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name){
        for(FaultFollowStateEnum nodeEnum : FaultFollowStateEnum.values()){
            if (StringUtils.equals(nodeEnum.name, name)){
                return nodeEnum.code;
            }
        }
        return null;
    }

    private static Map<Integer, FaultFollowStateEnum> map = Arrays.stream(FaultFollowStateEnum.values()).collect(Collectors.toMap(FaultFollowStateEnum::getCode, e -> e));
    private static Map<Integer,String> valueMap = Arrays.stream(FaultFollowStateEnum.values()).collect(Collectors.toMap(FaultFollowStateEnum::getCode, e -> e.getName()));

}
