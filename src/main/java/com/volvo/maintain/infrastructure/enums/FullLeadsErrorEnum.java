package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @since 2023/12/25
 */
@Getter
@AllArgsConstructor
public enum FullLeadsErrorEnum {

    /**
     * 区域信息查询异常
     */
    ERROR_QUERY_AREA(1001, "区域信息获取异常！"),
    /**
     * 建议进厂时间为空
     */
    ERROR_DATE_IS_EMPTY(1002, "建议进厂时间不能为空！"),
    /**
     * 建议进厂时间范围超长
     */
    ERROR_DATE_EXCEED_YEAR(1003, "建议进厂时间查询跨度不能超过一年！"),
    /**
     * 参数异常
     */
    INVALID_PARAM(1004, "参数异常！"),
    /**
     * 批次号不能为空
     */
    INVALID_BATCH_NO(1005, "批次号不能为空！"),
    ERROR_REPAIR_ORDER_QUERY(1006, "工单信息查询失败！"),
    ;

    private final Integer value;

    private final String desc;
}
