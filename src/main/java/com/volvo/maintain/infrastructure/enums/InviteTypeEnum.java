package com.volvo.maintain.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description 邀约线索类型枚举类
 *
 * <AUTHOR>
 * @date 2023/8/22 14:44
 */
@Getter
@AllArgsConstructor
public enum InviteTypeEnum {
    FIRST_MAINTENANCE("82381001", "首保", 82381001, Boolean.TRUE),
    REGULAR_MAINTENANCE("82381002", "定保", 82381002, Boolean.TRUE),
    LOST_CUSTOMER("82381006", "流失客户", 82381006, Boolean.TRUE),
    LOST_WARNING("82381012", "流失预警", 82381012, Boolean.TRUE),
    PART_CLUE("82381013", "零附件线索", 82381013, Boolean.FALSE);

    private final String code;
    private final String name;
    private final Integer intCode;
    //判断是否保养灯类的线索
    private final Boolean maiIndicator;
    public static String getCodeByName(String name) {
        for (InviteTypeEnum clueType : InviteTypeEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }

    public static boolean containsCode(String code) {
        for (InviteTypeEnum inviteType : InviteTypeEnum.values()) {
            if (!inviteType.getMaiIndicator()) {
                continue;
            }
            if (inviteType.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public static String getNameByCode(String code) {
        for (InviteTypeEnum clueType : InviteTypeEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }
}
