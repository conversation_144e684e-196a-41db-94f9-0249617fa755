package com.volvo.maintain.infrastructure.aspect;

import com.volvo.maintain.infrastructure.annotation.SmartCache;
import com.volvo.maintain.infrastructure.cache.SmartCacheData;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 智能缓存切面
 * 基于 signQuantity 接口的缓存机制实现
 */
@Slf4j
@Aspect
@Component
public class SmartCacheAspect {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ApplicationContext applicationContext;

    private final ExpressionParser parser = new SpelExpressionParser();

    @Around("@annotation(smartCache)")
    public Object around(ProceedingJoinPoint joinPoint, SmartCache smartCache) throws Throwable {
        // 生成缓存键
        String cacheKey = generateCacheKey(joinPoint, smartCache);
        String lockKey = cacheKey + ":lock";
        String flagKey = cacheKey + ":refresh:flag";

        log.info("SmartCache processing: method={}, cacheKey={}", 
                joinPoint.getSignature().toShortString(), cacheKey);

        // 尝试从缓存获取数据
        SmartCacheData<?> cacheData = getFromCache(cacheKey);

        if (cacheData != null) {
            // 检查是否需要异步刷新
            tryAsyncRefresh(joinPoint, smartCache, cacheData, lockKey, flagKey);
            log.info("SmartCache hit: returning cached data for key={}", cacheKey);
            return cacheData.getData();
        }

        // 缓存不存在，尝试获取锁进行实时查询
        return getDataWithLock(joinPoint, smartCache, cacheKey, lockKey);
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(ProceedingJoinPoint joinPoint, SmartCache smartCache) {
        String keyPrefix = smartCache.keyPrefix();
        String keyExpression = smartCache.keyExpression();

        if (keyExpression.isEmpty()) {
            throw new IllegalArgumentException("SmartCache keyExpression cannot be empty");
        }

        // 解析SpEL表达式
        Expression expression = parser.parseExpression(keyExpression);
        EvaluationContext context = new StandardEvaluationContext();

        // 设置方法参数
        Object[] args = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = signature.getParameterNames();

        for (int i = 0; i < args.length; i++) {
            context.setVariable(paramNames[i], args[i]);
            context.setVariable("p" + i, args[i]);
        }
        context.setVariable("args", args);

        String keyValue = expression.getValue(context, String.class);
        return keyPrefix + keyValue;
    }

    /**
     * 从缓存获取数据
     */
    private SmartCacheData<?> getFromCache(String cacheKey) {
        try {
            RBucket<SmartCacheData<?>> bucket = redissonClient.getBucket(cacheKey);
            return bucket.get();
        } catch (Exception e) {
            log.error("SmartCache getFromCache failed: cacheKey={}, error={}", cacheKey, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 尝试异步刷新缓存
     */
    private void tryAsyncRefresh(ProceedingJoinPoint joinPoint, SmartCache smartCache, 
                                SmartCacheData<?> cacheData, String lockKey, String flagKey) {
        if (cacheData.needsRefresh(smartCache.refreshThresholdMinutes())) {
            try {
                RBucket<String> flagBucket = redissonClient.getBucket(flagKey);
                
                // 检查是否已有异步刷新任务在执行
                if (flagBucket.get() != null) {
                    log.debug("SmartCache async refresh already in progress, skip: flagKey={}", flagKey);
                    return;
                }
                
                // 设置刷新标记
                flagBucket.set("true", smartCache.refreshFlagExpireMinutes(), TimeUnit.MINUTES);
                log.info("SmartCache set refresh flag and trigger async refresh: flagKey={}", flagKey);
                
                // 异步刷新
                asyncRefresh(joinPoint, smartCache, lockKey);
                
            } catch (Exception e) {
                log.error("SmartCache tryAsyncRefresh failed, fallback to sync refresh: error={}", e.getMessage());
            }
        }
    }

    /**
     * 异步刷新缓存
     */
    private void asyncRefresh(ProceedingJoinPoint joinPoint, SmartCache smartCache, String lockKey) {
        log.info("SmartCache async refresh preparing: lockKey={}", lockKey);
        
        // 获取线程池
        ThreadPoolTaskExecutor threadPool = getThreadPool(smartCache.threadPoolName());
        
        CompletableFuture.runAsync(() -> {
            RLock lock = redissonClient.getLock(lockKey);
            try {
                if (lock.tryLock(smartCache.lockWaitTime(), -1, smartCache.lockTimeUnit())) {
                    try {
                        log.info("SmartCache async refresh started: lockKey={}", lockKey);
                        Object freshData = joinPoint.proceed();
                        String cacheKey = generateCacheKey(joinPoint, smartCache);
                        saveToCache(cacheKey, freshData);
                        log.info("SmartCache async refresh completed: cacheKey={}", cacheKey);
                    } catch (Throwable e) {
                        log.error("SmartCache async refresh execution failed: error={}", e.getMessage(), e);
                    } finally {
                        if (lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                    }
                } else {
                    log.warn("SmartCache async refresh failed to acquire lock: lockKey={}", lockKey);
                }
            } catch (Exception e) {
                log.error("SmartCache async refresh failed: lockKey={}, error={}", lockKey, e.getMessage(), e);
            }
        }, threadPool);
    }

    /**
     * 使用分布式锁获取数据
     */
    private Object getDataWithLock(ProceedingJoinPoint joinPoint, SmartCache smartCache, 
                                  String cacheKey, String lockKey) throws Throwable {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(smartCache.lockWaitTime(), -1, smartCache.lockTimeUnit())) {
                try {
                    // 双重检查缓存
                    SmartCacheData<?> cacheData = getFromCache(cacheKey);
                    if (cacheData != null) {
                        log.info("SmartCache double-check hit: returning cached data for key={}", cacheKey);
                        return cacheData.getData();
                    }
                    
                    // 执行实际方法
                    log.info("SmartCache executing real method: cacheKey={}", cacheKey);
                    Object result = joinPoint.proceed();
                    
                    // 保存到缓存
                    saveToCache(cacheKey, result);
                    return result;
                } finally {
                    lock.unlock();
                }
            } else {
                // 获取锁失败的降级策略
                if (smartCache.enableFallback()) {
                    log.warn("SmartCache lock acquisition failed, returning stale cache: cacheKey={}", cacheKey);
                    SmartCacheData<?> staleData = getFromCache(cacheKey);
                    if (staleData != null && staleData.getData() != null) {
                        return staleData.getData();
                    }
                }
                log.error("SmartCache lock acquisition failed and no fallback data: cacheKey={}", cacheKey);
                return getDefaultValue(smartCache.cacheType());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("SmartCache lock acquisition interrupted: {}", e.getMessage());
            return getDefaultValue(smartCache.cacheType());
        }
    }

    /**
     * 保存数据到缓存（永久存储）
     */
    private void saveToCache(String cacheKey, Object data) {
        try {
            SmartCacheData<Object> cacheData = new SmartCacheData<>(data, cacheKey);
            RBucket<SmartCacheData<Object>> bucket = redissonClient.getBucket(cacheKey);
            bucket.set(cacheData); // 永久存储，不设置过期时间
            log.info("SmartCache data saved: cacheKey={}", cacheKey);
        } catch (Exception e) {
            log.error("SmartCache saveToCache failed: cacheKey={}, error={}", cacheKey, e.getMessage(), e);
        }
    }

    /**
     * 获取线程池
     */
    private ThreadPoolTaskExecutor getThreadPool(String threadPoolName) {
        try {
            return applicationContext.getBean(threadPoolName, ThreadPoolTaskExecutor.class);
        } catch (Exception e) {
            log.warn("SmartCache failed to get thread pool: {}, using default", threadPoolName);
            return applicationContext.getBean("threadSignQuantity", ThreadPoolTaskExecutor.class);
        }
    }

    
    /**
     * 获取默认值
     */
    private Object getDefaultValue(Class<?> cacheType) {
        if (cacheType == null || cacheType == Object.class) {
            return null;
        }

        // 处理基本类型
        if (cacheType.isPrimitive()) {
            return getPrimitiveDefaultValue(cacheType);
        }

        // 处理数组类型
        if (cacheType.isArray()) {
            return java.lang.reflect.Array.newInstance(cacheType.getComponentType(), 0);
        }

        // 处理集合类型
        if (Collection.class.isAssignableFrom(cacheType)) {
            return createEmptyCollection(cacheType);
        }

        // 处理Map类型
        if (Map.class.isAssignableFrom(cacheType)) {
            return createEmptyMap(cacheType);
        }

        // 处理字符串
        if (cacheType == String.class) {
            return "";
        }

        // 对于其他引用类型，尝试创建实例
        try {
            // 检查是否有可访问的无参构造函数
            java.lang.reflect.Constructor<?> constructor = cacheType.getDeclaredConstructor();
            if (!constructor.isAccessible()) {
                constructor.setAccessible(true);
            }
            return constructor.newInstance();
        } catch (NoSuchMethodException e) {
            // 没有无参构造函数
            log.info("getDefaultValue,类{}没有无参构造函数", cacheType.getName());
        } catch (Exception e) {
            log.warn("getDefaultValue,创建{}实例失败:", cacheType.getName(), e);
        }

        return null;
    }

    /**
     * 创建空集合
     */
    private Collection<?> createEmptyCollection(Class<?> collectionType) {
        try {
            if (collectionType == List.class || collectionType == ArrayList.class) {
                return new ArrayList<>();
            } else if (collectionType == Set.class || collectionType == HashSet.class) {
                return new HashSet<>();
            } else if (collectionType == LinkedList.class) {
                return new LinkedList<>();
            } else {
                return (Collection<?>) collectionType.getDeclaredConstructor().newInstance();
            }
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 创建空Map
     */
    private Map<?, ?> createEmptyMap(Class<?> mapType) {
        try {
            if (mapType == Map.class || mapType == HashMap.class) {
                return new HashMap<>();
            } else if (mapType == LinkedHashMap.class) {
                return new LinkedHashMap<>();
            } else if (mapType == TreeMap.class) {
                return new TreeMap<>();
            } else {
                return (Map<?, ?>) mapType.getDeclaredConstructor().newInstance();
            }
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    /**
     * 获取基本类型的默认值
     */
    private Object getPrimitiveDefaultValue(Class<?> primitiveType) {
        if (primitiveType == boolean.class) return false;
        if (primitiveType == byte.class) return (byte) 0;
        if (primitiveType == short.class) return (short) 0;
        if (primitiveType == int.class) return 0;
        if (primitiveType == long.class) return 0L;
        if (primitiveType == float.class) return 0.0f;
        if (primitiveType == double.class) return 0.0d;
        if (primitiveType == char.class) return '\u0000';
        return null;
    }

}
