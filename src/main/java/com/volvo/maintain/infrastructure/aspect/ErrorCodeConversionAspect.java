package com.volvo.maintain.infrastructure.aspect;

import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.util.ErrorConversionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 异常转换与抛出
 * @Date 2024/10/16 16:44
 */
@Aspect
@Component
public class ErrorCodeConversionAspect {

    @AfterReturning(pointcut = "execution(* *(..)) && @annotation(com.volvo.maintain.infrastructure.annotation.ErrorCodeConversion)", returning = "result")
    public void afterReturningAdvice(JoinPoint joinPoint, Object result) {
        // 在这里处理返回结果
        ErrorConversionUtils.errorCodeConversion((DmsResponse<?>)result);
    }
}
