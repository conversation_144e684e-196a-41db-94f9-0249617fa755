package com.volvo.maintain.infrastructure.consumer;

import com.volvo.maintain.application.maintainlead.service.Em90VehicleDeliverService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * redis 过期监听
 */
@Component
@Slf4j
public class DeviceKeyExpirationEventMessageListener extends KeyExpirationEventMessageListener {
	@Autowired
	private Em90VehicleDeliverService em90VehicleDeliverService;

	public DeviceKeyExpirationEventMessageListener(RedisMessageListenerContainer listenerContainer) {
		super(listenerContainer);
	}

	/**
	 * 监听失效的key，key格式为keeplive_deviceId
	 */
	@Override
	public void onMessage(Message message, byte[] pattern) {
		// 此处为统一入口，请单独撰写server接口进行转发，并统一用ek为前缀命名，请勿在当前类撰写任何业务代码
		// 当实际业务较多的时候，将进一步进行性能优化，暂全部为同步串行
		String key = message.toString();
		log.info("received msg:{}", key);
		Objects.requireNonNull(key, "received key isNull");

		// em90 push 处理
		em90VehicleDeliverService.ekEm90push(key);

		// 其它业务接口

		// 其它业务接口2

		// 其它业务接口3

		// ......
	}
}
