package com.volvo.maintain.infrastructure.consumer.accidentClues;

import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.application.maintainlead.dto.MQSynchronizeDataDto;
import com.volvo.maintain.application.maintainlead.dto.white.WhiteListDto;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainAuthFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.white.VehicleHealthCheckWhiteListVo;
import com.yonyou.cyx.function.exception.ServiceBizException;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@RocketMQMessageListener(
	nameServer = "${rocketmq.dealerBlackList.name-server}",
	topic = "${rocketmq.dealerBlackList.topic:topic_dealer_black_list}",
	consumerGroup = "${rocketmq.dealerBlackList.consumer.group:group_dealer_black_list}",
	selectorExpression = "${rocketmq.dealerBlackList.tag:tag_dealer_black_list}",
	accessKey = "${rocketmq.dealerBlackList.consumer.access-key}",
	secretKey = "${rocketmq.dealerBlackList.consumer.secret-key}",
	tlsEnable = "${rocketmq.dealerBlackList.consumer.tls-enable}")
@Component
public class BlackListConsumer implements RocketMQListener<String> {

	@Autowired
	private DomainMaintainAuthFeign domainMaintainAuthFeign;

	@Qualifier("thread360Pool")
	@Autowired
	private ThreadPoolTaskExecutor thread360Pool;

	@Override
	public void onMessage(String message) {
		log.info("liteCrmToNewbieBlackList requestParams:{}", JSONObject.toJSONString(message));
		if (StringUtils.isBlank(message)) {
			return;
		}
		// 消息内容处理
		CompletableFuture.supplyAsync(() -> messageStorageService(message), thread360Pool);
	}

	/**
	 * 监听消息 做插入
	 * @param message 监听mq 消息
	 * @return 返回最终结果
	 */
	private Boolean messageStorageService(String message) {
		try {
			mqMessageIngestionService(message);
		}catch (Exception e){
			log.error("liteCrmToNewbieBlackList Failed to synchronize data:{}", JSONObject.toJSONString(e));
			return false;
		}
		// 返回结果
		return true;
	}

	/**
	 * 监听mq 消息同步数据
	 * @param message 消息内容
	 */
	public void mqMessageIngestionService(String message) {
		// deleted blackList data;
		blackList();
		List<MQSynchronizeDataDto> dealerInfoList = JSONObject.parseArray(message, MQSynchronizeDataDto.class);
		if (CollectionUtils.isEmpty(dealerInfoList)) {
			log.info("liteCrmToNewbieBlackList Failed to synchronize data:{}", message);
			return;
		}
		List<String> dealerList = dealerInfoList.stream().filter(Objects::nonNull).map(MQSynchronizeDataDto::getDealerCode).collect(Collectors.toList());
		// add blackList data
		synchronizationBlackList(dealerList);
	}

	/**
	 * Add blacklist data ps:Message content through rocketMQ synchronizationBlackList
	 *
	 * @param dealerList the list of owner codes to be added to the black list
	 * @throws ServiceBizException if the call to insert the whitelist fails or if the response is not successful
	 */
	private void synchronizationBlackList(List<String> dealerList) {
		// 不存在添加数据
		WhiteListDto whiteListDto = new WhiteListDto();
		whiteListDto.setOwnerCode(dealerList);
		whiteListDto.setModType(CommonConstant.WECOM_ACCIDENT_LEAD_TYPE);
		whiteListDto.setRosterType(CommonConstant.BLACKLIST_TYPE);
		whiteListDto.setCreatedBy("liteCrm");
		whiteListDto.setUpdatedBy("liteCrm");
		whiteListDto.setBusinessDescription("liteCrmToNewbie add BackList");
		DmsResponse<Integer> flag = domainMaintainAuthFeign.insertWhitelist(whiteListDto);
		log.info("insertWhitelist response:{}", JSONObject.toJSONString(flag));
		if (flag.isFail() || null != flag.getData()) {
			throw new ServiceBizException("调用领域权限失败！");
		}
	}

	/**
	 * Performs the process of blacklisting a domain.
	 * It uses the domainMaintainAuthFeign component to make the blacklisting request.
	 * If the request fails, it throws a ServiceBizException with the message "调用领域权限失败！".
	 *
	 * @throws ServiceBizException if the blacklisting request fails.
	 */
	private void blackList() {
		DmsResponse<Void> blackListResponse = domainMaintainAuthFeign.blackList();
		log.info("blackList response:{}", JSONObject.toJSONString(blackListResponse));
		if (blackListResponse.isFail()) {
			throw new ServiceBizException("调用领域权限失败！");
		}
	}
}
