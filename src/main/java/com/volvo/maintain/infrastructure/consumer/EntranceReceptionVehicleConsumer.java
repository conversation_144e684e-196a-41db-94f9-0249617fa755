package com.volvo.maintain.infrastructure.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.message.MessageSendDto;
import com.volvo.maintain.application.maintainlead.service.EM90ServiceLeadService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.CommonMethodService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.PrecheckOrderTagService;
import com.volvo.maintain.application.maintainlead.service.customerProfile.VipCustomService;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderVO;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.ApplicationAftersalesManagementFeign;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.util.DateUtil;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;
import com.volvo.maintain.interfaces.vo.VehicleEntranceVo;
import com.yonyou.cyx.function.exception.ServiceBizException;
import com.volvo.maintain.interfaces.vo.VehicleEntranceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Slf4j
@RocketMQMessageListener(nameServer = "${rocketmq.tag.name-server}",
		topic = "${rocketmq.tag.topic}",
		selectorExpression = "${rocketmq.tag.tag}",
		consumerGroup = "${rocketmq.tag.consumer.group}",
		accessKey = "${rocketmq.tag.consumer.access-key}",
		secretKey = "${rocketmq.tag.consumer.secret-key}")
@Component
public class EntranceReceptionVehicleConsumer implements RocketMQListener<String> {

	@Autowired
	private VipCustomService vipCustomService;

	@Autowired
	private PrecheckOrderTagService precheckOrderTagService;

	@Autowired
	private EM90ServiceLeadService em90ServiceLeadService;
	@Autowired
	private CommonMethodService commonMethodService;
	@Autowired
	private ApplicationAftersalesManagementFeign applicationAftersalesManagementFeign;

	@Qualifier("thread360Pool")
	@Autowired
	private ThreadPoolTaskExecutor thread360Pool;

	@Override
	public void onMessage(String message) {
		log.info("entrance-reception.vehicle:{}", message);
		if (StringUtils.isEmpty(message)) {
			return;
		}

		// vip进厂邮件推送
		CompletableFuture.supplyAsync(() -> vipFuture(message), thread360Pool);

		// 落库标签数据
		//CompletableFuture.supplyAsync(() -> recheckOrderFuture(message), thread360Pool);

		// em90车辆进店提醒
		CompletableFuture.supplyAsync(() -> em90PushFuture(message), thread360Pool);
	}

	private Boolean em90PushFuture(String message) {
		try {
			em90Push(message);
		}catch (Exception e){
			log.info("推送EM90车辆进店提醒失败:{}",e);
			return false;
		}
		// 返回结果
		return true;
	}

	Boolean recheckOrderFuture(String message) {
		try{
			VehicleEntranceVo vehicleEntranceVO = JSON.parseObject(message, VehicleEntranceVo.class);
			vehicleEntranceVO = vipCustomService.getVehicleEntranceVO(vehicleEntranceVO);
			List<VinLicenseDto> vinLicenseList = new ArrayList<>();

			VinLicenseDto vinLicenseDto = new VinLicenseDto();
			vinLicenseDto.setLicense(vehicleEntranceVO.getLicensePlate());
			vinLicenseDto.setVin(vehicleEntranceVO.getVin());

			vinLicenseList.add(vinLicenseDto);
			log.info("环检单标签信息，vinLicenseList:{}", vinLicenseList);
			precheckOrderTagService.queryPrecheckOrderTag(vehicleEntranceVO.getDealerCode(), vinLicenseList,null);
		}catch (Exception e){
			log.error("环检单标签失败：",e);
			return false;
		}
		// 返回结果
		return true;
	}

	private Boolean vipFuture(String message) {
		try{
			vipCustomService.saveEntranceReceptionVehicle(message);
		}catch (Exception e){
			log.error("推送邮件失败：",e);
			return false;
		}
		// 返回结果
		return true;
	}

	private void em90Push(String message) {
		String content = "【${license}、${vin}】EM90车辆于${arrivalTime}进店【${ownerName}+${ownerCode}】";
		log.info("{}", message);
		VehicleEntranceVo vehicleEntranceVO = JSON.parseObject(message, VehicleEntranceVo.class);
		log.info("vehicleEntranceVO:{}", JSONObject.toJSONString(vehicleEntranceVO));
		if (StringUtils.isBlank(vehicleEntranceVO.getDealerCode()) || StringUtils.isBlank(vehicleEntranceVO.getLicensePlate())) {
			log.info("em90Push dealerCode isBlank licensePlate isBlank");
			return ;
		}
		//根据车牌号查询vin
		String vin  = em90ServiceLeadService.queryOwnerVehicle(vehicleEntranceVO);
		log.info("vin:{}",vin);
		if (StringUtils.isNotEmpty(vin)) {
			//根据vin获取车辆信息
			TmVehicleDto tmVehicleDto = commonMethodService.queryVehicle(vin);
			log.info("车辆信息:{}", JSONObject.toJSONString(tmVehicleDto));
			//获取本地配置
			Boolean result = em90ServiceLeadService.queryConfig(CommonConstant.EM90_CONFIG_KEY,tmVehicleDto.getModelCode());
			//查询当前车辆在本店中是否有未结算的维修工单（非PDS）
			RepairOrderVO repairOrderVO = em90ServiceLeadService.queryUnsettledRepairOrder(vehicleEntranceVO.getDealerCode(), vin);
			log.info("工单信息:{}", JSONObject.toJSONString(repairOrderVO));
			//获取经销商姓名
			String ownerName = em90ServiceLeadService.queryDealerName(vehicleEntranceVO.getDealerCode());
			//判断工单是否为空 为空则推送企微/短信
			if (ObjectUtils.isEmpty(repairOrderVO) && result) {
				//拼接消息
				Map<String, String> map = new HashMap<>();
				map.put("vin", vin);
				map.put("license", vehicleEntranceVO.getLicensePlate());
				map.put("arrivalTime", DateUtil.formatDefaultDateTimes(vehicleEntranceVO.getEntryTime()));
				map.put("ownerCode", vehicleEntranceVO.getDealerCode());
				map.put("ownerName", ownerName);
				//推送企微
				String pushContent = em90ServiceLeadService.replacePlaceholders(map, content);
				pushQw(pushContent, vehicleEntranceVO, vin);
				//推送短信
				//获取服务经理手机号
				EmpByRoleCodeDto empByRoleCodeDto = new EmpByRoleCodeDto();
				empByRoleCodeDto.setIsOnjob(10081001);
				empByRoleCodeDto.setRoleCode(Arrays.asList("FWJL"));
				empByRoleCodeDto.setCompanyCode(vehicleEntranceVO.getDealerCode());
				List<EmpByRoleCodeDto> empByRoleCodeDtos =  commonMethodService.getEmpByRoleCodeDtos(empByRoleCodeDto);
				if (empByRoleCodeDtos!=null) {
					String phones = empByRoleCodeDtos.stream()
							.map(EmpByRoleCodeDto::getPhone)
							.filter(Objects::nonNull) // 过滤掉为null的phone
							.collect(Collectors.joining(","));
					PushMessageRecordVo pushMessageRecordVo = new PushMessageRecordVo();
					pushMessageRecordVo.setSinceType(2);
					pushMessageRecordVo.setBizNo(CommonConstant.EM90_VEHICLE_ENTRY_REMINDER);
					String subBizNo = vehicleEntranceVO.getDealerCode() + "/" + vehicleEntranceVO.getLicensePlate() + "/" + DateUtil.formatDefaultDateTimes(vehicleEntranceVO.getEntryTime()) ;
					log.info("subBizNo:{}", subBizNo);
					pushMessageRecordVo.setSubBizNo(subBizNo);
					MessageSendDto messageSendDto = new MessageSendDto();
					messageSendDto.setParamMap(map);
					messageSendDto.setMobiles(phones);
					messageSendDto.setTemplateId("14bb6ec3-685f-46b0-90ee-78b3f7f04993");
					commonMethodService.pushSms("EM90VehicleEntranceRemindConsumer", pushMessageRecordVo, messageSendDto);
				}
			}
		}
	}

	private void pushQw(String pushContent, VehicleEntranceVo vehicleEntranceVO, String vin) {
		try {
			PushMessageRecordDto pushMessageRecordDto = new PushMessageRecordDto();
			pushMessageRecordDto.setBizNo(CommonConstant.EM90_VEHICLE_ENTRY_REMINDER);
			String subBizNo = vehicleEntranceVO.getDealerCode() + "/" + vehicleEntranceVO.getLicensePlate() + "/" + DateUtil.formatDefaultDateTimes(vehicleEntranceVO.getEntryTime());
			log.info("subBizNo:{}", subBizNo);
			pushMessageRecordDto.setSubBizNo(subBizNo);
			pushMessageRecordDto.setSinceType(6);
			EmailBodyDto emailBodyDto = new EmailBodyDto();
			emailBodyDto.setVin(vin);
			pushMessageRecordDto.setReqParams(JSONObject.toJSONString(emailBodyDto));
			pushMessageRecordDto.setContent(pushContent);
			DmsResponse<String> vehicleByUserId = applicationAftersalesManagementFeign.getVehicleByUserId(vin);
			if(vehicleByUserId.isFail() && StringUtils.isBlank(vehicleByUserId.getData())){
				throw new ServiceBizException("获取服务顾问失败:{}", JSONObject.toJSONString(vehicleByUserId));
			}
			pushMessageRecordDto.setUserId(vehicleByUserId.getData());
			applicationAftersalesManagementFeign.pushMessage(pushMessageRecordDto);
		}catch (Exception e){
			log.info("EM90推送企微失败:{}",JSONObject.toJSONString(e));
		}

	}

}
