package com.volvo.maintain.infrastructure.consumer;

import com.volvo.maintain.application.maintainlead.service.Em90VehicleDeliverService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 取送车消息监听
 */
@Slf4j
@RocketMQMessageListener(nameServer = "${rocketmq.tag.name-server}",
		topic = "${rocketmq.Em90TakeSend.topic:TOPIC_MIDEND_ORDER}",
		selectorExpression = "${rocketmq.Em90TakeSend.tag:pickup_car_driver_dms}",
		consumerGroup = "${rocketmq.tag.consumer.group-em}",
		accessKey = "${rocketmq.tag.consumer.access-key}",
		secretKey = "${rocketmq.tag.consumer.secret-key}")
@Component
public class Em90TakeSendConsumer implements RocketMQListener<String> {

	@Autowired
	Em90VehicleDeliverService em90VehicleDeliverService;

	@Override
	public void onMessage(String message) {
		log.info("Em90TakeSendConsumer message:{}", message);
		if (StringUtils.isEmpty(message)) {
			return;
		}
		//em90取送车提醒
		try {
			em90VehicleDeliverService.em90Deliver(message);
		} catch (Exception e) {
			log.error("em90取送车提醒 异常", e);
		}
	}
}
