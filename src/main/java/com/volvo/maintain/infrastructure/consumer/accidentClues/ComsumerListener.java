package com.volvo.maintain.infrastructure.consumer.accidentClues;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.application.maintainlead.dto.CompanyDetailByCodeDto;
import com.volvo.maintain.application.maintainlead.dto.IsExistByCodeDto;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.AccidentClueCrmInfoMqDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.OwnerRuleDto;
import com.volvo.maintain.application.maintainlead.service.AccidentCluesOwnerRuleService;
import com.volvo.maintain.infrastructure.gateway.DmscusCustomerFeign;
import com.volvo.maintain.infrastructure.gateway.DomainMaintainLeadFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndOrgCenterFeign;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RefreshScope
@RocketMQMessageListener(topic = "${topic.liteCrm}",nameServer = "${rocketmq.name-server}",
        consumerGroup = "${rocketmq.consumer.group}",tlsEnable = "${rocketmq.consumer.tls-enable}",enableMsgTrace = true)
@Component
@Slf4j
public class ComsumerListener implements RocketMQListener<MessageExt> {
    @Autowired
    private MidEndOrgCenterFeign midEndOrgCenterFeign;
    @Autowired
    private AccidentCluesOwnerRuleService accidentCluesOwnerRuleService;
    @Autowired
    private DmscusCustomerFeign dmscusCustomerFeign;
    @Autowired
    private DomainMaintainLeadFeign domainMaintainLeadFeign;

    @Override
    public void onMessage(MessageExt t) {
        log.info("crm to newbie clue mq info:{}",t);
        String msgId = t.getMsgId();
        log.info(new Date() + " Receive message, Topic is:" + t.getTopic() + ", MsgId is:" + msgId);
        String body=new String(t.getBody());

        String accidentClue = "\"leadsType\":\"103\"";
        if (StrUtil.contains(body, accidentClue)||StrUtil.contains(body,"leadsType:103")){
            this.accidentClueMessageStorageService(body);
            return;
        }
        // 调用故障灯接口
        dmscusCustomerFeign.faultLightConsumerStatus(body);
    }

    /**
     * 监听消息 做插入
     * @param message 监听mq 消息
     * @return 返回最终结果
     */
    private Boolean accidentClueMessageStorageService(String message) {
        try {
            mqMessageIngestionService(message);
        }catch (Exception e){
            log.error("liteCrmToNewbie accident clue status Failed to synchronize data:{}", JSONObject.toJSONString(e));
            return false;
        }
        // 返回结果
        return true;
    }

    /**
     * 监听mq 消息同步数据
     * @param message 消息内容
     */
    public void mqMessageIngestionService(String message) {
        List<AccidentClueCrmInfoMqDto> parseArray = JSONArray.parseArray(message, AccidentClueCrmInfoMqDto.class);
        // 自建线索短信发送，
//        try{
//            accidentCluesOwnerRuleService.newbieClueReminder(parseArray);
//        }catch (Exception e){
//            log.info("push message failed when newbie accident clue creating");
//        }
        List<AccidentClueCrmInfoMqDto> collect = parseArray.stream().filter(item -> StringUtils.isNotBlank(item.getDealerCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(collect)){
            log.info("mqMessageIngestionService:{}", collect);
            String collect1 = collect.stream().map(AccidentClueCrmInfoMqDto::getDealerCode).collect(Collectors.joining(","));
            IsExistByCodeDto build2 = IsExistByCodeDto.builder().companyCode(collect1).build();
            ResponseDto<List<CompanyDetailByCodeDto>> listResponseDto = midEndOrgCenterFeign.selectByCompanyCode(build2);
            if (Objects.isNull(listResponseDto) || listResponseDto.isFail()) {
                log.error("调用mid-end-org-center查询经销商名称失败:{}", listResponseDto);
                throw new ServiceBizException("查询经销商失败");
            }
           if (CollectionUtils.isNotEmpty(listResponseDto.getData())) {
                parseArray.stream().forEach(item -> {
                    if(StringUtils.isNotBlank(item.getDealerCode())){
                        // 查询即将分配人
                        OwnerRuleDto ownerRuleDto = accidentCluesOwnerRuleService.selectAllocatedInfo(item.getDealerCode());
                        if(null != ownerRuleDto){
                            item.setFollowPeoPer(ownerRuleDto.getUserId());
                            item.setFollowPeoPerName(ownerRuleDto.getUserName());
                        }
                        // 赋值大区小区
                        for (CompanyDetailByCodeDto itme2:listResponseDto.getData()) {
                            if(item.getDealerCode().equals(itme2.getCompanyCode())){
                                item.setAfterBigAreaId(itme2.getAfterBigAreaId());
                                item.setAfterBigAreaName(itme2.getAfterBigAreaName());
                                item.setAfterSmallAreaId(itme2.getAfterSmallAreaId());
                                item.setAfterSmallAreaName(itme2.getAfterSmallAreaName());
                                item.setProvinceId(itme2.getProvinceId()+"");
                                item.setProvinceName(itme2.getProvinceName());
                                item.setCityId(itme2.getCityId()+"");
                                item.setCityName(itme2.getCityName());
                            }
                        }
                    }
                });
            }
            log.info("mqMessageIngestionService,查询经销商大区小区结束;{}", listResponseDto);
        }
        domainMaintainLeadFeign.updateAccidentClueCrmInfo(parseArray);
        log.info("liteCrmToNewbie accident clue status domain end");
    }
}
