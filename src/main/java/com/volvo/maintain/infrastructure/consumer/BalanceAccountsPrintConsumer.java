package com.volvo.maintain.infrastructure.consumer;

import com.alibaba.fastjson.JSON;
import com.volvo.maintain.application.maintainlead.dto.SettlementEventDto;
import com.volvo.maintain.infrastructure.gateway.DmscloudServiceFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Slf4j
@RocketMQMessageListener(nameServer = "${rocketmq.tag.name-server}",
        topic = "${rocketmq.balanceAccountsPrint.topic:TOPIC_BALANCE_ACCOUNTS_PRINT}",
        selectorExpression = "${rocketmq.balanceAccountsPrint.tag:BALANCE_ACCOUNTS_PRINT}",
            consumerGroup = "${rocketmq.balanceAccountsPrint.group:GID_ENTRANCE_RECEPTION_VEHICLE_V4}",
        accessKey = "${rocketmq.tag.consumer.access-key}",
        secretKey = "${rocketmq.tag.consumer.secret-key}")
public class BalanceAccountsPrintConsumer implements RocketMQListener<String> {
    @Autowired
    private DmscloudServiceFeign dmscloudServiceFeign;

    @Override
    public void onMessage(String message) {
        log.info("BalanceAccountsPrintConsumer onMessage :{}",message);
        if (StringUtils.isEmpty(message)){
            log.info("BalanceAccountsPrintConsumer onMessage isEmpty");
            return;
        }
        SettlementEventDto settlementEventDto = JSON.parseObject(message, SettlementEventDto.class);
        dmscloudServiceFeign.rinseBalanceAccountsPrint(settlementEventDto);
    }
}
