package com.volvo.maintain.infrastructure.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 智能缓存注解
 * 基于 signQuantity 接口的缓存机制设计
 * 
 * 特性：
 * 1. 永久缓存 + 异步刷新
 * 2. 分布式锁防止并发查询
 * 3. 降级策略保证可用性
 * 4. 支持自定义缓存键生成
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SmartCache {
    
    /**
     * 缓存键前缀
     * 最终缓存键格式：prefix + keyExpression计算结果
     */
    String keyPrefix() default "";
    
    /**
     * 缓存键表达式，支持SpEL
     * 例如：#ownerCode, #p0, #args[0] 等
     */
    String keyExpression() default "";
    
    /**
     * 缓存刷新阈值（分钟）
     * 超过此时间将触发异步刷新
     */
    long refreshThresholdMinutes() default 8L;
    
    /**
     * 分布式锁等待时间
     */
    long lockWaitTime() default 1L;
    
    /**
     * 分布式锁等待时间单位
     */
    TimeUnit lockTimeUnit() default TimeUnit.SECONDS;
    
    /**
     * 异步刷新标记过期时间（分钟）
     * 防止异步刷新任务重复执行
     */
    long refreshFlagExpireMinutes() default 1L;
    
    /**
     * 线程池Bean名称
     * 用于异步刷新任务
     */
    String threadPoolName() default "threadSignQuantity";
    
    /**
     * 是否启用降级策略
     * true: 获取锁失败时返回过期缓存
     * false: 获取锁失败时返回空结果
     */
    boolean enableFallback() default true;
    
    /**
     * 缓存数据类型
     * 用于反序列化
     */
    Class<?> cacheType() default Object.class;
}
