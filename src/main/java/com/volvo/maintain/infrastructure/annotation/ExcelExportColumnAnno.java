package com.volvo.maintain.infrastructure.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Description excel导出字段注解
 * @Date 2024/11/19 16:41
 */

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelExportColumnAnno {

    /**
     * 字段名称
     * @return
     */
    String columName() default "";

    /**
     * 字段描述
     * @return
     */
    String columDesc() default "";
}
