package com.volvo.maintain.infrastructure.cache;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 智能缓存数据包装类
 * 基于 SignQuantityCacheDTO 设计
 */
@Slf4j
@Data
public class SmartCacheData<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 缓存的实际数据
     */
    private T data;

    /**
     * 缓存创建时间
     */
    private LocalDateTime cacheTime;

    /**
     * 数据版本号（用于并发控制）
     */
    private Long version;

    /**
     * 缓存键
     */
    private String cacheKey;

    public SmartCacheData() {
        this.cacheTime = LocalDateTime.now();
        this.version = System.currentTimeMillis();
    }

    public SmartCacheData(T data, String cacheKey) {
        this.data = data;
        this.cacheKey = cacheKey;
        this.cacheTime = LocalDateTime.now();
        this.version = System.currentTimeMillis();
    }

    /**
     * 检查缓存是否需要刷新
     * @param refreshThresholdMinutes 刷新阈值（分钟）
     * @return true 需要刷新，false 不需要刷新
     */
    public boolean needsRefresh(long refreshThresholdMinutes) {
        LocalDateTime now = LocalDateTime.now();
        boolean needRefresh = cacheTime.isBefore(now.minusMinutes(refreshThresholdMinutes));
        log.debug("SmartCacheData needsRefresh check: cacheTime={}, now={}, threshold={}min, needRefresh={}", 
                cacheTime, now, refreshThresholdMinutes, needRefresh);
        return needRefresh;
    }
}
