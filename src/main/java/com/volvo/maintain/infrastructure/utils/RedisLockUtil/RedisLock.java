package com.volvo.maintain.infrastructure.utils.RedisLockUtil;

import com.yonyou.cyx.framework.compent.redis.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@Slf4j
public class RedisLock implements IDistributedLock {
    /**
     * redis client
     */
    @Autowired
    RedisClient redisClient;

    private String lockKey;                 // 锁的键值
    private int expireMsecs = 15 * 1000;    // 锁超时, 防止线程得到锁之后, 不去释放锁
    private int timeoutMsecs = 15 * 1000;   // 锁等待, 防止线程饥饿
    private boolean locked = false; // 是否已经获取锁
    private int lockWait = 200; //锁获取后等待时间

    RedisLock(String lockKey) {
        this.lockKey = lockKey;
    }

    RedisLock(String lockKey, int timeoutMsecs) {
        this.lockKey = lockKey;
        this.timeoutMsecs = timeoutMsecs;
    }

    RedisLock(String lockKey, int expireMsecs, int timeoutMsecs) {
        this.lockKey = lockKey;
        this.expireMsecs = expireMsecs;
        this.timeoutMsecs = timeoutMsecs;
    }

    RedisLock(String lockKey, int expireMsecs, int timeoutMsecs, int lockWait) {
        this.lockKey = lockKey;
        this.expireMsecs = expireMsecs;
        this.timeoutMsecs = timeoutMsecs;
        this.lockWait = lockWait;
    }

    public String getLockKey() {
        return this.lockKey;
    }

    @Override
    public synchronized boolean acquire() {
        int timeout = timeoutMsecs;

        if (redisClient == null) {
            redisClient  = SpringContextUtil.getBean(RedisClient.class);
        }

        try {
            int i = 0;
            while (timeout >= 0) {
                long expires = System.currentTimeMillis() + expireMsecs + 1;
                String expiresStr = String.valueOf(expires); // 锁到期时间
                i++;
                if (Boolean.TRUE.equals( redisClient.valueOps.setIfAbsent(lockKey, expiresStr))) {
                    //稍加等待上级事务提交
                    this.sleep();
                    locked = true;
                    log.info("[1]成功获取nx锁![{}],第[{}]次获取到", lockKey, i);
                    return true;
                }
                Object currentValueStr = redisClient.get(lockKey); // redis里的时间


                // 判断是否为空, 不为空的情况下, 如果被其他线程设置了值, 则第二个条件判断是过不去的
                if (currentValueStr != null && Long.parseLong(currentValueStr.toString()) < System.currentTimeMillis()) {
                    Object oldValueStr = redisClient.valueOps.getAndSet(lockKey, expiresStr);

                    // 获取上一个锁到期时间, 并设置现在的锁到期时间DistributedLockUtil
                    // 只有一个线程才能获取上一个线程的设置时间
                    // 如果这个时候, 多个线程恰好都到了这里, 但是只有一个线程的设置值和当前值相同, 它才有权利获取锁
                    if (oldValueStr != null && oldValueStr.equals(currentValueStr)) {
                        //稍加等待上级事务提交
                        this.sleep();
                        locked = true;
                        log.info("[2]成功获取nx锁![{}],第[{}]次获取到", lockKey, i);
                        return true;
                    }
                }

                timeout -= 100;
                Thread.sleep(100);
            }
        } catch (Exception e) {
            log.error("获取锁出现异常, 必须释放: {}", e.getMessage());
        }

        return false;
    }


    private void sleep() throws InterruptedException {
        if (lockWait > 0) {
            Thread.sleep(lockWait);
        }
    }

    @Override
    public synchronized void release() {
        log.info("开始释放nx锁!");
        if (redisClient == null) {
            redisClient = SpringContextUtil.getBean(RedisClient.class);
        }

        try {
            if (locked) {
                Object currentValueStr = redisClient.get(lockKey); // redis里的时间

                // 校验是否超过有效期, 如果不在有效期内, 那说明当前锁已经失效, 不能进行删除锁操作
                if (currentValueStr != null && Long.parseLong(currentValueStr.toString()) > System.currentTimeMillis()) {
                    redisClient.del(lockKey);
                    locked = false;
                    log.info("[3] 成功释放nx锁![{}]", lockKey);
                }
            }
        } catch (Exception e) {
            log.error("释放锁出现异常, 必须释放: {}", e.getMessage());
        }
    }

    @Override
    public boolean antiDuplication() {
        if (redisClient == null) {
            redisClient = SpringContextUtil.getBean(RedisClient.class);
        }
        try {
            long expires = System.currentTimeMillis() + expireMsecs + 1;
            String expiresStr = String.valueOf(expires); // 锁到期时间
            if (Boolean.TRUE.equals(redisClient.valueOps.setIfAbsent(lockKey, expiresStr))) {
                //稍加等待上级事务提交
                this.sleep();
                locked = true;
                log.info("[1]成功获取nx锁![{}]", lockKey);
                return true;
            }
            Object currentValueStr = redisClient.get(lockKey); // redis里的时间
            // 判断是否为空, 不为空的情况下, 如果被其他线程设置了值, 则第二个条件判断是过不去的
            if (currentValueStr != null && Long.parseLong(currentValueStr.toString()) < System.currentTimeMillis()) {
                Object oldValueStr = redisClient.valueOps.getAndSet(lockKey, expiresStr);
                // 获取上一个锁到期时间, 并设置现在的锁到期时间DistributedLockUtil
                // 只有一个线程才能获取上一个线程的设置时间
                // 如果这个时候, 多个线程恰好都到了这里, 但是只有一个线程的设置值和当前值相同, 它才有权利获取锁
                if (oldValueStr != null && oldValueStr.equals(currentValueStr)) {
                    //稍加等待上级事务提交
                    this.sleep();
                    locked = true;
                    log.info("[2]成功获取nx锁![{}]", lockKey);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("获取锁出现异常, 必须释放: {}", e.getMessage());
        }
        return false;
    }
}