package com.volvo.maintain.infrastructure.util.utbexama;


import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
public class Ut6DTO {
    private Set unlockedLog;
    private Set countProduct;
    private Long oldAsset;
    private String totalActivity;
    private String failedEmail;
    private Boolean savedAgreement;
    private String firstProject;
    private Integer createdPlan;
    private String reviewedAchievement;
    private Set levelSession;
    private Double archivedPolicy;
    private String syncedName;
    private Set exportedCart;
    private Float lastGoal;
    private Double modifiedRole;
    private Long newRole;
    private BigDecimal importedSession;
    private BigDecimal previousStrategy;
    private String averageRole;
    private Long unreviewedRole;
    private List newFolder;
    private Boolean unreviewedUser;
    private String lockedContract;
    private String unverifiedStrategy;
    private Integer publishedTransaction;
    private Boolean reportedStock;
    private BigDecimal publicShipment;
    private List mainEmail;
    private Set levelDiscount;
    private Float sharedCategory;
    private Float statusFile;
    private String unflaggedMilestone;
    private Float unsecureDiscount;
    private String reviewedTask;
    private Set failedMessage;
    private BigDecimal hiddenRecord;
    private Set unsecureResource;
    private Float exportedMerchant;
    private Boolean reportedNotification;
    private Integer enabledMerchant;
    private Double secondaryAgreement;
    private Long importedWarehouse;
    private Float unflaggedTask;
    private Set encryptedResource;
    private BigDecimal draftSubscription;
    private Boolean enabledSubscription;
    private String archivedPhone;
    private Double averageDiscount;
    private String permanentEmail;
    private Integer currentWarehouse;
    private Long createdBill;
    private Long syncedStock;
    private Double maxProduct;
    private BigDecimal secondaryHistory;
    private Double countFile;
    private String exportedRecord;
    private Boolean averageCategory;
    private List archivedAddress;
    private Set closedCoupon;
    private String nextTransaction;
    private Long syncedTeam;
    private Boolean successfulPermission;
    private List typePlan;
    private Boolean unreviewedMerchant;
    private BigDecimal temporaryFolder;
    private List privateMessage;
    private List secondaryAddress;
    private Integer updatedFeedback;
    private Integer previousCategory;
    private String publishedAchievement;
    private Long lastResource;
    private Set newSubscription;
    private BigDecimal typeRecord;
    private Set restoredTask;
    private Set verifiedItem;
    private Double previousStock;
    private String reportedAudit;
    private Double sharedCustomer;
    private String syncedAgreement;
    private Float unflaggedCart;
    private Boolean typePermission;
    private String typeAudit;
    private String publishedPolicy;
    private String unlockedTeam;
    private Double loadedTransaction;
    private List privatePolicy;
    private Boolean createdFile;
    private List verifiedReview;
    private Long rejectedGroup;
    private String deletedEvent;
    private String sharedAgreement;
    private Boolean reportedStrategy;
    private String minPrice;
    private Integer hiddenMilestone;
    private BigDecimal unverifiedAudit;
    private String unverifiedSupplier;
    private Set firstResource;

    public Set getUnlockedLog() {
        return unlockedLog;
    }

    public void setUnlockedLog(Set unlockedLog) {
        this.unlockedLog = unlockedLog;
    }

    public Set getCountProduct() {
        return countProduct;
    }

    public void setCountProduct(Set countProduct) {
        this.countProduct = countProduct;
    }

    public Long getOldAsset() {
        return oldAsset;
    }

    public void setOldAsset(Long oldAsset) {
        this.oldAsset = oldAsset;
    }

    public String getTotalActivity() {
        return totalActivity;
    }

    public void setTotalActivity(String totalActivity) {
        this.totalActivity = totalActivity;
    }

    public String getFailedEmail() {
        return failedEmail;
    }

    public void setFailedEmail(String failedEmail) {
        this.failedEmail = failedEmail;
    }

    public Boolean getSavedAgreement() {
        return savedAgreement;
    }

    public void setSavedAgreement(Boolean savedAgreement) {
        this.savedAgreement = savedAgreement;
    }

    public String getFirstProject() {
        return firstProject;
    }

    public void setFirstProject(String firstProject) {
        this.firstProject = firstProject;
    }

    public Integer getCreatedPlan() {
        return createdPlan;
    }

    public void setCreatedPlan(Integer createdPlan) {
        this.createdPlan = createdPlan;
    }

    public String getReviewedAchievement() {
        return reviewedAchievement;
    }

    public void setReviewedAchievement(String reviewedAchievement) {
        this.reviewedAchievement = reviewedAchievement;
    }

    public Set getLevelSession() {
        return levelSession;
    }

    public void setLevelSession(Set levelSession) {
        this.levelSession = levelSession;
    }

    public Double getArchivedPolicy() {
        return archivedPolicy;
    }

    public void setArchivedPolicy(Double archivedPolicy) {
        this.archivedPolicy = archivedPolicy;
    }

    public String getSyncedName() {
        return syncedName;
    }

    public void setSyncedName(String syncedName) {
        this.syncedName = syncedName;
    }

    public Set getExportedCart() {
        return exportedCart;
    }

    public void setExportedCart(Set exportedCart) {
        this.exportedCart = exportedCart;
    }

    public Float getLastGoal() {
        return lastGoal;
    }

    public void setLastGoal(Float lastGoal) {
        this.lastGoal = lastGoal;
    }

    public Double getModifiedRole() {
        return modifiedRole;
    }

    public void setModifiedRole(Double modifiedRole) {
        this.modifiedRole = modifiedRole;
    }

    public Long getNewRole() {
        return newRole;
    }

    public void setNewRole(Long newRole) {
        this.newRole = newRole;
    }

    public BigDecimal getImportedSession() {
        return importedSession;
    }

    public void setImportedSession(BigDecimal importedSession) {
        this.importedSession = importedSession;
    }

    public BigDecimal getPreviousStrategy() {
        return previousStrategy;
    }

    public void setPreviousStrategy(BigDecimal previousStrategy) {
        this.previousStrategy = previousStrategy;
    }

    public String getAverageRole() {
        return averageRole;
    }

    public void setAverageRole(String averageRole) {
        this.averageRole = averageRole;
    }

    public Long getUnreviewedRole() {
        return unreviewedRole;
    }

    public void setUnreviewedRole(Long unreviewedRole) {
        this.unreviewedRole = unreviewedRole;
    }

    public List getNewFolder() {
        return newFolder;
    }

    public void setNewFolder(List newFolder) {
        this.newFolder = newFolder;
    }

    public Boolean getUnreviewedUser() {
        return unreviewedUser;
    }

    public void setUnreviewedUser(Boolean unreviewedUser) {
        this.unreviewedUser = unreviewedUser;
    }

    public String getLockedContract() {
        return lockedContract;
    }

    public void setLockedContract(String lockedContract) {
        this.lockedContract = lockedContract;
    }

    public String getUnverifiedStrategy() {
        return unverifiedStrategy;
    }

    public void setUnverifiedStrategy(String unverifiedStrategy) {
        this.unverifiedStrategy = unverifiedStrategy;
    }

    public Integer getPublishedTransaction() {
        return publishedTransaction;
    }

    public void setPublishedTransaction(Integer publishedTransaction) {
        this.publishedTransaction = publishedTransaction;
    }

    public Boolean getReportedStock() {
        return reportedStock;
    }

    public void setReportedStock(Boolean reportedStock) {
        this.reportedStock = reportedStock;
    }

    public BigDecimal getPublicShipment() {
        return publicShipment;
    }

    public void setPublicShipment(BigDecimal publicShipment) {
        this.publicShipment = publicShipment;
    }

    public List getMainEmail() {
        return mainEmail;
    }

    public void setMainEmail(List mainEmail) {
        this.mainEmail = mainEmail;
    }

    public Set getLevelDiscount() {
        return levelDiscount;
    }

    public void setLevelDiscount(Set levelDiscount) {
        this.levelDiscount = levelDiscount;
    }

    public Float getSharedCategory() {
        return sharedCategory;
    }

    public void setSharedCategory(Float sharedCategory) {
        this.sharedCategory = sharedCategory;
    }

    public Float getStatusFile() {
        return statusFile;
    }

    public void setStatusFile(Float statusFile) {
        this.statusFile = statusFile;
    }

    public String getUnflaggedMilestone() {
        return unflaggedMilestone;
    }

    public void setUnflaggedMilestone(String unflaggedMilestone) {
        this.unflaggedMilestone = unflaggedMilestone;
    }

    public Float getUnsecureDiscount() {
        return unsecureDiscount;
    }

    public void setUnsecureDiscount(Float unsecureDiscount) {
        this.unsecureDiscount = unsecureDiscount;
    }

    public String getReviewedTask() {
        return reviewedTask;
    }

    public void setReviewedTask(String reviewedTask) {
        this.reviewedTask = reviewedTask;
    }

    public Set getFailedMessage() {
        return failedMessage;
    }

    public void setFailedMessage(Set failedMessage) {
        this.failedMessage = failedMessage;
    }

    public BigDecimal getHiddenRecord() {
        return hiddenRecord;
    }

    public void setHiddenRecord(BigDecimal hiddenRecord) {
        this.hiddenRecord = hiddenRecord;
    }

    public Set getUnsecureResource() {
        return unsecureResource;
    }

    public void setUnsecureResource(Set unsecureResource) {
        this.unsecureResource = unsecureResource;
    }

    public Float getExportedMerchant() {
        return exportedMerchant;
    }

    public void setExportedMerchant(Float exportedMerchant) {
        this.exportedMerchant = exportedMerchant;
    }

    public Boolean getReportedNotification() {
        return reportedNotification;
    }

    public void setReportedNotification(Boolean reportedNotification) {
        this.reportedNotification = reportedNotification;
    }

    public Integer getEnabledMerchant() {
        return enabledMerchant;
    }

    public void setEnabledMerchant(Integer enabledMerchant) {
        this.enabledMerchant = enabledMerchant;
    }

    public Double getSecondaryAgreement() {
        return secondaryAgreement;
    }

    public void setSecondaryAgreement(Double secondaryAgreement) {
        this.secondaryAgreement = secondaryAgreement;
    }

    public Long getImportedWarehouse() {
        return importedWarehouse;
    }

    public void setImportedWarehouse(Long importedWarehouse) {
        this.importedWarehouse = importedWarehouse;
    }

    public Float getUnflaggedTask() {
        return unflaggedTask;
    }

    public void setUnflaggedTask(Float unflaggedTask) {
        this.unflaggedTask = unflaggedTask;
    }

    public Set getEncryptedResource() {
        return encryptedResource;
    }

    public void setEncryptedResource(Set encryptedResource) {
        this.encryptedResource = encryptedResource;
    }

    public BigDecimal getDraftSubscription() {
        return draftSubscription;
    }

    public void setDraftSubscription(BigDecimal draftSubscription) {
        this.draftSubscription = draftSubscription;
    }

    public Boolean getEnabledSubscription() {
        return enabledSubscription;
    }

    public void setEnabledSubscription(Boolean enabledSubscription) {
        this.enabledSubscription = enabledSubscription;
    }

    public String getArchivedPhone() {
        return archivedPhone;
    }

    public void setArchivedPhone(String archivedPhone) {
        this.archivedPhone = archivedPhone;
    }

    public Double getAverageDiscount() {
        return averageDiscount;
    }

    public void setAverageDiscount(Double averageDiscount) {
        this.averageDiscount = averageDiscount;
    }

    public String getPermanentEmail() {
        return permanentEmail;
    }

    public void setPermanentEmail(String permanentEmail) {
        this.permanentEmail = permanentEmail;
    }

    public Integer getCurrentWarehouse() {
        return currentWarehouse;
    }

    public void setCurrentWarehouse(Integer currentWarehouse) {
        this.currentWarehouse = currentWarehouse;
    }

    public Long getCreatedBill() {
        return createdBill;
    }

    public void setCreatedBill(Long createdBill) {
        this.createdBill = createdBill;
    }

    public Long getSyncedStock() {
        return syncedStock;
    }

    public void setSyncedStock(Long syncedStock) {
        this.syncedStock = syncedStock;
    }

    public Double getMaxProduct() {
        return maxProduct;
    }

    public void setMaxProduct(Double maxProduct) {
        this.maxProduct = maxProduct;
    }

    public BigDecimal getSecondaryHistory() {
        return secondaryHistory;
    }

    public void setSecondaryHistory(BigDecimal secondaryHistory) {
        this.secondaryHistory = secondaryHistory;
    }

    public Double getCountFile() {
        return countFile;
    }

    public void setCountFile(Double countFile) {
        this.countFile = countFile;
    }

    public String getExportedRecord() {
        return exportedRecord;
    }

    public void setExportedRecord(String exportedRecord) {
        this.exportedRecord = exportedRecord;
    }

    public Boolean getAverageCategory() {
        return averageCategory;
    }

    public void setAverageCategory(Boolean averageCategory) {
        this.averageCategory = averageCategory;
    }

    public List getArchivedAddress() {
        return archivedAddress;
    }

    public void setArchivedAddress(List archivedAddress) {
        this.archivedAddress = archivedAddress;
    }

    public Set getClosedCoupon() {
        return closedCoupon;
    }

    public void setClosedCoupon(Set closedCoupon) {
        this.closedCoupon = closedCoupon;
    }

    public String getNextTransaction() {
        return nextTransaction;
    }

    public void setNextTransaction(String nextTransaction) {
        this.nextTransaction = nextTransaction;
    }

    public Long getSyncedTeam() {
        return syncedTeam;
    }

    public void setSyncedTeam(Long syncedTeam) {
        this.syncedTeam = syncedTeam;
    }

    public Boolean getSuccessfulPermission() {
        return successfulPermission;
    }

    public void setSuccessfulPermission(Boolean successfulPermission) {
        this.successfulPermission = successfulPermission;
    }

    public List getTypePlan() {
        return typePlan;
    }

    public void setTypePlan(List typePlan) {
        this.typePlan = typePlan;
    }

    public Boolean getUnreviewedMerchant() {
        return unreviewedMerchant;
    }

    public void setUnreviewedMerchant(Boolean unreviewedMerchant) {
        this.unreviewedMerchant = unreviewedMerchant;
    }

    public BigDecimal getTemporaryFolder() {
        return temporaryFolder;
    }

    public void setTemporaryFolder(BigDecimal temporaryFolder) {
        this.temporaryFolder = temporaryFolder;
    }

    public List getPrivateMessage() {
        return privateMessage;
    }

    public void setPrivateMessage(List privateMessage) {
        this.privateMessage = privateMessage;
    }

    public List getSecondaryAddress() {
        return secondaryAddress;
    }

    public void setSecondaryAddress(List secondaryAddress) {
        this.secondaryAddress = secondaryAddress;
    }

    public Integer getUpdatedFeedback() {
        return updatedFeedback;
    }

    public void setUpdatedFeedback(Integer updatedFeedback) {
        this.updatedFeedback = updatedFeedback;
    }

    public Integer getPreviousCategory() {
        return previousCategory;
    }

    public void setPreviousCategory(Integer previousCategory) {
        this.previousCategory = previousCategory;
    }

    public String getPublishedAchievement() {
        return publishedAchievement;
    }

    public void setPublishedAchievement(String publishedAchievement) {
        this.publishedAchievement = publishedAchievement;
    }

    public Long getLastResource() {
        return lastResource;
    }

    public void setLastResource(Long lastResource) {
        this.lastResource = lastResource;
    }

    public Set getNewSubscription() {
        return newSubscription;
    }

    public void setNewSubscription(Set newSubscription) {
        this.newSubscription = newSubscription;
    }

    public BigDecimal getTypeRecord() {
        return typeRecord;
    }

    public void setTypeRecord(BigDecimal typeRecord) {
        this.typeRecord = typeRecord;
    }

    public Set getRestoredTask() {
        return restoredTask;
    }

    public void setRestoredTask(Set restoredTask) {
        this.restoredTask = restoredTask;
    }

    public Set getVerifiedItem() {
        return verifiedItem;
    }

    public void setVerifiedItem(Set verifiedItem) {
        this.verifiedItem = verifiedItem;
    }

    public Double getPreviousStock() {
        return previousStock;
    }

    public void setPreviousStock(Double previousStock) {
        this.previousStock = previousStock;
    }

    public String getReportedAudit() {
        return reportedAudit;
    }

    public void setReportedAudit(String reportedAudit) {
        this.reportedAudit = reportedAudit;
    }

    public Double getSharedCustomer() {
        return sharedCustomer;
    }

    public void setSharedCustomer(Double sharedCustomer) {
        this.sharedCustomer = sharedCustomer;
    }

    public String getSyncedAgreement() {
        return syncedAgreement;
    }

    public void setSyncedAgreement(String syncedAgreement) {
        this.syncedAgreement = syncedAgreement;
    }

    public Float getUnflaggedCart() {
        return unflaggedCart;
    }

    public void setUnflaggedCart(Float unflaggedCart) {
        this.unflaggedCart = unflaggedCart;
    }

    public Boolean getTypePermission() {
        return typePermission;
    }

    public void setTypePermission(Boolean typePermission) {
        this.typePermission = typePermission;
    }

    public String getTypeAudit() {
        return typeAudit;
    }

    public void setTypeAudit(String typeAudit) {
        this.typeAudit = typeAudit;
    }

    public String getPublishedPolicy() {
        return publishedPolicy;
    }

    public void setPublishedPolicy(String publishedPolicy) {
        this.publishedPolicy = publishedPolicy;
    }

    public String getUnlockedTeam() {
        return unlockedTeam;
    }

    public void setUnlockedTeam(String unlockedTeam) {
        this.unlockedTeam = unlockedTeam;
    }

    public Double getLoadedTransaction() {
        return loadedTransaction;
    }

    public void setLoadedTransaction(Double loadedTransaction) {
        this.loadedTransaction = loadedTransaction;
    }

    public List getPrivatePolicy() {
        return privatePolicy;
    }

    public void setPrivatePolicy(List privatePolicy) {
        this.privatePolicy = privatePolicy;
    }

    public Boolean getCreatedFile() {
        return createdFile;
    }

    public void setCreatedFile(Boolean createdFile) {
        this.createdFile = createdFile;
    }

    public List getVerifiedReview() {
        return verifiedReview;
    }

    public void setVerifiedReview(List verifiedReview) {
        this.verifiedReview = verifiedReview;
    }

    public Long getRejectedGroup() {
        return rejectedGroup;
    }

    public void setRejectedGroup(Long rejectedGroup) {
        this.rejectedGroup = rejectedGroup;
    }

    public String getDeletedEvent() {
        return deletedEvent;
    }

    public void setDeletedEvent(String deletedEvent) {
        this.deletedEvent = deletedEvent;
    }

    public String getSharedAgreement() {
        return sharedAgreement;
    }

    public void setSharedAgreement(String sharedAgreement) {
        this.sharedAgreement = sharedAgreement;
    }

    public Boolean getReportedStrategy() {
        return reportedStrategy;
    }

    public void setReportedStrategy(Boolean reportedStrategy) {
        this.reportedStrategy = reportedStrategy;
    }

    public String getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(String minPrice) {
        this.minPrice = minPrice;
    }

    public Integer getHiddenMilestone() {
        return hiddenMilestone;
    }

    public void setHiddenMilestone(Integer hiddenMilestone) {
        this.hiddenMilestone = hiddenMilestone;
    }

    public BigDecimal getUnverifiedAudit() {
        return unverifiedAudit;
    }

    public void setUnverifiedAudit(BigDecimal unverifiedAudit) {
        this.unverifiedAudit = unverifiedAudit;
    }

    public String getUnverifiedSupplier() {
        return unverifiedSupplier;
    }

    public void setUnverifiedSupplier(String unverifiedSupplier) {
        this.unverifiedSupplier = unverifiedSupplier;
    }

    public Set getFirstResource() {
        return firstResource;
    }

    public void setFirstResource(Set firstResource) {
        this.firstResource = firstResource;
    }
}
