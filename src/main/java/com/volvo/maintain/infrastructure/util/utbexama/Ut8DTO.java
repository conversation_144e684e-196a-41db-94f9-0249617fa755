package com.volvo.maintain.infrastructure.util.utbexama;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
public class Ut8DTO {
    private Set syncedItem;
    private String restoredAchievement;
    private Long verifiedCoupon;
    private Double secondaryTask;
    private Long inactivePhone;
    private Long draftEvent;
    private Float mainAddress;
    private Boolean totalReport;
    private Set maxStock;
    private Set minDate;
    private String hiddenEmail;
    private Boolean secureFile;
    private String unlockedSchedule;
    private Long restoredGoal;
    private Double minPhone;
    private Set temporarySession;
    private Long syncedBill;
    private List reviewedName;
    private Float enabledShipment;
    private Integer publishedRecord;
    private String newItem;
    private Double enabledDocument;
    private Double lastReview;
    private Boolean modifiedAccount;
    private String levelCustomer;
    private Double totalUser;
    private Long unsecurePayment;
    private Integer createdContract;
    private Float privateBrand;
    private BigDecimal lockedReport;
    private String pendingNotification;
    private BigDecimal oldCart;
    private Long firstRecord;
    private Set closedEvent;
    private Long closedProject;
    private String typePrice;
    private Long createdPlan;
    private Long pendingAchievement;
    private Boolean oldDocument;
    private String initialGoal;
    private Double verifiedDocument;
    private Float unsecureBill;
    private List permanentAgreement;
    private Float firstRole;
    private Set maxAddress;
    private String publicShipment;
    private Set temporaryAsset;
    private Set privateEmail;
    private Set primaryPermission;
    private Float mainDepartment;
    private Boolean totalProduct;
    private List maxProduct;
    private Integer secureGoal;
    private Integer completedItem;
    private Long decryptedDepartment;
    private Set exportedDocument;
    private Integer restoredReport;
    private Boolean archivedLog;
    private Float oldAchievement;
    private Boolean minPrice;
    private String savedFile;
    private List exportedBill;
    private BigDecimal lockedGroup;
    private String openMessage;
    private String closedDiscount;
    private Integer firstAchievement;
    private String inactiveReport;
    private Float levelAudit;
    private List temporaryStock;
    private BigDecimal closedActivity;
    private Double encryptedCategory;
    private Set loadedDocument;
    private List verifiedEmail;
    private String unverifiedGoal;
    private String unlockedCart;
    private BigDecimal exportedAddress;
    private Boolean maxAsset;
    private Long openPlan;
    private String primaryItem;
    private String restoredNotification;
    private Set completedEvent;
    private Long firstWarehouse;
    private Boolean initialMerchant;
    private Integer archivedCoupon;
    private Integer lastFolder;
    private Integer secureMerchant;
    private Integer importedPlan;
    private Float archivedPolicy;
    private Integer reviewedGoal;
    private List disabledMessage;
    private BigDecimal verifiedMerchant;
    private Long countMilestone;
    private Boolean temporaryPlan;
    private List completedDiscount;
    private Double temporaryPermission;
    private Boolean oldRole;
    private String sharedRole;
    private Long failedGoal;
    private Float loadedAgreement;

    public Set getSyncedItem() {
        return syncedItem;
    }

    public void setSyncedItem(Set syncedItem) {
        this.syncedItem = syncedItem;
    }

    public String getRestoredAchievement() {
        return restoredAchievement;
    }

    public void setRestoredAchievement(String restoredAchievement) {
        this.restoredAchievement = restoredAchievement;
    }

    public Long getVerifiedCoupon() {
        return verifiedCoupon;
    }

    public void setVerifiedCoupon(Long verifiedCoupon) {
        this.verifiedCoupon = verifiedCoupon;
    }

    public Double getSecondaryTask() {
        return secondaryTask;
    }

    public void setSecondaryTask(Double secondaryTask) {
        this.secondaryTask = secondaryTask;
    }

    public Long getInactivePhone() {
        return inactivePhone;
    }

    public void setInactivePhone(Long inactivePhone) {
        this.inactivePhone = inactivePhone;
    }

    public Long getDraftEvent() {
        return draftEvent;
    }

    public void setDraftEvent(Long draftEvent) {
        this.draftEvent = draftEvent;
    }

    public Float getMainAddress() {
        return mainAddress;
    }

    public void setMainAddress(Float mainAddress) {
        this.mainAddress = mainAddress;
    }

    public Boolean getTotalReport() {
        return totalReport;
    }

    public void setTotalReport(Boolean totalReport) {
        this.totalReport = totalReport;
    }

    public Set getMaxStock() {
        return maxStock;
    }

    public void setMaxStock(Set maxStock) {
        this.maxStock = maxStock;
    }

    public Set getMinDate() {
        return minDate;
    }

    public void setMinDate(Set minDate) {
        this.minDate = minDate;
    }

    public String getHiddenEmail() {
        return hiddenEmail;
    }

    public void setHiddenEmail(String hiddenEmail) {
        this.hiddenEmail = hiddenEmail;
    }

    public Boolean getSecureFile() {
        return secureFile;
    }

    public void setSecureFile(Boolean secureFile) {
        this.secureFile = secureFile;
    }

    public String getUnlockedSchedule() {
        return unlockedSchedule;
    }

    public void setUnlockedSchedule(String unlockedSchedule) {
        this.unlockedSchedule = unlockedSchedule;
    }

    public Long getRestoredGoal() {
        return restoredGoal;
    }

    public void setRestoredGoal(Long restoredGoal) {
        this.restoredGoal = restoredGoal;
    }

    public Double getMinPhone() {
        return minPhone;
    }

    public void setMinPhone(Double minPhone) {
        this.minPhone = minPhone;
    }

    public Set getTemporarySession() {
        return temporarySession;
    }

    public void setTemporarySession(Set temporarySession) {
        this.temporarySession = temporarySession;
    }

    public Long getSyncedBill() {
        return syncedBill;
    }

    public void setSyncedBill(Long syncedBill) {
        this.syncedBill = syncedBill;
    }

    public List getReviewedName() {
        return reviewedName;
    }

    public void setReviewedName(List reviewedName) {
        this.reviewedName = reviewedName;
    }

    public Float getEnabledShipment() {
        return enabledShipment;
    }

    public void setEnabledShipment(Float enabledShipment) {
        this.enabledShipment = enabledShipment;
    }

    public Integer getPublishedRecord() {
        return publishedRecord;
    }

    public void setPublishedRecord(Integer publishedRecord) {
        this.publishedRecord = publishedRecord;
    }

    public String getNewItem() {
        return newItem;
    }

    public void setNewItem(String newItem) {
        this.newItem = newItem;
    }

    public Double getEnabledDocument() {
        return enabledDocument;
    }

    public void setEnabledDocument(Double enabledDocument) {
        this.enabledDocument = enabledDocument;
    }

    public Double getLastReview() {
        return lastReview;
    }

    public void setLastReview(Double lastReview) {
        this.lastReview = lastReview;
    }

    public Boolean getModifiedAccount() {
        return modifiedAccount;
    }

    public void setModifiedAccount(Boolean modifiedAccount) {
        this.modifiedAccount = modifiedAccount;
    }

    public String getLevelCustomer() {
        return levelCustomer;
    }

    public void setLevelCustomer(String levelCustomer) {
        this.levelCustomer = levelCustomer;
    }

    public Double getTotalUser() {
        return totalUser;
    }

    public void setTotalUser(Double totalUser) {
        this.totalUser = totalUser;
    }

    public Long getUnsecurePayment() {
        return unsecurePayment;
    }

    public void setUnsecurePayment(Long unsecurePayment) {
        this.unsecurePayment = unsecurePayment;
    }

    public Integer getCreatedContract() {
        return createdContract;
    }

    public void setCreatedContract(Integer createdContract) {
        this.createdContract = createdContract;
    }

    public Float getPrivateBrand() {
        return privateBrand;
    }

    public void setPrivateBrand(Float privateBrand) {
        this.privateBrand = privateBrand;
    }

    public BigDecimal getLockedReport() {
        return lockedReport;
    }

    public void setLockedReport(BigDecimal lockedReport) {
        this.lockedReport = lockedReport;
    }

    public String getPendingNotification() {
        return pendingNotification;
    }

    public void setPendingNotification(String pendingNotification) {
        this.pendingNotification = pendingNotification;
    }

    public BigDecimal getOldCart() {
        return oldCart;
    }

    public void setOldCart(BigDecimal oldCart) {
        this.oldCart = oldCart;
    }

    public Long getFirstRecord() {
        return firstRecord;
    }

    public void setFirstRecord(Long firstRecord) {
        this.firstRecord = firstRecord;
    }

    public Set getClosedEvent() {
        return closedEvent;
    }

    public void setClosedEvent(Set closedEvent) {
        this.closedEvent = closedEvent;
    }

    public Long getClosedProject() {
        return closedProject;
    }

    public void setClosedProject(Long closedProject) {
        this.closedProject = closedProject;
    }

    public String getTypePrice() {
        return typePrice;
    }

    public void setTypePrice(String typePrice) {
        this.typePrice = typePrice;
    }

    public Long getCreatedPlan() {
        return createdPlan;
    }

    public void setCreatedPlan(Long createdPlan) {
        this.createdPlan = createdPlan;
    }

    public Long getPendingAchievement() {
        return pendingAchievement;
    }

    public void setPendingAchievement(Long pendingAchievement) {
        this.pendingAchievement = pendingAchievement;
    }

    public Boolean getOldDocument() {
        return oldDocument;
    }

    public void setOldDocument(Boolean oldDocument) {
        this.oldDocument = oldDocument;
    }

    public String getInitialGoal() {
        return initialGoal;
    }

    public void setInitialGoal(String initialGoal) {
        this.initialGoal = initialGoal;
    }

    public Double getVerifiedDocument() {
        return verifiedDocument;
    }

    public void setVerifiedDocument(Double verifiedDocument) {
        this.verifiedDocument = verifiedDocument;
    }

    public Float getUnsecureBill() {
        return unsecureBill;
    }

    public void setUnsecureBill(Float unsecureBill) {
        this.unsecureBill = unsecureBill;
    }

    public List getPermanentAgreement() {
        return permanentAgreement;
    }

    public void setPermanentAgreement(List permanentAgreement) {
        this.permanentAgreement = permanentAgreement;
    }

    public Float getFirstRole() {
        return firstRole;
    }

    public void setFirstRole(Float firstRole) {
        this.firstRole = firstRole;
    }

    public Set getMaxAddress() {
        return maxAddress;
    }

    public void setMaxAddress(Set maxAddress) {
        this.maxAddress = maxAddress;
    }

    public String getPublicShipment() {
        return publicShipment;
    }

    public void setPublicShipment(String publicShipment) {
        this.publicShipment = publicShipment;
    }

    public Set getTemporaryAsset() {
        return temporaryAsset;
    }

    public void setTemporaryAsset(Set temporaryAsset) {
        this.temporaryAsset = temporaryAsset;
    }

    public Set getPrivateEmail() {
        return privateEmail;
    }

    public void setPrivateEmail(Set privateEmail) {
        this.privateEmail = privateEmail;
    }

    public Set getPrimaryPermission() {
        return primaryPermission;
    }

    public void setPrimaryPermission(Set primaryPermission) {
        this.primaryPermission = primaryPermission;
    }

    public Float getMainDepartment() {
        return mainDepartment;
    }

    public void setMainDepartment(Float mainDepartment) {
        this.mainDepartment = mainDepartment;
    }

    public Boolean getTotalProduct() {
        return totalProduct;
    }

    public void setTotalProduct(Boolean totalProduct) {
        this.totalProduct = totalProduct;
    }

    public List getMaxProduct() {
        return maxProduct;
    }

    public void setMaxProduct(List maxProduct) {
        this.maxProduct = maxProduct;
    }

    public Integer getSecureGoal() {
        return secureGoal;
    }

    public void setSecureGoal(Integer secureGoal) {
        this.secureGoal = secureGoal;
    }

    public Integer getCompletedItem() {
        return completedItem;
    }

    public void setCompletedItem(Integer completedItem) {
        this.completedItem = completedItem;
    }

    public Long getDecryptedDepartment() {
        return decryptedDepartment;
    }

    public void setDecryptedDepartment(Long decryptedDepartment) {
        this.decryptedDepartment = decryptedDepartment;
    }

    public Set getExportedDocument() {
        return exportedDocument;
    }

    public void setExportedDocument(Set exportedDocument) {
        this.exportedDocument = exportedDocument;
    }

    public Integer getRestoredReport() {
        return restoredReport;
    }

    public void setRestoredReport(Integer restoredReport) {
        this.restoredReport = restoredReport;
    }

    public Boolean getArchivedLog() {
        return archivedLog;
    }

    public void setArchivedLog(Boolean archivedLog) {
        this.archivedLog = archivedLog;
    }

    public Float getOldAchievement() {
        return oldAchievement;
    }

    public void setOldAchievement(Float oldAchievement) {
        this.oldAchievement = oldAchievement;
    }

    public Boolean getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(Boolean minPrice) {
        this.minPrice = minPrice;
    }

    public String getSavedFile() {
        return savedFile;
    }

    public void setSavedFile(String savedFile) {
        this.savedFile = savedFile;
    }

    public List getExportedBill() {
        return exportedBill;
    }

    public void setExportedBill(List exportedBill) {
        this.exportedBill = exportedBill;
    }

    public BigDecimal getLockedGroup() {
        return lockedGroup;
    }

    public void setLockedGroup(BigDecimal lockedGroup) {
        this.lockedGroup = lockedGroup;
    }

    public String getOpenMessage() {
        return openMessage;
    }

    public void setOpenMessage(String openMessage) {
        this.openMessage = openMessage;
    }

    public String getClosedDiscount() {
        return closedDiscount;
    }

    public void setClosedDiscount(String closedDiscount) {
        this.closedDiscount = closedDiscount;
    }

    public Integer getFirstAchievement() {
        return firstAchievement;
    }

    public void setFirstAchievement(Integer firstAchievement) {
        this.firstAchievement = firstAchievement;
    }

    public String getInactiveReport() {
        return inactiveReport;
    }

    public void setInactiveReport(String inactiveReport) {
        this.inactiveReport = inactiveReport;
    }

    public Float getLevelAudit() {
        return levelAudit;
    }

    public void setLevelAudit(Float levelAudit) {
        this.levelAudit = levelAudit;
    }

    public List getTemporaryStock() {
        return temporaryStock;
    }

    public void setTemporaryStock(List temporaryStock) {
        this.temporaryStock = temporaryStock;
    }

    public BigDecimal getClosedActivity() {
        return closedActivity;
    }

    public void setClosedActivity(BigDecimal closedActivity) {
        this.closedActivity = closedActivity;
    }

    public Double getEncryptedCategory() {
        return encryptedCategory;
    }

    public void setEncryptedCategory(Double encryptedCategory) {
        this.encryptedCategory = encryptedCategory;
    }

    public Set getLoadedDocument() {
        return loadedDocument;
    }

    public void setLoadedDocument(Set loadedDocument) {
        this.loadedDocument = loadedDocument;
    }

    public List getVerifiedEmail() {
        return verifiedEmail;
    }

    public void setVerifiedEmail(List verifiedEmail) {
        this.verifiedEmail = verifiedEmail;
    }

    public String getUnverifiedGoal() {
        return unverifiedGoal;
    }

    public void setUnverifiedGoal(String unverifiedGoal) {
        this.unverifiedGoal = unverifiedGoal;
    }

    public String getUnlockedCart() {
        return unlockedCart;
    }

    public void setUnlockedCart(String unlockedCart) {
        this.unlockedCart = unlockedCart;
    }

    public BigDecimal getExportedAddress() {
        return exportedAddress;
    }

    public void setExportedAddress(BigDecimal exportedAddress) {
        this.exportedAddress = exportedAddress;
    }

    public Boolean getMaxAsset() {
        return maxAsset;
    }

    public void setMaxAsset(Boolean maxAsset) {
        this.maxAsset = maxAsset;
    }

    public Long getOpenPlan() {
        return openPlan;
    }

    public void setOpenPlan(Long openPlan) {
        this.openPlan = openPlan;
    }

    public String getPrimaryItem() {
        return primaryItem;
    }

    public void setPrimaryItem(String primaryItem) {
        this.primaryItem = primaryItem;
    }

    public String getRestoredNotification() {
        return restoredNotification;
    }

    public void setRestoredNotification(String restoredNotification) {
        this.restoredNotification = restoredNotification;
    }

    public Set getCompletedEvent() {
        return completedEvent;
    }

    public void setCompletedEvent(Set completedEvent) {
        this.completedEvent = completedEvent;
    }

    public Long getFirstWarehouse() {
        return firstWarehouse;
    }

    public void setFirstWarehouse(Long firstWarehouse) {
        this.firstWarehouse = firstWarehouse;
    }

    public Boolean getInitialMerchant() {
        return initialMerchant;
    }

    public void setInitialMerchant(Boolean initialMerchant) {
        this.initialMerchant = initialMerchant;
    }

    public Integer getArchivedCoupon() {
        return archivedCoupon;
    }

    public void setArchivedCoupon(Integer archivedCoupon) {
        this.archivedCoupon = archivedCoupon;
    }

    public Integer getLastFolder() {
        return lastFolder;
    }

    public void setLastFolder(Integer lastFolder) {
        this.lastFolder = lastFolder;
    }

    public Integer getSecureMerchant() {
        return secureMerchant;
    }

    public void setSecureMerchant(Integer secureMerchant) {
        this.secureMerchant = secureMerchant;
    }

    public Integer getImportedPlan() {
        return importedPlan;
    }

    public void setImportedPlan(Integer importedPlan) {
        this.importedPlan = importedPlan;
    }

    public Float getArchivedPolicy() {
        return archivedPolicy;
    }

    public void setArchivedPolicy(Float archivedPolicy) {
        this.archivedPolicy = archivedPolicy;
    }

    public Integer getReviewedGoal() {
        return reviewedGoal;
    }

    public void setReviewedGoal(Integer reviewedGoal) {
        this.reviewedGoal = reviewedGoal;
    }

    public List getDisabledMessage() {
        return disabledMessage;
    }

    public void setDisabledMessage(List disabledMessage) {
        this.disabledMessage = disabledMessage;
    }

    public BigDecimal getVerifiedMerchant() {
        return verifiedMerchant;
    }

    public void setVerifiedMerchant(BigDecimal verifiedMerchant) {
        this.verifiedMerchant = verifiedMerchant;
    }

    public Long getCountMilestone() {
        return countMilestone;
    }

    public void setCountMilestone(Long countMilestone) {
        this.countMilestone = countMilestone;
    }

    public Boolean getTemporaryPlan() {
        return temporaryPlan;
    }

    public void setTemporaryPlan(Boolean temporaryPlan) {
        this.temporaryPlan = temporaryPlan;
    }

    public List getCompletedDiscount() {
        return completedDiscount;
    }

    public void setCompletedDiscount(List completedDiscount) {
        this.completedDiscount = completedDiscount;
    }

    public Double getTemporaryPermission() {
        return temporaryPermission;
    }

    public void setTemporaryPermission(Double temporaryPermission) {
        this.temporaryPermission = temporaryPermission;
    }

    public Boolean getOldRole() {
        return oldRole;
    }

    public void setOldRole(Boolean oldRole) {
        this.oldRole = oldRole;
    }

    public String getSharedRole() {
        return sharedRole;
    }

    public void setSharedRole(String sharedRole) {
        this.sharedRole = sharedRole;
    }

    public Long getFailedGoal() {
        return failedGoal;
    }

    public void setFailedGoal(Long failedGoal) {
        this.failedGoal = failedGoal;
    }

    public Float getLoadedAgreement() {
        return loadedAgreement;
    }

    public void setLoadedAgreement(Float loadedAgreement) {
        this.loadedAgreement = loadedAgreement;
    }
}
