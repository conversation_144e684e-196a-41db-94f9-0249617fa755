package com.volvo.maintain.infrastructure.util;

import cn.hutool.core.map.MapUtil;
import com.yonyou.cyx.function.exception.ServiceBizException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {
	private static final Logger logger = LoggerFactory.getLogger(StringUtil.class);

	public final static String EMPTY_STRING = "";
	public final static String BLANK_SPRING_STRING = " ";
	public static final char CHAR_TILDE = '~';

	private StringUtil() {
	}

	/**
	 * 路径编码默认 UTF-8
	 */
	public static String urlEncode(String url) {
		return urlEncode(url, "UTF-8");
	}

	/**
	 * 路径编码
	 */
	public static String urlEncode(String url, String enc) {
		try {
			url = URLEncoder.encode(url, "UTF-8");
		} catch (Exception e) {
			logger.error("urlEncode error", e);
		}
		return url;
	}

	public static String replaceSubstring(String original, int startIndex, int lengthToReplace, String replacement) {
		// 确保替换的起始位置和长度在合理范围内
		if (startIndex < 0 || startIndex >= original.length() || lengthToReplace < 0) {
			throw new IllegalArgumentException("Invalid start index or length");
		}

		// 使用 StringBuilder 创建可变字符串
		StringBuilder stringBuilder = new StringBuilder(original);

		// 使用 replace 方法替换指定位置和长度的字符
		stringBuilder.replace(startIndex, startIndex + lengthToReplace, replacement);

		// 返回替换后的字符串
		return stringBuilder.toString();
	}

	/**
	 * 检查对象是否为null，如果是，则抛出ServiceBizException。
	 * 所有的类都继承Object。
	 * @param object 要检查的对象
	 * @param message 如果对象为null，抛出异常时使用的消息
	 */
	public static void requireNonNull(Object object, String message) {
		// 针对字符串进行额外的空白检查
		if (object instanceof String && ((String) object).trim().isEmpty()) {
			throw new ServiceBizException(message);
		}
		// ....
		if (object instanceof List && CollectionUtils.isEmpty((List<?>) object)) {
			throw new ServiceBizException(message);
		}
		// .....
		if (object instanceof Map && MapUtil.isEmpty((Map<?, ?>) object)) {
			throw new ServiceBizException(message);
		}
		if (Objects.isNull(object)) {
			throw new ServiceBizException(message);
		}
	}

	/**
	 * 校验是否是合法手机号
	 */
	public static boolean isMobile(final String str) {
		Pattern p = null;
		Matcher m = null;
		boolean b = false;
		/* 1、手机号码只包括+符号，0~9数字； 2、手机号码开头应该是 + / 0 / 1 这3种字符其中之一，后续都是数字。
		 * 3、如果手机号码首字符是1，那么要保证号码长度11位，和全部都是数字。 */
		if(str == null || str.length()<3) {
			return false;
		}
		String firstNumber = str.substring(0,1);
		String secondNumber = str.substring(0,2);
		if(firstNumber.equals("+")) {
			Pattern pattern = Pattern.compile("[0-9]*");
			b = pattern.matcher(str.substring(1)).matches();
		}else if(secondNumber.equals("00")) {
			Pattern pattern = Pattern.compile("[0-9]*");
			b = pattern.matcher(str.substring(2)).matches();
		}else if(str != null && str.length() == 11) {
			p = Pattern.compile("^[1][3-9][0-9]{9}$"); // 验证手机号
			m = p.matcher(str);
			b = m.matches();
		} else if (firstNumber.equals("0") && str.length() == 12) {
			p = Pattern.compile("^[1][3-9][0-9]{9}$"); // 验证手机号
			m = p.matcher(str.substring(1));
			b = m.matches();
		} else {
			b = false;
		}
		return b;
	}
	   /**
     * 处理前端传入的字符串，确保其长度不超过MySQL varchar(200)的限制。
     * 如果超过，将会被截断到200个字符
     *
     * @param input 从前端传入的字符串
     * @return 调整后的字符串
     */
    public static String processUserInput(String input, Integer length) {
        if (input == null) {
            return null;  // 传入为null，直接返回null
        }
        // 去除前后的空白字符
        input = input.trim();
        // 处理特殊字符和转义字符
        input = input.replaceAll("\\s{2,}", " "); // 多个空白替换为一个空白
        input = escapeSpecialCharacters(input);
        // 超过200个字符的长度限制进行截断
        if (input.length() > length) {
            input = input.substring(0, length);
        }
        return input;
    }

    /**
     * 对字符串中的特殊字符进行转义
     * @param input 需要转义的字符串
     * @return 转义后的字符串
     */
    public static String escapeSpecialCharacters(String input) {
        // 转义逻辑   单引号转义
        String escapedInput = input.replace("'", "''");
        // 防止HTML注入的基本转义
        escapedInput = escapedInput.replaceAll("&", "&amp;");
        escapedInput = escapedInput.replaceAll("<", "&lt;");
        escapedInput = escapedInput.replaceAll(">", "&gt;");
        escapedInput = escapedInput.replaceAll("\"", "&quot;");
        return escapedInput;
    }
	/**
	 * 日期截取
	 */
	public static String dateSubStrin(String dateStr) {
		if(StringUtils.isNotEmpty(dateStr) && dateStr.length() > 10){
			return dateStr.substring(0, 10);
		}
		return dateStr;
	}
}
