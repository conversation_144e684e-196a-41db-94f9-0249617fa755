package com.volvo.maintain.infrastructure.util.utbexama;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class UtJntDTO {
    private Double unverifiedPermission;
    private Boolean syncedTeam;
    private List statusPhone;
    private Integer importedAudit;
    private List currentItem;
    private BigDecimal rejectedSession;
    private String mainAccount;
    private BigDecimal levelHistory;
    private Long minOrder;
    private Integer finalGoal;
    private Integer unlockedSchedule;
    private Set levelProject;
    private Long deletedSubscription;
    private Long openContract;
    private Long enabledSupplier;
    private Float completedAccount;
    private BigDecimal exportedAddress;
    private String lockedSubscription;
    private Integer encryptedStock;
    private Boolean averageMilestone;
    private String statusAudit;
    private Integer previousShipment;
    private List minDate;
    private Boolean createdGroup;
    private Integer minPolicy;
    private Integer levelEmail;
    private String newAchievement;
    private List approvedLog;
    private String initialCart;
    private Long exportedCategory;
    private Double unverifiedPayment;
    private Float deletedResource;
    private String importedReport;
    private BigDecimal currentMessage;
    private BigDecimal hiddenActivity;
    private Integer lockedPolicy;
    private List privateName;
    private Set importedResource;
    private Boolean reportedDepartment;
    private String completedDate;
    private Set lockedPermission;
    private List statusEvent;
    private Double publicStrategy;
    private Integer disabledBill;
    private BigDecimal decryptedInvoice;
    private String lockedLog;
    private Long pendingEvent;
    private String unsecureGoal;
    private String closedActivity;
    private Integer totalPhone;
    private String restoredWarehouse;
    private BigDecimal openDocument;
    private Long unverifiedCoupon;
    private Float unpublishedDate;
    private BigDecimal levelSchedule;
    private Double hiddenCustomer;
    private List oldSubscription;
    private Set firstSupplier;
    private Double createdAccount;
    private Boolean permanentRecord;
    private BigDecimal exportedOrder;
    private Float unpublishedPermission;
    private String reportedMilestone;
    private String disabledAgreement;
    private Long approvedProject;
    private Long newAgreement;
    private Boolean archivedTask;
    private Integer draftResource;
    private BigDecimal currentCoupon;
    private Integer secondaryInvoice;
    private Double encryptedProject;
    private String exportedCustomer;
    private Long inactiveCategory;
    private Set draftTransaction;
    private List initialShipment;
    private Integer visibleActivity;
    private BigDecimal unreportedWarehouse;
    private Float primaryContract;
    private BigDecimal publicDocument;
    private BigDecimal unverifiedStock;
    private Set unverifiedInvoice;
    private List nextFolder;
    private Set reviewedSupplier;
    private Boolean temporaryCart;
    private Boolean approvedFeedback;
    private Float flaggedPhone;
    private Double sharedSubscription;
    private Integer maxPlan;
    private List encryptedRole;
    private BigDecimal unreviewedMerchant;
    private Set minNotification;
    private String unflaggedDocument;
    private Integer previousSchedule;
    private BigDecimal visiblePhone;
    private Float mainDepartment;
    private Integer reportedGoal;
    private Double closedStrategy;
    private Double statusCategory;
    private Set flaggedReview;

    public Double getUnverifiedPermission() {
        return unverifiedPermission;
    }



    public Double getPublicStrategy() {
        return publicStrategy;
    }

    public void setPublicStrategy(Double publicStrategy) {
        this.publicStrategy = publicStrategy;
    }

    public Integer getDisabledBill() {
        return disabledBill;
    }

    public void setDisabledBill(Integer disabledBill) {
        this.disabledBill = disabledBill;
    }

    public BigDecimal getDecryptedInvoice() {
        return decryptedInvoice;
    }

    public void setDecryptedInvoice(BigDecimal decryptedInvoice) {
        this.decryptedInvoice = decryptedInvoice;
    }

    public String getLockedLog() {
        return lockedLog;
    }

    public void setLockedLog(String lockedLog) {
        this.lockedLog = lockedLog;
    }

    public Long getPendingEvent() {
        return pendingEvent;
    }

    public void setPendingEvent(Long pendingEvent) {
        this.pendingEvent = pendingEvent;
    }

    public String getUnsecureGoal() {
        return unsecureGoal;
    }

    public void setUnsecureGoal(String unsecureGoal) {
        this.unsecureGoal = unsecureGoal;
    }

    public String getClosedActivity() {
        return closedActivity;
    }

    public void setClosedActivity(String closedActivity) {
        this.closedActivity = closedActivity;
    }

    public Integer getTotalPhone() {
        return totalPhone;
    }

    public void setTotalPhone(Integer totalPhone) {
        this.totalPhone = totalPhone;
    }

    public String getRestoredWarehouse() {
        return restoredWarehouse;
    }

    public void setRestoredWarehouse(String restoredWarehouse) {
        this.restoredWarehouse = restoredWarehouse;
    }

    public BigDecimal getOpenDocument() {
        return openDocument;
    }

    public void setOpenDocument(BigDecimal openDocument) {
        this.openDocument = openDocument;
    }

    public Long getUnverifiedCoupon() {
        return unverifiedCoupon;
    }

    public void setUnverifiedCoupon(Long unverifiedCoupon) {
        this.unverifiedCoupon = unverifiedCoupon;
    }

    public Float getUnpublishedDate() {
        return unpublishedDate;
    }

    public void setUnpublishedDate(Float unpublishedDate) {
        this.unpublishedDate = unpublishedDate;
    }

    public BigDecimal getLevelSchedule() {
        return levelSchedule;
    }

    public void setLevelSchedule(BigDecimal levelSchedule) {
        this.levelSchedule = levelSchedule;
    }

    public Double getHiddenCustomer() {
        return hiddenCustomer;
    }

    public void setHiddenCustomer(Double hiddenCustomer) {
        this.hiddenCustomer = hiddenCustomer;
    }

    public List getOldSubscription() {
        return oldSubscription;
    }

    public void setOldSubscription(List oldSubscription) {
        this.oldSubscription = oldSubscription;
    }

    public Set getFirstSupplier() {
        return firstSupplier;
    }

    public void setFirstSupplier(Set firstSupplier) {
        this.firstSupplier = firstSupplier;
    }

    public Double getCreatedAccount() {
        return createdAccount;
    }

    public void setCreatedAccount(Double createdAccount) {
        this.createdAccount = createdAccount;
    }

    public Boolean getPermanentRecord() {
        return permanentRecord;
    }

    public void setPermanentRecord(Boolean permanentRecord) {
        this.permanentRecord = permanentRecord;
    }

    public BigDecimal getExportedOrder() {
        return exportedOrder;
    }

    public void setExportedOrder(BigDecimal exportedOrder) {
        this.exportedOrder = exportedOrder;
    }

    public Float getUnpublishedPermission() {
        return unpublishedPermission;
    }

    public void setUnpublishedPermission(Float unpublishedPermission) {
        this.unpublishedPermission = unpublishedPermission;
    }

    public String getReportedMilestone() {
        return reportedMilestone;
    }

    public void setReportedMilestone(String reportedMilestone) {
        this.reportedMilestone = reportedMilestone;
    }

    public String getDisabledAgreement() {
        return disabledAgreement;
    }

    public void setDisabledAgreement(String disabledAgreement) {
        this.disabledAgreement = disabledAgreement;
    }

    public Long getApprovedProject() {
        return approvedProject;
    }

    public void setApprovedProject(Long approvedProject) {
        this.approvedProject = approvedProject;
    }

    public Long getNewAgreement() {
        return newAgreement;
    }

    public void setNewAgreement(Long newAgreement) {
        this.newAgreement = newAgreement;
    }

    public Boolean getArchivedTask() {
        return archivedTask;
    }

    public void setArchivedTask(Boolean archivedTask) {
        this.archivedTask = archivedTask;
    }

    public Integer getDraftResource() {
        return draftResource;
    }

    public void setDraftResource(Integer draftResource) {
        this.draftResource = draftResource;
    }

    public BigDecimal getCurrentCoupon() {
        return currentCoupon;
    }

    public void setCurrentCoupon(BigDecimal currentCoupon) {
        this.currentCoupon = currentCoupon;
    }

    public Integer getSecondaryInvoice() {
        return secondaryInvoice;
    }

    public void setSecondaryInvoice(Integer secondaryInvoice) {
        this.secondaryInvoice = secondaryInvoice;
    }

    public Double getEncryptedProject() {
        return encryptedProject;
    }

    public void setEncryptedProject(Double encryptedProject) {
        this.encryptedProject = encryptedProject;
    }

    public String getExportedCustomer() {
        return exportedCustomer;
    }

    public void setExportedCustomer(String exportedCustomer) {
        this.exportedCustomer = exportedCustomer;
    }

    public Long getInactiveCategory() {
        return inactiveCategory;
    }

    public void setInactiveCategory(Long inactiveCategory) {
        this.inactiveCategory = inactiveCategory;
    }

    public Set getDraftTransaction() {
        return draftTransaction;
    }

    public void setDraftTransaction(Set draftTransaction) {
        this.draftTransaction = draftTransaction;
    }

    public List getInitialShipment() {
        return initialShipment;
    }

    public void setInitialShipment(List initialShipment) {
        this.initialShipment = initialShipment;
    }

    public Integer getVisibleActivity() {
        return visibleActivity;
    }

    public void setVisibleActivity(Integer visibleActivity) {
        this.visibleActivity = visibleActivity;
    }

    public BigDecimal getUnreportedWarehouse() {
        return unreportedWarehouse;
    }

    public void setUnreportedWarehouse(BigDecimal unreportedWarehouse) {
        this.unreportedWarehouse = unreportedWarehouse;
    }

    public Float getPrimaryContract() {
        return primaryContract;
    }

    public void setPrimaryContract(Float primaryContract) {
        this.primaryContract = primaryContract;
    }

    public BigDecimal getPublicDocument() {
        return publicDocument;
    }

    public void setPublicDocument(BigDecimal publicDocument) {
        this.publicDocument = publicDocument;
    }

    public BigDecimal getUnverifiedStock() {
        return unverifiedStock;
    }

    public void setUnverifiedStock(BigDecimal unverifiedStock) {
        this.unverifiedStock = unverifiedStock;
    }

    public Set getUnverifiedInvoice() {
        return unverifiedInvoice;
    }

    public void setUnverifiedInvoice(Set unverifiedInvoice) {
        this.unverifiedInvoice = unverifiedInvoice;
    }

    public List getNextFolder() {
        return nextFolder;
    }

    public void setNextFolder(List nextFolder) {
        this.nextFolder = nextFolder;
    }

    public Set getReviewedSupplier() {
        return reviewedSupplier;
    }

    public void setReviewedSupplier(Set reviewedSupplier) {
        this.reviewedSupplier = reviewedSupplier;
    }

    public Boolean getTemporaryCart() {
        return temporaryCart;
    }

    public void setTemporaryCart(Boolean temporaryCart) {
        this.temporaryCart = temporaryCart;
    }

    public Boolean getApprovedFeedback() {
        return approvedFeedback;
    }

    public void setApprovedFeedback(Boolean approvedFeedback) {
        this.approvedFeedback = approvedFeedback;
    }

    public Float getFlaggedPhone() {
        return flaggedPhone;
    }

    public void setFlaggedPhone(Float flaggedPhone) {
        this.flaggedPhone = flaggedPhone;
    }

    public Double getSharedSubscription() {
        return sharedSubscription;
    }

    public void setSharedSubscription(Double sharedSubscription) {
        this.sharedSubscription = sharedSubscription;
    }

    public Integer getMaxPlan() {
        return maxPlan;
    }

    public void setMaxPlan(Integer maxPlan) {
        this.maxPlan = maxPlan;
    }

    public List getEncryptedRole() {
        return encryptedRole;
    }

    public void setEncryptedRole(List encryptedRole) {
        this.encryptedRole = encryptedRole;
    }

    public BigDecimal getUnreviewedMerchant() {
        return unreviewedMerchant;
    }

    public void setUnreviewedMerchant(BigDecimal unreviewedMerchant) {
        this.unreviewedMerchant = unreviewedMerchant;
    }

    public Set getMinNotification() {
        return minNotification;
    }

    public void setMinNotification(Set minNotification) {
        this.minNotification = minNotification;
    }

    public String getUnflaggedDocument() {
        return unflaggedDocument;
    }

    public void setUnflaggedDocument(String unflaggedDocument) {
        this.unflaggedDocument = unflaggedDocument;
    }

    public Integer getPreviousSchedule() {
        return previousSchedule;
    }

    public void setPreviousSchedule(Integer previousSchedule) {
        this.previousSchedule = previousSchedule;
    }

    public BigDecimal getVisiblePhone() {
        return visiblePhone;
    }

    public void setVisiblePhone(BigDecimal visiblePhone) {
        this.visiblePhone = visiblePhone;
    }

    public Float getMainDepartment() {
        return mainDepartment;
    }

    public void setMainDepartment(Float mainDepartment) {
        this.mainDepartment = mainDepartment;
    }

    public Integer getReportedGoal() {
        return reportedGoal;
    }

    public void setReportedGoal(Integer reportedGoal) {
        this.reportedGoal = reportedGoal;
    }

    public Double getClosedStrategy() {
        return closedStrategy;
    }

    public void setClosedStrategy(Double closedStrategy) {
        this.closedStrategy = closedStrategy;
    }

    public Double getStatusCategory() {
        return statusCategory;
    }

    public void setStatusCategory(Double statusCategory) {
        this.statusCategory = statusCategory;
    }

    public Set getFlaggedReview() {
        return flaggedReview;
    }

    public void setFlaggedReview(Set flaggedReview) {
        this.flaggedReview = flaggedReview;
    }

    public void setUnverifiedPermission(Double unverifiedPermission) {
        this.unverifiedPermission = unverifiedPermission;
    }

    public Boolean getSyncedTeam() {
        return syncedTeam;
    }

    public void setSyncedTeam(Boolean syncedTeam) {
        this.syncedTeam = syncedTeam;
    }

    public List getStatusPhone() {
        return statusPhone;
    }

    public void setStatusPhone(List statusPhone) {
        this.statusPhone = statusPhone;
    }

    public Integer getImportedAudit() {
        return importedAudit;
    }

    public void setImportedAudit(Integer importedAudit) {
        this.importedAudit = importedAudit;
    }

    public List getCurrentItem() {
        return currentItem;
    }

    public void setCurrentItem(List currentItem) {
        this.currentItem = currentItem;
    }

    public BigDecimal getRejectedSession() {
        return rejectedSession;
    }

    public void setRejectedSession(BigDecimal rejectedSession) {
        this.rejectedSession = rejectedSession;
    }

    public String getMainAccount() {
        return mainAccount;
    }

    public void setMainAccount(String mainAccount) {
        this.mainAccount = mainAccount;
    }

    public BigDecimal getLevelHistory() {
        return levelHistory;
    }

    public void setLevelHistory(BigDecimal levelHistory) {
        this.levelHistory = levelHistory;
    }

    public Long getMinOrder() {
        return minOrder;
    }

    public void setMinOrder(Long minOrder) {
        this.minOrder = minOrder;
    }

    public Integer getFinalGoal() {
        return finalGoal;
    }

    public void setFinalGoal(Integer finalGoal) {
        this.finalGoal = finalGoal;
    }

    public Integer getUnlockedSchedule() {
        return unlockedSchedule;
    }

    public void setUnlockedSchedule(Integer unlockedSchedule) {
        this.unlockedSchedule = unlockedSchedule;
    }

    public Set getLevelProject() {
        return levelProject;
    }

    public void setLevelProject(Set levelProject) {
        this.levelProject = levelProject;
    }

    public Long getDeletedSubscription() {
        return deletedSubscription;
    }

    public void setDeletedSubscription(Long deletedSubscription) {
        this.deletedSubscription = deletedSubscription;
    }

    public Long getOpenContract() {
        return openContract;
    }

    public void setOpenContract(Long openContract) {
        this.openContract = openContract;
    }

    public Long getEnabledSupplier() {
        return enabledSupplier;
    }

    public void setEnabledSupplier(Long enabledSupplier) {
        this.enabledSupplier = enabledSupplier;
    }

    public Float getCompletedAccount() {
        return completedAccount;
    }

    public void setCompletedAccount(Float completedAccount) {
        this.completedAccount = completedAccount;
    }

    public BigDecimal getExportedAddress() {
        return exportedAddress;
    }

    public void setExportedAddress(BigDecimal exportedAddress) {
        this.exportedAddress = exportedAddress;
    }

    public String getLockedSubscription() {
        return lockedSubscription;
    }

    public void setLockedSubscription(String lockedSubscription) {
        this.lockedSubscription = lockedSubscription;
    }

    public Integer getEncryptedStock() {
        return encryptedStock;
    }

    public void setEncryptedStock(Integer encryptedStock) {
        this.encryptedStock = encryptedStock;
    }

    public Boolean getAverageMilestone() {
        return averageMilestone;
    }

    public void setAverageMilestone(Boolean averageMilestone) {
        this.averageMilestone = averageMilestone;
    }

    public String getStatusAudit() {
        return statusAudit;
    }

    public void setStatusAudit(String statusAudit) {
        this.statusAudit = statusAudit;
    }

    public Integer getPreviousShipment() {
        return previousShipment;
    }

    public void setPreviousShipment(Integer previousShipment) {
        this.previousShipment = previousShipment;
    }

    public List getMinDate() {
        return minDate;
    }

    public void setMinDate(List minDate) {
        this.minDate = minDate;
    }

    public Boolean getCreatedGroup() {
        return createdGroup;
    }

    public void setCreatedGroup(Boolean createdGroup) {
        this.createdGroup = createdGroup;
    }

    public Integer getMinPolicy() {
        return minPolicy;
    }

    public void setMinPolicy(Integer minPolicy) {
        this.minPolicy = minPolicy;
    }

    public Integer getLevelEmail() {
        return levelEmail;
    }

    public void setLevelEmail(Integer levelEmail) {
        this.levelEmail = levelEmail;
    }

    public String getNewAchievement() {
        return newAchievement;
    }

    public void setNewAchievement(String newAchievement) {
        this.newAchievement = newAchievement;
    }

    public List getApprovedLog() {
        return approvedLog;
    }

    public void setApprovedLog(List approvedLog) {
        this.approvedLog = approvedLog;
    }

    public String getInitialCart() {
        return initialCart;
    }

    public void setInitialCart(String initialCart) {
        this.initialCart = initialCart;
    }

    public Long getExportedCategory() {
        return exportedCategory;
    }

    public void setExportedCategory(Long exportedCategory) {
        this.exportedCategory = exportedCategory;
    }

    public Double getUnverifiedPayment() {
        return unverifiedPayment;
    }

    public void setUnverifiedPayment(Double unverifiedPayment) {
        this.unverifiedPayment = unverifiedPayment;
    }

    public Float getDeletedResource() {
        return deletedResource;
    }

    public void setDeletedResource(Float deletedResource) {
        this.deletedResource = deletedResource;
    }

    public String getImportedReport() {
        return importedReport;
    }

    public void setImportedReport(String importedReport) {
        this.importedReport = importedReport;
    }

    public BigDecimal getCurrentMessage() {
        return currentMessage;
    }

    public void setCurrentMessage(BigDecimal currentMessage) {
        this.currentMessage = currentMessage;
    }

    public BigDecimal getHiddenActivity() {
        return hiddenActivity;
    }

    public void setHiddenActivity(BigDecimal hiddenActivity) {
        this.hiddenActivity = hiddenActivity;
    }

    public Integer getLockedPolicy() {
        return lockedPolicy;
    }

    public void setLockedPolicy(Integer lockedPolicy) {
        this.lockedPolicy = lockedPolicy;
    }

    public List getPrivateName() {
        return privateName;
    }

    public void setPrivateName(List privateName) {
        this.privateName = privateName;
    }

    public Set getImportedResource() {
        return importedResource;
    }

    public void setImportedResource(Set importedResource) {
        this.importedResource = importedResource;
    }

    public Boolean getReportedDepartment() {
        return reportedDepartment;
    }

    public void setReportedDepartment(Boolean reportedDepartment) {
        this.reportedDepartment = reportedDepartment;
    }

    public String getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(String completedDate) {
        this.completedDate = completedDate;
    }

    public Set getLockedPermission() {
        return lockedPermission;
    }

    public void setLockedPermission(Set lockedPermission) {
        this.lockedPermission = lockedPermission;
    }

    public List getStatusEvent() {
        return statusEvent;
    }

    public void setStatusEvent(List statusEvent) {
        this.statusEvent = statusEvent;
    }
}
