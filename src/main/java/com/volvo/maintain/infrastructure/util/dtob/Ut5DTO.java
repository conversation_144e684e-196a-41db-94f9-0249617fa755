package com.volvo.maintain.infrastructure.util.dtob;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
public class Ut5DTO {
    private Double primarySupplier;
    private Integer unlockedItem;
    private Float inactiveCart;
    private Integer unpublishedCategory;
    private String syncedLog;
    private Integer verifiedPrice;
    private Integer deletedBill;
    private Long unlockedStrategy;
    private Double unreviewedWarehouse;
    private BigDecimal previousPayment;
    private String unflaggedPolicy;
    private String completedHistory;
    private List activePermission;
    private Integer closedShipment;
    private Integer unpublishedGoal;
    private Float levelPlan;
    private String exportedInvoice;
    private String archivedBrand;
    private String lockedDepartment;
    private String levelFolder;
    private Integer nextAgreement;
    private Integer lockedUser;
    private BigDecimal nextPhone;
    private Double newMilestone;
    private Float archivedAgreement;
    private Set typeRole;
    private Boolean previousStock;
    private Integer securePolicy;
    private Float temporaryReview;
    private BigDecimal savedDate;
    private String publishedItem;
    private Double newReview;
    private Float encryptedShipment;
    private Double previousFile;
    private Long newDocument;
    private Set restoredResource;
    private Long savedHistory;
    private Long previousPhone;
    private Double activeGoal;
    private Boolean publicReview;
    private BigDecimal unpublishedRole;
    private String reviewedGoal;
    private Float unflaggedDate;
    private Boolean temporaryDocument;
    private Boolean maxPlan;
    private BigDecimal initialPermission;
    private List secondaryBill;
    private Float hiddenStrategy;
    private String rejectedPolicy;
    private List failedEvent;
    private List draftBrand;
    private Float newReport;
    private Integer sharedBrand;
    private Long closedRecord;
    private String unverifiedPlan;
    private String visibleAsset;
    private Set unsecureMessage;
    private Set decryptedEvent;
    private BigDecimal decryptedDiscount;
    private Double maxTask;
    private String nextContract;
    private String secondaryDate;
    private Set minTransaction;
    private BigDecimal unsecurePermission;
    private Boolean disabledActivity;
    private String enabledHistory;
    private Double decryptedLog;
    private Set permanentWarehouse;
    private String minEmail;
    private Boolean completedRecord;
    private Set hiddenReport;
    private String unreviewedPhone;
    private Float countMerchant;
    private String draftAchievement;
    private Boolean draftLog;
    private BigDecimal previousTask;
    private Integer pendingInvoice;
    private String countDepartment;
    private String updatedNotification;
    private Boolean temporaryLog;
    private Boolean archivedFeedback;
    private String deletedGoal;
    private Float deletedShipment;
    private String syncedSchedule;
    private String flaggedItem;
    private String unsecureRecord;
    private Float activeCart;
    private Double unreviewedHistory;
    private BigDecimal previousGoal;
    private String totalItem;
    private String updatedEmail;
    private BigDecimal visibleStrategy;
    private Long countTeam;
    private Long inactiveProduct;
    private List levelDocument;
    private String exportedRole;
    private Integer lockedFolder;
    private Boolean decryptedPlan;
    private Long primaryGroup;
}
