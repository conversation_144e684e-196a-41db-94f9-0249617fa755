package com.volvo.maintain.infrastructure.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.volvo.maintain.application.maintainlead.dto.message.AppPushWithTemplateDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.EmpDetailsDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.SendPushNoticeDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.UsedCarGetManagerByRoleCodeDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.UsedCarGetManagerByRoleCodeMapDto;
import com.volvo.maintain.infrastructure.constants.CommonConstant;
import com.volvo.maintain.infrastructure.gateway.MidEndAuthCenterFeign;
import com.volvo.maintain.infrastructure.gateway.MidEndMessageCenterFeign;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import com.yonyou.cyx.function.exception.ServiceBizException;
import com.yonyou.cyx.function.utils.jsonserializer.JSONUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class ConvertRoleCodeToEmployeeIdUtils {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private MidEndAuthCenterFeign midEndAuthCenterFeign;
    @Resource
    private MidEndMessageCenterFeign midEndMessageCenterFeign;

    @Value("${volvo.message.mailAppId}")
    public String volvoAppId;


    public void sendAppMessage(AppPushWithTemplateDto appPushWithTemplateDto, String dealerCode, List<String> userIdList, boolean flag) {
        logger.info("替换件/block send Mail Notice Parts Manager and Warehouse keeper：{}", dealerCode);
        String responseMsg = midEndAuthCenterFeign.getManagerByRoleCode(UsedCarGetManagerByRoleCodeMapDto.builder()
                .data(UsedCarGetManagerByRoleCodeDto.builder()
                        .companyCode(dealerCode)
                        .isOnjob(10081001)
                        .roleCode(Arrays.asList(CommonConstant.USED_KGY_PUSH_ROLE_CODE, CommonConstant.USED_LJJL_PUSH_ROLE_CODE))
                        .build()).build().toMaps());
        logger.info("midEndAuthCenterFeign.getManagerByRoleCode(UsedCarGetManagerByRoleCodeDto.builder() final Original response:{}", responseMsg);
        Map<String, Object> configMap = JSONUtil.jsonToMap(responseMsg);
        List<Map<String, Object>> configData = JSONUtil.jsonToMapList(JSONUtil.objectToJson(configMap.get("data")));
        List<String> userId = configData.stream().map(o -> o.get("userId")).filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList());
        logger.info("server advise id :{}",JSON.toJSONString(userIdList));
        if (CollectionUtils.isNotEmpty(userId) && CollectionUtils.isNotEmpty(userIdList)) {
            userId.addAll(userIdList);
        }
        logger.info("mid-end-auth-center response data result:{}", JSON.toJSONString(userId));
        if (CollectionUtil.isNotEmpty(configData)) {
            SendPushNoticeDto sendPushNoticeDto = new SendPushNoticeDto();
            sendPushNoticeDto.setTargetCodes(userId);
            sendPushNoticeDto.setTemplateCode(flag ? CommonConstant.MAIL_NOTICE_TEMPLATE_CODE_block : CommonConstant.MAIL_NOTICE_TEMPLATE_CODE);
            Map<String, Object> templateParams = Maps.newHashMap();
            String roNo = appPushWithTemplateDto.getRoNo();
            if (StringUtils.isNotBlank(roNo)) {
                templateParams.put("roNo", roNo);
                templateParams.put("partsNo",appPushWithTemplateDto.getPartsNo().stream().map(String::valueOf).collect(Collectors.joining(",")));
                sendPushNoticeDto.setTemplateParams(templateParams);
                logger.info("[售后OSCC替换件/block零件状态]-调用消息中心参数:{}", JSON.toJSONString(sendPushNoticeDto));
                MidResponse<Void> messageResultPushVO = this.sendAppPushTemplateMessage(sendPushNoticeDto);
                logger.info("[售后OSCC替换件/block零件状态发送站内信]-推送站内信结束,响应信息为:{}", messageResultPushVO);
            }
        }

    }

    private MidResponse<Void> sendAppPushTemplateMessage(SendPushNoticeDto sendPushNoticeDto) {
        if (StringUtils.isBlank(sendPushNoticeDto.getTemplateCode())) {
            throw new ServiceBizException("APP PUSH模板CODE 不可为空");
        }
        if (CollUtil.isEmpty(sendPushNoticeDto.getTargetCodes())) {
            throw new ServiceBizException("接收人清单不可为空");
        }
        if (CollUtil.isEmpty(sendPushNoticeDto.getTemplateParams())) {
            sendPushNoticeDto.setTemplateParams(Maps.newHashMap());
        }
        //如果APPID是空的 则使用默认的APPID
        if (StringUtils.isBlank(sendPushNoticeDto.getAppId())) {
            sendPushNoticeDto.setAppId(volvoAppId);
        }
        logger.info("MessagePushServicesImpl#sendAppPushTemplateMessage sendPushNoticeDTO:{}", sendPushNoticeDto);
        List<String> userIds = sendPushNoticeDto.getTargetCodes();
        List<String> aliases = getAliasesByUserIds(userIds);
        if (CollectionUtils.isEmpty(aliases)) {
            throw new ServiceBizException("未获取到对应的别名:{}", userIds);
        }
        sendPushNoticeDto.setTargetCodes(aliases);
        MidResponse<Void> resultPushVO = midEndMessageCenterFeign.sendAppsPushWithTemplate(sendPushNoticeDto.getAppId(), sendPushNoticeDto);
        logger.info("MessagePushServicesImpl#sendAppPushTemplateMessage resultPushVO:{}", resultPushVO);
        return resultPushVO;
    }


    private List<String> getAliasesByUserIds(List<String> userIds) {
        List<String> aliases = null;
        if (!CollectionUtils.isEmpty(userIds)) {
            List<EmpDetailsDto> empVos = listEmpIds(userIds);
            if (!CollectionUtils.isEmpty(empVos)) {
                aliases = new LinkedList<>();
                for (EmpDetailsDto empVo : empVos) {
                    aliases.add("E" + empVo.getEmpId());
                }
            }
        }
        return aliases;
    }

    public List<EmpDetailsDto> listEmpIds(List<String> userIds) {
        UsedCarGetManagerByRoleCodeMapDto dto = new UsedCarGetManagerByRoleCodeMapDto();
        dto.setData(UsedCarGetManagerByRoleCodeDto.builder().userIds(userIds).build());
        logger.info("调用中台获得消息下发的人入参：{}", JSONUtil.objectToJson(dto));
        MidResponse<List<EmpDetailsDto>> restResultResponse = midEndAuthCenterFeign.queryEmpDetailsByIds(dto);
        logger.info("调用中台获得消息下发的人出参：{}", JSONUtil.objectToJson(restResultResponse));
        return restResultResponse.getData();
    }

}
