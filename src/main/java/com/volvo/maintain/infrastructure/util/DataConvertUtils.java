package com.volvo.maintain.infrastructure.util;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 数据转换
 *
 */
@Component
@Slf4j
public class DataConvertUtils {
	
    private DataConvertUtils() {
		super();
	}

    /**
     * String集合转String
     * @return 
     */
    public static String stringListToString(List<String> list){
    	if(CollectionUtils.isEmpty(list)) {
    		return null;
    	}
        return String.join(",", list);
    }
    
    /**
     * String转String集合
     * @return 
     */
    public static List<String> stringToStringList(Object str){
    	if(ObjectUtils.isEmpty(str)) {
    		return new ArrayList<>();
    	}
        return Arrays.asList(str.toString().split(","));
    }
    
    /**
     * Integer集合转String
     * @return 
     */
    public static String integerListToString(List<Integer> list){
    	if(CollectionUtils.isEmpty(list)) {
    		return null;
    	}
    	return list.stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(","));
    }
    
    /**
     * String转Integer集合
     * @return 
     */
    public static List<Integer> stringToIntegerList(Object str){
    	if(ObjectUtils.isEmpty(str)) {
    		return new ArrayList<>();
    	}
    	return Arrays.stream(str.toString().split(",")).map(Integer::parseInt).collect(Collectors.toList());
    }
    
    
    /**
     * String 转集合通用处理
     */
    /**
     * String转Integer集合
     * @return 
     */
    public static Map<String, Object> mapToMapConvertStringToListData(Map<String, Object> maps, Class<?> clazz){
    	try {
    		/* 得到类中的所有属性集合 */
    		Field[] fs = clazz.getDeclaredFields();
    		for (int i = 0; i < fs.length; i++) {
    			reflexDetermine(maps, fs, i, false);
    		}
		} catch (Exception e) {
			log.info("对象数据转换异常：", e);
		}
    	return maps;
    }

    /**
     * 反射获取
     * @param maps
     * @param flag
     * @param fs
     * @param i
     */
	private static void reflexDetermine(Map<String, Object> maps, Field[] fs, int i, boolean flag) {
		try {
			Field f = fs[i];
			f.setAccessible(true);
			// 设置些属性是可以访问的
			Class<?> type = f.getType();
			String name = f.getName();
			if(List.class.isAssignableFrom(type)) {
				Object object = maps.get(name);
				maps.put(name, object);
				ParameterizedType genericType = (ParameterizedType) f.getGenericType();
				Class<?> type2 = (Class<?>)genericType.getActualTypeArguments()[0];
				if(Integer.class.isAssignableFrom(type2)) {
					if(flag) {
						List<Integer> list = (List)object;
						String integerListToString = integerListToString(list);
						maps.put(name, integerListToString);
					} else {						
						List<Integer> stringToIntegerList = stringToIntegerList(object);
						maps.put(name, stringToIntegerList);
					}
				} else if(String.class.isAssignableFrom(type2)) {
					if(flag) {
						List<String> list = (List)object;
						String stringListToString = stringListToString(list);
						maps.put(name, stringListToString);
					} else {						
						List<String> stringToStringList = stringToStringList(object);
						maps.put(name, stringToStringList);
					}
				}
			}
		} catch (Exception e) {
			log.info("对象反射异常：",e);
		}
	}
	
    /**
     * 集合转String 集合
     * @return 
     */
    public static Map<String, Object> mapToMapConvertListToStringData(Map<String, Object> maps, Object obj){
    	try {
    		Class userCla = (Class) obj.getClass();
    		/* 得到类中的所有属性集合 */
    		Field[] fs = userCla.getDeclaredFields();
    		for (int i = 0; i < fs.length; i++) {
    			reflexDetermine(maps, fs, i, true);
    		}
		} catch (Exception e) {
			log.info("数据转换异常：", e);
		}
    	return maps;
    }
    
    
    /**
     * 将Object转换为下载中心可用Map  屏蔽List带来的参数缺失问题
     * @return 
     */
    public static Map<String, Object> objConverDownlodParameterMap(Object obj){
    	Map<String, Object> maps = BeanUtil.beanToMap(obj);
    	return mapToMapConvertListToStringData(maps, obj);
    }
    
    /**
     * Map还原成对象 包括List还原
     * @return 
     */
    public static <T> T downlodParameterMapConverObj(Map<String, Object> maps, Class<T> clazz){
    	Map<String, Object> mapToMapConvertStringToListData = mapToMapConvertStringToListData(maps, clazz);
    	return BeanUtil.mapToBean(mapToMapConvertStringToListData, clazz, false);
    }
    
    
    /**
     * 时间格式化
     * @return 
     */
    public static Map mapDateFormat(Map<String, Object> map){
    	for (Map.Entry<String, Object> entry : map.entrySet()) {
    		try {				
    			String key = entry.getKey();
    			Object val = entry.getValue();
    			Class<? extends Object> class1 = val.getClass();
    			String typeName = class1.getTypeName();
    			log.info("key: {},类型：{}", key, typeName);
    			String substring = val.toString().replace("T", " ").substring(0,19);
    			map.put(key, substring);
			} catch (Exception e) {
				log.info("错误转换：", e);
			}
		}
		return map;
    }
}
