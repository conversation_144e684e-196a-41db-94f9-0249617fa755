package com.volvo.maintain.infrastructure.util.utbexama;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class UtKntDTO {
    private List unsecureProject;
    private Integer importedPhone;
    private Long nextAchievement;
    private Integer statusSupplier;
    private String verifiedTransaction;
    private Long successfulHistory;
    private String disabledShipment;
    private Double inactiveBill;
    private Long approvedPlan;
    private Set secondaryMerchant;
    private String previousHistory;
    private List unsecureTask;
    private BigDecimal restoredGoal;
    private String temporaryMerchant;
    private Double openSubscription;
    private Integer currentCategory;
    private Set currentTask;
    private String enabledShipment;
    private List savedReview;
    private Integer decryptedFile;
    private Boolean typePayment;
    private String nextTeam;
    private BigDecimal initialAudit;
    private Boolean exportedContract;
    private Integer modifiedEmail;
    private BigDecimal initialNotification;
    private String createdSchedule;
    private Integer createdRecord;
    private String encryptedContract;
    private List visibleBill;
    private BigDecimal verifiedProject;
    private String mainProduct;
    private Integer lastItem;
    private BigDecimal encryptedPermission;
    private Boolean typePhone;
    private String activeDate;
    private Integer flaggedBrand;
    private Float maxDepartment;
    private String syncedPhone;
    private List modifiedTask;
    private Integer averagePhone;
    private Boolean maxName;
    private Long encryptedReview;
    private Float loadedMerchant;
    private Integer archivedCustomer;
    private List primaryTransaction;
    private Long secondaryAddress;
    private Boolean statusFolder;
    private Long verifiedPolicy;
    private Set previousPermission;
    private BigDecimal archivedPlan;
    private Float secondarySupplier;
    private String failedTeam;
    private Float completedMessage;
    private Float minPolicy;
    private String exportedOrder;
    private Set mainMessage;
    private Long primaryCoupon;
    private String activeItem;
    private List failedSession;
    private String restoredEvent;
    private Long reviewedNotification;
    private Float primaryProduct;
    private Boolean nextPayment;
    private String unflaggedLog;
    private BigDecimal secondarySubscription;
    private Boolean unpublishedAgreement;
    private Double averagePayment;
    private Long publishedGroup;
    private String unverifiedRecord;
    private Float initialMilestone;
    private Double averageProject;
    private Integer failedGroup;
    private BigDecimal countSubscription;
    private String failedEmail;
    private Boolean publicFolder;
    private Boolean encryptedInvoice;
    private String privatePolicy;
    private List lockedFolder;
    private Float unpublishedAccount;
    private String syncedPrice;
    private Integer syncedFile;
    private Long inactiveProject;
    private Long activeCoupon;
    private Set publicTransaction;
    private String publicPolicy;
    private String averageGoal;
    private String approvedDocument;
    private Boolean rejectedReview;
    private Long enabledFolder;
    private Float activeEvent;
    private Set openStock;
    private Boolean lastName;

    public List getUnsecureProject() {
        return unsecureProject;
    }

    public void setUnsecureProject(List unsecureProject) {
        this.unsecureProject = unsecureProject;
    }

    public Integer getImportedPhone() {
        return importedPhone;
    }

    public void setImportedPhone(Integer importedPhone) {
        this.importedPhone = importedPhone;
    }

    public Long getNextAchievement() {
        return nextAchievement;
    }

    public void setNextAchievement(Long nextAchievement) {
        this.nextAchievement = nextAchievement;
    }

    public Integer getStatusSupplier() {
        return statusSupplier;
    }

    public void setStatusSupplier(Integer statusSupplier) {
        this.statusSupplier = statusSupplier;
    }

    public String getVerifiedTransaction() {
        return verifiedTransaction;
    }

    public void setVerifiedTransaction(String verifiedTransaction) {
        this.verifiedTransaction = verifiedTransaction;
    }

    public Long getSuccessfulHistory() {
        return successfulHistory;
    }

    public void setSuccessfulHistory(Long successfulHistory) {
        this.successfulHistory = successfulHistory;
    }

    public String getDisabledShipment() {
        return disabledShipment;
    }

    public void setDisabledShipment(String disabledShipment) {
        this.disabledShipment = disabledShipment;
    }

    public Double getInactiveBill() {
        return inactiveBill;
    }

    public void setInactiveBill(Double inactiveBill) {
        this.inactiveBill = inactiveBill;
    }

    public Long getApprovedPlan() {
        return approvedPlan;
    }

    public void setApprovedPlan(Long approvedPlan) {
        this.approvedPlan = approvedPlan;
    }

    public Set getSecondaryMerchant() {
        return secondaryMerchant;
    }

    public void setSecondaryMerchant(Set secondaryMerchant) {
        this.secondaryMerchant = secondaryMerchant;
    }

    public String getPreviousHistory() {
        return previousHistory;
    }

    public void setPreviousHistory(String previousHistory) {
        this.previousHistory = previousHistory;
    }

    public List getUnsecureTask() {
        return unsecureTask;
    }

    public void setUnsecureTask(List unsecureTask) {
        this.unsecureTask = unsecureTask;
    }

    public BigDecimal getRestoredGoal() {
        return restoredGoal;
    }

    public void setRestoredGoal(BigDecimal restoredGoal) {
        this.restoredGoal = restoredGoal;
    }

    public String getTemporaryMerchant() {
        return temporaryMerchant;
    }

    public void setTemporaryMerchant(String temporaryMerchant) {
        this.temporaryMerchant = temporaryMerchant;
    }

    public Double getOpenSubscription() {
        return openSubscription;
    }

    public void setOpenSubscription(Double openSubscription) {
        this.openSubscription = openSubscription;
    }

    public Integer getCurrentCategory() {
        return currentCategory;
    }

    public void setCurrentCategory(Integer currentCategory) {
        this.currentCategory = currentCategory;
    }

    public Set getCurrentTask() {
        return currentTask;
    }

    public void setCurrentTask(Set currentTask) {
        this.currentTask = currentTask;
    }

    public String getEnabledShipment() {
        return enabledShipment;
    }

    public void setEnabledShipment(String enabledShipment) {
        this.enabledShipment = enabledShipment;
    }

    public List getSavedReview() {
        return savedReview;
    }

    public void setSavedReview(List savedReview) {
        this.savedReview = savedReview;
    }

    public Integer getDecryptedFile() {
        return decryptedFile;
    }

    public void setDecryptedFile(Integer decryptedFile) {
        this.decryptedFile = decryptedFile;
    }

    public Boolean getTypePayment() {
        return typePayment;
    }

    public void setTypePayment(Boolean typePayment) {
        this.typePayment = typePayment;
    }

    public String getNextTeam() {
        return nextTeam;
    }

    public void setNextTeam(String nextTeam) {
        this.nextTeam = nextTeam;
    }

    public BigDecimal getInitialAudit() {
        return initialAudit;
    }

    public void setInitialAudit(BigDecimal initialAudit) {
        this.initialAudit = initialAudit;
    }

    public Boolean getExportedContract() {
        return exportedContract;
    }

    public void setExportedContract(Boolean exportedContract) {
        this.exportedContract = exportedContract;
    }

    public Integer getModifiedEmail() {
        return modifiedEmail;
    }

    public void setModifiedEmail(Integer modifiedEmail) {
        this.modifiedEmail = modifiedEmail;
    }

    public BigDecimal getInitialNotification() {
        return initialNotification;
    }

    public void setInitialNotification(BigDecimal initialNotification) {
        this.initialNotification = initialNotification;
    }

    public String getCreatedSchedule() {
        return createdSchedule;
    }

    public void setCreatedSchedule(String createdSchedule) {
        this.createdSchedule = createdSchedule;
    }

    public Integer getCreatedRecord() {
        return createdRecord;
    }

    public void setCreatedRecord(Integer createdRecord) {
        this.createdRecord = createdRecord;
    }

    public String getEncryptedContract() {
        return encryptedContract;
    }

    public void setEncryptedContract(String encryptedContract) {
        this.encryptedContract = encryptedContract;
    }

    public List getVisibleBill() {
        return visibleBill;
    }

    public void setVisibleBill(List visibleBill) {
        this.visibleBill = visibleBill;
    }

    public BigDecimal getVerifiedProject() {
        return verifiedProject;
    }

    public void setVerifiedProject(BigDecimal verifiedProject) {
        this.verifiedProject = verifiedProject;
    }

    public String getMainProduct() {
        return mainProduct;
    }

    public void setMainProduct(String mainProduct) {
        this.mainProduct = mainProduct;
    }

    public Integer getLastItem() {
        return lastItem;
    }

    public void setLastItem(Integer lastItem) {
        this.lastItem = lastItem;
    }

    public BigDecimal getEncryptedPermission() {
        return encryptedPermission;
    }

    public void setEncryptedPermission(BigDecimal encryptedPermission) {
        this.encryptedPermission = encryptedPermission;
    }

    public Boolean getTypePhone() {
        return typePhone;
    }

    public void setTypePhone(Boolean typePhone) {
        this.typePhone = typePhone;
    }

    public String getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(String activeDate) {
        this.activeDate = activeDate;
    }

    public Integer getFlaggedBrand() {
        return flaggedBrand;
    }

    public void setFlaggedBrand(Integer flaggedBrand) {
        this.flaggedBrand = flaggedBrand;
    }

    public Float getMaxDepartment() {
        return maxDepartment;
    }

    public void setMaxDepartment(Float maxDepartment) {
        this.maxDepartment = maxDepartment;
    }

    public String getSyncedPhone() {
        return syncedPhone;
    }

    public void setSyncedPhone(String syncedPhone) {
        this.syncedPhone = syncedPhone;
    }

    public List getModifiedTask() {
        return modifiedTask;
    }

    public void setModifiedTask(List modifiedTask) {
        this.modifiedTask = modifiedTask;
    }

    public Integer getAveragePhone() {
        return averagePhone;
    }

    public void setAveragePhone(Integer averagePhone) {
        this.averagePhone = averagePhone;
    }

    public Boolean getMaxName() {
        return maxName;
    }

    public void setMaxName(Boolean maxName) {
        this.maxName = maxName;
    }

    public Long getEncryptedReview() {
        return encryptedReview;
    }

    public void setEncryptedReview(Long encryptedReview) {
        this.encryptedReview = encryptedReview;
    }

    public Float getLoadedMerchant() {
        return loadedMerchant;
    }

    public void setLoadedMerchant(Float loadedMerchant) {
        this.loadedMerchant = loadedMerchant;
    }

    public Integer getArchivedCustomer() {
        return archivedCustomer;
    }

    public void setArchivedCustomer(Integer archivedCustomer) {
        this.archivedCustomer = archivedCustomer;
    }

    public List getPrimaryTransaction() {
        return primaryTransaction;
    }

    public void setPrimaryTransaction(List primaryTransaction) {
        this.primaryTransaction = primaryTransaction;
    }

    public Long getSecondaryAddress() {
        return secondaryAddress;
    }

    public void setSecondaryAddress(Long secondaryAddress) {
        this.secondaryAddress = secondaryAddress;
    }

    public Boolean getStatusFolder() {
        return statusFolder;
    }

    public void setStatusFolder(Boolean statusFolder) {
        this.statusFolder = statusFolder;
    }

    public Long getVerifiedPolicy() {
        return verifiedPolicy;
    }

    public void setVerifiedPolicy(Long verifiedPolicy) {
        this.verifiedPolicy = verifiedPolicy;
    }

    public Set getPreviousPermission() {
        return previousPermission;
    }

    public void setPreviousPermission(Set previousPermission) {
        this.previousPermission = previousPermission;
    }

    public BigDecimal getArchivedPlan() {
        return archivedPlan;
    }

    public void setArchivedPlan(BigDecimal archivedPlan) {
        this.archivedPlan = archivedPlan;
    }

    public Float getSecondarySupplier() {
        return secondarySupplier;
    }

    public void setSecondarySupplier(Float secondarySupplier) {
        this.secondarySupplier = secondarySupplier;
    }

    public String getFailedTeam() {
        return failedTeam;
    }

    public void setFailedTeam(String failedTeam) {
        this.failedTeam = failedTeam;
    }

    public Float getCompletedMessage() {
        return completedMessage;
    }

    public void setCompletedMessage(Float completedMessage) {
        this.completedMessage = completedMessage;
    }

    public Float getMinPolicy() {
        return minPolicy;
    }

    public void setMinPolicy(Float minPolicy) {
        this.minPolicy = minPolicy;
    }

    public String getExportedOrder() {
        return exportedOrder;
    }

    public void setExportedOrder(String exportedOrder) {
        this.exportedOrder = exportedOrder;
    }

    public Set getMainMessage() {
        return mainMessage;
    }

    public void setMainMessage(Set mainMessage) {
        this.mainMessage = mainMessage;
    }

    public Long getPrimaryCoupon() {
        return primaryCoupon;
    }

    public void setPrimaryCoupon(Long primaryCoupon) {
        this.primaryCoupon = primaryCoupon;
    }

    public String getActiveItem() {
        return activeItem;
    }

    public void setActiveItem(String activeItem) {
        this.activeItem = activeItem;
    }

    public List getFailedSession() {
        return failedSession;
    }

    public void setFailedSession(List failedSession) {
        this.failedSession = failedSession;
    }

    public String getRestoredEvent() {
        return restoredEvent;
    }

    public void setRestoredEvent(String restoredEvent) {
        this.restoredEvent = restoredEvent;
    }

    public Long getReviewedNotification() {
        return reviewedNotification;
    }

    public void setReviewedNotification(Long reviewedNotification) {
        this.reviewedNotification = reviewedNotification;
    }

    public Float getPrimaryProduct() {
        return primaryProduct;
    }

    public void setPrimaryProduct(Float primaryProduct) {
        this.primaryProduct = primaryProduct;
    }

    public Boolean getNextPayment() {
        return nextPayment;
    }

    public void setNextPayment(Boolean nextPayment) {
        this.nextPayment = nextPayment;
    }

    public String getUnflaggedLog() {
        return unflaggedLog;
    }

    public void setUnflaggedLog(String unflaggedLog) {
        this.unflaggedLog = unflaggedLog;
    }

    public BigDecimal getSecondarySubscription() {
        return secondarySubscription;
    }

    public void setSecondarySubscription(BigDecimal secondarySubscription) {
        this.secondarySubscription = secondarySubscription;
    }

    public Boolean getUnpublishedAgreement() {
        return unpublishedAgreement;
    }

    public void setUnpublishedAgreement(Boolean unpublishedAgreement) {
        this.unpublishedAgreement = unpublishedAgreement;
    }

    public Double getAveragePayment() {
        return averagePayment;
    }

    public void setAveragePayment(Double averagePayment) {
        this.averagePayment = averagePayment;
    }

    public Long getPublishedGroup() {
        return publishedGroup;
    }

    public void setPublishedGroup(Long publishedGroup) {
        this.publishedGroup = publishedGroup;
    }

    public String getUnverifiedRecord() {
        return unverifiedRecord;
    }

    public void setUnverifiedRecord(String unverifiedRecord) {
        this.unverifiedRecord = unverifiedRecord;
    }

    public Float getInitialMilestone() {
        return initialMilestone;
    }

    public void setInitialMilestone(Float initialMilestone) {
        this.initialMilestone = initialMilestone;
    }

    public Double getAverageProject() {
        return averageProject;
    }

    public void setAverageProject(Double averageProject) {
        this.averageProject = averageProject;
    }

    public Integer getFailedGroup() {
        return failedGroup;
    }

    public void setFailedGroup(Integer failedGroup) {
        this.failedGroup = failedGroup;
    }

    public BigDecimal getCountSubscription() {
        return countSubscription;
    }

    public void setCountSubscription(BigDecimal countSubscription) {
        this.countSubscription = countSubscription;
    }

    public String getFailedEmail() {
        return failedEmail;
    }

    public void setFailedEmail(String failedEmail) {
        this.failedEmail = failedEmail;
    }

    public Boolean getPublicFolder() {
        return publicFolder;
    }

    public void setPublicFolder(Boolean publicFolder) {
        this.publicFolder = publicFolder;
    }

    public Boolean getEncryptedInvoice() {
        return encryptedInvoice;
    }

    public void setEncryptedInvoice(Boolean encryptedInvoice) {
        this.encryptedInvoice = encryptedInvoice;
    }

    public String getPrivatePolicy() {
        return privatePolicy;
    }

    public void setPrivatePolicy(String privatePolicy) {
        this.privatePolicy = privatePolicy;
    }

    public List getLockedFolder() {
        return lockedFolder;
    }

    public void setLockedFolder(List lockedFolder) {
        this.lockedFolder = lockedFolder;
    }

    public Float getUnpublishedAccount() {
        return unpublishedAccount;
    }

    public void setUnpublishedAccount(Float unpublishedAccount) {
        this.unpublishedAccount = unpublishedAccount;
    }

    public String getSyncedPrice() {
        return syncedPrice;
    }

    public void setSyncedPrice(String syncedPrice) {
        this.syncedPrice = syncedPrice;
    }

    public Integer getSyncedFile() {
        return syncedFile;
    }

    public void setSyncedFile(Integer syncedFile) {
        this.syncedFile = syncedFile;
    }

    public Long getInactiveProject() {
        return inactiveProject;
    }

    public void setInactiveProject(Long inactiveProject) {
        this.inactiveProject = inactiveProject;
    }

    public Long getActiveCoupon() {
        return activeCoupon;
    }

    public void setActiveCoupon(Long activeCoupon) {
        this.activeCoupon = activeCoupon;
    }

    public Set getPublicTransaction() {
        return publicTransaction;
    }

    public void setPublicTransaction(Set publicTransaction) {
        this.publicTransaction = publicTransaction;
    }

    public String getPublicPolicy() {
        return publicPolicy;
    }

    public void setPublicPolicy(String publicPolicy) {
        this.publicPolicy = publicPolicy;
    }

    public String getAverageGoal() {
        return averageGoal;
    }

    public void setAverageGoal(String averageGoal) {
        this.averageGoal = averageGoal;
    }

    public String getApprovedDocument() {
        return approvedDocument;
    }

    public void setApprovedDocument(String approvedDocument) {
        this.approvedDocument = approvedDocument;
    }

    public Boolean getRejectedReview() {
        return rejectedReview;
    }

    public void setRejectedReview(Boolean rejectedReview) {
        this.rejectedReview = rejectedReview;
    }

    public Long getEnabledFolder() {
        return enabledFolder;
    }

    public void setEnabledFolder(Long enabledFolder) {
        this.enabledFolder = enabledFolder;
    }

    public Float getActiveEvent() {
        return activeEvent;
    }

    public void setActiveEvent(Float activeEvent) {
        this.activeEvent = activeEvent;
    }

    public Set getOpenStock() {
        return openStock;
    }

    public void setOpenStock(Set openStock) {
        this.openStock = openStock;
    }

    public Boolean getLastName() {
        return lastName;
    }

    public void setLastName(Boolean lastName) {
        this.lastName = lastName;
    }
}
