package com.volvo.maintain.infrastructure.util.dtob;


import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class UtADTO {

	private Long unverifiedMilestone;
	private List completedUser;
	private Set oldOrder;
	private Integer lockedProduct;
	private Float updatedNotification;
	private Boolean typeAudit;
	private List loadedAchievement;
	private Double deletedEvent;
	private String publicItem;
	private Double publishedMilestone;
	private Float completedStrategy;
	private BigDecimal draftCustomer;
	private Float visibleStock;
	private BigDecimal oldPolicy;
	private Float privateCustomer;
	private List statusAudit;
	private Float updatedCategory;
	private BigDecimal countProduct;
	private Integer temporaryWarehouse;
	private Boolean unflaggedReview;
	private Integer firstShipment;
	private String draftName;
	private Long draftFeedback;
	private Long completedPolicy;
	private String secureDiscount;
	private String updatedName;
	private Integer unreviewedDiscount;
	private String privateReport;
	private Integer pendingMerchant;
	private Float typeTeam;
	private Set firstDiscount;
	private String firstCoupon;
	private Double unreportedAddress;
	private Set syncedDocument;
	private Set publishedUser;
	private Set nextStrategy;
	private List unlockedPhone;
	private Float reportedEvent;
	private Long unpublishedAudit;
	private Double reviewedActivity;
	private Float flaggedSupplier;
	private Double currentLog;
	private Set unverifiedPolicy;
	private Set unreportedStrategy;
	private String oldPrice;
	private Double finalGoal;
	private Set nextPayment;
	private Set oldGroup;
	private String importedGroup;
	private Boolean syncedLog;
	private String previousPlan;
	private Set mainEvent;
	private String lastHistory;
	private Long exportedStock;
	private List lastName;
	private Integer unpublishedSchedule;
	private Integer countTeam;
	private BigDecimal decryptedOrder;
	private Integer privateMilestone;
	private Long closedBrand;
	private Double decryptedPlan;
	private Integer reviewedTeam;
	private List unreportedBrand;
	private Set lockedAsset;
	private String publishedStrategy;
	private Set syncedWarehouse;
	private Long disabledBill;
	private Integer flaggedDate;
	private Double typeGoal;
	private Double sharedProduct;
	private Float sharedDepartment;
	private Long initialStrategy;
	private Set currentNotification;
	private Float updatedMilestone;
	private Boolean exportedMilestone;
	private Set visibleActivity;
	private String permanentName;
	private String secondaryEvent;
	private String countGoal;
	private Boolean levelDiscount;
	private Float unreviewedGoal;
	private BigDecimal exportedAudit;
	private Long reviewedFolder;
	private Boolean totalName;
	private Integer unlockedCustomer;
	private Long inactiveAudit;
	private String rejectedInvoice;
	private Set newPolicy;
	private Set unreportedOrder;

	public Long getUnverifiedMilestone() {
		return unverifiedMilestone;
	}

	public void setUnverifiedMilestone(Long unverifiedMilestone) {
		this.unverifiedMilestone = unverifiedMilestone;
	}

	public List getCompletedUser() {
		return completedUser;
	}

	public void setCompletedUser(List completedUser) {
		this.completedUser = completedUser;
	}

	public Set getOldOrder() {
		return oldOrder;
	}

	public void setOldOrder(Set oldOrder) {
		this.oldOrder = oldOrder;
	}

	public Integer getLockedProduct() {
		return lockedProduct;
	}

	public void setLockedProduct(Integer lockedProduct) {
		this.lockedProduct = lockedProduct;
	}

	public Float getUpdatedNotification() {
		return updatedNotification;
	}

	public void setUpdatedNotification(Float updatedNotification) {
		this.updatedNotification = updatedNotification;
	}

	public Boolean getTypeAudit() {
		return typeAudit;
	}

	public void setTypeAudit(Boolean typeAudit) {
		this.typeAudit = typeAudit;
	}

	public List getLoadedAchievement() {
		return loadedAchievement;
	}

	public void setLoadedAchievement(List loadedAchievement) {
		this.loadedAchievement = loadedAchievement;
	}

	public Double getDeletedEvent() {
		return deletedEvent;
	}

	public void setDeletedEvent(Double deletedEvent) {
		this.deletedEvent = deletedEvent;
	}

	public String getPublicItem() {
		return publicItem;
	}

	public void setPublicItem(String publicItem) {
		this.publicItem = publicItem;
	}

	public Double getPublishedMilestone() {
		return publishedMilestone;
	}

	public void setPublishedMilestone(Double publishedMilestone) {
		this.publishedMilestone = publishedMilestone;
	}

	public Float getCompletedStrategy() {
		return completedStrategy;
	}

	public void setCompletedStrategy(Float completedStrategy) {
		this.completedStrategy = completedStrategy;
	}

	public BigDecimal getDraftCustomer() {
		return draftCustomer;
	}

	public void setDraftCustomer(BigDecimal draftCustomer) {
		this.draftCustomer = draftCustomer;
	}

	public Float getVisibleStock() {
		return visibleStock;
	}

	public void setVisibleStock(Float visibleStock) {
		this.visibleStock = visibleStock;
	}

	public BigDecimal getOldPolicy() {
		return oldPolicy;
	}

	public void setOldPolicy(BigDecimal oldPolicy) {
		this.oldPolicy = oldPolicy;
	}

	public Float getPrivateCustomer() {
		return privateCustomer;
	}

	public void setPrivateCustomer(Float privateCustomer) {
		this.privateCustomer = privateCustomer;
	}

	public List getStatusAudit() {
		return statusAudit;
	}

	public void setStatusAudit(List statusAudit) {
		this.statusAudit = statusAudit;
	}

	public Float getUpdatedCategory() {
		return updatedCategory;
	}

	public void setUpdatedCategory(Float updatedCategory) {
		this.updatedCategory = updatedCategory;
	}

	public BigDecimal getCountProduct() {
		return countProduct;
	}

	public void setCountProduct(BigDecimal countProduct) {
		this.countProduct = countProduct;
	}

	public Integer getTemporaryWarehouse() {
		return temporaryWarehouse;
	}

	public void setTemporaryWarehouse(Integer temporaryWarehouse) {
		this.temporaryWarehouse = temporaryWarehouse;
	}

	public Boolean getUnflaggedReview() {
		return unflaggedReview;
	}

	public void setUnflaggedReview(Boolean unflaggedReview) {
		this.unflaggedReview = unflaggedReview;
	}

	public Integer getFirstShipment() {
		return firstShipment;
	}

	public void setFirstShipment(Integer firstShipment) {
		this.firstShipment = firstShipment;
	}

	public String getDraftName() {
		return draftName;
	}

	public void setDraftName(String draftName) {
		this.draftName = draftName;
	}

	public Long getDraftFeedback() {
		return draftFeedback;
	}

	public void setDraftFeedback(Long draftFeedback) {
		this.draftFeedback = draftFeedback;
	}

	public Long getCompletedPolicy() {
		return completedPolicy;
	}

	public void setCompletedPolicy(Long completedPolicy) {
		this.completedPolicy = completedPolicy;
	}

	public String getSecureDiscount() {
		return secureDiscount;
	}

	public void setSecureDiscount(String secureDiscount) {
		this.secureDiscount = secureDiscount;
	}

	public String getUpdatedName() {
		return updatedName;
	}

	public void setUpdatedName(String updatedName) {
		this.updatedName = updatedName;
	}

	public Integer getUnreviewedDiscount() {
		return unreviewedDiscount;
	}

	public void setUnreviewedDiscount(Integer unreviewedDiscount) {
		this.unreviewedDiscount = unreviewedDiscount;
	}

	public String getPrivateReport() {
		return privateReport;
	}

	public void setPrivateReport(String privateReport) {
		this.privateReport = privateReport;
	}

	public Integer getPendingMerchant() {
		return pendingMerchant;
	}

	public void setPendingMerchant(Integer pendingMerchant) {
		this.pendingMerchant = pendingMerchant;
	}

	public Float getTypeTeam() {
		return typeTeam;
	}

	public void setTypeTeam(Float typeTeam) {
		this.typeTeam = typeTeam;
	}

	public Set getFirstDiscount() {
		return firstDiscount;
	}

	public void setFirstDiscount(Set firstDiscount) {
		this.firstDiscount = firstDiscount;
	}

	public String getFirstCoupon() {
		return firstCoupon;
	}

	public void setFirstCoupon(String firstCoupon) {
		this.firstCoupon = firstCoupon;
	}

	public Double getUnreportedAddress() {
		return unreportedAddress;
	}

	public void setUnreportedAddress(Double unreportedAddress) {
		this.unreportedAddress = unreportedAddress;
	}

	public Set getSyncedDocument() {
		return syncedDocument;
	}

	public void setSyncedDocument(Set syncedDocument) {
		this.syncedDocument = syncedDocument;
	}

	public Set getPublishedUser() {
		return publishedUser;
	}

	public void setPublishedUser(Set publishedUser) {
		this.publishedUser = publishedUser;
	}

	public Set getNextStrategy() {
		return nextStrategy;
	}

	public void setNextStrategy(Set nextStrategy) {
		this.nextStrategy = nextStrategy;
	}

	public List getUnlockedPhone() {
		return unlockedPhone;
	}

	public void setUnlockedPhone(List unlockedPhone) {
		this.unlockedPhone = unlockedPhone;
	}

	public Float getReportedEvent() {
		return reportedEvent;
	}

	public void setReportedEvent(Float reportedEvent) {
		this.reportedEvent = reportedEvent;
	}

	public Long getUnpublishedAudit() {
		return unpublishedAudit;
	}

	public void setUnpublishedAudit(Long unpublishedAudit) {
		this.unpublishedAudit = unpublishedAudit;
	}

	public Double getReviewedActivity() {
		return reviewedActivity;
	}

	public void setReviewedActivity(Double reviewedActivity) {
		this.reviewedActivity = reviewedActivity;
	}

	public Float getFlaggedSupplier() {
		return flaggedSupplier;
	}

	public void setFlaggedSupplier(Float flaggedSupplier) {
		this.flaggedSupplier = flaggedSupplier;
	}

	public Double getCurrentLog() {
		return currentLog;
	}

	public void setCurrentLog(Double currentLog) {
		this.currentLog = currentLog;
	}

	public Set getUnverifiedPolicy() {
		return unverifiedPolicy;
	}

	public void setUnverifiedPolicy(Set unverifiedPolicy) {
		this.unverifiedPolicy = unverifiedPolicy;
	}

	public Set getUnreportedStrategy() {
		return unreportedStrategy;
	}

	public void setUnreportedStrategy(Set unreportedStrategy) {
		this.unreportedStrategy = unreportedStrategy;
	}

	public String getOldPrice() {
		return oldPrice;
	}

	public void setOldPrice(String oldPrice) {
		this.oldPrice = oldPrice;
	}

	public Double getFinalGoal() {
		return finalGoal;
	}

	public void setFinalGoal(Double finalGoal) {
		this.finalGoal = finalGoal;
	}

	public Set getNextPayment() {
		return nextPayment;
	}

	public void setNextPayment(Set nextPayment) {
		this.nextPayment = nextPayment;
	}

	public Set getOldGroup() {
		return oldGroup;
	}

	public void setOldGroup(Set oldGroup) {
		this.oldGroup = oldGroup;
	}

	public String getImportedGroup() {
		return importedGroup;
	}

	public void setImportedGroup(String importedGroup) {
		this.importedGroup = importedGroup;
	}

	public Boolean getSyncedLog() {
		return syncedLog;
	}

	public void setSyncedLog(Boolean syncedLog) {
		this.syncedLog = syncedLog;
	}

	public String getPreviousPlan() {
		return previousPlan;
	}

	public void setPreviousPlan(String previousPlan) {
		this.previousPlan = previousPlan;
	}

	public Set getMainEvent() {
		return mainEvent;
	}

	public void setMainEvent(Set mainEvent) {
		this.mainEvent = mainEvent;
	}

	public String getLastHistory() {
		return lastHistory;
	}

	public void setLastHistory(String lastHistory) {
		this.lastHistory = lastHistory;
	}

	public Long getExportedStock() {
		return exportedStock;
	}

	public void setExportedStock(Long exportedStock) {
		this.exportedStock = exportedStock;
	}

	public List getLastName() {
		return lastName;
	}

	public void setLastName(List lastName) {
		this.lastName = lastName;
	}

	public Integer getUnpublishedSchedule() {
		return unpublishedSchedule;
	}

	public void setUnpublishedSchedule(Integer unpublishedSchedule) {
		this.unpublishedSchedule = unpublishedSchedule;
	}

	public Integer getCountTeam() {
		return countTeam;
	}

	public void setCountTeam(Integer countTeam) {
		this.countTeam = countTeam;
	}

	public BigDecimal getDecryptedOrder() {
		return decryptedOrder;
	}

	public void setDecryptedOrder(BigDecimal decryptedOrder) {
		this.decryptedOrder = decryptedOrder;
	}

	public Integer getPrivateMilestone() {
		return privateMilestone;
	}

	public void setPrivateMilestone(Integer privateMilestone) {
		this.privateMilestone = privateMilestone;
	}

	public Long getClosedBrand() {
		return closedBrand;
	}

	public void setClosedBrand(Long closedBrand) {
		this.closedBrand = closedBrand;
	}

	public Double getDecryptedPlan() {
		return decryptedPlan;
	}

	public void setDecryptedPlan(Double decryptedPlan) {
		this.decryptedPlan = decryptedPlan;
	}

	public Integer getReviewedTeam() {
		return reviewedTeam;
	}

	public void setReviewedTeam(Integer reviewedTeam) {
		this.reviewedTeam = reviewedTeam;
	}

	public List getUnreportedBrand() {
		return unreportedBrand;
	}

	public void setUnreportedBrand(List unreportedBrand) {
		this.unreportedBrand = unreportedBrand;
	}

	public Set getLockedAsset() {
		return lockedAsset;
	}

	public void setLockedAsset(Set lockedAsset) {
		this.lockedAsset = lockedAsset;
	}

	public String getPublishedStrategy() {
		return publishedStrategy;
	}

	public void setPublishedStrategy(String publishedStrategy) {
		this.publishedStrategy = publishedStrategy;
	}

	public Set getSyncedWarehouse() {
		return syncedWarehouse;
	}

	public void setSyncedWarehouse(Set syncedWarehouse) {
		this.syncedWarehouse = syncedWarehouse;
	}

	public Long getDisabledBill() {
		return disabledBill;
	}

	public void setDisabledBill(Long disabledBill) {
		this.disabledBill = disabledBill;
	}

	public Integer getFlaggedDate() {
		return flaggedDate;
	}

	public void setFlaggedDate(Integer flaggedDate) {
		this.flaggedDate = flaggedDate;
	}

	public Double getTypeGoal() {
		return typeGoal;
	}

	public void setTypeGoal(Double typeGoal) {
		this.typeGoal = typeGoal;
	}

	public Double getSharedProduct() {
		return sharedProduct;
	}

	public void setSharedProduct(Double sharedProduct) {
		this.sharedProduct = sharedProduct;
	}

	public Float getSharedDepartment() {
		return sharedDepartment;
	}

	public void setSharedDepartment(Float sharedDepartment) {
		this.sharedDepartment = sharedDepartment;
	}

	public Long getInitialStrategy() {
		return initialStrategy;
	}

	public void setInitialStrategy(Long initialStrategy) {
		this.initialStrategy = initialStrategy;
	}

	public Set getCurrentNotification() {
		return currentNotification;
	}

	public void setCurrentNotification(Set currentNotification) {
		this.currentNotification = currentNotification;
	}

	public Float getUpdatedMilestone() {
		return updatedMilestone;
	}

	public void setUpdatedMilestone(Float updatedMilestone) {
		this.updatedMilestone = updatedMilestone;
	}

	public Boolean getExportedMilestone() {
		return exportedMilestone;
	}

	public void setExportedMilestone(Boolean exportedMilestone) {
		this.exportedMilestone = exportedMilestone;
	}

	public Set getVisibleActivity() {
		return visibleActivity;
	}

	public void setVisibleActivity(Set visibleActivity) {
		this.visibleActivity = visibleActivity;
	}

	public String getPermanentName() {
		return permanentName;
	}

	public void setPermanentName(String permanentName) {
		this.permanentName = permanentName;
	}

	public String getSecondaryEvent() {
		return secondaryEvent;
	}

	public void setSecondaryEvent(String secondaryEvent) {
		this.secondaryEvent = secondaryEvent;
	}

	public String getCountGoal() {
		return countGoal;
	}

	public void setCountGoal(String countGoal) {
		this.countGoal = countGoal;
	}

	public Boolean getLevelDiscount() {
		return levelDiscount;
	}

	public void setLevelDiscount(Boolean levelDiscount) {
		this.levelDiscount = levelDiscount;
	}

	public Float getUnreviewedGoal() {
		return unreviewedGoal;
	}

	public void setUnreviewedGoal(Float unreviewedGoal) {
		this.unreviewedGoal = unreviewedGoal;
	}

	public BigDecimal getExportedAudit() {
		return exportedAudit;
	}

	public void setExportedAudit(BigDecimal exportedAudit) {
		this.exportedAudit = exportedAudit;
	}

	public Long getReviewedFolder() {
		return reviewedFolder;
	}

	public void setReviewedFolder(Long reviewedFolder) {
		this.reviewedFolder = reviewedFolder;
	}

	public Boolean getTotalName() {
		return totalName;
	}

	public void setTotalName(Boolean totalName) {
		this.totalName = totalName;
	}

	public Integer getUnlockedCustomer() {
		return unlockedCustomer;
	}

	public void setUnlockedCustomer(Integer unlockedCustomer) {
		this.unlockedCustomer = unlockedCustomer;
	}

	public Long getInactiveAudit() {
		return inactiveAudit;
	}

	public void setInactiveAudit(Long inactiveAudit) {
		this.inactiveAudit = inactiveAudit;
	}

	public String getRejectedInvoice() {
		return rejectedInvoice;
	}

	public void setRejectedInvoice(String rejectedInvoice) {
		this.rejectedInvoice = rejectedInvoice;
	}

	public Set getNewPolicy() {
		return newPolicy;
	}

	public void setNewPolicy(Set newPolicy) {
		this.newPolicy = newPolicy;
	}

	public Set getUnreportedOrder() {
		return unreportedOrder;
	}

	public void setUnreportedOrder(Set unreportedOrder) {
		this.unreportedOrder = unreportedOrder;
	}
}
