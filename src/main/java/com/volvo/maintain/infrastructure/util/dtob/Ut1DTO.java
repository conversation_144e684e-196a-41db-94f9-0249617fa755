package com.volvo.maintain.infrastructure.util.dtob;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
@Data
public class Ut1DTO {

    private Long savedCart;
    private List lockedName;
    private Double createdRole;
    private Boolean maxCategory;
    private Double countAchievement;
    private Double loadedDepartment;
    private Double createdSession;
    private String minReview;
    private String levelRole;
    private Float unpublishedInvoice;
    private Double unreportedMilestone;
    private Set secondaryEvent;
    private Double unreportedCustomer;
    private String totalCart;
    private Boolean flaggedFolder;
    private String newTransaction;
    private Boolean rejectedPolicy;
    private Float decryptedEvent;
    private Double unverifiedPrice;
    private Boolean averageDocument;
    private Integer inactiveActivity;
    private String closedTransaction;
    private Set oldAsset;
    private Float exportedResource;
    private Float flaggedLog;
    private Float maxAddress;
    private List failedDate;
    private Float createdRecord;
    private Float lockedDocument;
    private Long newUser;
    private String restoredSubscription;
    private String updatedContract;
    private BigDecimal openPayment;
    private BigDecimal primaryMilestone;
    private Long savedShipment;
    private Integer unverifiedFile;
    private BigDecimal privateMerchant;
    private String oldGroup;
    private Long sharedAudit;
    private String unlockedRecord;
    private List deletedProject;
    private String closedRecord;
    private String successfulName;
    private BigDecimal nextRole;
    private String mainFeedback;
    private Float countAddress;
    private String sharedSchedule;
    private Long unsecureSchedule;
    private Long failedTeam;
    private Integer visibleDate;
    private String draftFolder;
    private Integer secondaryDepartment;
    private Double syncedAchievement;
    private Long minAccount;
    private String updatedEvent;
    private Integer countFolder;
    private Boolean firstHistory;
    private Set previousBrand;
    private Double secondaryHistory;
    private String reportedAudit;
    private BigDecimal mainShipment;
    private List unlockedAudit;
    private Double levelSession;
    private Integer unsecureAudit;
    private Float updatedName;
    private String finalReport;
    private Double reviewedAchievement;
    private Double disabledCoupon;
    private Double totalHistory;
    private String disabledContract;
    private Set lastBill;
    private Set verifiedUser;
    private Double averageInvoice;
    private Double pendingEvent;
    private Double initialMessage;
    private BigDecimal firstSupplier;
    private BigDecimal restoredTransaction;
    private Integer unpublishedActivity;
    private Double totalOrder;
    private String privateMessage;
    private String restoredRole;
    private List currentDocument;
    private String draftSchedule;
    private List visibleMerchant;
    private String primaryActivity;
    private Set privateTask;
    private List unsecureLog;
    private String successfulReport;
    private Long lastDiscount;
    private Set unverifiedEvent;
    private Integer secondaryMilestone;
    private Boolean lastSchedule;
    private Set previousUser;
    private Long draftNotification;
    private String syncedUser;
    private Boolean finalBrand;
    private BigDecimal openCoupon;
    private BigDecimal approvedProject;
}
