package com.volvo.maintain.infrastructure.util.utbexama;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yonyou.cyx.function.utils.jsonserializer.localdatetime.JsonLocalDateTimeDeserializer;
import com.yonyou.cyx.function.utils.jsonserializer.localdatetime.JsonLocalDateTimeSerializer;

import java.time.LocalDateTime;

public class UtGntDTO {
    /**
     * 主键ID
     */
    private Long itemId;

    /**
     * 保修单ID
     */
    private Long warrantyId;

    /**
     * 序号
     */
    private Long jobNo;

    /**
     * 支持文件
     */
    private String supportFile;

    /**
     * 活动编号
     */
    private String activityNo;

    /**
     * 本次参加活动次数
     */
    private Integer activityNumber;

    /**
     * 第几次上传
     */
    private Integer uploadTotal;

    /**
     * 上传时间
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime uploadTime;

    /**
     * 项目代码
     */
    private String itemCode;

    /**
     * 交修项目名称
     */
    private String itemName;

    /**
     * 保修类别1
     */
    private String warrantyType1;

    /**
     * 保修类别2
     */
    private String warrantyType2;

    /**
     * CSC代码
     */
    private String cscCode;

    /**
     * 原因代码
     */
    private String reasonCode;

    /**
     * QB代码
     */
    private String qbNo;

    /**
     * 故障零件号
     */
    private String faultPart;

    /**
     * 客户症状描述
     */
    private String symptomRemark;

    /**
     * 客户原因描述
     */
    private String reasonRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 只有在保修类别为“零件保修”“附件保修”时，可以修改
     */
    @JsonDeserialize(using = JsonLocalDateTimeDeserializer.class)
    @JsonSerialize(using = JsonLocalDateTimeSerializer.class)
    private LocalDateTime sparePartsDate;

    /**
     * 只有在保修类别为“零件保修”“附件保修”时，可以修改
     */
    private String purchPart;

    /**
     * 维修说明
     */
    private String repairRemark;

    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Long orgId;

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public Long getWarrantyId() {
        return warrantyId;
    }

    public void setWarrantyId(Long warrantyId) {
        this.warrantyId = warrantyId;
    }

    public Long getJobNo() {
        return jobNo;
    }

    public void setJobNo(Long jobNo) {
        this.jobNo = jobNo;
    }

    public String getSupportFile() {
        return supportFile;
    }

    public void setSupportFile(String supportFile) {
        this.supportFile = supportFile;
    }

    public String getActivityNo() {
        return activityNo;
    }

    public void setActivityNo(String activityNo) {
        this.activityNo = activityNo;
    }

    public Integer getActivityNumber() {
        return activityNumber;
    }

    public void setActivityNumber(Integer activityNumber) {
        this.activityNumber = activityNumber;
    }

    public Integer getUploadTotal() {
        return uploadTotal;
    }

    public void setUploadTotal(Integer uploadTotal) {
        this.uploadTotal = uploadTotal;
    }

    public LocalDateTime getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getWarrantyType1() {
        return warrantyType1;
    }

    public void setWarrantyType1(String warrantyType1) {
        this.warrantyType1 = warrantyType1;
    }

    public String getWarrantyType2() {
        return warrantyType2;
    }

    public void setWarrantyType2(String warrantyType2) {
        this.warrantyType2 = warrantyType2;
    }

    public String getCscCode() {
        return cscCode;
    }

    public void setCscCode(String cscCode) {
        this.cscCode = cscCode;
    }

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getQbNo() {
        return qbNo;
    }

    public void setQbNo(String qbNo) {
        this.qbNo = qbNo;
    }

    public String getFaultPart() {
        return faultPart;
    }

    public void setFaultPart(String faultPart) {
        this.faultPart = faultPart;
    }

    public String getSymptomRemark() {
        return symptomRemark;
    }

    public void setSymptomRemark(String symptomRemark) {
        this.symptomRemark = symptomRemark;
    }

    public String getReasonRemark() {
        return reasonRemark;
    }

    public void setReasonRemark(String reasonRemark) {
        this.reasonRemark = reasonRemark;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getSparePartsDate() {
        return sparePartsDate;
    }

    public void setSparePartsDate(LocalDateTime sparePartsDate) {
        this.sparePartsDate = sparePartsDate;
    }

    public String getPurchPart() {
        return purchPart;
    }

    public void setPurchPart(String purchPart) {
        this.purchPart = purchPart;
    }

    public String getRepairRemark() {
        return repairRemark;
    }

    public void setRepairRemark(String repairRemark) {
        this.repairRemark = repairRemark;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getOwnerParCode() {
        return ownerParCode;
    }

    public void setOwnerParCode(String ownerParCode) {
        this.ownerParCode = ownerParCode;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Integer getDataSources() {
        return dataSources;
    }

    public void setDataSources(Integer dataSources) {
        this.dataSources = dataSources;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除，1：删除，0：未删除
     */
    private Integer isDelete;

    /**
     * 是否有效
     */
    private Integer isValid;
}
