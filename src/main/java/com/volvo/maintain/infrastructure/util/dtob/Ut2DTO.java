package com.volvo.maintain.infrastructure.util.dtob;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
@Data
public class Ut2DTO {
    private String activeSupplier;
    private Float savedFeedback;
    private String unverifiedBill;
    private List deletedShipment;
    private Boolean unverifiedDepartment;
    private Float unsecureCustomer;
    private List unreviewedCategory;
    private Long flaggedRecord;
    private Double rejectedMessage;
    private Integer levelRole;
    private String unsecureStrategy;
    private List publicStrategy;
    private Integer privateBrand;
    private Float reviewedInvoice;
    private String createdBill;
    private Boolean secureFeedback;
    private Set maxStock;
    private Integer createdFolder;
    private Float previousSupplier;
    private Long lockedTeam;
    private Long unsecureDocument;
    private Double unsecureNotification;
    private Long permanentHistory;
    private Boolean draftSession;
    private Float approvedResource;
    private Boolean modifiedTransaction;
    private Long initialUser;
    private BigDecimal permanentPayment;
    private String hiddenCart;
    private Boolean rejectedEmail;
    private Long closedProduct;
    private Double previousBrand;
    private Long verifiedOrder;
    private Boolean maxEvent;
    private Float statusFile;
    private Long restoredMilestone;
    private Boolean publishedBrand;
    private BigDecimal encryptedGroup;
    private Integer draftPayment;
    private String permanentGoal;
    private Set finalLog;
    private List pendingPhone;
    private BigDecimal publicInvoice;
    private Float nextOrder;
    private Integer lockedAgreement;
    private String failedStock;
    private Boolean pendingOrder;
    private Boolean secondarySchedule;
    private Set verifiedReview;
    private Boolean unreportedLog;
    private Float verifiedSubscription;
    private Long nextMessage;
    private String privateDepartment;
    private List verifiedAchievement;
    private Boolean temporaryRole;
    private BigDecimal previousName;
    private Integer lastEvent;
    private Boolean createdCategory;
    private Double firstBill;
    private Set unsecureGroup;
    private Double temporaryPermission;
    private String currentStock;
    private Long averageCart;
    private Float activeEmail;
    private Float activeCustomer;
    private Float finalUser;
    private Double pendingLog;
    private Float unflaggedItem;
    private Boolean decryptedAgreement;
    private Boolean reportedFeedback;
    private String minAchievement;
    private List minFeedback;
    private BigDecimal importedAddress;
    private Integer archivedGoal;
    private String maxBill;
    private String importedTask;
    private Double sharedTransaction;
    private Long reportedEvent;
    private Set publishedProduct;
    private Integer finalOrder;
    private Long unlockedSession;
    private Boolean newFile;
    private String unsecureFile;
    private Long secondarySupplier;
    private BigDecimal countFeedback;
    private Float unreviewedCart;
    private String firstInvoice;
    private Integer newPlan;
    private List closedProject;
    private BigDecimal successfulDocument;
    private Long updatedReport;
    private Float unflaggedBrand;
    private Integer mainHistory;
    private Boolean oldPrice;
    private Float archivedCustomer;
    private Float importedWarehouse;
    private Float exportedItem;
    private Integer unflaggedGroup;
    private Long primaryFolder;
}
