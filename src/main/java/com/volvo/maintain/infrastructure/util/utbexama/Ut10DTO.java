package com.volvo.maintain.infrastructure.util.utbexama;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
public class Ut10DTO {
    private Integer lockedBill;
    private List approvedReport;
    private String archivedSubscription;
    private Float totalCategory;
    private List verifiedBill;
    private BigDecimal loadedReport;
    private BigDecimal openSupplier;
    private String totalAccount;
    private String openPhone;
    private Long statusTransaction;
    private List levelAsset;
    private Integer minPlan;
    private String disabledCategory;
    private BigDecimal reviewedEvent;
    private String maxPolicy;
    private Float exportedDepartment;
    private String createdLog;
    private BigDecimal previousPermission;
    private Integer completedShipment;
    private Boolean lastUser;
    private Integer inactiveBill;
    private BigDecimal currentItem;
    private Long publicFolder;
    private Long inactiveCoupon;
    private Long activeUser;
    private List initialTeam;
    private List successfulAgreement;
    private Boolean permanentFeedback;
    private String pendingPlan;
    private String flaggedSchedule;
    private Set primaryNotification;
    private Boolean closedProduct;
    private Float levelAgreement;
    private Integer statusMessage;
    private Integer firstPermission;
    private Set draftAudit;
    private List closedWarehouse;
    private Double draftMessage;
    private Long updatedFeedback;
    private Long mainLog;
    private BigDecimal unreviewedAccount;
    private Double updatedDate;
    private String reportedGroup;
    private Integer oldName;
    private String archivedAchievement;
    private BigDecimal minFeedback;
    private List initialBrand;
    private String activeCategory;
    private Boolean minStock;
    private Double sharedStock;
    private Double modifiedAgreement;
    private List averageRole;
    private Long publishedReport;
    private Boolean finalTask;
    private Long exportedEvent;
    private Long unsecureName;
    private Float createdCategory;
    private Double unsecureTransaction;
    private Integer lockedCart;
    private Float typeTask;
    private Boolean loadedBill;
    private Boolean savedItem;
    private BigDecimal restoredNotification;
    private Long unreviewedPayment;
    private Long encryptedDate;
    private Double encryptedDocument;
    private String levelEmail;
    private String disabledUser;
    private BigDecimal inactiveOrder;
    private Set privatePolicy;
    private Boolean publishedName;
    private Float reviewedShipment;
    private String restoredName;
    private String reviewedSession;
    private Float createdInvoice;
    private Boolean finalFeedback;
    private String mainReview;
    private List loadedDiscount;
    private Float savedRole;
    private Boolean finalMerchant;
    private Set permanentDiscount;
    private BigDecimal maxMerchant;
    private Boolean draftFile;
    private Set averageProduct;
    private BigDecimal typeStock;
    private Boolean flaggedPolicy;
    private String restoredGroup;
    private Long oldPrice;
    private List lastCategory;
    private List oldAudit;
    private String reviewedTeam;
    private Float visibleShipment;
    private Double createdBill;
    private List previousName;
    private Float approvedTask;
    private Float oldUser;
    private String restoredBrand;
    private Double syncedPermission;
    private Boolean unsecureAsset;
    private Double initialResource;

    public Integer getLockedBill() {
        return lockedBill;
    }

    public void setLockedBill(Integer lockedBill) {
        this.lockedBill = lockedBill;
    }

    public List getApprovedReport() {
        return approvedReport;
    }

    public void setApprovedReport(List approvedReport) {
        this.approvedReport = approvedReport;
    }

    public String getArchivedSubscription() {
        return archivedSubscription;
    }

    public void setArchivedSubscription(String archivedSubscription) {
        this.archivedSubscription = archivedSubscription;
    }

    public Float getTotalCategory() {
        return totalCategory;
    }

    public void setTotalCategory(Float totalCategory) {
        this.totalCategory = totalCategory;
    }

    public List getVerifiedBill() {
        return verifiedBill;
    }

    public void setVerifiedBill(List verifiedBill) {
        this.verifiedBill = verifiedBill;
    }

    public BigDecimal getLoadedReport() {
        return loadedReport;
    }

    public void setLoadedReport(BigDecimal loadedReport) {
        this.loadedReport = loadedReport;
    }

    public BigDecimal getOpenSupplier() {
        return openSupplier;
    }

    public void setOpenSupplier(BigDecimal openSupplier) {
        this.openSupplier = openSupplier;
    }

    public String getTotalAccount() {
        return totalAccount;
    }

    public void setTotalAccount(String totalAccount) {
        this.totalAccount = totalAccount;
    }

    public String getOpenPhone() {
        return openPhone;
    }

    public void setOpenPhone(String openPhone) {
        this.openPhone = openPhone;
    }

    public Long getStatusTransaction() {
        return statusTransaction;
    }

    public void setStatusTransaction(Long statusTransaction) {
        this.statusTransaction = statusTransaction;
    }

    public List getLevelAsset() {
        return levelAsset;
    }

    public void setLevelAsset(List levelAsset) {
        this.levelAsset = levelAsset;
    }

    public Integer getMinPlan() {
        return minPlan;
    }

    public void setMinPlan(Integer minPlan) {
        this.minPlan = minPlan;
    }

    public String getDisabledCategory() {
        return disabledCategory;
    }

    public void setDisabledCategory(String disabledCategory) {
        this.disabledCategory = disabledCategory;
    }

    public BigDecimal getReviewedEvent() {
        return reviewedEvent;
    }

    public void setReviewedEvent(BigDecimal reviewedEvent) {
        this.reviewedEvent = reviewedEvent;
    }

    public String getMaxPolicy() {
        return maxPolicy;
    }

    public void setMaxPolicy(String maxPolicy) {
        this.maxPolicy = maxPolicy;
    }

    public Float getExportedDepartment() {
        return exportedDepartment;
    }

    public void setExportedDepartment(Float exportedDepartment) {
        this.exportedDepartment = exportedDepartment;
    }

    public String getCreatedLog() {
        return createdLog;
    }

    public void setCreatedLog(String createdLog) {
        this.createdLog = createdLog;
    }

    public BigDecimal getPreviousPermission() {
        return previousPermission;
    }

    public void setPreviousPermission(BigDecimal previousPermission) {
        this.previousPermission = previousPermission;
    }

    public Integer getCompletedShipment() {
        return completedShipment;
    }

    public void setCompletedShipment(Integer completedShipment) {
        this.completedShipment = completedShipment;
    }

    public Boolean getLastUser() {
        return lastUser;
    }

    public void setLastUser(Boolean lastUser) {
        this.lastUser = lastUser;
    }

    public Integer getInactiveBill() {
        return inactiveBill;
    }

    public void setInactiveBill(Integer inactiveBill) {
        this.inactiveBill = inactiveBill;
    }

    public BigDecimal getCurrentItem() {
        return currentItem;
    }

    public void setCurrentItem(BigDecimal currentItem) {
        this.currentItem = currentItem;
    }

    public Long getPublicFolder() {
        return publicFolder;
    }

    public void setPublicFolder(Long publicFolder) {
        this.publicFolder = publicFolder;
    }

    public Long getInactiveCoupon() {
        return inactiveCoupon;
    }

    public void setInactiveCoupon(Long inactiveCoupon) {
        this.inactiveCoupon = inactiveCoupon;
    }

    public Long getActiveUser() {
        return activeUser;
    }

    public void setActiveUser(Long activeUser) {
        this.activeUser = activeUser;
    }

    public List getInitialTeam() {
        return initialTeam;
    }

    public void setInitialTeam(List initialTeam) {
        this.initialTeam = initialTeam;
    }

    public List getSuccessfulAgreement() {
        return successfulAgreement;
    }

    public void setSuccessfulAgreement(List successfulAgreement) {
        this.successfulAgreement = successfulAgreement;
    }

    public Boolean getPermanentFeedback() {
        return permanentFeedback;
    }

    public void setPermanentFeedback(Boolean permanentFeedback) {
        this.permanentFeedback = permanentFeedback;
    }

    public String getPendingPlan() {
        return pendingPlan;
    }

    public void setPendingPlan(String pendingPlan) {
        this.pendingPlan = pendingPlan;
    }

    public String getFlaggedSchedule() {
        return flaggedSchedule;
    }

    public void setFlaggedSchedule(String flaggedSchedule) {
        this.flaggedSchedule = flaggedSchedule;
    }

    public Set getPrimaryNotification() {
        return primaryNotification;
    }

    public void setPrimaryNotification(Set primaryNotification) {
        this.primaryNotification = primaryNotification;
    }

    public Boolean getClosedProduct() {
        return closedProduct;
    }

    public void setClosedProduct(Boolean closedProduct) {
        this.closedProduct = closedProduct;
    }

    public Float getLevelAgreement() {
        return levelAgreement;
    }

    public void setLevelAgreement(Float levelAgreement) {
        this.levelAgreement = levelAgreement;
    }

    public Integer getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(Integer statusMessage) {
        this.statusMessage = statusMessage;
    }

    public Integer getFirstPermission() {
        return firstPermission;
    }

    public void setFirstPermission(Integer firstPermission) {
        this.firstPermission = firstPermission;
    }

    public Set getDraftAudit() {
        return draftAudit;
    }

    public void setDraftAudit(Set draftAudit) {
        this.draftAudit = draftAudit;
    }

    public List getClosedWarehouse() {
        return closedWarehouse;
    }

    public void setClosedWarehouse(List closedWarehouse) {
        this.closedWarehouse = closedWarehouse;
    }

    public Double getDraftMessage() {
        return draftMessage;
    }

    public void setDraftMessage(Double draftMessage) {
        this.draftMessage = draftMessage;
    }

    public Long getUpdatedFeedback() {
        return updatedFeedback;
    }

    public void setUpdatedFeedback(Long updatedFeedback) {
        this.updatedFeedback = updatedFeedback;
    }

    public Long getMainLog() {
        return mainLog;
    }

    public void setMainLog(Long mainLog) {
        this.mainLog = mainLog;
    }

    public BigDecimal getUnreviewedAccount() {
        return unreviewedAccount;
    }

    public void setUnreviewedAccount(BigDecimal unreviewedAccount) {
        this.unreviewedAccount = unreviewedAccount;
    }

    public Double getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Double updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getReportedGroup() {
        return reportedGroup;
    }

    public void setReportedGroup(String reportedGroup) {
        this.reportedGroup = reportedGroup;
    }

    public Integer getOldName() {
        return oldName;
    }

    public void setOldName(Integer oldName) {
        this.oldName = oldName;
    }

    public String getArchivedAchievement() {
        return archivedAchievement;
    }

    public void setArchivedAchievement(String archivedAchievement) {
        this.archivedAchievement = archivedAchievement;
    }

    public BigDecimal getMinFeedback() {
        return minFeedback;
    }

    public void setMinFeedback(BigDecimal minFeedback) {
        this.minFeedback = minFeedback;
    }

    public List getInitialBrand() {
        return initialBrand;
    }

    public void setInitialBrand(List initialBrand) {
        this.initialBrand = initialBrand;
    }

    public String getActiveCategory() {
        return activeCategory;
    }

    public void setActiveCategory(String activeCategory) {
        this.activeCategory = activeCategory;
    }

    public Boolean getMinStock() {
        return minStock;
    }

    public void setMinStock(Boolean minStock) {
        this.minStock = minStock;
    }

    public Double getSharedStock() {
        return sharedStock;
    }

    public void setSharedStock(Double sharedStock) {
        this.sharedStock = sharedStock;
    }

    public Double getModifiedAgreement() {
        return modifiedAgreement;
    }

    public void setModifiedAgreement(Double modifiedAgreement) {
        this.modifiedAgreement = modifiedAgreement;
    }

    public List getAverageRole() {
        return averageRole;
    }

    public void setAverageRole(List averageRole) {
        this.averageRole = averageRole;
    }

    public Long getPublishedReport() {
        return publishedReport;
    }

    public void setPublishedReport(Long publishedReport) {
        this.publishedReport = publishedReport;
    }

    public Boolean getFinalTask() {
        return finalTask;
    }

    public void setFinalTask(Boolean finalTask) {
        this.finalTask = finalTask;
    }

    public Long getExportedEvent() {
        return exportedEvent;
    }

    public void setExportedEvent(Long exportedEvent) {
        this.exportedEvent = exportedEvent;
    }

    public Long getUnsecureName() {
        return unsecureName;
    }

    public void setUnsecureName(Long unsecureName) {
        this.unsecureName = unsecureName;
    }

    public Float getCreatedCategory() {
        return createdCategory;
    }

    public void setCreatedCategory(Float createdCategory) {
        this.createdCategory = createdCategory;
    }

    public Double getUnsecureTransaction() {
        return unsecureTransaction;
    }

    public void setUnsecureTransaction(Double unsecureTransaction) {
        this.unsecureTransaction = unsecureTransaction;
    }

    public Integer getLockedCart() {
        return lockedCart;
    }

    public void setLockedCart(Integer lockedCart) {
        this.lockedCart = lockedCart;
    }

    public Float getTypeTask() {
        return typeTask;
    }

    public void setTypeTask(Float typeTask) {
        this.typeTask = typeTask;
    }

    public Boolean getLoadedBill() {
        return loadedBill;
    }

    public void setLoadedBill(Boolean loadedBill) {
        this.loadedBill = loadedBill;
    }

    public Boolean getSavedItem() {
        return savedItem;
    }

    public void setSavedItem(Boolean savedItem) {
        this.savedItem = savedItem;
    }

    public BigDecimal getRestoredNotification() {
        return restoredNotification;
    }

    public void setRestoredNotification(BigDecimal restoredNotification) {
        this.restoredNotification = restoredNotification;
    }

    public Long getUnreviewedPayment() {
        return unreviewedPayment;
    }

    public void setUnreviewedPayment(Long unreviewedPayment) {
        this.unreviewedPayment = unreviewedPayment;
    }

    public Long getEncryptedDate() {
        return encryptedDate;
    }

    public void setEncryptedDate(Long encryptedDate) {
        this.encryptedDate = encryptedDate;
    }

    public Double getEncryptedDocument() {
        return encryptedDocument;
    }

    public void setEncryptedDocument(Double encryptedDocument) {
        this.encryptedDocument = encryptedDocument;
    }

    public String getLevelEmail() {
        return levelEmail;
    }

    public void setLevelEmail(String levelEmail) {
        this.levelEmail = levelEmail;
    }

    public String getDisabledUser() {
        return disabledUser;
    }

    public void setDisabledUser(String disabledUser) {
        this.disabledUser = disabledUser;
    }

    public BigDecimal getInactiveOrder() {
        return inactiveOrder;
    }

    public void setInactiveOrder(BigDecimal inactiveOrder) {
        this.inactiveOrder = inactiveOrder;
    }

    public Set getPrivatePolicy() {
        return privatePolicy;
    }

    public void setPrivatePolicy(Set privatePolicy) {
        this.privatePolicy = privatePolicy;
    }

    public Boolean getPublishedName() {
        return publishedName;
    }

    public void setPublishedName(Boolean publishedName) {
        this.publishedName = publishedName;
    }

    public Float getReviewedShipment() {
        return reviewedShipment;
    }

    public void setReviewedShipment(Float reviewedShipment) {
        this.reviewedShipment = reviewedShipment;
    }

    public String getRestoredName() {
        return restoredName;
    }

    public void setRestoredName(String restoredName) {
        this.restoredName = restoredName;
    }

    public String getReviewedSession() {
        return reviewedSession;
    }

    public void setReviewedSession(String reviewedSession) {
        this.reviewedSession = reviewedSession;
    }

    public Float getCreatedInvoice() {
        return createdInvoice;
    }

    public void setCreatedInvoice(Float createdInvoice) {
        this.createdInvoice = createdInvoice;
    }

    public Boolean getFinalFeedback() {
        return finalFeedback;
    }

    public void setFinalFeedback(Boolean finalFeedback) {
        this.finalFeedback = finalFeedback;
    }

    public String getMainReview() {
        return mainReview;
    }

    public void setMainReview(String mainReview) {
        this.mainReview = mainReview;
    }

    public List getLoadedDiscount() {
        return loadedDiscount;
    }

    public void setLoadedDiscount(List loadedDiscount) {
        this.loadedDiscount = loadedDiscount;
    }

    public Float getSavedRole() {
        return savedRole;
    }

    public void setSavedRole(Float savedRole) {
        this.savedRole = savedRole;
    }

    public Boolean getFinalMerchant() {
        return finalMerchant;
    }

    public void setFinalMerchant(Boolean finalMerchant) {
        this.finalMerchant = finalMerchant;
    }

    public Set getPermanentDiscount() {
        return permanentDiscount;
    }

    public void setPermanentDiscount(Set permanentDiscount) {
        this.permanentDiscount = permanentDiscount;
    }

    public BigDecimal getMaxMerchant() {
        return maxMerchant;
    }

    public void setMaxMerchant(BigDecimal maxMerchant) {
        this.maxMerchant = maxMerchant;
    }

    public Boolean getDraftFile() {
        return draftFile;
    }

    public void setDraftFile(Boolean draftFile) {
        this.draftFile = draftFile;
    }

    public Set getAverageProduct() {
        return averageProduct;
    }

    public void setAverageProduct(Set averageProduct) {
        this.averageProduct = averageProduct;
    }

    public BigDecimal getTypeStock() {
        return typeStock;
    }

    public void setTypeStock(BigDecimal typeStock) {
        this.typeStock = typeStock;
    }

    public Boolean getFlaggedPolicy() {
        return flaggedPolicy;
    }

    public void setFlaggedPolicy(Boolean flaggedPolicy) {
        this.flaggedPolicy = flaggedPolicy;
    }

    public String getRestoredGroup() {
        return restoredGroup;
    }

    public void setRestoredGroup(String restoredGroup) {
        this.restoredGroup = restoredGroup;
    }

    public Long getOldPrice() {
        return oldPrice;
    }

    public void setOldPrice(Long oldPrice) {
        this.oldPrice = oldPrice;
    }

    public List getLastCategory() {
        return lastCategory;
    }

    public void setLastCategory(List lastCategory) {
        this.lastCategory = lastCategory;
    }

    public List getOldAudit() {
        return oldAudit;
    }

    public void setOldAudit(List oldAudit) {
        this.oldAudit = oldAudit;
    }

    public String getReviewedTeam() {
        return reviewedTeam;
    }

    public void setReviewedTeam(String reviewedTeam) {
        this.reviewedTeam = reviewedTeam;
    }

    public Float getVisibleShipment() {
        return visibleShipment;
    }

    public void setVisibleShipment(Float visibleShipment) {
        this.visibleShipment = visibleShipment;
    }

    public Double getCreatedBill() {
        return createdBill;
    }

    public void setCreatedBill(Double createdBill) {
        this.createdBill = createdBill;
    }

    public List getPreviousName() {
        return previousName;
    }

    public void setPreviousName(List previousName) {
        this.previousName = previousName;
    }

    public Float getApprovedTask() {
        return approvedTask;
    }

    public void setApprovedTask(Float approvedTask) {
        this.approvedTask = approvedTask;
    }

    public Float getOldUser() {
        return oldUser;
    }

    public void setOldUser(Float oldUser) {
        this.oldUser = oldUser;
    }

    public String getRestoredBrand() {
        return restoredBrand;
    }

    public void setRestoredBrand(String restoredBrand) {
        this.restoredBrand = restoredBrand;
    }

    public Double getSyncedPermission() {
        return syncedPermission;
    }

    public void setSyncedPermission(Double syncedPermission) {
        this.syncedPermission = syncedPermission;
    }

    public Boolean getUnsecureAsset() {
        return unsecureAsset;
    }

    public void setUnsecureAsset(Boolean unsecureAsset) {
        this.unsecureAsset = unsecureAsset;
    }

    public Double getInitialResource() {
        return initialResource;
    }

    public void setInitialResource(Double initialResource) {
        this.initialResource = initialResource;
    }
}
