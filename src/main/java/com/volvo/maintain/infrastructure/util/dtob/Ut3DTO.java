package com.volvo.maintain.infrastructure.util.dtob;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
@Data
public class Ut3DTO {
    private String unsecureWarehouse;
    private Float successfulPayment;
    private Set maxPrice;
    private Boolean nextMessage;
    private Boolean maxItem;
    private Boolean approvedWarehouse;
    private Double currentLog;
    private String initialPermission;
    private String initialFolder;
    private List draftStock;
    private Boolean inactiveCoupon;
    private Long secondarySession;
    private BigDecimal maxAsset;
    private Set maxFolder;
    private String activeTask;
    private String unpublishedEmail;
    private Integer reportedPermission;
    private Set unsecureShipment;
    private List unreportedReview;
    private List verifiedSchedule;
    private String publishedCart;
    private List nextPermission;
    private String modifiedTeam;
    private Set permanentGoal;
    private String syncedProject;
    private Set decryptedFeedback;
    private List successfulNotification;
    private BigDecimal reportedTeam;
    private Set activeSupplier;
    private Float unpublishedUser;
    private Long loadedItem;
    private String previousHistory;
    private Double approvedShipment;
    private BigDecimal failedPlan;
    private Long reportedMerchant;
    private Double hiddenSupplier;
    private String privatePrice;
    private Float hiddenUser;
    private String openFile;
    private Long sharedSession;
    private String approvedSchedule;
    private String typeInvoice;
    private String modifiedStock;
    private BigDecimal firstCategory;
    private Long exportedPolicy;
    private Double unreportedStrategy;
    private Set visibleDepartment;
    private BigDecimal finalProduct;
    private Long updatedBill;
    private String decryptedResource;
    private Float maxSubscription;
    private Float primarySupplier;
    private String previousStrategy;
    private Long currentRecord;
    private Integer currentOrder;
    private String permanentMerchant;
    private Boolean flaggedPayment;
    private Set lockedProduct;
    private BigDecimal levelPlan;
    private Set hiddenPrice;
    private Long disabledGoal;
    private String verifiedGroup;
    private Long archivedStrategy;
    private Integer primaryTask;
    private String unreviewedWarehouse;
    private Integer countShipment;
    private String updatedFolder;
    private String previousEmail;
    private Float importedShipment;
    private Set mainCart;
    private Set sharedCustomer;
    private Float publishedCustomer;
    private Set lockedTransaction;
    private Long sharedDate;
    private Integer loadedNotification;
    private Integer enabledSchedule;
    private BigDecimal levelUser;
    private Double rejectedAsset;
    private Long unpublishedCart;
    private Long savedAsset;
    private Boolean unreportedCoupon;
    private Double activeFolder;
    private BigDecimal visibleStrategy;
    private Set countPrice;
    private Set lockedEmail;
    private Set closedAchievement;
    private String mainSupplier;
    private Float exportedAudit;
    private String temporarySubscription;
    private Integer privateBrand;
    private Boolean createdPrice;
    private Float reportedCategory;
    private List importedTeam;
    private Boolean encryptedEmail;
    private BigDecimal reviewedReport;
    private Integer restoredRecord;
    private Integer activeUser;
    private Float pendingGoal;
}
