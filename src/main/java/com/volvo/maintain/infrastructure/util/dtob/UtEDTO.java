
package com.volvo.maintain.infrastructure.util.dtob;



import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class UtEDTO {
	private BigDecimal secondaryShipment;
	private Set loadedTask;
	private Integer reportedPrice;
	private Double publishedRecord;
	private Float pendingAddress;
	private Boolean openAccount;
	private String rejectedUser;
	private String nextFile;
	private Boolean inactiveGroup;
	private Integer unsecureEmail;
	private Set completedSupplier;
	private Long archivedPermission;
	private Long secureStock;
	private Long unverifiedRole;
	private Long openMessage;
	private BigDecimal exportedPlan;
	private Long loadedAddress;
	private Integer exportedAddress;
	private Double lockedBill;
	private Float restoredReview;
	private BigDecimal completedGoal;
	private Boolean closedFeedback;
	private Long failedUser;
	private String updatedFolder;
	private Long unsecureUser;
	private Float loadedPolicy;
	private Long unpublishedCategory;
	private String reportedContract;
	private Double unlockedPrice;
	private Boolean verifiedEvent;
	private List sharedAccount;
	private Integer totalBrand;
	private Double updatedDate;
	private Boolean temporaryReport;
	private Long mainDocument;
	private Set secureFeedback;
	private String reviewedRole;
	private String previousFile;
	private Float permanentPhone;
	private BigDecimal flaggedUser;
	private Boolean failedTeam;
	private String savedGoal;
	private List initialPolicy;
	private Integer minPermission;
	private String archivedAudit;
	private BigDecimal restoredResource;
	private Long initialDate;
	private Double finalPermission;
	private Double closedActivity;
	private String savedReview;
	private List verifiedTask;
	private String exportedPhone;
	private Long reviewedGroup;
	private String disabledName;
	private Long firstProject;
	private List nextProject;
	private String activeShipment;
	private List publicWarehouse;
	private Set approvedEvent;
	private String modifiedMessage;
	private Set approvedResource;
	private Integer totalSupplier;
	private Double nextWarehouse;
	private String unreviewedUser;
	private String inactiveEmail;
	private Boolean successfulGoal;
	private Boolean lastAudit;
	private Float successfulMilestone;
	private Long totalAudit;
	private String verifiedGoal;
	private String importedPolicy;
	private Long firstAddress;
	private Double deletedGoal;
	private BigDecimal failedSchedule;
	private Integer statusCart;
	private Set flaggedGoal;
	private BigDecimal initialFeedback;
	private Float importedShipment;
	private Integer minSubscription;
	private String permanentFeedback;
	private Long firstNotification;
	private String reviewedResource;
	private Set rejectedRole;
	private List sharedLog;
	private Set temporaryPlan;
	private List visibleEvent;
	private Set openBrand;
	private Long restoredCustomer;
	private String exportedAccount;
	private String securePermission;
	private BigDecimal verifiedNotification;


	public BigDecimal getSecondaryShipment() {
		return secondaryShipment;
	}

	public void setSecondaryShipment(BigDecimal secondaryShipment) {
		this.secondaryShipment = secondaryShipment;
	}

	public Set getLoadedTask() {
		return loadedTask;
	}

	public void setLoadedTask(Set loadedTask) {
		this.loadedTask = loadedTask;
	}

	public Integer getReportedPrice() {
		return reportedPrice;
	}

	public void setReportedPrice(Integer reportedPrice) {
		this.reportedPrice = reportedPrice;
	}

	public Double getPublishedRecord() {
		return publishedRecord;
	}

	public void setPublishedRecord(Double publishedRecord) {
		this.publishedRecord = publishedRecord;
	}

	public Float getPendingAddress() {
		return pendingAddress;
	}

	public void setPendingAddress(Float pendingAddress) {
		this.pendingAddress = pendingAddress;
	}

	public Boolean getOpenAccount() {
		return openAccount;
	}

	public void setOpenAccount(Boolean openAccount) {
		this.openAccount = openAccount;
	}

	public String getRejectedUser() {
		return rejectedUser;
	}

	public void setRejectedUser(String rejectedUser) {
		this.rejectedUser = rejectedUser;
	}

	public String getNextFile() {
		return nextFile;
	}

	public void setNextFile(String nextFile) {
		this.nextFile = nextFile;
	}

	public Boolean getInactiveGroup() {
		return inactiveGroup;
	}

	public void setInactiveGroup(Boolean inactiveGroup) {
		this.inactiveGroup = inactiveGroup;
	}

	public Integer getUnsecureEmail() {
		return unsecureEmail;
	}

	public void setUnsecureEmail(Integer unsecureEmail) {
		this.unsecureEmail = unsecureEmail;
	}

	public Set getCompletedSupplier() {
		return completedSupplier;
	}

	public void setCompletedSupplier(Set completedSupplier) {
		this.completedSupplier = completedSupplier;
	}

	public Long getArchivedPermission() {
		return archivedPermission;
	}

	public void setArchivedPermission(Long archivedPermission) {
		this.archivedPermission = archivedPermission;
	}

	public Long getSecureStock() {
		return secureStock;
	}

	public void setSecureStock(Long secureStock) {
		this.secureStock = secureStock;
	}

	public Long getUnverifiedRole() {
		return unverifiedRole;
	}

	public void setUnverifiedRole(Long unverifiedRole) {
		this.unverifiedRole = unverifiedRole;
	}

	public Long getOpenMessage() {
		return openMessage;
	}

	public void setOpenMessage(Long openMessage) {
		this.openMessage = openMessage;
	}

	public BigDecimal getExportedPlan() {
		return exportedPlan;
	}

	public void setExportedPlan(BigDecimal exportedPlan) {
		this.exportedPlan = exportedPlan;
	}

	public Long getLoadedAddress() {
		return loadedAddress;
	}

	public void setLoadedAddress(Long loadedAddress) {
		this.loadedAddress = loadedAddress;
	}

	public Integer getExportedAddress() {
		return exportedAddress;
	}

	public void setExportedAddress(Integer exportedAddress) {
		this.exportedAddress = exportedAddress;
	}

	public Double getLockedBill() {
		return lockedBill;
	}

	public void setLockedBill(Double lockedBill) {
		this.lockedBill = lockedBill;
	}

	public Float getRestoredReview() {
		return restoredReview;
	}

	public void setRestoredReview(Float restoredReview) {
		this.restoredReview = restoredReview;
	}

	public BigDecimal getCompletedGoal() {
		return completedGoal;
	}

	public void setCompletedGoal(BigDecimal completedGoal) {
		this.completedGoal = completedGoal;
	}

	public Boolean getClosedFeedback() {
		return closedFeedback;
	}

	public void setClosedFeedback(Boolean closedFeedback) {
		this.closedFeedback = closedFeedback;
	}

	public Long getFailedUser() {
		return failedUser;
	}

	public void setFailedUser(Long failedUser) {
		this.failedUser = failedUser;
	}

	public String getUpdatedFolder() {
		return updatedFolder;
	}

	public void setUpdatedFolder(String updatedFolder) {
		this.updatedFolder = updatedFolder;
	}

	public Long getUnsecureUser() {
		return unsecureUser;
	}

	public void setUnsecureUser(Long unsecureUser) {
		this.unsecureUser = unsecureUser;
	}

	public Float getLoadedPolicy() {
		return loadedPolicy;
	}

	public void setLoadedPolicy(Float loadedPolicy) {
		this.loadedPolicy = loadedPolicy;
	}

	public Long getUnpublishedCategory() {
		return unpublishedCategory;
	}

	public void setUnpublishedCategory(Long unpublishedCategory) {
		this.unpublishedCategory = unpublishedCategory;
	}

	public String getReportedContract() {
		return reportedContract;
	}

	public void setReportedContract(String reportedContract) {
		this.reportedContract = reportedContract;
	}

	public Double getUnlockedPrice() {
		return unlockedPrice;
	}

	public void setUnlockedPrice(Double unlockedPrice) {
		this.unlockedPrice = unlockedPrice;
	}

	public Boolean getVerifiedEvent() {
		return verifiedEvent;
	}

	public void setVerifiedEvent(Boolean verifiedEvent) {
		this.verifiedEvent = verifiedEvent;
	}

	public List getSharedAccount() {
		return sharedAccount;
	}

	public void setSharedAccount(List sharedAccount) {
		this.sharedAccount = sharedAccount;
	}

	public Integer getTotalBrand() {
		return totalBrand;
	}

	public void setTotalBrand(Integer totalBrand) {
		this.totalBrand = totalBrand;
	}

	public Double getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Double updatedDate) {
		this.updatedDate = updatedDate;
	}

	public Boolean getTemporaryReport() {
		return temporaryReport;
	}

	public void setTemporaryReport(Boolean temporaryReport) {
		this.temporaryReport = temporaryReport;
	}

	public Long getMainDocument() {
		return mainDocument;
	}

	public void setMainDocument(Long mainDocument) {
		this.mainDocument = mainDocument;
	}

	public Set getSecureFeedback() {
		return secureFeedback;
	}

	public void setSecureFeedback(Set secureFeedback) {
		this.secureFeedback = secureFeedback;
	}

	public String getReviewedRole() {
		return reviewedRole;
	}

	public void setReviewedRole(String reviewedRole) {
		this.reviewedRole = reviewedRole;
	}

	public String getPreviousFile() {
		return previousFile;
	}

	public void setPreviousFile(String previousFile) {
		this.previousFile = previousFile;
	}

	public Float getPermanentPhone() {
		return permanentPhone;
	}

	public void setPermanentPhone(Float permanentPhone) {
		this.permanentPhone = permanentPhone;
	}

	public BigDecimal getFlaggedUser() {
		return flaggedUser;
	}

	public void setFlaggedUser(BigDecimal flaggedUser) {
		this.flaggedUser = flaggedUser;
	}

	public Boolean getFailedTeam() {
		return failedTeam;
	}

	public void setFailedTeam(Boolean failedTeam) {
		this.failedTeam = failedTeam;
	}

	public String getSavedGoal() {
		return savedGoal;
	}

	public void setSavedGoal(String savedGoal) {
		this.savedGoal = savedGoal;
	}

	public List getInitialPolicy() {
		return initialPolicy;
	}

	public void setInitialPolicy(List initialPolicy) {
		this.initialPolicy = initialPolicy;
	}

	public Integer getMinPermission() {
		return minPermission;
	}

	public void setMinPermission(Integer minPermission) {
		this.minPermission = minPermission;
	}

	public String getArchivedAudit() {
		return archivedAudit;
	}

	public void setArchivedAudit(String archivedAudit) {
		this.archivedAudit = archivedAudit;
	}

	public BigDecimal getRestoredResource() {
		return restoredResource;
	}

	public void setRestoredResource(BigDecimal restoredResource) {
		this.restoredResource = restoredResource;
	}

	public Long getInitialDate() {
		return initialDate;
	}

	public void setInitialDate(Long initialDate) {
		this.initialDate = initialDate;
	}

	public Double getFinalPermission() {
		return finalPermission;
	}

	public void setFinalPermission(Double finalPermission) {
		this.finalPermission = finalPermission;
	}

	public Double getClosedActivity() {
		return closedActivity;
	}

	public void setClosedActivity(Double closedActivity) {
		this.closedActivity = closedActivity;
	}

	public String getSavedReview() {
		return savedReview;
	}

	public void setSavedReview(String savedReview) {
		this.savedReview = savedReview;
	}

	public List getVerifiedTask() {
		return verifiedTask;
	}

	public void setVerifiedTask(List verifiedTask) {
		this.verifiedTask = verifiedTask;
	}

	public String getExportedPhone() {
		return exportedPhone;
	}

	public void setExportedPhone(String exportedPhone) {
		this.exportedPhone = exportedPhone;
	}

	public Long getReviewedGroup() {
		return reviewedGroup;
	}

	public void setReviewedGroup(Long reviewedGroup) {
		this.reviewedGroup = reviewedGroup;
	}

	public String getDisabledName() {
		return disabledName;
	}

	public void setDisabledName(String disabledName) {
		this.disabledName = disabledName;
	}

	public Long getFirstProject() {
		return firstProject;
	}

	public void setFirstProject(Long firstProject) {
		this.firstProject = firstProject;
	}

	public List getNextProject() {
		return nextProject;
	}

	public void setNextProject(List nextProject) {
		this.nextProject = nextProject;
	}

	public String getActiveShipment() {
		return activeShipment;
	}

	public void setActiveShipment(String activeShipment) {
		this.activeShipment = activeShipment;
	}

	public List getPublicWarehouse() {
		return publicWarehouse;
	}

	public void setPublicWarehouse(List publicWarehouse) {
		this.publicWarehouse = publicWarehouse;
	}

	public Set getApprovedEvent() {
		return approvedEvent;
	}

	public void setApprovedEvent(Set approvedEvent) {
		this.approvedEvent = approvedEvent;
	}

	public String getModifiedMessage() {
		return modifiedMessage;
	}

	public void setModifiedMessage(String modifiedMessage) {
		this.modifiedMessage = modifiedMessage;
	}

	public Set getApprovedResource() {
		return approvedResource;
	}

	public void setApprovedResource(Set approvedResource) {
		this.approvedResource = approvedResource;
	}

	public Integer getTotalSupplier() {
		return totalSupplier;
	}

	public void setTotalSupplier(Integer totalSupplier) {
		this.totalSupplier = totalSupplier;
	}

	public Double getNextWarehouse() {
		return nextWarehouse;
	}

	public void setNextWarehouse(Double nextWarehouse) {
		this.nextWarehouse = nextWarehouse;
	}

	public String getUnreviewedUser() {
		return unreviewedUser;
	}

	public void setUnreviewedUser(String unreviewedUser) {
		this.unreviewedUser = unreviewedUser;
	}

	public String getInactiveEmail() {
		return inactiveEmail;
	}

	public void setInactiveEmail(String inactiveEmail) {
		this.inactiveEmail = inactiveEmail;
	}

	public Boolean getSuccessfulGoal() {
		return successfulGoal;
	}

	public void setSuccessfulGoal(Boolean successfulGoal) {
		this.successfulGoal = successfulGoal;
	}

	public Boolean getLastAudit() {
		return lastAudit;
	}

	public void setLastAudit(Boolean lastAudit) {
		this.lastAudit = lastAudit;
	}

	public Float getSuccessfulMilestone() {
		return successfulMilestone;
	}

	public void setSuccessfulMilestone(Float successfulMilestone) {
		this.successfulMilestone = successfulMilestone;
	}

	public Long getTotalAudit() {
		return totalAudit;
	}

	public void setTotalAudit(Long totalAudit) {
		this.totalAudit = totalAudit;
	}

	public String getVerifiedGoal() {
		return verifiedGoal;
	}

	public void setVerifiedGoal(String verifiedGoal) {
		this.verifiedGoal = verifiedGoal;
	}

	public String getImportedPolicy() {
		return importedPolicy;
	}

	public void setImportedPolicy(String importedPolicy) {
		this.importedPolicy = importedPolicy;
	}

	public Long getFirstAddress() {
		return firstAddress;
	}

	public void setFirstAddress(Long firstAddress) {
		this.firstAddress = firstAddress;
	}

	public Double getDeletedGoal() {
		return deletedGoal;
	}

	public void setDeletedGoal(Double deletedGoal) {
		this.deletedGoal = deletedGoal;
	}

	public BigDecimal getFailedSchedule() {
		return failedSchedule;
	}

	public void setFailedSchedule(BigDecimal failedSchedule) {
		this.failedSchedule = failedSchedule;
	}

	public Integer getStatusCart() {
		return statusCart;
	}

	public void setStatusCart(Integer statusCart) {
		this.statusCart = statusCart;
	}

	public Set getFlaggedGoal() {
		return flaggedGoal;
	}

	public void setFlaggedGoal(Set flaggedGoal) {
		this.flaggedGoal = flaggedGoal;
	}

	public BigDecimal getInitialFeedback() {
		return initialFeedback;
	}

	public void setInitialFeedback(BigDecimal initialFeedback) {
		this.initialFeedback = initialFeedback;
	}

	public Float getImportedShipment() {
		return importedShipment;
	}

	public void setImportedShipment(Float importedShipment) {
		this.importedShipment = importedShipment;
	}

	public Integer getMinSubscription() {
		return minSubscription;
	}

	public void setMinSubscription(Integer minSubscription) {
		this.minSubscription = minSubscription;
	}

	public String getPermanentFeedback() {
		return permanentFeedback;
	}

	public void setPermanentFeedback(String permanentFeedback) {
		this.permanentFeedback = permanentFeedback;
	}

	public Long getFirstNotification() {
		return firstNotification;
	}

	public void setFirstNotification(Long firstNotification) {
		this.firstNotification = firstNotification;
	}

	public String getReviewedResource() {
		return reviewedResource;
	}

	public void setReviewedResource(String reviewedResource) {
		this.reviewedResource = reviewedResource;
	}

	public Set getRejectedRole() {
		return rejectedRole;
	}

	public void setRejectedRole(Set rejectedRole) {
		this.rejectedRole = rejectedRole;
	}

	public List getSharedLog() {
		return sharedLog;
	}

	public void setSharedLog(List sharedLog) {
		this.sharedLog = sharedLog;
	}

	public Set getTemporaryPlan() {
		return temporaryPlan;
	}

	public void setTemporaryPlan(Set temporaryPlan) {
		this.temporaryPlan = temporaryPlan;
	}

	public List getVisibleEvent() {
		return visibleEvent;
	}

	public void setVisibleEvent(List visibleEvent) {
		this.visibleEvent = visibleEvent;
	}

	public Set getOpenBrand() {
		return openBrand;
	}

	public void setOpenBrand(Set openBrand) {
		this.openBrand = openBrand;
	}

	public Long getRestoredCustomer() {
		return restoredCustomer;
	}

	public void setRestoredCustomer(Long restoredCustomer) {
		this.restoredCustomer = restoredCustomer;
	}

	public String getExportedAccount() {
		return exportedAccount;
	}

	public void setExportedAccount(String exportedAccount) {
		this.exportedAccount = exportedAccount;
	}

	public String getSecurePermission() {
		return securePermission;
	}

	public void setSecurePermission(String securePermission) {
		this.securePermission = securePermission;
	}

	public BigDecimal getVerifiedNotification() {
		return verifiedNotification;
	}

	public void setVerifiedNotification(BigDecimal verifiedNotification) {
		this.verifiedNotification = verifiedNotification;
	}
}

