package com.volvo.maintain.infrastructure.util;

import com.yonyou.cyx.function.exception.UtilException;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

public class DateUtil {
	public final static String IOS_8601_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";
	public final static String SIMPLE_DATE_FORMAT = "yyyy-MM-dd";
	public final static String SIMPLE_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm";
	public final static String FULL_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	public final static String ACCURATE_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS";
	public final static String SIMPLE_DATE_MONTH_FORMAT = "yyyy-MM";
	public final static String SIMPLE_MONTH_FORMAT = "MM/dd";
	public final static String SIMPLE_TIME_FORMAT = "HH:mm:ss";
	public final static String FULL_DATE2_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
	public final static String FULL_DATE2_TIME_NOTSYMBOL_FORMAT = "yyyyMMddHHmmssSSS";

	/**
	 * 对日期时间字符串转化为日期对象，格式：yyyy-MM-dd HH:mm:ss
	 *
	 * @param dateTime
	 * @return
	 * <AUTHOR>
	 * @date 2016年6月29日
	 */
	public static Date parseDefaultDateTime(String dateTime) {
		return parseDate(dateTime, DateUtil.FULL_DATE_TIME_FORMAT);
	}

	/**
	 * 对日期时间字符串转化为日期对象，格式：yyyy-MM-dd HH:mm:ss
	 *
	 * @param dateTime
	 * @return
	 * <AUTHOR>
	 * @date 2016年6月29日
	 */
	public static Date parseDefaultDateTimeMin(String dateTime) {
		return parseDate(dateTime, DateUtil.SIMPLE_DATE_TIME_FORMAT);
	}

	/**
	 * 对日期时间字符串转化为日期对象，格式：yyyy-MM-dd
	 *
	 * @param dateTime
	 * @return
	 * <AUTHOR>
	 * @date 2016年6月29日
	 */
	public static Date parseDefaultDate(String dateTime) {
		return parseDate(dateTime, DateUtil.SIMPLE_DATE_FORMAT);
	}

	/**
	 * 对日期时间字符串转化为日期对象，格式：yyyy-MM
	 *
	 * @param dateTime
	 * @return
	 * <AUTHOR>
	 * @date 2016年10月11日
	 */

	public static Date parseDefaultDateMonth(String dateTime) {
		return parseDate(dateTime, DateUtil.SIMPLE_DATE_MONTH_FORMAT);
	}

	/**
	 * 格式化默认日期,格式：yyyy-MM-dd
	 *
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2016年6月29日
	 */
	public static String formatDefaultDate(Date date) {
		return formatDateByFormat(date, DateUtil.SIMPLE_DATE_FORMAT);
	}

	/**
	 * 日期,格式：MM/dd
	 *
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2016年11月25日
	 */

	public static String parseMonth(Date date) {
		return formatDateByFormat(date, DateUtil.SIMPLE_MONTH_FORMAT);
	}

	/**
	 * 格式化默认日期,格式：yyyy-MM-dd HH:mm
	 *
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2016年6月29日
	 */
	public static String formatDefaultDateTime(Date date) {
		return formatDateByFormat(date, DateUtil.SIMPLE_DATE_TIME_FORMAT);
	}

	/**
	 * 格式化默认日期,格式：yyyy-MM-dd HH:mm
	 *
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2016年6月29日
	 */
	public static String formatDefaultDateTimes(Date date) {
		return formatDateByFormat(date, DateUtil.FULL_DATE_TIME_FORMAT);
	}

	/**
	 * 将当前时间截掉时分秒
	 *
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2016年12月21日
	 */
	public static Date truncDate(Date date) {
		String formatDate = formatDefaultDateTime(date);
		return parseDefaultDate(formatDate);
	}

	/**
	 * 字符串转化时间
	 *
	 * @param time
	 * @param format
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2016年6月29日
	 */

	public static Date parseDate(String time, String format) {
		if (StringUtils.isBlank(time)) {
			return null;
		}
		if (StringUtils.isBlank(format)) {
			throw new UtilException("日期格式不正确");
		}
		SimpleDateFormat dateFormat = new SimpleDateFormat(format);
		try {
			return dateFormat.parse(time);
		} catch (Exception e) {
			throw new UtilException("日期转换出错：" + e.getMessage(), e);
		}
	}

	/*
	 * <AUTHOR> 根据当前日期计算n天后的日期
	 * @date 2016年3月28日
	 * @param date
	 * @param n
	 * @return
	 */

	public static Date addDay(Date date, int n) {
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.DATE, n);
		Date destDay = c.getTime();
		return destDay;
	}

	/*
	 * <AUTHOR> 以指定的格式来格式化日期
	 * @date 2016年3月28日
	 * @param date
	 * @param format
	 * @return
	 */

	public static String formatDateByFormat(Date date, String format) {
		String result = StringUtil.EMPTY_STRING;
		if (date != null) {
			try {
				SimpleDateFormat sdf = new SimpleDateFormat(format);
				result = sdf.format(date);
			} catch (Exception ex) {
				throw new UtilException("日期转换出错：" + ex.getMessage(), ex);
			}
		}
		return result;
	}

	/**
	 * 加1天
	 *
	 * @param param
	 * @return
	 * <AUTHOR>
	 * @date 2016年8月29日
	 */
	public static Date addOneDay(Object param) {
		return addDay(parseDefaultDate(param + ""), 1);
		// return formatDefaultDate(value);
	}

	/**
	 * 指定日期的第一天
	 *
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2016年9月30日
	 */

	public static Date getFirstDayOfMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1);
		return parseDefaultDate(formatDefaultDate(calendar.getTime()));
	}

	/**
	 * 指定日期的最后一天
	 *
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2016年9月30日
	 */

	public static Date getLastDayOfMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1);
		calendar.roll(Calendar.DATE, -1);
		return parseDefaultDate(formatDefaultDate(calendar.getTime()));
	}

	/**
	 * 获取上个月最后一天
	 * @param date
	 * @return
	 */
	public static Date getLastDayOfLastMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);

		// 将日期设置为该月的第一天
		calendar.set(Calendar.DAY_OF_MONTH, 1);

		// 再减去一天，就得到了本月的最后一天
		calendar.add(Calendar.DAY_OF_MONTH, -1);

		return calendar.getTime();
	}

	/**
	 * 获取下一个月的第一天
	 *
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2016年9月30日
	 */

	public static Date getPerFirstDayOfMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, 1);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
		return parseDefaultDate(formatDefaultDate(calendar.getTime()));
	}

	/**
	 * 比较两个时间相差多少分钟
	 *
	 * @param endDate 结束时间
	 * @param nowDate 开始时间
	 * @return
	 * <AUTHOR>
	 * @date 2016年10月28日
	 */
	public static Long toCompareTime(Date endDate, Date nowDate) {
		long nd = 1000 * 24 * 60 * 60L;
		long nh = 1000 * 60 * 60L;
		long nm = 1000 * 60L;
		// long ns = 1000;
		// 获得两个时间的毫秒时间差异
		long diff = endDate.getTime() - nowDate.getTime();
		// 计算差多少天
		long day = diff / nd;
		// 计算差多少小时
		long hour = diff % nd / nh;
		// 计算差多少分钟
		long min = diff % nd % nh / nm;
		// 计算差多少秒//输出结果
		// long sec = diff % nd % nh % nm / ns;
		return (Long) day * 24 * 60 + hour * 60 + min;
	}

	/**
	 * 截取想要的日期类型-String
	 *
	 * @param sourceDateTime
	 * @param souceDateFormat
	 * @param destionDataFormat
	 * @return
	 * <AUTHOR>
	 * @date 2016年12月16日
	 */

	public static String formatDateStrByFormat(String sourceDateTime, String souceDateFormat,
											   String destionDataFormat) {
		Date sourceDate = parseDate(sourceDateTime, souceDateFormat);
		return formatDateByFormat(sourceDate, destionDataFormat);
	}

	/**
	 * 获取下一个月日期
	 *
	 * @param year
	 * @param month
	 * @return
	 * <AUTHOR>
	 * @date 2017年1月3日
	 */
	public static Calendar getNextMonthDate(int year, int month) {
		Calendar cal = Calendar.getInstance();
		cal.set(year, month, 1);
		cal.add(Calendar.MONTH, 1);
		return cal;
	}

	/**
	 * 当前月
	 *
	 * @param date
	 * @param n
	 * @return
	 * <AUTHOR>
	 * @date 2017年1月19日
	 */

	public static Date addMonth(Date date, int n) {
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.MONTH, n);
		Date destDay = c.getTime();
		return destDay;
	}

	/**
	 * 得到指定日期的月份
	 *
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2017年4月19日
	 */

	public static int getMonth(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return cal.get(Calendar.MONTH) + 1;
	}

	public static String getMonthString(Date date) {
		StringBuffer sb = new StringBuffer();
		append(sb, getMonth(date), ' ');
		return sb.toString();
	}

	public static int getDay(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return cal.get(Calendar.DAY_OF_MONTH);
	}

	private static void append(StringBuffer sb, int v, char split) {
		if (v < 10) {
			sb.append('0');
		}
		sb.append(v).append(split);
	}

	public static String getDayString(Date date) {
		StringBuffer sb = new StringBuffer();
		append(sb, getDay(date), ' ');
		return sb.toString();
	}

	/**
	 * 年
	 *
	 * @param date
	 * @return
	 * <AUTHOR>
	 * @date 2017年4月19日
	 */

	public static int getYear(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return cal.get(Calendar.YEAR);
	}

	public static String getYearString(Date date) {
		StringBuffer sb = new StringBuffer();
		append(sb, getYear(date), ' ');
		return sb.toString();
	}

	public static Date currentTimeMillis() {
		return Calendar.getInstance().getTime();
	}

	/**
	 * @param time
	 * @return
	 * @description 转换时间默认格式
	 */
	public static Date parseDateDefault(Object time) {
		return parseDate(String.valueOf(time), "yyyy-MM-dd HH:mm:ss");
	}

	public static String parseDateStr(Date date, int day, String dateFormat) {

		SimpleDateFormat format = new SimpleDateFormat(dateFormat);

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(date);
		lastDate.set(Calendar.DATE, day);

		return format.format(lastDate.getTime());
	}

	public static Date parseDate(Date date, int day) {

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(date);
		lastDate.set(Calendar.DATE, lastDate.get(Calendar.DATE) + day);

		return lastDate.getTime();
	}

	//比较两个时间大小
	public static Integer compareDate(String startDate, String endDate) throws ParseException {

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");

		return format.parse(startDate).compareTo(format.parse(endDate));
	}

	public static Date getHourDate(Date date, int num) {

		SimpleDateFormat format = new SimpleDateFormat("HH:mm");

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(date);
		lastDate.add(Calendar.HOUR, num);// 当前时间加num个小时

		return lastDate.getTime();
	}

	public static Date getParseDate(Date date, int day) {

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(date);
		lastDate.set(Calendar.DATE, lastDate.get(Calendar.DATE) + day);

		return lastDate.getTime();
	}

	public static Date getHourDateS(Date date, int num) {

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(date);
		lastDate.add(Calendar.MINUTE, num);// 当前时间加num个小时

		return lastDate.getTime();
	}

	public static String parseDate(Date date, int day, String dateFormat) {

		SimpleDateFormat format = new SimpleDateFormat(dateFormat);

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(date);
		lastDate.set(Calendar.DATE, lastDate.get(Calendar.DATE) + day);

		return format.format(lastDate.getTime());
	}

	//获取周时
	public static String getWeekDate(int week, int num) {

		SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.SIMPLE_DATE_FORMAT);

		Calendar lastDate = Calendar.getInstance();
		lastDate.add(Calendar.WEEK_OF_YEAR, week);
		lastDate.set(Calendar.DAY_OF_WEEK, num);

		return formatter.format(lastDate.getTime());
	}

	public static Date getDate(Date date, int num) {

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(date);
		lastDate.add(Calendar.HOUR, num);// 当前时间加num个小时

		return lastDate.getTime();
	}

	public static String getDate(String date, int num) {

		SimpleDateFormat format = new SimpleDateFormat("HH:mm");

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(Objects.requireNonNull(parseDateStrToDate(date, "HH:mm")));
		lastDate.add(Calendar.HOUR, num);// 当前时间加num个小时

		return format.format(lastDate.getTime());
	}

	public static Date parseDateStrToDate(String dataStr, String pattern) {
		try {
			String patternTemp = "yyyy-MM-dd";
			if (pattern != null && pattern.length() > 0) {
				patternTemp = pattern;
			}
			SimpleDateFormat format = new SimpleDateFormat(patternTemp);
			return format.parse(dataStr);
		} catch (Exception e) {
		}
		return null;
	}

	//比较两个时间大小
	public static Integer dateCompare1(String startDate, String endDate) throws ParseException {

		SimpleDateFormat format = new SimpleDateFormat("HH:mm");

		return format.parse(startDate).compareTo(format.parse(endDate));
	}

	public static String getDateStr(Date date, int num) {

		SimpleDateFormat format = new SimpleDateFormat("HH:mm");

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(date);
		lastDate.add(Calendar.HOUR, num);// 当前时间加num个小时

		return format.format(lastDate.getTime());
	}

	public static String parseDateStr(Date date, int day) {

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(date);
		lastDate.set(Calendar.DATE, lastDate.get(Calendar.DATE) + day);

		return format.format(lastDate.getTime());
	}

	public static String parseDate(String date) {

		SimpleDateFormat format = new SimpleDateFormat("E");

		return format.format(parseDateStrToDate(date, "yyyy-MM-dd"));
	}

	/**
	 * 获取一周的跨度时间
	 *
	 * @param dateStr
	 * @return
	 */
	public static String getWeeks(String dateStr) {

		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date date = null;
		try {
			date = simpleDateFormat.parse(dateStr);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		// 转为calendar格式
		Calendar calendar = Calendar.getInstance();
		assert date != null;
		calendar.setTime(date);
		// 如果是周日
		if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
			calendar.add(Calendar.DAY_OF_YEAR, -1);
		}
		// 获取当前日期是当周的第i天
		int i = calendar.get(Calendar.DAY_OF_WEEK) - 1;

		// 获取当前日期所在周的第一天
		calendar.add(Calendar.DATE, -i + 1);
		String monday = simpleDateFormat.format(calendar.getTime());
		// 获取当前日期所在周的最后一天
		calendar.add(Calendar.DATE, 6);
		String sunday = simpleDateFormat.format(calendar.getTime());

		return monday + "/" + sunday;
	}

	public static String parseDateStr(String date, int day) throws ParseException {

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(format.parse(date));
		lastDate.set(Calendar.DATE, lastDate.get(Calendar.DATE) + day);

		return format.format(lastDate.getTime());
	}

	public static String getHourStr(String hour, int num) throws ParseException {

		SimpleDateFormat format = new SimpleDateFormat("HH:mm");

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(format.parse(hour));
		lastDate.add(Calendar.HOUR, num);// 当前时间加num个小时

		return format.format(lastDate.getTime());
	}

	//比较两个时间大小
	public static Integer compareDateTime(String startDate, String endDate) throws ParseException {

		SimpleDateFormat format = new SimpleDateFormat("HH:mm");

		return format.parse(startDate).compareTo(format.parse(endDate));
	}

	public static Date getNextWeekEnd() {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

		Calendar lastDate = Calendar.getInstance();
		lastDate.add(Calendar.WEEK_OF_YEAR, 0);//
		lastDate.set(Calendar.DAY_OF_WEEK, 1);// 下一周的最后一天

		return lastDate.getTime();
	}

	public static String getSunday(int week) {

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

		Calendar cld = Calendar.getInstance(Locale.CHINA);
		cld.add(Calendar.WEEK_OF_YEAR, week);
		cld.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);

		return formatter.format(cld.getTime());
	}

	public static String getMonday(int week) {

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

		Calendar cld = Calendar.getInstance(Locale.CHINA);
		cld.add(Calendar.WEEK_OF_YEAR, week);
		cld.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);

		return formatter.format(cld.getTime());
	}

	public static String getMinutesStr(String hour, int minutes) throws ParseException {

		SimpleDateFormat format = new SimpleDateFormat(SIMPLE_TIME_FORMAT);

		Calendar lastDate = Calendar.getInstance();
		lastDate.setTime(format.parse(hour));
		lastDate.add(Calendar.MINUTE, minutes);
		;// 当前时间加num个小时

		return format.format(lastDate.getTime());
	}

	public static Integer compareDateTime(String startDate, String endDate, String formats) throws ParseException {

		SimpleDateFormat format = new SimpleDateFormat(formats);

		return format.parse(startDate).compareTo(format.parse(endDate));
	}

	public static Date addYears(Date date, int amount) {
		return add(date, 1, amount);
	}

	public static Date addMonths(Date date, int amount) {
		return add(date, 2, amount);
	}

	public static Date addWeeks(Date date, int amount) {
		return add(date, 3, amount);
	}

	public static Date addDays(Date date, int amount) {
		return add(date, 5, amount);
	}

	public static Date addHours(Date date, int amount) {
		return add(date, 11, amount);
	}

	public static Date addMinutes(Date date, int amount) {
		return add(date, 12, amount);
	}

	public static Date addSeconds(Date date, int amount) {
		return add(date, 13, amount);
	}

	public static Date addMilliseconds(Date date, int amount) {
		return add(date, 14, amount);
	}

	/**
	 * @deprecated
	 */
	public static Date add(Date date, int calendarField, int amount) {
		if (date == null) {
			throw new IllegalArgumentException("The date must not be null");
		} else {
			Calendar c = Calendar.getInstance();
			c.setTime(date);
			c.add(calendarField, amount);
			return c.getTime();
		}
	}

	/**
	 * 得到本周周一
	 */
	public static String getMondayOfThisWeek() {
		Calendar c = Calendar.getInstance();
		int dayofweek = c.get(Calendar.DAY_OF_WEEK) - 1;
		if (dayofweek == 0) {
			dayofweek = 7;
		}
		c.add(Calendar.DATE, -dayofweek + 1);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(c.getTime());
	}
	/**
	 * 格式化当前日期,格式：yyyy-MM-dd
	 *
	 * @return
	 * <AUTHOR>
	 * @date 2016年6月29日
	 */
	public static String queryNow() {
		return formatDateByFormat(new Date(), DateUtil.SIMPLE_DATE_FORMAT);
	}




	/**
	 * 获取当天开始时间
	 */
	public static String getNowBegin() {

		LocalDateTime date = LocalDateTime.now();
		DateTimeFormatter df = DateTimeFormatter.ofPattern(FULL_DATE_TIME_FORMAT);
		LocalDateTime startOfTheDay = LocalDateTime.of(date.toLocalDate(), LocalTime.MIN);

		return df.format(startOfTheDay);
	}


	/**
	 * 获取当天结束时间
	 */
	public static String getNowEnd() {

		LocalDateTime date = LocalDateTime.now();
		DateTimeFormatter df = DateTimeFormatter.ofPattern(FULL_DATE_TIME_FORMAT);
		LocalDateTime endOfTheDay = LocalDateTime.of(date.toLocalDate(), LocalTime.MAX);
		return df.format(endOfTheDay);
	}


	/**
	 * 当前时间-指定分钟
	 */
	public static String queryNowTime(Integer timeOut) {
		// 获取当前日期和时间
		Calendar calendar = Calendar.getInstance();
		// 在当前日期上进行相应的操作（这里是减去15分钟）
		calendar.add(Calendar.MINUTE, -timeOut);
		return formatDefaultDateTimes(calendar.getTime());
	}

	/**
	 * localDateTime 转为 Date
	 */
	public static Date convertToDate(LocalDateTime localDateTime) {
		return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
	}

//	public static void main(String[] args) throws ParseException {
//		String str = "2024-12-02 23:56:58";
//		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:ss:mm");
//		Date parse = format.parse(str);
//		Date lastDayOfMonth = getLastDayOfLastMonth(parse);
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//		System.out.println("该月的最后一天是：" + sdf.format(lastDayOfMonth));
//	}
}
