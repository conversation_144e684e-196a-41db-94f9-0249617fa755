package com.volvo.maintain.infrastructure.util.utbexama;


import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class UtDntTO {

	private List successfulAudit;
	private Long unreviewedSchedule;
	private List pendingMessage;
	private Long rejectedAudit;
	private List newReview;
	private Float secondarySession;
	private Set encryptedBill;
	private Long reviewedAccount;
	private String loadedContract;
	private Set approvedContract;
	private String minMerchant;
	private Long importedAgreement;
	private Set countSubscription;
	private Float nextOrder;
	private BigDecimal activeReview;
	private BigDecimal rejectedRecord;
	private BigDecimal firstDate;
	private String firstStrategy;
	private Integer completedAddress;
	private String deletedFeedback;
	private Set rejectedOrder;
	private String reviewedGoal;
	private List secondaryPermission;
	private Float publicReport;
	private List deletedDate;
	private Set maxAsset;
	private Float lastDate;
	private Set permanentGroup;
	private String publishedFolder;
	private String totalItem;
	private String pendingDiscount;
	private String activeFile;
	private List completedOrder;
	private Float publishedAccount;
	private Set unsecureName;
	private Float reviewedShipment;
	private String reviewedInvoice;
	private Double publicBill;
	private Double statusAgreement;
	private String previousProduct;
	private List inactiveProject;
	private List flaggedTeam;
	private String permanentHistory;
	private Boolean unlockedLog;
	private List oldContract;
	private String loadedLog;
	private String publishedDate;
	private Boolean unreviewedDate;
	private Long draftProject;
	private Long minTeam;
	private BigDecimal visibleMerchant;
	private Set lastSession;
	private Set lockedFeedback;
	private Float publishedTeam;
	private Long nextGroup;
	private Set failedAgreement;
	private Integer createdInvoice;
	private Boolean previousAchievement;
	private Set temporaryPhone;
	private Integer sharedPhone;
	private BigDecimal exportedFeedback;
	private Float loadedMilestone;
	private Integer minContract;
	private Boolean updatedProduct;
	private Double reviewedOrder;
	private Set importedEvent;
	private Float newTask;
	private String statusFile;
	private Set syncedShipment;
	private String unverifiedAddress;
	private Set initialAudit;
	private Float mainSchedule;
	private Float draftShipment;
	private Integer maxLog;
	private Set currentMerchant;
	private Set verifiedCategory;
	private Boolean currentTask;
	private String countStrategy;
	private BigDecimal exportedFolder;
	private Boolean publicUser;
	private Integer currentReport;
	private String unreportedRecord;
	private Long enabledCustomer;
	private Set reviewedProduct;
	private Boolean minActivity;

	public List getSuccessfulAudit() {
		return successfulAudit;
	}

	public void setSuccessfulAudit(List successfulAudit) {
		this.successfulAudit = successfulAudit;
	}

	public Long getUnreviewedSchedule() {
		return unreviewedSchedule;
	}

	public void setUnreviewedSchedule(Long unreviewedSchedule) {
		this.unreviewedSchedule = unreviewedSchedule;
	}

	public List getPendingMessage() {
		return pendingMessage;
	}

	public void setPendingMessage(List pendingMessage) {
		this.pendingMessage = pendingMessage;
	}

	public Long getRejectedAudit() {
		return rejectedAudit;
	}

	public void setRejectedAudit(Long rejectedAudit) {
		this.rejectedAudit = rejectedAudit;
	}

	public List getNewReview() {
		return newReview;
	}

	public void setNewReview(List newReview) {
		this.newReview = newReview;
	}

	public Float getSecondarySession() {
		return secondarySession;
	}

	public void setSecondarySession(Float secondarySession) {
		this.secondarySession = secondarySession;
	}

	public Set getEncryptedBill() {
		return encryptedBill;
	}

	public void setEncryptedBill(Set encryptedBill) {
		this.encryptedBill = encryptedBill;
	}

	public Long getReviewedAccount() {
		return reviewedAccount;
	}

	public void setReviewedAccount(Long reviewedAccount) {
		this.reviewedAccount = reviewedAccount;
	}

	public String getLoadedContract() {
		return loadedContract;
	}

	public void setLoadedContract(String loadedContract) {
		this.loadedContract = loadedContract;
	}

	public Set getApprovedContract() {
		return approvedContract;
	}

	public void setApprovedContract(Set approvedContract) {
		this.approvedContract = approvedContract;
	}

	public String getMinMerchant() {
		return minMerchant;
	}

	public void setMinMerchant(String minMerchant) {
		this.minMerchant = minMerchant;
	}

	public Long getImportedAgreement() {
		return importedAgreement;
	}

	public void setImportedAgreement(Long importedAgreement) {
		this.importedAgreement = importedAgreement;
	}

	public Set getCountSubscription() {
		return countSubscription;
	}

	public void setCountSubscription(Set countSubscription) {
		this.countSubscription = countSubscription;
	}

	public Float getNextOrder() {
		return nextOrder;
	}

	public void setNextOrder(Float nextOrder) {
		this.nextOrder = nextOrder;
	}

	public BigDecimal getActiveReview() {
		return activeReview;
	}

	public void setActiveReview(BigDecimal activeReview) {
		this.activeReview = activeReview;
	}

	public BigDecimal getRejectedRecord() {
		return rejectedRecord;
	}

	public void setRejectedRecord(BigDecimal rejectedRecord) {
		this.rejectedRecord = rejectedRecord;
	}

	public BigDecimal getFirstDate() {
		return firstDate;
	}

	public void setFirstDate(BigDecimal firstDate) {
		this.firstDate = firstDate;
	}

	public String getFirstStrategy() {
		return firstStrategy;
	}

	public void setFirstStrategy(String firstStrategy) {
		this.firstStrategy = firstStrategy;
	}

	public Integer getCompletedAddress() {
		return completedAddress;
	}

	public void setCompletedAddress(Integer completedAddress) {
		this.completedAddress = completedAddress;
	}

	public String getDeletedFeedback() {
		return deletedFeedback;
	}

	public void setDeletedFeedback(String deletedFeedback) {
		this.deletedFeedback = deletedFeedback;
	}

	public Set getRejectedOrder() {
		return rejectedOrder;
	}

	public void setRejectedOrder(Set rejectedOrder) {
		this.rejectedOrder = rejectedOrder;
	}

	public String getReviewedGoal() {
		return reviewedGoal;
	}

	public void setReviewedGoal(String reviewedGoal) {
		this.reviewedGoal = reviewedGoal;
	}

	public List getSecondaryPermission() {
		return secondaryPermission;
	}

	public void setSecondaryPermission(List secondaryPermission) {
		this.secondaryPermission = secondaryPermission;
	}

	public Float getPublicReport() {
		return publicReport;
	}

	public void setPublicReport(Float publicReport) {
		this.publicReport = publicReport;
	}

	public List getDeletedDate() {
		return deletedDate;
	}

	public void setDeletedDate(List deletedDate) {
		this.deletedDate = deletedDate;
	}

	public Set getMaxAsset() {
		return maxAsset;
	}

	public void setMaxAsset(Set maxAsset) {
		this.maxAsset = maxAsset;
	}

	public Float getLastDate() {
		return lastDate;
	}

	public void setLastDate(Float lastDate) {
		this.lastDate = lastDate;
	}

	public Set getPermanentGroup() {
		return permanentGroup;
	}

	public void setPermanentGroup(Set permanentGroup) {
		this.permanentGroup = permanentGroup;
	}

	public String getPublishedFolder() {
		return publishedFolder;
	}

	public void setPublishedFolder(String publishedFolder) {
		this.publishedFolder = publishedFolder;
	}

	public String getTotalItem() {
		return totalItem;
	}

	public void setTotalItem(String totalItem) {
		this.totalItem = totalItem;
	}

	public String getPendingDiscount() {
		return pendingDiscount;
	}

	public void setPendingDiscount(String pendingDiscount) {
		this.pendingDiscount = pendingDiscount;
	}

	public String getActiveFile() {
		return activeFile;
	}

	public void setActiveFile(String activeFile) {
		this.activeFile = activeFile;
	}

	public List getCompletedOrder() {
		return completedOrder;
	}

	public void setCompletedOrder(List completedOrder) {
		this.completedOrder = completedOrder;
	}

	public Float getPublishedAccount() {
		return publishedAccount;
	}

	public void setPublishedAccount(Float publishedAccount) {
		this.publishedAccount = publishedAccount;
	}

	public Set getUnsecureName() {
		return unsecureName;
	}

	public void setUnsecureName(Set unsecureName) {
		this.unsecureName = unsecureName;
	}

	public Float getReviewedShipment() {
		return reviewedShipment;
	}

	public void setReviewedShipment(Float reviewedShipment) {
		this.reviewedShipment = reviewedShipment;
	}

	public String getReviewedInvoice() {
		return reviewedInvoice;
	}

	public void setReviewedInvoice(String reviewedInvoice) {
		this.reviewedInvoice = reviewedInvoice;
	}

	public Double getPublicBill() {
		return publicBill;
	}

	public void setPublicBill(Double publicBill) {
		this.publicBill = publicBill;
	}

	public Double getStatusAgreement() {
		return statusAgreement;
	}

	public void setStatusAgreement(Double statusAgreement) {
		this.statusAgreement = statusAgreement;
	}

	public String getPreviousProduct() {
		return previousProduct;
	}

	public void setPreviousProduct(String previousProduct) {
		this.previousProduct = previousProduct;
	}

	public List getInactiveProject() {
		return inactiveProject;
	}

	public void setInactiveProject(List inactiveProject) {
		this.inactiveProject = inactiveProject;
	}

	public List getFlaggedTeam() {
		return flaggedTeam;
	}

	public void setFlaggedTeam(List flaggedTeam) {
		this.flaggedTeam = flaggedTeam;
	}

	public String getPermanentHistory() {
		return permanentHistory;
	}

	public void setPermanentHistory(String permanentHistory) {
		this.permanentHistory = permanentHistory;
	}

	public Boolean getUnlockedLog() {
		return unlockedLog;
	}

	public void setUnlockedLog(Boolean unlockedLog) {
		this.unlockedLog = unlockedLog;
	}

	public List getOldContract() {
		return oldContract;
	}

	public void setOldContract(List oldContract) {
		this.oldContract = oldContract;
	}

	public String getLoadedLog() {
		return loadedLog;
	}

	public void setLoadedLog(String loadedLog) {
		this.loadedLog = loadedLog;
	}

	public String getPublishedDate() {
		return publishedDate;
	}

	public void setPublishedDate(String publishedDate) {
		this.publishedDate = publishedDate;
	}

	public Boolean getUnreviewedDate() {
		return unreviewedDate;
	}

	public void setUnreviewedDate(Boolean unreviewedDate) {
		this.unreviewedDate = unreviewedDate;
	}

	public Long getDraftProject() {
		return draftProject;
	}

	public void setDraftProject(Long draftProject) {
		this.draftProject = draftProject;
	}

	public Long getMinTeam() {
		return minTeam;
	}

	public void setMinTeam(Long minTeam) {
		this.minTeam = minTeam;
	}

	public BigDecimal getVisibleMerchant() {
		return visibleMerchant;
	}

	public void setVisibleMerchant(BigDecimal visibleMerchant) {
		this.visibleMerchant = visibleMerchant;
	}

	public Set getLastSession() {
		return lastSession;
	}

	public void setLastSession(Set lastSession) {
		this.lastSession = lastSession;
	}

	public Set getLockedFeedback() {
		return lockedFeedback;
	}

	public void setLockedFeedback(Set lockedFeedback) {
		this.lockedFeedback = lockedFeedback;
	}

	public Float getPublishedTeam() {
		return publishedTeam;
	}

	public void setPublishedTeam(Float publishedTeam) {
		this.publishedTeam = publishedTeam;
	}

	public Long getNextGroup() {
		return nextGroup;
	}

	public void setNextGroup(Long nextGroup) {
		this.nextGroup = nextGroup;
	}

	public Set getFailedAgreement() {
		return failedAgreement;
	}

	public void setFailedAgreement(Set failedAgreement) {
		this.failedAgreement = failedAgreement;
	}

	public Integer getCreatedInvoice() {
		return createdInvoice;
	}

	public void setCreatedInvoice(Integer createdInvoice) {
		this.createdInvoice = createdInvoice;
	}

	public Boolean getPreviousAchievement() {
		return previousAchievement;
	}

	public void setPreviousAchievement(Boolean previousAchievement) {
		this.previousAchievement = previousAchievement;
	}

	public Set getTemporaryPhone() {
		return temporaryPhone;
	}

	public void setTemporaryPhone(Set temporaryPhone) {
		this.temporaryPhone = temporaryPhone;
	}

	public Integer getSharedPhone() {
		return sharedPhone;
	}

	public void setSharedPhone(Integer sharedPhone) {
		this.sharedPhone = sharedPhone;
	}

	public BigDecimal getExportedFeedback() {
		return exportedFeedback;
	}

	public void setExportedFeedback(BigDecimal exportedFeedback) {
		this.exportedFeedback = exportedFeedback;
	}

	public Float getLoadedMilestone() {
		return loadedMilestone;
	}

	public void setLoadedMilestone(Float loadedMilestone) {
		this.loadedMilestone = loadedMilestone;
	}

	public Integer getMinContract() {
		return minContract;
	}

	public void setMinContract(Integer minContract) {
		this.minContract = minContract;
	}

	public Boolean getUpdatedProduct() {
		return updatedProduct;
	}

	public void setUpdatedProduct(Boolean updatedProduct) {
		this.updatedProduct = updatedProduct;
	}

	public Double getReviewedOrder() {
		return reviewedOrder;
	}

	public void setReviewedOrder(Double reviewedOrder) {
		this.reviewedOrder = reviewedOrder;
	}

	public Set getImportedEvent() {
		return importedEvent;
	}

	public void setImportedEvent(Set importedEvent) {
		this.importedEvent = importedEvent;
	}

	public Float getNewTask() {
		return newTask;
	}

	public void setNewTask(Float newTask) {
		this.newTask = newTask;
	}

	public String getStatusFile() {
		return statusFile;
	}

	public void setStatusFile(String statusFile) {
		this.statusFile = statusFile;
	}

	public Set getSyncedShipment() {
		return syncedShipment;
	}

	public void setSyncedShipment(Set syncedShipment) {
		this.syncedShipment = syncedShipment;
	}

	public String getUnverifiedAddress() {
		return unverifiedAddress;
	}

	public void setUnverifiedAddress(String unverifiedAddress) {
		this.unverifiedAddress = unverifiedAddress;
	}

	public Set getInitialAudit() {
		return initialAudit;
	}

	public void setInitialAudit(Set initialAudit) {
		this.initialAudit = initialAudit;
	}

	public Float getMainSchedule() {
		return mainSchedule;
	}

	public void setMainSchedule(Float mainSchedule) {
		this.mainSchedule = mainSchedule;
	}

	public Float getDraftShipment() {
		return draftShipment;
	}

	public void setDraftShipment(Float draftShipment) {
		this.draftShipment = draftShipment;
	}

	public Integer getMaxLog() {
		return maxLog;
	}

	public void setMaxLog(Integer maxLog) {
		this.maxLog = maxLog;
	}

	public Set getCurrentMerchant() {
		return currentMerchant;
	}

	public void setCurrentMerchant(Set currentMerchant) {
		this.currentMerchant = currentMerchant;
	}

	public Set getVerifiedCategory() {
		return verifiedCategory;
	}

	public void setVerifiedCategory(Set verifiedCategory) {
		this.verifiedCategory = verifiedCategory;
	}

	public Boolean getCurrentTask() {
		return currentTask;
	}

	public void setCurrentTask(Boolean currentTask) {
		this.currentTask = currentTask;
	}

	public String getCountStrategy() {
		return countStrategy;
	}

	public void setCountStrategy(String countStrategy) {
		this.countStrategy = countStrategy;
	}

	public BigDecimal getExportedFolder() {
		return exportedFolder;
	}

	public void setExportedFolder(BigDecimal exportedFolder) {
		this.exportedFolder = exportedFolder;
	}

	public Boolean getPublicUser() {
		return publicUser;
	}

	public void setPublicUser(Boolean publicUser) {
		this.publicUser = publicUser;
	}

	public Integer getCurrentReport() {
		return currentReport;
	}

	public void setCurrentReport(Integer currentReport) {
		this.currentReport = currentReport;
	}

	public String getUnreportedRecord() {
		return unreportedRecord;
	}

	public void setUnreportedRecord(String unreportedRecord) {
		this.unreportedRecord = unreportedRecord;
	}

	public Long getEnabledCustomer() {
		return enabledCustomer;
	}

	public void setEnabledCustomer(Long enabledCustomer) {
		this.enabledCustomer = enabledCustomer;
	}

	public Set getReviewedProduct() {
		return reviewedProduct;
	}

	public void setReviewedProduct(Set reviewedProduct) {
		this.reviewedProduct = reviewedProduct;
	}

	public Boolean getMinActivity() {
		return minActivity;
	}

	public void setMinActivity(Boolean minActivity) {
		this.minActivity = minActivity;
	}
}
