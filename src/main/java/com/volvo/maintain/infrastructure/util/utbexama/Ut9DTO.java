package com.volvo.maintain.infrastructure.util.utbexama;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
public class Ut9DTO {
    private Boolean averageItem;
    private Boolean unreviewedDiscount;
    private Integer mainProject;
    private Integer enabledRole;
    private String modifiedFolder;
    private Integer completedAccount;
    private String syncedStock;
    private Double closedPhone;
    private String typePayment;
    private Integer draftRecord;
    private String pendingItem;
    private BigDecimal finalBill;
    private Float nextCoupon;
    private Double hiddenProject;
    private String primaryTask;
    private Integer primaryEvent;
    private BigDecimal mainSubscription;
    private List previousPolicy;
    private Set syncedBrand;
    private Set publicAudit;
    private Float newProject;
    private String decryptedEvent;
    private Integer nextPhone;
    private List flaggedItem;
    private Boolean activeActivity;
    private Long rejectedCustomer;
    private Float enabledDocument;
    private Float inactivePlan;
    private String previousMilestone;
    private Set hiddenCustomer;
    private Long hiddenTransaction;
    private List currentReport;
    private BigDecimal initialAsset;
    private Integer maxSchedule;
    private Double approvedContract;
    private Long mainMilestone;
    private Set decryptedNotification;
    private BigDecimal savedSupplier;
    private BigDecimal secondaryUser;
    private Long levelHistory;
    private Double newStock;
    private Double verifiedPermission;
    private String nextTransaction;
    private Long currentDepartment;
    private Integer visibleActivity;
    private BigDecimal pendingUser;
    private String enabledActivity;
    private Double unlockedLog;
    private Float secondaryReport;
    private String rejectedFeedback;
    private Double levelPhone;
    private Boolean lockedAchievement;
    private Set statusStrategy;
    private Long flaggedMessage;
    private Double unlockedGoal;
    private String initialTeam;
    private Double updatedPrice;
    private List minOrder;
    private Float nextWarehouse;
    private BigDecimal averageReview;
    private Set syncedDiscount;
    private Float pendingEmail;
    private BigDecimal secureDocument;
    private String primaryCart;
    private BigDecimal deletedCart;
    private Double reportedShipment;
    private String decryptedAgreement;
    private String savedPolicy;
    private Integer sharedName;
    private Set inactiveBill;
    private Boolean secureCart;
    private Boolean permanentLog;
    private List openItem;
    private BigDecimal publishedMilestone;
    private String levelPermission;
    private Integer successfulTransaction;
    private Long temporarySession;
    private String unsecureFolder;
    private Integer restoredGroup;
    private Set hiddenStock;
    private Float finalReview;
    private Set decryptedFolder;
    private Double failedCoupon;
    private String syncedSchedule;
    private Long oldActivity;
    private BigDecimal typeFile;
    private List oldMessage;
    private String encryptedAsset;
    private Double firstProject;
    private List permanentContract;
    private String unpublishedUser;
    private BigDecimal flaggedOrder;
    private Long modifiedContract;
    private String reviewedUser;
    private Boolean successfulActivity;
    private String pendingResource;
    private Long encryptedTransaction;
    private Float secondaryPayment;
    private Integer previousGroup;

    public Boolean getAverageItem() {
        return averageItem;
    }

    public void setAverageItem(Boolean averageItem) {
        this.averageItem = averageItem;
    }

    public Boolean getUnreviewedDiscount() {
        return unreviewedDiscount;
    }

    public void setUnreviewedDiscount(Boolean unreviewedDiscount) {
        this.unreviewedDiscount = unreviewedDiscount;
    }

    public Integer getMainProject() {
        return mainProject;
    }

    public void setMainProject(Integer mainProject) {
        this.mainProject = mainProject;
    }

    public Integer getEnabledRole() {
        return enabledRole;
    }

    public void setEnabledRole(Integer enabledRole) {
        this.enabledRole = enabledRole;
    }

    public String getModifiedFolder() {
        return modifiedFolder;
    }

    public void setModifiedFolder(String modifiedFolder) {
        this.modifiedFolder = modifiedFolder;
    }

    public Integer getCompletedAccount() {
        return completedAccount;
    }

    public void setCompletedAccount(Integer completedAccount) {
        this.completedAccount = completedAccount;
    }

    public String getSyncedStock() {
        return syncedStock;
    }

    public void setSyncedStock(String syncedStock) {
        this.syncedStock = syncedStock;
    }

    public Double getClosedPhone() {
        return closedPhone;
    }

    public void setClosedPhone(Double closedPhone) {
        this.closedPhone = closedPhone;
    }

    public String getTypePayment() {
        return typePayment;
    }

    public void setTypePayment(String typePayment) {
        this.typePayment = typePayment;
    }

    public Integer getDraftRecord() {
        return draftRecord;
    }

    public void setDraftRecord(Integer draftRecord) {
        this.draftRecord = draftRecord;
    }

    public String getPendingItem() {
        return pendingItem;
    }

    public void setPendingItem(String pendingItem) {
        this.pendingItem = pendingItem;
    }

    public BigDecimal getFinalBill() {
        return finalBill;
    }

    public void setFinalBill(BigDecimal finalBill) {
        this.finalBill = finalBill;
    }

    public Float getNextCoupon() {
        return nextCoupon;
    }

    public void setNextCoupon(Float nextCoupon) {
        this.nextCoupon = nextCoupon;
    }

    public Double getHiddenProject() {
        return hiddenProject;
    }

    public void setHiddenProject(Double hiddenProject) {
        this.hiddenProject = hiddenProject;
    }

    public String getPrimaryTask() {
        return primaryTask;
    }

    public void setPrimaryTask(String primaryTask) {
        this.primaryTask = primaryTask;
    }

    public Integer getPrimaryEvent() {
        return primaryEvent;
    }

    public void setPrimaryEvent(Integer primaryEvent) {
        this.primaryEvent = primaryEvent;
    }

    public BigDecimal getMainSubscription() {
        return mainSubscription;
    }

    public void setMainSubscription(BigDecimal mainSubscription) {
        this.mainSubscription = mainSubscription;
    }

    public List getPreviousPolicy() {
        return previousPolicy;
    }

    public void setPreviousPolicy(List previousPolicy) {
        this.previousPolicy = previousPolicy;
    }

    public Set getSyncedBrand() {
        return syncedBrand;
    }

    public void setSyncedBrand(Set syncedBrand) {
        this.syncedBrand = syncedBrand;
    }

    public Set getPublicAudit() {
        return publicAudit;
    }

    public void setPublicAudit(Set publicAudit) {
        this.publicAudit = publicAudit;
    }

    public Float getNewProject() {
        return newProject;
    }

    public void setNewProject(Float newProject) {
        this.newProject = newProject;
    }

    public String getDecryptedEvent() {
        return decryptedEvent;
    }

    public void setDecryptedEvent(String decryptedEvent) {
        this.decryptedEvent = decryptedEvent;
    }

    public Integer getNextPhone() {
        return nextPhone;
    }

    public void setNextPhone(Integer nextPhone) {
        this.nextPhone = nextPhone;
    }

    public List getFlaggedItem() {
        return flaggedItem;
    }

    public void setFlaggedItem(List flaggedItem) {
        this.flaggedItem = flaggedItem;
    }

    public Boolean getActiveActivity() {
        return activeActivity;
    }

    public void setActiveActivity(Boolean activeActivity) {
        this.activeActivity = activeActivity;
    }

    public Long getRejectedCustomer() {
        return rejectedCustomer;
    }

    public void setRejectedCustomer(Long rejectedCustomer) {
        this.rejectedCustomer = rejectedCustomer;
    }

    public Float getEnabledDocument() {
        return enabledDocument;
    }

    public void setEnabledDocument(Float enabledDocument) {
        this.enabledDocument = enabledDocument;
    }

    public Float getInactivePlan() {
        return inactivePlan;
    }

    public void setInactivePlan(Float inactivePlan) {
        this.inactivePlan = inactivePlan;
    }

    public String getPreviousMilestone() {
        return previousMilestone;
    }

    public void setPreviousMilestone(String previousMilestone) {
        this.previousMilestone = previousMilestone;
    }

    public Set getHiddenCustomer() {
        return hiddenCustomer;
    }

    public void setHiddenCustomer(Set hiddenCustomer) {
        this.hiddenCustomer = hiddenCustomer;
    }

    public Long getHiddenTransaction() {
        return hiddenTransaction;
    }

    public void setHiddenTransaction(Long hiddenTransaction) {
        this.hiddenTransaction = hiddenTransaction;
    }

    public List getCurrentReport() {
        return currentReport;
    }

    public void setCurrentReport(List currentReport) {
        this.currentReport = currentReport;
    }

    public BigDecimal getInitialAsset() {
        return initialAsset;
    }

    public void setInitialAsset(BigDecimal initialAsset) {
        this.initialAsset = initialAsset;
    }

    public Integer getMaxSchedule() {
        return maxSchedule;
    }

    public void setMaxSchedule(Integer maxSchedule) {
        this.maxSchedule = maxSchedule;
    }

    public Double getApprovedContract() {
        return approvedContract;
    }

    public void setApprovedContract(Double approvedContract) {
        this.approvedContract = approvedContract;
    }

    public Long getMainMilestone() {
        return mainMilestone;
    }

    public void setMainMilestone(Long mainMilestone) {
        this.mainMilestone = mainMilestone;
    }

    public Set getDecryptedNotification() {
        return decryptedNotification;
    }

    public void setDecryptedNotification(Set decryptedNotification) {
        this.decryptedNotification = decryptedNotification;
    }

    public BigDecimal getSavedSupplier() {
        return savedSupplier;
    }

    public void setSavedSupplier(BigDecimal savedSupplier) {
        this.savedSupplier = savedSupplier;
    }

    public BigDecimal getSecondaryUser() {
        return secondaryUser;
    }

    public void setSecondaryUser(BigDecimal secondaryUser) {
        this.secondaryUser = secondaryUser;
    }

    public Long getLevelHistory() {
        return levelHistory;
    }

    public void setLevelHistory(Long levelHistory) {
        this.levelHistory = levelHistory;
    }

    public Double getNewStock() {
        return newStock;
    }

    public void setNewStock(Double newStock) {
        this.newStock = newStock;
    }

    public Double getVerifiedPermission() {
        return verifiedPermission;
    }

    public void setVerifiedPermission(Double verifiedPermission) {
        this.verifiedPermission = verifiedPermission;
    }

    public String getNextTransaction() {
        return nextTransaction;
    }

    public void setNextTransaction(String nextTransaction) {
        this.nextTransaction = nextTransaction;
    }

    public Long getCurrentDepartment() {
        return currentDepartment;
    }

    public void setCurrentDepartment(Long currentDepartment) {
        this.currentDepartment = currentDepartment;
    }

    public Integer getVisibleActivity() {
        return visibleActivity;
    }

    public void setVisibleActivity(Integer visibleActivity) {
        this.visibleActivity = visibleActivity;
    }

    public BigDecimal getPendingUser() {
        return pendingUser;
    }

    public void setPendingUser(BigDecimal pendingUser) {
        this.pendingUser = pendingUser;
    }

    public String getEnabledActivity() {
        return enabledActivity;
    }

    public void setEnabledActivity(String enabledActivity) {
        this.enabledActivity = enabledActivity;
    }

    public Double getUnlockedLog() {
        return unlockedLog;
    }

    public void setUnlockedLog(Double unlockedLog) {
        this.unlockedLog = unlockedLog;
    }

    public Float getSecondaryReport() {
        return secondaryReport;
    }

    public void setSecondaryReport(Float secondaryReport) {
        this.secondaryReport = secondaryReport;
    }

    public String getRejectedFeedback() {
        return rejectedFeedback;
    }

    public void setRejectedFeedback(String rejectedFeedback) {
        this.rejectedFeedback = rejectedFeedback;
    }

    public Double getLevelPhone() {
        return levelPhone;
    }

    public void setLevelPhone(Double levelPhone) {
        this.levelPhone = levelPhone;
    }

    public Boolean getLockedAchievement() {
        return lockedAchievement;
    }

    public void setLockedAchievement(Boolean lockedAchievement) {
        this.lockedAchievement = lockedAchievement;
    }

    public Set getStatusStrategy() {
        return statusStrategy;
    }

    public void setStatusStrategy(Set statusStrategy) {
        this.statusStrategy = statusStrategy;
    }

    public Long getFlaggedMessage() {
        return flaggedMessage;
    }

    public void setFlaggedMessage(Long flaggedMessage) {
        this.flaggedMessage = flaggedMessage;
    }

    public Double getUnlockedGoal() {
        return unlockedGoal;
    }

    public void setUnlockedGoal(Double unlockedGoal) {
        this.unlockedGoal = unlockedGoal;
    }

    public String getInitialTeam() {
        return initialTeam;
    }

    public void setInitialTeam(String initialTeam) {
        this.initialTeam = initialTeam;
    }

    public Double getUpdatedPrice() {
        return updatedPrice;
    }

    public void setUpdatedPrice(Double updatedPrice) {
        this.updatedPrice = updatedPrice;
    }

    public List getMinOrder() {
        return minOrder;
    }

    public void setMinOrder(List minOrder) {
        this.minOrder = minOrder;
    }

    public Float getNextWarehouse() {
        return nextWarehouse;
    }

    public void setNextWarehouse(Float nextWarehouse) {
        this.nextWarehouse = nextWarehouse;
    }

    public BigDecimal getAverageReview() {
        return averageReview;
    }

    public void setAverageReview(BigDecimal averageReview) {
        this.averageReview = averageReview;
    }

    public Set getSyncedDiscount() {
        return syncedDiscount;
    }

    public void setSyncedDiscount(Set syncedDiscount) {
        this.syncedDiscount = syncedDiscount;
    }

    public Float getPendingEmail() {
        return pendingEmail;
    }

    public void setPendingEmail(Float pendingEmail) {
        this.pendingEmail = pendingEmail;
    }

    public BigDecimal getSecureDocument() {
        return secureDocument;
    }

    public void setSecureDocument(BigDecimal secureDocument) {
        this.secureDocument = secureDocument;
    }

    public String getPrimaryCart() {
        return primaryCart;
    }

    public void setPrimaryCart(String primaryCart) {
        this.primaryCart = primaryCart;
    }

    public BigDecimal getDeletedCart() {
        return deletedCart;
    }

    public void setDeletedCart(BigDecimal deletedCart) {
        this.deletedCart = deletedCart;
    }

    public Double getReportedShipment() {
        return reportedShipment;
    }

    public void setReportedShipment(Double reportedShipment) {
        this.reportedShipment = reportedShipment;
    }

    public String getDecryptedAgreement() {
        return decryptedAgreement;
    }

    public void setDecryptedAgreement(String decryptedAgreement) {
        this.decryptedAgreement = decryptedAgreement;
    }

    public String getSavedPolicy() {
        return savedPolicy;
    }

    public void setSavedPolicy(String savedPolicy) {
        this.savedPolicy = savedPolicy;
    }

    public Integer getSharedName() {
        return sharedName;
    }

    public void setSharedName(Integer sharedName) {
        this.sharedName = sharedName;
    }

    public Set getInactiveBill() {
        return inactiveBill;
    }

    public void setInactiveBill(Set inactiveBill) {
        this.inactiveBill = inactiveBill;
    }

    public Boolean getSecureCart() {
        return secureCart;
    }

    public void setSecureCart(Boolean secureCart) {
        this.secureCart = secureCart;
    }

    public Boolean getPermanentLog() {
        return permanentLog;
    }

    public void setPermanentLog(Boolean permanentLog) {
        this.permanentLog = permanentLog;
    }

    public List getOpenItem() {
        return openItem;
    }

    public void setOpenItem(List openItem) {
        this.openItem = openItem;
    }

    public BigDecimal getPublishedMilestone() {
        return publishedMilestone;
    }

    public void setPublishedMilestone(BigDecimal publishedMilestone) {
        this.publishedMilestone = publishedMilestone;
    }

    public String getLevelPermission() {
        return levelPermission;
    }

    public void setLevelPermission(String levelPermission) {
        this.levelPermission = levelPermission;
    }

    public Integer getSuccessfulTransaction() {
        return successfulTransaction;
    }

    public void setSuccessfulTransaction(Integer successfulTransaction) {
        this.successfulTransaction = successfulTransaction;
    }

    public Long getTemporarySession() {
        return temporarySession;
    }

    public void setTemporarySession(Long temporarySession) {
        this.temporarySession = temporarySession;
    }

    public String getUnsecureFolder() {
        return unsecureFolder;
    }

    public void setUnsecureFolder(String unsecureFolder) {
        this.unsecureFolder = unsecureFolder;
    }

    public Integer getRestoredGroup() {
        return restoredGroup;
    }

    public void setRestoredGroup(Integer restoredGroup) {
        this.restoredGroup = restoredGroup;
    }

    public Set getHiddenStock() {
        return hiddenStock;
    }

    public void setHiddenStock(Set hiddenStock) {
        this.hiddenStock = hiddenStock;
    }

    public Float getFinalReview() {
        return finalReview;
    }

    public void setFinalReview(Float finalReview) {
        this.finalReview = finalReview;
    }

    public Set getDecryptedFolder() {
        return decryptedFolder;
    }

    public void setDecryptedFolder(Set decryptedFolder) {
        this.decryptedFolder = decryptedFolder;
    }

    public Double getFailedCoupon() {
        return failedCoupon;
    }

    public void setFailedCoupon(Double failedCoupon) {
        this.failedCoupon = failedCoupon;
    }

    public String getSyncedSchedule() {
        return syncedSchedule;
    }

    public void setSyncedSchedule(String syncedSchedule) {
        this.syncedSchedule = syncedSchedule;
    }

    public Long getOldActivity() {
        return oldActivity;
    }

    public void setOldActivity(Long oldActivity) {
        this.oldActivity = oldActivity;
    }

    public BigDecimal getTypeFile() {
        return typeFile;
    }

    public void setTypeFile(BigDecimal typeFile) {
        this.typeFile = typeFile;
    }

    public List getOldMessage() {
        return oldMessage;
    }

    public void setOldMessage(List oldMessage) {
        this.oldMessage = oldMessage;
    }

    public String getEncryptedAsset() {
        return encryptedAsset;
    }

    public void setEncryptedAsset(String encryptedAsset) {
        this.encryptedAsset = encryptedAsset;
    }

    public Double getFirstProject() {
        return firstProject;
    }

    public void setFirstProject(Double firstProject) {
        this.firstProject = firstProject;
    }

    public List getPermanentContract() {
        return permanentContract;
    }

    public void setPermanentContract(List permanentContract) {
        this.permanentContract = permanentContract;
    }

    public String getUnpublishedUser() {
        return unpublishedUser;
    }

    public void setUnpublishedUser(String unpublishedUser) {
        this.unpublishedUser = unpublishedUser;
    }

    public BigDecimal getFlaggedOrder() {
        return flaggedOrder;
    }

    public void setFlaggedOrder(BigDecimal flaggedOrder) {
        this.flaggedOrder = flaggedOrder;
    }

    public Long getModifiedContract() {
        return modifiedContract;
    }

    public void setModifiedContract(Long modifiedContract) {
        this.modifiedContract = modifiedContract;
    }

    public String getReviewedUser() {
        return reviewedUser;
    }

    public void setReviewedUser(String reviewedUser) {
        this.reviewedUser = reviewedUser;
    }

    public Boolean getSuccessfulActivity() {
        return successfulActivity;
    }

    public void setSuccessfulActivity(Boolean successfulActivity) {
        this.successfulActivity = successfulActivity;
    }

    public String getPendingResource() {
        return pendingResource;
    }

    public void setPendingResource(String pendingResource) {
        this.pendingResource = pendingResource;
    }

    public Long getEncryptedTransaction() {
        return encryptedTransaction;
    }

    public void setEncryptedTransaction(Long encryptedTransaction) {
        this.encryptedTransaction = encryptedTransaction;
    }

    public Float getSecondaryPayment() {
        return secondaryPayment;
    }

    public void setSecondaryPayment(Float secondaryPayment) {
        this.secondaryPayment = secondaryPayment;
    }

    public Integer getPreviousGroup() {
        return previousGroup;
    }

    public void setPreviousGroup(Integer previousGroup) {
        this.previousGroup = previousGroup;
    }
}
