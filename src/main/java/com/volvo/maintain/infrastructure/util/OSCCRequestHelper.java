package com.volvo.maintain.infrastructure.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.infrastructure.config.OSCCProperties;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

import static java.nio.charset.StandardCharsets.UTF_8;

@Component
@Slf4j
public class OSCCRequestHelper {

    @Autowired
    private OSCCProperties properties;


    public Map<String, Object> sendPost(String url, Map<String, Object> param) {
        String appSecret = properties.getAppSecret();
        String appKey = properties.getAppKey();
        String baseUrl = properties.getBaseUrl();
        log.info("appSecret:{} appKey:{} ", appSecret, appKey);
        log.info("baseUrl:{} url:{} ", baseUrl, url);
        param.put("pin", properties.getPin());
        param.put("enterpriseNo", properties.getEnterpriseNo());
        param.put("ownerNo", properties.getOwnerNo());
        param.put("tenantId", properties.getTenantId());
        List<Map<String, Object>> maps = Collections.singletonList(param);
        String s = JSONArray.toJSONString(maps);
//        log.info("入参:{}", s);
        try {
            String post = sendRequest(baseUrl, url,
                    properties.getDomain(), appKey,
                    appSecret, "2.0", s);
            return JSONObject.parseObject(post, Map.class);
        } catch (Exception e) {
            log.error("调用OSCC接口出错", e);
            throw new ServiceBizException("调用OSCC接口出错");
        }

    }


    private static String md5Hex(String string) throws NoSuchAlgorithmException {
        StringBuilder md5Hex = new StringBuilder();
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        for (byte b : md5.digest(string.getBytes(UTF_8))) {
            md5Hex.append(String.format("%02X", b));
        }
        return md5Hex.toString();
    }

    private static String generateSign(String appKey, String appSecret, String method,
                                       String timestamp, String version, String paramJson) throws NoSuchAlgorithmException {
        String string = String.join("", new String[]{
                appSecret,
                "app_key", appKey,
                "method", method,
                "param_json", paramJson,
                "timestamp", timestamp,
                "v", version,
                appSecret});
        return md5Hex(string);
    }

    private static String sendRequest(String baseUrl,
                                      String method,
                                      String domain,
                                      String appKey,
                                      String appSecret,
                                      String version,
                                      String requestBody) throws Exception {
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String timestamp = dateFormat.format(new Date());
        String sign = generateSign(appKey, appSecret, method, timestamp, version, requestBody);
        HttpURLConnection connection = null;
        String uri = baseUrl + method;
        Map<String, String> query = new HashMap<>();
        query.put("LOP-DN", domain);
        query.put("app_key", appKey);
        query.put("timestamp", timestamp);
        query.put("v", "2.0");
        query.put("sign", sign);
        query.put("algorithm", "md5-salt");
        URL url = new URL(uri + "?" + httpBuildQuery(query));
        try {
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setRequestProperty("Accept", "*/*");
            connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            try (OutputStream outputStream = connection.getOutputStream()) {
                outputStream.write(requestBody.getBytes(UTF_8));
            }
            try (InputStream inputStream = connection.getInputStream()) {
                StringBuilder stringBuilder = new StringBuilder();
                byte[] buffer = new byte[1024];
                int n;
                while ((n = inputStream.read(buffer)) > 0) {
                    stringBuilder.append(new String(buffer, 0, n));
                }
                return stringBuilder.toString();
            }
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    public static String httpBuildQuery(Map<String, String> query) throws UnsupportedEncodingException {
        StringBuilder stringBuilder = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : query.entrySet()) {
            if (!first) {
                stringBuilder.append("&");
            } else {
                first = false;
            }
            stringBuilder.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), UTF_8.name()));
        }
        return stringBuilder.toString();
    }



}
