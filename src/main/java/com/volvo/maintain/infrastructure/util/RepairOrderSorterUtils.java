package com.volvo.maintain.infrastructure.util;
import java.util.Comparator;
import java.util.List;

import com.volvo.maintain.application.maintainlead.vo.RepairOrderV2VO;

public class RepairOrderSorterUtils {
	
    private RepairOrderSorterUtils() {
		super();
	}

	public static void sortRepairOrders(List<RepairOrderV2VO> orders) {
        orders.sort(new RepairOrderComparator());
    }

    private static class RepairOrderComparator implements Comparator<RepairOrderV2VO> {
        @Override
        public int compare(RepairOrderV2VO o1, RepairOrderV2VO o2) {
            // 1. 主排序逻辑：根据completeTag和finalInspectionTag分组
            int group1 = getGroupPriority(o1);
            int group2 = getGroupPriority(o2);
            if (group1 != group2) {
                return Integer.compare(group1, group2);
            }

            // 2. 组内二次排序：根据roNo最后两位的数值顺序
            return compareRoNoSuffix(o1.getRoNo(), o2.getRoNo());
        }

        /**
         * 计算分组优先级
         * 1. 最高优先级: completeTag=10041002(竣工) 且 finalInspectionTag=10041001(终检通过) -> 优先级0
         * 2. 次高优先级: finalInspectionTag=10041002(终检未通过) -> 优先级1
         * 3. 最低优先级: completeTag=10041001(未竣工) -> 优先级2
         * 4. 其他情况: 优先级3（放在最后）
         */
        private int getGroupPriority(RepairOrderV2VO order) {
            Long completeTag = order.getCompleteTag();
            String finalInspectionTag = order.getFinalInspectionTag();

            // 第一优先级：竣工且终检通过
            if (completeTag != null && completeTag == 10041002L && 
                finalInspectionTag != null && finalInspectionTag.equals("10041001")) {
                return 0;
            }
            // 第二优先级：终检未通过
            if (completeTag != null && completeTag == 10041002L && finalInspectionTag != null && finalInspectionTag.equals("10041002")) {
                return 1;
            }
            // 第三优先级：未竣工
            if (completeTag != null && completeTag == 10041001L) {
                return 2;
            }
            // 其他情况
            return 3;
        }

        /**
         * 比较roNo的最后两位字符
         * 1. 提取最后2个字符（不足2位时取全部）
         * 2. 尝试解析为数字
         * 3. 数字按数值顺序比较，非数字按字符串顺序比较
         */
        private int compareRoNoSuffix(String roNo1, String roNo2) {
            String suffix1 = getLastTwoChars(roNo1);
            String suffix2 = getLastTwoChars(roNo2);
            
            Integer num1 = parseAsInteger(suffix1);
            Integer num2 = parseAsInteger(suffix2);
            
            // 两个都是数字的情况
            if (num1 != null && num2 != null) {
                return num1.compareTo(num2);
            }
            // 只有roNo1是数字
            if (num1 != null) {
                return -1;
            }
            // 只有roNo2是数字
            if (num2 != null) {
                return 1;
            }
            // 两个都不是数字，按字符串比较
            return suffix1.compareTo(suffix2);
        }

        /**
         * 获取字符串的最后两个字符
         */
        private String getLastTwoChars(String str) {
            if (str == null || str.isEmpty()) {
                return "";
            }
            int len = str.length();
            return len <= 2 ? str : str.substring(len - 2);
        }

        /**
         * 尝试将字符串解析为整数
         */
        private Integer parseAsInteger(String str) {
            if (str == null || str.isEmpty()) {
                return null;
            }
            try {
                return Integer.parseInt(str);
            } catch (NumberFormatException e) {
                return null;
            }
        }
    }
}
