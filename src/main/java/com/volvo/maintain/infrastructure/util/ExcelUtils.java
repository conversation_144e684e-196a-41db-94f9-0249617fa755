package com.volvo.maintain.infrastructure.util;

import com.volvo.maintain.infrastructure.annotation.ExcelExportColumnAnno;
import com.yonyou.cyx.framework.service.excel.ExcelExportColumn;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description excel导出utils
 * @Date 2024/11/19 16:39
 */
public class ExcelUtils {

    /**
     * 获取excel导出的字段
     *
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<ExcelExportColumn> getExportColumn(Class<T> clazz) {
        // 获取所有声明的字段
        Field[] fields = clazz.getDeclaredFields();
        List<ExcelExportColumn> exportColumnList = new ArrayList<>();
        for (Field field : fields) {
            // 检查字段是否有指定的注解
            if (field.isAnnotationPresent(ExcelExportColumnAnno.class)) {
                // 获取注解实例
                ExcelExportColumnAnno annotation = field.getAnnotation(ExcelExportColumnAnno.class);
                exportColumnList.add(new ExcelExportColumn(annotation.columName(), annotation.columDesc()));
            }
        }
        return exportColumnList;
    }
}
