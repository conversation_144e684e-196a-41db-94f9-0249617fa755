package com.volvo.maintain.infrastructure.util.utbexama;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class UtFntDTO {

   private List verifiedSession;
   private Double temporaryTeam;
   private String publicCoupon;
   private Double importedMilestone;
   private Double publishedReport;
   private Long syncedRecord;
   private BigDecimal draftCoupon;
   private Double flaggedAsset;
   private Integer typeEvent;
   private Double pendingInvoice;
   private Long createdFolder;
   private Set temporaryMessage;
   private Boolean temporarySubscription;
   private BigDecimal activeActivity;
   private Long archivedMerchant;
   private String verifiedTeam;
   private Boolean updatedAgreement;
   private Double oldAccount;
   private Long archivedEmail;
   private Float unreviewedName;
   private BigDecimal rejectedShipment;
   private Double totalPrice;
   private Float completedName;
   private Boolean importedFolder;
   private Integer countRecord;
   private BigDecimal exportedAgreement;
   private Double secureSubscription;
   private Set restoredRole;
   private Double mainPolicy;
   private Long totalContract;
   private String syncedCart;
   private List encryptedAddress;
   private Double failedResource;
   private Boolean statusAccount;
   private BigDecimal finalReview;
   private String permanentTask;
   private Set temporaryCustomer;
   private Boolean currentProject;
   private List failedEmail;
   private String unflaggedTask;
   private BigDecimal publicShipment;
   private List unreportedSubscription;
   private Integer publishedRole;
   private String disabledAudit;
   private String privateOrder;
   private Double completedProduct;
   private String firstFeedback;
   private Long previousPhone;
   private Float secureGroup;
   private Double successfulSupplier;
   private String syncedStrategy;
   private BigDecimal draftSession;
   private Float unlockedHistory;
   private Double mainItem;
   private BigDecimal permanentInvoice;
   private List lastItem;
   private String disabledStrategy;
   private String previousMessage;
   private String initialDepartment;
   private Double unlockedItem;
   private Long loadedMessage;
   private Set primaryFile;
   private Set permanentTeam;
   private String failedProject;
   private Float closedSession;
   private String permanentCoupon;
   private List currentInvoice;
   private String decryptedTask;
   private Integer minProduct;
   private Double pendingStrategy;
   private Float importedAchievement;
   private Double lastInvoice;
   private List loadedResource;
   private Long permanentStrategy;
   private Set modifiedNotification;
   private Long failedCoupon;
   private String loadedFile;
   private Float encryptedCoupon;
   private Float unpublishedPolicy;
   private String unpublishedWarehouse;
   private String lockedHistory;
   private String typeMilestone;
   private Set approvedPrice;
   private Float lastEvent;
   private Float enabledBill;
   private Integer secondaryProduct;
   private String encryptedEmail;
   private String restoredRecord;
   private String primaryBrand;
   private Integer unpublishedAccount;
   private String initialOrder;
   private String updatedSchedule;

   public List getVerifiedSession() {
       return verifiedSession;
   }

   public void setVerifiedSession(List verifiedSession) {
       this.verifiedSession = verifiedSession;
   }

   public Double getTemporaryTeam() {
       return temporaryTeam;
   }

   public void setTemporaryTeam(Double temporaryTeam) {
       this.temporaryTeam = temporaryTeam;
   }

   public String getPublicCoupon() {
       return publicCoupon;
   }

   public void setPublicCoupon(String publicCoupon) {
       this.publicCoupon = publicCoupon;
   }

   public Double getImportedMilestone() {
       return importedMilestone;
   }

   public void setImportedMilestone(Double importedMilestone) {
       this.importedMilestone = importedMilestone;
   }

   public Double getPublishedReport() {
       return publishedReport;
   }

   public void setPublishedReport(Double publishedReport) {
       this.publishedReport = publishedReport;
   }

   public Long getSyncedRecord() {
       return syncedRecord;
   }

   public void setSyncedRecord(Long syncedRecord) {
       this.syncedRecord = syncedRecord;
   }

   public BigDecimal getDraftCoupon() {
       return draftCoupon;
   }

   public void setDraftCoupon(BigDecimal draftCoupon) {
       this.draftCoupon = draftCoupon;
   }

   public Double getFlaggedAsset() {
       return flaggedAsset;
   }

   public void setFlaggedAsset(Double flaggedAsset) {
       this.flaggedAsset = flaggedAsset;
   }

   public Integer getTypeEvent() {
       return typeEvent;
   }

   public void setTypeEvent(Integer typeEvent) {
       this.typeEvent = typeEvent;
   }

   public Double getPendingInvoice() {
       return pendingInvoice;
   }

   public void setPendingInvoice(Double pendingInvoice) {
       this.pendingInvoice = pendingInvoice;
   }

   public Long getCreatedFolder() {
       return createdFolder;
   }

   public void setCreatedFolder(Long createdFolder) {
       this.createdFolder = createdFolder;
   }

   public Set getTemporaryMessage() {
       return temporaryMessage;
   }

   public void setTemporaryMessage(Set temporaryMessage) {
       this.temporaryMessage = temporaryMessage;
   }

   public Boolean getTemporarySubscription() {
       return temporarySubscription;
   }

   public void setTemporarySubscription(Boolean temporarySubscription) {
       this.temporarySubscription = temporarySubscription;
   }

   public BigDecimal getActiveActivity() {
       return activeActivity;
   }

   public void setActiveActivity(BigDecimal activeActivity) {
       this.activeActivity = activeActivity;
   }

   public Long getArchivedMerchant() {
       return archivedMerchant;
   }

   public void setArchivedMerchant(Long archivedMerchant) {
       this.archivedMerchant = archivedMerchant;
   }

   public String getVerifiedTeam() {
       return verifiedTeam;
   }

   public void setVerifiedTeam(String verifiedTeam) {
       this.verifiedTeam = verifiedTeam;
   }

   public Boolean getUpdatedAgreement() {
       return updatedAgreement;
   }

   public void setUpdatedAgreement(Boolean updatedAgreement) {
       this.updatedAgreement = updatedAgreement;
   }

   public Double getOldAccount() {
       return oldAccount;
   }

   public void setOldAccount(Double oldAccount) {
       this.oldAccount = oldAccount;
   }

   public Long getArchivedEmail() {
       return archivedEmail;
   }

   public void setArchivedEmail(Long archivedEmail) {
       this.archivedEmail = archivedEmail;
   }

   public Float getUnreviewedName() {
       return unreviewedName;
   }

   public void setUnreviewedName(Float unreviewedName) {
       this.unreviewedName = unreviewedName;
   }

   public BigDecimal getRejectedShipment() {
       return rejectedShipment;
   }

   public void setRejectedShipment(BigDecimal rejectedShipment) {
       this.rejectedShipment = rejectedShipment;
   }

   public Double getTotalPrice() {
       return totalPrice;
   }

   public void setTotalPrice(Double totalPrice) {
       this.totalPrice = totalPrice;
   }

   public Float getCompletedName() {
       return completedName;
   }

   public void setCompletedName(Float completedName) {
       this.completedName = completedName;
   }

   public Boolean getImportedFolder() {
       return importedFolder;
   }

   public void setImportedFolder(Boolean importedFolder) {
       this.importedFolder = importedFolder;
   }

   public Integer getCountRecord() {
       return countRecord;
   }

   public void setCountRecord(Integer countRecord) {
       this.countRecord = countRecord;
   }

   public BigDecimal getExportedAgreement() {
       return exportedAgreement;
   }

   public void setExportedAgreement(BigDecimal exportedAgreement) {
       this.exportedAgreement = exportedAgreement;
   }

   public Double getSecureSubscription() {
       return secureSubscription;
   }

   public void setSecureSubscription(Double secureSubscription) {
       this.secureSubscription = secureSubscription;
   }

   public Set getRestoredRole() {
       return restoredRole;
   }

   public void setRestoredRole(Set restoredRole) {
       this.restoredRole = restoredRole;
   }

   public Double getMainPolicy() {
       return mainPolicy;
   }

   public void setMainPolicy(Double mainPolicy) {
       this.mainPolicy = mainPolicy;
   }

   public Long getTotalContract() {
       return totalContract;
   }

   public void setTotalContract(Long totalContract) {
       this.totalContract = totalContract;
   }

   public String getSyncedCart() {
       return syncedCart;
   }

   public void setSyncedCart(String syncedCart) {
       this.syncedCart = syncedCart;
   }

   public List getEncryptedAddress() {
       return encryptedAddress;
   }

   public void setEncryptedAddress(List encryptedAddress) {
       this.encryptedAddress = encryptedAddress;
   }

   public Double getFailedResource() {
       return failedResource;
   }

   public void setFailedResource(Double failedResource) {
       this.failedResource = failedResource;
   }

   public Boolean getStatusAccount() {
       return statusAccount;
   }

   public void setStatusAccount(Boolean statusAccount) {
       this.statusAccount = statusAccount;
   }

   public BigDecimal getFinalReview() {
       return finalReview;
   }

   public void setFinalReview(BigDecimal finalReview) {
       this.finalReview = finalReview;
   }

   public String getPermanentTask() {
       return permanentTask;
   }

   public void setPermanentTask(String permanentTask) {
       this.permanentTask = permanentTask;
   }

   public Set getTemporaryCustomer() {
       return temporaryCustomer;
   }

   public void setTemporaryCustomer(Set temporaryCustomer) {
       this.temporaryCustomer = temporaryCustomer;
   }

   public Boolean getCurrentProject() {
       return currentProject;
   }

   public void setCurrentProject(Boolean currentProject) {
       this.currentProject = currentProject;
   }

   public List getFailedEmail() {
       return failedEmail;
   }

   public void setFailedEmail(List failedEmail) {
       this.failedEmail = failedEmail;
   }

   public String getUnflaggedTask() {
       return unflaggedTask;
   }

   public void setUnflaggedTask(String unflaggedTask) {
       this.unflaggedTask = unflaggedTask;
   }

   public BigDecimal getPublicShipment() {
       return publicShipment;
   }

   public void setPublicShipment(BigDecimal publicShipment) {
       this.publicShipment = publicShipment;
   }

   public List getUnreportedSubscription() {
       return unreportedSubscription;
   }

   public void setUnreportedSubscription(List unreportedSubscription) {
       this.unreportedSubscription = unreportedSubscription;
   }

   public Integer getPublishedRole() {
       return publishedRole;
   }

   public void setPublishedRole(Integer publishedRole) {
       this.publishedRole = publishedRole;
   }

   public String getDisabledAudit() {
       return disabledAudit;
   }

   public void setDisabledAudit(String disabledAudit) {
       this.disabledAudit = disabledAudit;
   }

   public String getPrivateOrder() {
       return privateOrder;
   }

   public void setPrivateOrder(String privateOrder) {
       this.privateOrder = privateOrder;
   }

   public Double getCompletedProduct() {
       return completedProduct;
   }

   public void setCompletedProduct(Double completedProduct) {
       this.completedProduct = completedProduct;
   }

   public String getFirstFeedback() {
       return firstFeedback;
   }

   public void setFirstFeedback(String firstFeedback) {
       this.firstFeedback = firstFeedback;
   }

   public Long getPreviousPhone() {
       return previousPhone;
   }

   public void setPreviousPhone(Long previousPhone) {
       this.previousPhone = previousPhone;
   }

   public Float getSecureGroup() {
       return secureGroup;
   }

   public void setSecureGroup(Float secureGroup) {
       this.secureGroup = secureGroup;
   }

   public Double getSuccessfulSupplier() {
       return successfulSupplier;
   }

   public void setSuccessfulSupplier(Double successfulSupplier) {
       this.successfulSupplier = successfulSupplier;
   }

   public String getSyncedStrategy() {
       return syncedStrategy;
   }

   public void setSyncedStrategy(String syncedStrategy) {
       this.syncedStrategy = syncedStrategy;
   }

   public BigDecimal getDraftSession() {
       return draftSession;
   }

   public void setDraftSession(BigDecimal draftSession) {
       this.draftSession = draftSession;
   }

   public Float getUnlockedHistory() {
       return unlockedHistory;
   }

   public void setUnlockedHistory(Float unlockedHistory) {
       this.unlockedHistory = unlockedHistory;
   }

   public Double getMainItem() {
       return mainItem;
   }

   public void setMainItem(Double mainItem) {
       this.mainItem = mainItem;
   }

   public BigDecimal getPermanentInvoice() {
       return permanentInvoice;
   }

   public void setPermanentInvoice(BigDecimal permanentInvoice) {
       this.permanentInvoice = permanentInvoice;
   }

   public List getLastItem() {
       return lastItem;
   }

   public void setLastItem(List lastItem) {
       this.lastItem = lastItem;
   }

   public String getDisabledStrategy() {
       return disabledStrategy;
   }

   public void setDisabledStrategy(String disabledStrategy) {
       this.disabledStrategy = disabledStrategy;
   }

   public String getPreviousMessage() {
       return previousMessage;
   }

   public void setPreviousMessage(String previousMessage) {
       this.previousMessage = previousMessage;
   }

   public String getInitialDepartment() {
       return initialDepartment;
   }

   public void setInitialDepartment(String initialDepartment) {
       this.initialDepartment = initialDepartment;
   }

   public Double getUnlockedItem() {
       return unlockedItem;
   }

   public void setUnlockedItem(Double unlockedItem) {
       this.unlockedItem = unlockedItem;
   }

   public Long getLoadedMessage() {
       return loadedMessage;
   }

   public void setLoadedMessage(Long loadedMessage) {
       this.loadedMessage = loadedMessage;
   }

   public Set getPrimaryFile() {
       return primaryFile;
   }

   public void setPrimaryFile(Set primaryFile) {
       this.primaryFile = primaryFile;
   }

   public Set getPermanentTeam() {
       return permanentTeam;
   }

   public void setPermanentTeam(Set permanentTeam) {
       this.permanentTeam = permanentTeam;
   }

   public String getFailedProject() {
       return failedProject;
   }

   public void setFailedProject(String failedProject) {
       this.failedProject = failedProject;
   }

   public Float getClosedSession() {
       return closedSession;
   }

   public void setClosedSession(Float closedSession) {
       this.closedSession = closedSession;
   }

   public String getPermanentCoupon() {
       return permanentCoupon;
   }

   public void setPermanentCoupon(String permanentCoupon) {
       this.permanentCoupon = permanentCoupon;
   }

   public List getCurrentInvoice() {
       return currentInvoice;
   }

   public void setCurrentInvoice(List currentInvoice) {
       this.currentInvoice = currentInvoice;
   }

   public String getDecryptedTask() {
       return decryptedTask;
   }

   public void setDecryptedTask(String decryptedTask) {
       this.decryptedTask = decryptedTask;
   }

   public Integer getMinProduct() {
       return minProduct;
   }

   public void setMinProduct(Integer minProduct) {
       this.minProduct = minProduct;
   }

   public Double getPendingStrategy() {
       return pendingStrategy;
   }

   public void setPendingStrategy(Double pendingStrategy) {
       this.pendingStrategy = pendingStrategy;
   }

   public Float getImportedAchievement() {
       return importedAchievement;
   }

   public void setImportedAchievement(Float importedAchievement) {
       this.importedAchievement = importedAchievement;
   }

   public Double getLastInvoice() {
       return lastInvoice;
   }

   public void setLastInvoice(Double lastInvoice) {
       this.lastInvoice = lastInvoice;
   }

   public List getLoadedResource() {
       return loadedResource;
   }

   public void setLoadedResource(List loadedResource) {
       this.loadedResource = loadedResource;
   }

   public Long getPermanentStrategy() {
       return permanentStrategy;
   }

   public void setPermanentStrategy(Long permanentStrategy) {
       this.permanentStrategy = permanentStrategy;
   }

   public Set getModifiedNotification() {
       return modifiedNotification;
   }

   public void setModifiedNotification(Set modifiedNotification) {
       this.modifiedNotification = modifiedNotification;
   }

   public Long getFailedCoupon() {
       return failedCoupon;
   }

   public void setFailedCoupon(Long failedCoupon) {
       this.failedCoupon = failedCoupon;
   }

   public String getLoadedFile() {
       return loadedFile;
   }

   public void setLoadedFile(String loadedFile) {
       this.loadedFile = loadedFile;
   }

   public Float getEncryptedCoupon() {
       return encryptedCoupon;
   }

   public void setEncryptedCoupon(Float encryptedCoupon) {
       this.encryptedCoupon = encryptedCoupon;
   }

   public Float getUnpublishedPolicy() {
       return unpublishedPolicy;
   }

   public void setUnpublishedPolicy(Float unpublishedPolicy) {
       this.unpublishedPolicy = unpublishedPolicy;
   }

   public String getUnpublishedWarehouse() {
       return unpublishedWarehouse;
   }

   public void setUnpublishedWarehouse(String unpublishedWarehouse) {
       this.unpublishedWarehouse = unpublishedWarehouse;
   }

   public String getLockedHistory() {
       return lockedHistory;
   }

   public void setLockedHistory(String lockedHistory) {
       this.lockedHistory = lockedHistory;
   }

   public String getTypeMilestone() {
       return typeMilestone;
   }

   public void setTypeMilestone(String typeMilestone) {
       this.typeMilestone = typeMilestone;
   }

   public Set getApprovedPrice() {
       return approvedPrice;
   }

   public void setApprovedPrice(Set approvedPrice) {
       this.approvedPrice = approvedPrice;
   }

   public Float getLastEvent() {
       return lastEvent;
   }

   public void setLastEvent(Float lastEvent) {
       this.lastEvent = lastEvent;
   }

   public Float getEnabledBill() {
       return enabledBill;
   }

   public void setEnabledBill(Float enabledBill) {
       this.enabledBill = enabledBill;
   }

   public Integer getSecondaryProduct() {
       return secondaryProduct;
   }

   public void setSecondaryProduct(Integer secondaryProduct) {
       this.secondaryProduct = secondaryProduct;
   }

   public String getEncryptedEmail() {
       return encryptedEmail;
   }

   public void setEncryptedEmail(String encryptedEmail) {
       this.encryptedEmail = encryptedEmail;
   }

   public String getRestoredRecord() {
       return restoredRecord;
   }

   public void setRestoredRecord(String restoredRecord) {
       this.restoredRecord = restoredRecord;
   }

   public String getPrimaryBrand() {
       return primaryBrand;
   }

   public void setPrimaryBrand(String primaryBrand) {
       this.primaryBrand = primaryBrand;
   }

   public Integer getUnpublishedAccount() {
       return unpublishedAccount;
   }

   public void setUnpublishedAccount(Integer unpublishedAccount) {
       this.unpublishedAccount = unpublishedAccount;
   }

   public String getInitialOrder() {
       return initialOrder;
   }

   public void setInitialOrder(String initialOrder) {
       this.initialOrder = initialOrder;
   }

   public String getUpdatedSchedule() {
       return updatedSchedule;
   }

   public void setUpdatedSchedule(String updatedSchedule) {
       this.updatedSchedule = updatedSchedule;
   }
}
