package com.volvo.maintain.infrastructure.util.utbexama;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class UtHntDTO {
    private Set deletedSupplier;
    private BigDecimal countSession;
    private Double loadedDepartment;
    private Set savedOrder;
    private String oldFile;
    private Set completedTask;
    private List verifiedMessage;
    private Float unreviewedSubscription;
    private String createdSession;
    private String deletedHistory;
    private Set approvedBrand;
    private Long unpublishedStock;
    private Double loadedActivity;
    private BigDecimal unreportedInvoice;
    private Set verifiedAgreement;
    private Boolean nextSupplier;
    private Set updatedBrand;
    private List verifiedStrategy;
    private Integer nextCoupon;
    private String averageAchievement;
    private List sharedAgreement;
    private BigDecimal restoredFile;
    private Float currentName;
    private String activeName;
    private List unlockedDepartment;
    private Set createdPolicy;
    private String flaggedNotification;
    private String createdSupplier;
    private Float temporaryAudit;
    private Integer hiddenAudit;
    private Boolean completedGroup;
    private Float unlockedProject;
    private Integer archivedOrder;
    private Double draftStrategy;
    private Long minFeedback;
    private Set publishedBrand;
    private Float publicBill;
    private Set finalCustomer;
    private Integer enabledFeedback;
    private Set temporaryProduct;
    private Double unreviewedPolicy;
    private Double levelPrice;
    private Long averageAudit;
    private Double verifiedProject;
    private Double unlockedMessage;
    private Float syncedBill;
    private List temporaryReport;
    private Boolean deletedLog;
    private Set approvedTeam;
    private Set publishedGoal;
    private List lastSchedule;
    private Long failedDepartment;
    private String finalDate;
    private String importedBrand;
    private Set secondaryName;
    private Set sharedStock;
    private String maxCategory;
    private BigDecimal lockedDiscount;
    private Long pendingMilestone;
    private String primaryTeam;
    private List loadedAudit;
    private Double unlockedPhone;
    private String deletedBrand;
    private BigDecimal oldGroup;
    private Set initialMerchant;
    private Float statusItem;
    private Long decryptedMilestone;
    private List inactiveDate;
    private Boolean publishedReview;
    private Long visibleAudit;
    private Long visiblePlan;
    private BigDecimal closedCoupon;
    private String modifiedWarehouse;
    private String newFile;
    private List minPlan;
    private BigDecimal maxMilestone;
    private String maxStrategy;
    private Integer restoredProject;
    private String secureDiscount;
    private BigDecimal secondaryTransaction;
    private Integer modifiedSchedule;
    private Boolean hiddenContract;
    private String publishedAsset;
    private Float pendingDiscount;
    private Set sharedDiscount;
    private Set reportedMerchant;
    private Integer primaryOrder;
    private Long oldSupplier;
    private String currentPrice;
    private List archivedHistory;
    private String savedGroup;
    private Long lockedPrice;
    private Long unreportedUser;
    private String secondaryProject;
    private Float currentSupplier;
    private Integer maxAgreement;
    private Integer restoredLog;
    private Long primaryPlan;
    private Set unreportedDate;

    public Set getDeletedSupplier() {
        return deletedSupplier;
    }

    public void setDeletedSupplier(Set deletedSupplier) {
        this.deletedSupplier = deletedSupplier;
    }

    public BigDecimal getCountSession() {
        return countSession;
    }

    public void setCountSession(BigDecimal countSession) {
        this.countSession = countSession;
    }

    public Double getLoadedDepartment() {
        return loadedDepartment;
    }

    public void setLoadedDepartment(Double loadedDepartment) {
        this.loadedDepartment = loadedDepartment;
    }

    public Set getSavedOrder() {
        return savedOrder;
    }

    public void setSavedOrder(Set savedOrder) {
        this.savedOrder = savedOrder;
    }

    public String getOldFile() {
        return oldFile;
    }

    public void setOldFile(String oldFile) {
        this.oldFile = oldFile;
    }

    public Set getCompletedTask() {
        return completedTask;
    }

    public void setCompletedTask(Set completedTask) {
        this.completedTask = completedTask;
    }

    public List getVerifiedMessage() {
        return verifiedMessage;
    }

    public void setVerifiedMessage(List verifiedMessage) {
        this.verifiedMessage = verifiedMessage;
    }

    public Float getUnreviewedSubscription() {
        return unreviewedSubscription;
    }

    public void setUnreviewedSubscription(Float unreviewedSubscription) {
        this.unreviewedSubscription = unreviewedSubscription;
    }

    public String getCreatedSession() {
        return createdSession;
    }

    public void setCreatedSession(String createdSession) {
        this.createdSession = createdSession;
    }

    public String getDeletedHistory() {
        return deletedHistory;
    }

    public void setDeletedHistory(String deletedHistory) {
        this.deletedHistory = deletedHistory;
    }

    public Set getApprovedBrand() {
        return approvedBrand;
    }

    public void setApprovedBrand(Set approvedBrand) {
        this.approvedBrand = approvedBrand;
    }

    public Long getUnpublishedStock() {
        return unpublishedStock;
    }

    public void setUnpublishedStock(Long unpublishedStock) {
        this.unpublishedStock = unpublishedStock;
    }

    public Double getLoadedActivity() {
        return loadedActivity;
    }

    public void setLoadedActivity(Double loadedActivity) {
        this.loadedActivity = loadedActivity;
    }

    public BigDecimal getUnreportedInvoice() {
        return unreportedInvoice;
    }

    public void setUnreportedInvoice(BigDecimal unreportedInvoice) {
        this.unreportedInvoice = unreportedInvoice;
    }

    public Set getVerifiedAgreement() {
        return verifiedAgreement;
    }

    public void setVerifiedAgreement(Set verifiedAgreement) {
        this.verifiedAgreement = verifiedAgreement;
    }

    public Boolean getNextSupplier() {
        return nextSupplier;
    }

    public void setNextSupplier(Boolean nextSupplier) {
        this.nextSupplier = nextSupplier;
    }

    public Set getUpdatedBrand() {
        return updatedBrand;
    }

    public void setUpdatedBrand(Set updatedBrand) {
        this.updatedBrand = updatedBrand;
    }

    public List getVerifiedStrategy() {
        return verifiedStrategy;
    }

    public void setVerifiedStrategy(List verifiedStrategy) {
        this.verifiedStrategy = verifiedStrategy;
    }

    public Integer getNextCoupon() {
        return nextCoupon;
    }

    public void setNextCoupon(Integer nextCoupon) {
        this.nextCoupon = nextCoupon;
    }

    public String getAverageAchievement() {
        return averageAchievement;
    }

    public void setAverageAchievement(String averageAchievement) {
        this.averageAchievement = averageAchievement;
    }

    public List getSharedAgreement() {
        return sharedAgreement;
    }

    public void setSharedAgreement(List sharedAgreement) {
        this.sharedAgreement = sharedAgreement;
    }

    public BigDecimal getRestoredFile() {
        return restoredFile;
    }

    public void setRestoredFile(BigDecimal restoredFile) {
        this.restoredFile = restoredFile;
    }

    public Float getCurrentName() {
        return currentName;
    }

    public void setCurrentName(Float currentName) {
        this.currentName = currentName;
    }

    public String getActiveName() {
        return activeName;
    }

    public void setActiveName(String activeName) {
        this.activeName = activeName;
    }

    public List getUnlockedDepartment() {
        return unlockedDepartment;
    }

    public void setUnlockedDepartment(List unlockedDepartment) {
        this.unlockedDepartment = unlockedDepartment;
    }

    public Set getCreatedPolicy() {
        return createdPolicy;
    }

    public void setCreatedPolicy(Set createdPolicy) {
        this.createdPolicy = createdPolicy;
    }

    public String getFlaggedNotification() {
        return flaggedNotification;
    }

    public void setFlaggedNotification(String flaggedNotification) {
        this.flaggedNotification = flaggedNotification;
    }

    public String getCreatedSupplier() {
        return createdSupplier;
    }

    public void setCreatedSupplier(String createdSupplier) {
        this.createdSupplier = createdSupplier;
    }

    public Float getTemporaryAudit() {
        return temporaryAudit;
    }

    public void setTemporaryAudit(Float temporaryAudit) {
        this.temporaryAudit = temporaryAudit;
    }

    public Integer getHiddenAudit() {
        return hiddenAudit;
    }

    public void setHiddenAudit(Integer hiddenAudit) {
        this.hiddenAudit = hiddenAudit;
    }

    public Boolean getCompletedGroup() {
        return completedGroup;
    }

    public void setCompletedGroup(Boolean completedGroup) {
        this.completedGroup = completedGroup;
    }

    public Float getUnlockedProject() {
        return unlockedProject;
    }

    public void setUnlockedProject(Float unlockedProject) {
        this.unlockedProject = unlockedProject;
    }

    public Integer getArchivedOrder() {
        return archivedOrder;
    }

    public void setArchivedOrder(Integer archivedOrder) {
        this.archivedOrder = archivedOrder;
    }

    public Double getDraftStrategy() {
        return draftStrategy;
    }

    public void setDraftStrategy(Double draftStrategy) {
        this.draftStrategy = draftStrategy;
    }

    public Long getMinFeedback() {
        return minFeedback;
    }

    public void setMinFeedback(Long minFeedback) {
        this.minFeedback = minFeedback;
    }

    public Set getPublishedBrand() {
        return publishedBrand;
    }

    public void setPublishedBrand(Set publishedBrand) {
        this.publishedBrand = publishedBrand;
    }

    public Float getPublicBill() {
        return publicBill;
    }

    public void setPublicBill(Float publicBill) {
        this.publicBill = publicBill;
    }

    public Set getFinalCustomer() {
        return finalCustomer;
    }

    public void setFinalCustomer(Set finalCustomer) {
        this.finalCustomer = finalCustomer;
    }

    public Integer getEnabledFeedback() {
        return enabledFeedback;
    }

    public void setEnabledFeedback(Integer enabledFeedback) {
        this.enabledFeedback = enabledFeedback;
    }

    public Set getTemporaryProduct() {
        return temporaryProduct;
    }

    public void setTemporaryProduct(Set temporaryProduct) {
        this.temporaryProduct = temporaryProduct;
    }

    public Double getUnreviewedPolicy() {
        return unreviewedPolicy;
    }

    public void setUnreviewedPolicy(Double unreviewedPolicy) {
        this.unreviewedPolicy = unreviewedPolicy;
    }

    public Double getLevelPrice() {
        return levelPrice;
    }

    public void setLevelPrice(Double levelPrice) {
        this.levelPrice = levelPrice;
    }

    public Long getAverageAudit() {
        return averageAudit;
    }

    public void setAverageAudit(Long averageAudit) {
        this.averageAudit = averageAudit;
    }

    public Double getVerifiedProject() {
        return verifiedProject;
    }

    public void setVerifiedProject(Double verifiedProject) {
        this.verifiedProject = verifiedProject;
    }

    public Double getUnlockedMessage() {
        return unlockedMessage;
    }

    public void setUnlockedMessage(Double unlockedMessage) {
        this.unlockedMessage = unlockedMessage;
    }

    public Float getSyncedBill() {
        return syncedBill;
    }

    public void setSyncedBill(Float syncedBill) {
        this.syncedBill = syncedBill;
    }

    public List getTemporaryReport() {
        return temporaryReport;
    }

    public void setTemporaryReport(List temporaryReport) {
        this.temporaryReport = temporaryReport;
    }

    public Boolean getDeletedLog() {
        return deletedLog;
    }

    public void setDeletedLog(Boolean deletedLog) {
        this.deletedLog = deletedLog;
    }

    public Set getApprovedTeam() {
        return approvedTeam;
    }

    public void setApprovedTeam(Set approvedTeam) {
        this.approvedTeam = approvedTeam;
    }

    public Set getPublishedGoal() {
        return publishedGoal;
    }

    public void setPublishedGoal(Set publishedGoal) {
        this.publishedGoal = publishedGoal;
    }

    public List getLastSchedule() {
        return lastSchedule;
    }

    public void setLastSchedule(List lastSchedule) {
        this.lastSchedule = lastSchedule;
    }

    public Long getFailedDepartment() {
        return failedDepartment;
    }

    public void setFailedDepartment(Long failedDepartment) {
        this.failedDepartment = failedDepartment;
    }

    public String getFinalDate() {
        return finalDate;
    }

    public void setFinalDate(String finalDate) {
        this.finalDate = finalDate;
    }

    public String getImportedBrand() {
        return importedBrand;
    }

    public void setImportedBrand(String importedBrand) {
        this.importedBrand = importedBrand;
    }

    public Set getSecondaryName() {
        return secondaryName;
    }

    public void setSecondaryName(Set secondaryName) {
        this.secondaryName = secondaryName;
    }

    public Set getSharedStock() {
        return sharedStock;
    }

    public void setSharedStock(Set sharedStock) {
        this.sharedStock = sharedStock;
    }

    public String getMaxCategory() {
        return maxCategory;
    }

    public void setMaxCategory(String maxCategory) {
        this.maxCategory = maxCategory;
    }

    public BigDecimal getLockedDiscount() {
        return lockedDiscount;
    }

    public void setLockedDiscount(BigDecimal lockedDiscount) {
        this.lockedDiscount = lockedDiscount;
    }

    public Long getPendingMilestone() {
        return pendingMilestone;
    }

    public void setPendingMilestone(Long pendingMilestone) {
        this.pendingMilestone = pendingMilestone;
    }

    public String getPrimaryTeam() {
        return primaryTeam;
    }

    public void setPrimaryTeam(String primaryTeam) {
        this.primaryTeam = primaryTeam;
    }

    public List getLoadedAudit() {
        return loadedAudit;
    }

    public void setLoadedAudit(List loadedAudit) {
        this.loadedAudit = loadedAudit;
    }

    public Double getUnlockedPhone() {
        return unlockedPhone;
    }

    public void setUnlockedPhone(Double unlockedPhone) {
        this.unlockedPhone = unlockedPhone;
    }

    public String getDeletedBrand() {
        return deletedBrand;
    }

    public void setDeletedBrand(String deletedBrand) {
        this.deletedBrand = deletedBrand;
    }

    public BigDecimal getOldGroup() {
        return oldGroup;
    }

    public void setOldGroup(BigDecimal oldGroup) {
        this.oldGroup = oldGroup;
    }

    public Set getInitialMerchant() {
        return initialMerchant;
    }

    public void setInitialMerchant(Set initialMerchant) {
        this.initialMerchant = initialMerchant;
    }

    public Float getStatusItem() {
        return statusItem;
    }

    public void setStatusItem(Float statusItem) {
        this.statusItem = statusItem;
    }

    public Long getDecryptedMilestone() {
        return decryptedMilestone;
    }

    public void setDecryptedMilestone(Long decryptedMilestone) {
        this.decryptedMilestone = decryptedMilestone;
    }

    public List getInactiveDate() {
        return inactiveDate;
    }

    public void setInactiveDate(List inactiveDate) {
        this.inactiveDate = inactiveDate;
    }

    public Boolean getPublishedReview() {
        return publishedReview;
    }

    public void setPublishedReview(Boolean publishedReview) {
        this.publishedReview = publishedReview;
    }

    public Long getVisibleAudit() {
        return visibleAudit;
    }

    public void setVisibleAudit(Long visibleAudit) {
        this.visibleAudit = visibleAudit;
    }

    public Long getVisiblePlan() {
        return visiblePlan;
    }

    public void setVisiblePlan(Long visiblePlan) {
        this.visiblePlan = visiblePlan;
    }

    public BigDecimal getClosedCoupon() {
        return closedCoupon;
    }

    public void setClosedCoupon(BigDecimal closedCoupon) {
        this.closedCoupon = closedCoupon;
    }

    public String getModifiedWarehouse() {
        return modifiedWarehouse;
    }

    public void setModifiedWarehouse(String modifiedWarehouse) {
        this.modifiedWarehouse = modifiedWarehouse;
    }

    public String getNewFile() {
        return newFile;
    }

    public void setNewFile(String newFile) {
        this.newFile = newFile;
    }

    public List getMinPlan() {
        return minPlan;
    }

    public void setMinPlan(List minPlan) {
        this.minPlan = minPlan;
    }

    public BigDecimal getMaxMilestone() {
        return maxMilestone;
    }

    public void setMaxMilestone(BigDecimal maxMilestone) {
        this.maxMilestone = maxMilestone;
    }

    public String getMaxStrategy() {
        return maxStrategy;
    }

    public void setMaxStrategy(String maxStrategy) {
        this.maxStrategy = maxStrategy;
    }

    public Integer getRestoredProject() {
        return restoredProject;
    }

    public void setRestoredProject(Integer restoredProject) {
        this.restoredProject = restoredProject;
    }

    public String getSecureDiscount() {
        return secureDiscount;
    }

    public void setSecureDiscount(String secureDiscount) {
        this.secureDiscount = secureDiscount;
    }

    public BigDecimal getSecondaryTransaction() {
        return secondaryTransaction;
    }

    public void setSecondaryTransaction(BigDecimal secondaryTransaction) {
        this.secondaryTransaction = secondaryTransaction;
    }

    public Integer getModifiedSchedule() {
        return modifiedSchedule;
    }

    public void setModifiedSchedule(Integer modifiedSchedule) {
        this.modifiedSchedule = modifiedSchedule;
    }

    public Boolean getHiddenContract() {
        return hiddenContract;
    }

    public void setHiddenContract(Boolean hiddenContract) {
        this.hiddenContract = hiddenContract;
    }

    public String getPublishedAsset() {
        return publishedAsset;
    }

    public void setPublishedAsset(String publishedAsset) {
        this.publishedAsset = publishedAsset;
    }

    public Float getPendingDiscount() {
        return pendingDiscount;
    }

    public void setPendingDiscount(Float pendingDiscount) {
        this.pendingDiscount = pendingDiscount;
    }

    public Set getSharedDiscount() {
        return sharedDiscount;
    }

    public void setSharedDiscount(Set sharedDiscount) {
        this.sharedDiscount = sharedDiscount;
    }

    public Set getReportedMerchant() {
        return reportedMerchant;
    }

    public void setReportedMerchant(Set reportedMerchant) {
        this.reportedMerchant = reportedMerchant;
    }

    public Integer getPrimaryOrder() {
        return primaryOrder;
    }

    public void setPrimaryOrder(Integer primaryOrder) {
        this.primaryOrder = primaryOrder;
    }

    public Long getOldSupplier() {
        return oldSupplier;
    }

    public void setOldSupplier(Long oldSupplier) {
        this.oldSupplier = oldSupplier;
    }

    public String getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(String currentPrice) {
        this.currentPrice = currentPrice;
    }

    public List getArchivedHistory() {
        return archivedHistory;
    }

    public void setArchivedHistory(List archivedHistory) {
        this.archivedHistory = archivedHistory;
    }

    public String getSavedGroup() {
        return savedGroup;
    }

    public void setSavedGroup(String savedGroup) {
        this.savedGroup = savedGroup;
    }

    public Long getLockedPrice() {
        return lockedPrice;
    }

    public void setLockedPrice(Long lockedPrice) {
        this.lockedPrice = lockedPrice;
    }

    public Long getUnreportedUser() {
        return unreportedUser;
    }

    public void setUnreportedUser(Long unreportedUser) {
        this.unreportedUser = unreportedUser;
    }

    public String getSecondaryProject() {
        return secondaryProject;
    }

    public void setSecondaryProject(String secondaryProject) {
        this.secondaryProject = secondaryProject;
    }

    public Float getCurrentSupplier() {
        return currentSupplier;
    }

    public void setCurrentSupplier(Float currentSupplier) {
        this.currentSupplier = currentSupplier;
    }

    public Integer getMaxAgreement() {
        return maxAgreement;
    }

    public void setMaxAgreement(Integer maxAgreement) {
        this.maxAgreement = maxAgreement;
    }

    public Integer getRestoredLog() {
        return restoredLog;
    }

    public void setRestoredLog(Integer restoredLog) {
        this.restoredLog = restoredLog;
    }

    public Long getPrimaryPlan() {
        return primaryPlan;
    }

    public void setPrimaryPlan(Long primaryPlan) {
        this.primaryPlan = primaryPlan;
    }

    public Set getUnreportedDate() {
        return unreportedDate;
    }

    public void setUnreportedDate(Set unreportedDate) {
        this.unreportedDate = unreportedDate;
    }
}
