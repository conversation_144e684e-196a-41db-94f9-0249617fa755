package com.volvo.maintain.infrastructure.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 校验参数合法性
 */
public class ParamsValidator {
    // 手机号合法性校验
    private static final String PHONE_NUMBER_REGEX = "^1[23456789]\\d{9}$";
    // 车架号校验不能为中文
    private static final String VIN_NUMBER_REGEX = "[\u4e00-\u9fa5]";
    private static final int VIN_LENGTH = 17;
    private static final String IS_NUMBER = ".*\\d.*";
    /**
     * 校验手机号码是否合法
     *
     * @param phoneNumber 要校验的手机号码
     * @return 如果手机号码合法则返回true，否则返回false
     */
    public static boolean validatePhoneNumber(String phoneNumber) {
        // 创建手机号码正则表达式的模式
        Pattern pattern = Pattern.compile(PHONE_NUMBER_REGEX);
        // 使用模式匹配手机号码
        Matcher matcher = pattern.matcher(phoneNumber);
        // 返回匹配结果
        return matcher.matches();
    }
    /**
     * 校验vin是否合法
     *
     * @param vin 要校验的手机号码
     * @return 如果手机号码合法则返回true，否则返回false
     */
    public static boolean validateVin(String vin) {
        // 创建手机号码正则表达式的模式
        Pattern pattern = Pattern.compile(VIN_NUMBER_REGEX);
        // 使用模式匹配手机号码
        Matcher matcher = pattern.matcher(vin);
        boolean flag = !matcher.find();
        boolean flag1 = vin.length() >= VIN_LENGTH;
        // 返回匹配结果
        return (flag && flag1);
    }
    /**
     * 校验是否有数字
     *
     */
    public static boolean validateNumber(String parm) {
        // 创建手机号码正则表达式的模式
        Pattern pattern = Pattern.compile(IS_NUMBER);
        // 使用模式匹配手机号码
        Matcher matcher = pattern.matcher(parm);
        // 返回匹配结果
        return !matcher.find();
    }
}
