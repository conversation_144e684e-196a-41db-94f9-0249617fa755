package com.volvo.maintain.infrastructure.util;


import java.lang.reflect.Field;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ObjectUtil {
	
	private ObjectUtil() {
		super();
	}
	
	/**
     * 反射对象转map
     * @param object 对象
     * @return map key属性，value值
     */
	public static Map<String, String> emailDtoToMap(Object object) {
        Map<String, String> map = new HashMap<>();
        Field[] fields = object.getClass().getDeclaredFields(); // 获取当前类的所有字段
        for (Field field : fields) {
            try {
                field.setAccessible(true); // 设置字段可访问，即便它们是private的
                Object value = field.get(object); // 获取字段的值
                if (value instanceof Map) {
                    ((Map<?, ?>) value).forEach((key, val) -> {
                        if (val != null) { // 在 Map 内部也检查来跳过 null 值
                            String mapKey = key.toString();
                            String mapValue = val.toString();
                            // 如果Map中已有同名的key，则追加值，否则直接放置
                            map.merge(mapKey, mapValue, (oldVal, newVal) -> oldVal + "," + newVal);
                        }
                    });
                } else {
                    String fieldKey = field.getName();
                    String fieldValue = String.valueOf(value);
                    // 如果Map中已有同名的key，则追加值，否则直接放置
                    map.merge(fieldKey, fieldValue, (oldVal, newVal) -> oldVal + "," + newVal);
                }
            } catch (IllegalAccessException e) {
            	log.error("转换失败:{}", e.getMessage());
            }
        }
        return map;
    }
    /**
     * 替换模板中的参数
     * @param replacements
     * @param content
     * @return
     */
	public static String replacePlaceholders(Map<String, String> replacements, String content) {
        // 遍历替换值的映射，依次替换占位符
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            if (StringUtils.isNotEmpty(entry.getValue())){
                content = content.replace(placeholder, entry.getValue());
            }else {
                content = content.replace(placeholder, "null");
            }
        }
        // 清理map
        replacements.clear();
        return content;
    }
	
	/**
	 * List中获取 某个属性最大值 对象返回
	 * @param <T>
	 * @param list
	 * @param comparator
	 * @return
	 */
	public static <T> T getMaxObjectWithStream(List<T> list, Comparator<T> comparator) {
		log.info("ObjectUtils-getMaxObjectWithStream: {}", JSON.toJSONString(list));
        return list.stream()
            .max(comparator)
            .orElse(null);
    }
}
