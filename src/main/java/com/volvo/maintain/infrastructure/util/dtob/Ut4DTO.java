package com.volvo.maintain.infrastructure.util.dtob;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
public class Ut4DTO {
    private String publishedRecord;
    private Float restoredFile;
    private Double unreviewedPermission;
    private BigDecimal publishedCart;
    private BigDecimal publicAddress;
    private String newReview;
    private Float oldEmail;
    private List secondaryPermission;
    private BigDecimal hiddenBill;
    private List disabledTransaction;
    private Float syncedStock;
    private Float exportedAsset;
    private Double closedSupplier;
    private Set currentDepartment;
    private List flaggedStrategy;
    private Float permanentSession;
    private String lastRecord;
    private List levelBrand;
    private BigDecimal publishedEvent;
    private Float firstTeam;
    private Float countReport;
    private String oldResource;
    private List draftProduct;
    private Long inactiveSupplier;
    private Double unverifiedDiscount;
    private Float newTask;
    private String totalName;
    private String openShipment;
    private Float inactiveBrand;
    private String totalMerchant;
    private Boolean countPolicy;
    private String typeTask;
    private Integer openPolicy;
    private String closedWarehouse;
    private Integer closedCart;
    private Integer temporaryFeedback;
    private Double disabledTask;
    private Set finalAchievement;
    private Boolean disabledShipment;
    private Double temporaryFile;
    private Integer syncedName;
    private Float updatedTask;
    private Float finalPermission;
    private Set archivedOrder;
    private List modifiedInvoice;
    private Integer completedTask;
    private String unreportedDepartment;
    private Integer inactiveCoupon;
    private BigDecimal primaryTask;
    private BigDecimal unreportedAgreement;
    private Boolean verifiedTransaction;
    private Long successfulProject;
    private String temporaryTask;
    private Float secureSchedule;
    private List totalEmail;
    private Long loadedHistory;
    private Boolean rejectedSession;
    private List typeCustomer;
    private Double openDiscount;
    private Boolean initialBrand;
    private String loadedPhone;
    private Boolean newGoal;
    private Integer updatedUser;
    private BigDecimal typeName;
    private String maxPlan;
    private Integer reportedLog;
    private Boolean modifiedFeedback;
    private Long publishedRole;
    private String unsecureCoupon;
    private Long syncedCart;
    private Boolean decryptedHistory;
    private String temporaryHistory;
    private String updatedDate;
    private List privateBrand;
    private String completedActivity;
    private Float pendingFeedback;
    private BigDecimal openActivity;
    private Set typePlan;
    private Float unlockedEvent;
    private String enabledDocument;
    private Set verifiedEvent;
    private Float reviewedMerchant;
    private String levelWarehouse;
    private Boolean unflaggedCoupon;
    private Boolean unflaggedPermission;
    private List exportedPhone;
    private Float publicDocument;
    private Float enabledCoupon;
    private Long inactiveStrategy;
    private String currentRecord;
    private List closedTeam;
    private List permanentTransaction;
    private BigDecimal publishedCategory;
    private Boolean typeShipment;
    private Double levelMessage;
    private Boolean createdHistory;
    private Long pendingPrice;
    private Integer decryptedAddress;
    private List nextAsset;
    private Double publishedActivity;
}
