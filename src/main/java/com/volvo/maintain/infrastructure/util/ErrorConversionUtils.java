package com.volvo.maintain.infrastructure.util;

import com.volvo.maintain.infrastructure.exception.DmsException;
import com.volvo.maintain.infrastructure.exception.ExceptionEnum;
import com.volvo.maintain.infrastructure.exception.UtilException;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.yonyou.cyx.function.exception.ServiceBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 下游异常统一判断
 *
 * <AUTHOR>
 * @date 2017年1月13日
 */
@Slf4j
public class ErrorConversionUtils {

	private ErrorConversionUtils() {
		super();
	}

	/**
	 * 错误码转换
	 */
	public static void errorCodeConversion(DmsResponse<?> result) {
		if(result==null) {
			throw new UtilException(ExceptionEnum.QUERY_TOKEN_ERROR);
		}
		String msg = StringUtils.isNotBlank(result.getErrMsg())?result.getErrMsg():result.getReturnMessage();
		if(!result.isSuccess()) {
			throw new ServiceBizException(msg, msg);
		}
	}

	/**
	 * 错误码转换
	 */
	public static void errorCodeConversion(DmsResponse<?> result, String code) {
		if (result == null) {
			throw new UtilException(ExceptionEnum.QUERY_TOKEN_ERROR);
		}
		String msg = StringUtils.isNotBlank(result.getErrMsg()) ? result.getErrMsg() : result.getReturnMessage();
		if (!result.isSuccess()) {
			Integer codeDef = 500;
			try {
				codeDef = Integer.valueOf(code);
			} catch (Exception e) {
				log.info("errorCodeConversion e:{}", e);
			}
			throw new DmsException(codeDef, msg, msg, msg);
		}
	}
}
