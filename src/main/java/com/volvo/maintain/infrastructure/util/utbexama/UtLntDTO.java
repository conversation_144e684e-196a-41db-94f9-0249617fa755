package com.volvo.maintain.infrastructure.util.utbexama;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class UtLntDTO {
    private Set unverifiedLog;
    private Set unflaggedPrice;
    private Set currentPolicy;
    private Boolean failedLog;
    private List nextInvoice;
    private String decryptedAgreement;
    private BigDecimal encryptedProject;
    private Integer unlockedName;
    private String visibleResource;
    private String openEvent;
    private String visibleTask;
    private Float archivedShipment;
    private Double oldPolicy;
    private Boolean maxStrategy;
    private Set publishedFile;
    private Long oldProduct;
    private String modifiedAddress;
    private Set maxAccount;
    private Boolean currentUser;
    private String completedSupplier;
    private BigDecimal privateTask;
    private Double privateEvent;
    private Integer inactiveCoupon;
    private Integer flaggedContract;
    private Boolean decryptedBill;
    private Float closedItem;
    private Double secureSession;
    private Double approvedSupplier;
    private Double totalRecord;
    private Boolean restoredProject;
    private Float finalFile;
    private Double primaryStock;
    private Boolean deletedAudit;
    private Boolean lockedCart;
    private Set secondaryPermission;
    private Integer oldFeedback;
    private List flaggedCart;
    private List temporaryProduct;
    private Double exportedStock;
    private Long nextProduct;
    private String secondaryName;
    private Integer primaryActivity;
    private Set totalOrder;
    private Integer lockedPayment;
    private Set currentAudit;
    private Boolean newRecord;
    private Integer primaryNotification;
    private List archivedWarehouse;
    private Set publicLog;
    private Long levelName;
    private Long hiddenActivity;
    private Long successfulRecord;
    private Set unreviewedDocument;
    private Integer loadedAudit;
    private String unsecureBill;
    private Long unverifiedResource;
    private Integer exportedSchedule;
    private List maxDiscount;
    private Boolean oldWarehouse;
    private Float lastItem;
    private List successfulHistory;
    private Long modifiedProduct;
    private Boolean unverifiedEmail;
    private Boolean inactivePrice;
    private String visibleWarehouse;
    private Integer closedHistory;
    private List importedPlan;
    private String oldDepartment;
    private String secondaryContract;
    private List lockedCoupon;
    private String importedAgreement;
    private Integer previousCart;
    private Long typeFolder;
    private String unpublishedShipment;
    private Boolean privateProject;
    private String visibleTeam;
    private Integer publishedMerchant;
    private Float totalDate;
    private String disabledHistory;
    private Double flaggedPayment;
    private Integer unflaggedMessage;
    private Boolean encryptedSession;
    private BigDecimal deletedTeam;
    private Long activeDepartment;
    private Float primarySubscription;
    private Boolean statusTask;
    private Boolean unsecureDiscount;
    private Long maxPrice;
    private Boolean draftMerchant;
    private Boolean temporaryDocument;
    private Float primaryName;
    private Double unreviewedAsset;
    private Integer primarySession;
    private Boolean countItem;
    private Long mainHistory;
    private Boolean previousDepartment;
    private String maxFolder;
    private Double privateFolder;

    public Set getUnverifiedLog() {
        return unverifiedLog;
    }

    public void setUnverifiedLog(Set unverifiedLog) {
        this.unverifiedLog = unverifiedLog;
    }

    public Set getUnflaggedPrice() {
        return unflaggedPrice;
    }

    public void setUnflaggedPrice(Set unflaggedPrice) {
        this.unflaggedPrice = unflaggedPrice;
    }

    public Set getCurrentPolicy() {
        return currentPolicy;
    }

    public void setCurrentPolicy(Set currentPolicy) {
        this.currentPolicy = currentPolicy;
    }

    public Boolean getFailedLog() {
        return failedLog;
    }

    public void setFailedLog(Boolean failedLog) {
        this.failedLog = failedLog;
    }

    public List getNextInvoice() {
        return nextInvoice;
    }

    public void setNextInvoice(List nextInvoice) {
        this.nextInvoice = nextInvoice;
    }

    public String getDecryptedAgreement() {
        return decryptedAgreement;
    }

    public void setDecryptedAgreement(String decryptedAgreement) {
        this.decryptedAgreement = decryptedAgreement;
    }

    public BigDecimal getEncryptedProject() {
        return encryptedProject;
    }

    public void setEncryptedProject(BigDecimal encryptedProject) {
        this.encryptedProject = encryptedProject;
    }

    public Integer getUnlockedName() {
        return unlockedName;
    }

    public void setUnlockedName(Integer unlockedName) {
        this.unlockedName = unlockedName;
    }

    public String getVisibleResource() {
        return visibleResource;
    }

    public void setVisibleResource(String visibleResource) {
        this.visibleResource = visibleResource;
    }

    public String getOpenEvent() {
        return openEvent;
    }

    public void setOpenEvent(String openEvent) {
        this.openEvent = openEvent;
    }

    public String getVisibleTask() {
        return visibleTask;
    }

    public void setVisibleTask(String visibleTask) {
        this.visibleTask = visibleTask;
    }

    public Float getArchivedShipment() {
        return archivedShipment;
    }

    public void setArchivedShipment(Float archivedShipment) {
        this.archivedShipment = archivedShipment;
    }

    public Double getOldPolicy() {
        return oldPolicy;
    }

    public void setOldPolicy(Double oldPolicy) {
        this.oldPolicy = oldPolicy;
    }

    public Boolean getMaxStrategy() {
        return maxStrategy;
    }

    public void setMaxStrategy(Boolean maxStrategy) {
        this.maxStrategy = maxStrategy;
    }

    public Set getPublishedFile() {
        return publishedFile;
    }

    public void setPublishedFile(Set publishedFile) {
        this.publishedFile = publishedFile;
    }

    public Long getOldProduct() {
        return oldProduct;
    }

    public void setOldProduct(Long oldProduct) {
        this.oldProduct = oldProduct;
    }

    public String getModifiedAddress() {
        return modifiedAddress;
    }

    public void setModifiedAddress(String modifiedAddress) {
        this.modifiedAddress = modifiedAddress;
    }

    public Set getMaxAccount() {
        return maxAccount;
    }

    public void setMaxAccount(Set maxAccount) {
        this.maxAccount = maxAccount;
    }

    public Boolean getCurrentUser() {
        return currentUser;
    }

    public void setCurrentUser(Boolean currentUser) {
        this.currentUser = currentUser;
    }

    public String getCompletedSupplier() {
        return completedSupplier;
    }

    public void setCompletedSupplier(String completedSupplier) {
        this.completedSupplier = completedSupplier;
    }

    public BigDecimal getPrivateTask() {
        return privateTask;
    }

    public void setPrivateTask(BigDecimal privateTask) {
        this.privateTask = privateTask;
    }

    public Double getPrivateEvent() {
        return privateEvent;
    }

    public void setPrivateEvent(Double privateEvent) {
        this.privateEvent = privateEvent;
    }

    public Integer getInactiveCoupon() {
        return inactiveCoupon;
    }

    public void setInactiveCoupon(Integer inactiveCoupon) {
        this.inactiveCoupon = inactiveCoupon;
    }

    public Integer getFlaggedContract() {
        return flaggedContract;
    }

    public void setFlaggedContract(Integer flaggedContract) {
        this.flaggedContract = flaggedContract;
    }

    public Boolean getDecryptedBill() {
        return decryptedBill;
    }

    public void setDecryptedBill(Boolean decryptedBill) {
        this.decryptedBill = decryptedBill;
    }

    public Float getClosedItem() {
        return closedItem;
    }

    public void setClosedItem(Float closedItem) {
        this.closedItem = closedItem;
    }

    public Double getSecureSession() {
        return secureSession;
    }

    public void setSecureSession(Double secureSession) {
        this.secureSession = secureSession;
    }

    public Double getApprovedSupplier() {
        return approvedSupplier;
    }

    public void setApprovedSupplier(Double approvedSupplier) {
        this.approvedSupplier = approvedSupplier;
    }

    public Double getTotalRecord() {
        return totalRecord;
    }

    public void setTotalRecord(Double totalRecord) {
        this.totalRecord = totalRecord;
    }

    public Boolean getRestoredProject() {
        return restoredProject;
    }

    public void setRestoredProject(Boolean restoredProject) {
        this.restoredProject = restoredProject;
    }

    public Float getFinalFile() {
        return finalFile;
    }

    public void setFinalFile(Float finalFile) {
        this.finalFile = finalFile;
    }

    public Double getPrimaryStock() {
        return primaryStock;
    }

    public void setPrimaryStock(Double primaryStock) {
        this.primaryStock = primaryStock;
    }

    public Boolean getDeletedAudit() {
        return deletedAudit;
    }

    public void setDeletedAudit(Boolean deletedAudit) {
        this.deletedAudit = deletedAudit;
    }

    public Boolean getLockedCart() {
        return lockedCart;
    }

    public void setLockedCart(Boolean lockedCart) {
        this.lockedCart = lockedCart;
    }

    public Set getSecondaryPermission() {
        return secondaryPermission;
    }

    public void setSecondaryPermission(Set secondaryPermission) {
        this.secondaryPermission = secondaryPermission;
    }

    public Integer getOldFeedback() {
        return oldFeedback;
    }

    public void setOldFeedback(Integer oldFeedback) {
        this.oldFeedback = oldFeedback;
    }

    public List getFlaggedCart() {
        return flaggedCart;
    }

    public void setFlaggedCart(List flaggedCart) {
        this.flaggedCart = flaggedCart;
    }

    public List getTemporaryProduct() {
        return temporaryProduct;
    }

    public void setTemporaryProduct(List temporaryProduct) {
        this.temporaryProduct = temporaryProduct;
    }

    public Double getExportedStock() {
        return exportedStock;
    }

    public void setExportedStock(Double exportedStock) {
        this.exportedStock = exportedStock;
    }

    public Long getNextProduct() {
        return nextProduct;
    }

    public void setNextProduct(Long nextProduct) {
        this.nextProduct = nextProduct;
    }

    public String getSecondaryName() {
        return secondaryName;
    }

    public void setSecondaryName(String secondaryName) {
        this.secondaryName = secondaryName;
    }

    public Integer getPrimaryActivity() {
        return primaryActivity;
    }

    public void setPrimaryActivity(Integer primaryActivity) {
        this.primaryActivity = primaryActivity;
    }

    public Set getTotalOrder() {
        return totalOrder;
    }

    public void setTotalOrder(Set totalOrder) {
        this.totalOrder = totalOrder;
    }

    public Integer getLockedPayment() {
        return lockedPayment;
    }

    public void setLockedPayment(Integer lockedPayment) {
        this.lockedPayment = lockedPayment;
    }

    public Set getCurrentAudit() {
        return currentAudit;
    }

    public void setCurrentAudit(Set currentAudit) {
        this.currentAudit = currentAudit;
    }

    public Boolean getNewRecord() {
        return newRecord;
    }

    public void setNewRecord(Boolean newRecord) {
        this.newRecord = newRecord;
    }

    public Integer getPrimaryNotification() {
        return primaryNotification;
    }

    public void setPrimaryNotification(Integer primaryNotification) {
        this.primaryNotification = primaryNotification;
    }

    public List getArchivedWarehouse() {
        return archivedWarehouse;
    }

    public void setArchivedWarehouse(List archivedWarehouse) {
        this.archivedWarehouse = archivedWarehouse;
    }

    public Set getPublicLog() {
        return publicLog;
    }

    public void setPublicLog(Set publicLog) {
        this.publicLog = publicLog;
    }

    public Long getLevelName() {
        return levelName;
    }

    public void setLevelName(Long levelName) {
        this.levelName = levelName;
    }

    public Long getHiddenActivity() {
        return hiddenActivity;
    }

    public void setHiddenActivity(Long hiddenActivity) {
        this.hiddenActivity = hiddenActivity;
    }

    public Long getSuccessfulRecord() {
        return successfulRecord;
    }

    public void setSuccessfulRecord(Long successfulRecord) {
        this.successfulRecord = successfulRecord;
    }

    public Set getUnreviewedDocument() {
        return unreviewedDocument;
    }

    public void setUnreviewedDocument(Set unreviewedDocument) {
        this.unreviewedDocument = unreviewedDocument;
    }

    public Integer getLoadedAudit() {
        return loadedAudit;
    }

    public void setLoadedAudit(Integer loadedAudit) {
        this.loadedAudit = loadedAudit;
    }

    public String getUnsecureBill() {
        return unsecureBill;
    }

    public void setUnsecureBill(String unsecureBill) {
        this.unsecureBill = unsecureBill;
    }

    public Long getUnverifiedResource() {
        return unverifiedResource;
    }

    public void setUnverifiedResource(Long unverifiedResource) {
        this.unverifiedResource = unverifiedResource;
    }

    public Integer getExportedSchedule() {
        return exportedSchedule;
    }

    public void setExportedSchedule(Integer exportedSchedule) {
        this.exportedSchedule = exportedSchedule;
    }

    public List getMaxDiscount() {
        return maxDiscount;
    }

    public void setMaxDiscount(List maxDiscount) {
        this.maxDiscount = maxDiscount;
    }

    public Boolean getOldWarehouse() {
        return oldWarehouse;
    }

    public void setOldWarehouse(Boolean oldWarehouse) {
        this.oldWarehouse = oldWarehouse;
    }

    public Float getLastItem() {
        return lastItem;
    }

    public void setLastItem(Float lastItem) {
        this.lastItem = lastItem;
    }

    public List getSuccessfulHistory() {
        return successfulHistory;
    }

    public void setSuccessfulHistory(List successfulHistory) {
        this.successfulHistory = successfulHistory;
    }

    public Long getModifiedProduct() {
        return modifiedProduct;
    }

    public void setModifiedProduct(Long modifiedProduct) {
        this.modifiedProduct = modifiedProduct;
    }

    public Boolean getUnverifiedEmail() {
        return unverifiedEmail;
    }

    public void setUnverifiedEmail(Boolean unverifiedEmail) {
        this.unverifiedEmail = unverifiedEmail;
    }

    public Boolean getInactivePrice() {
        return inactivePrice;
    }

    public void setInactivePrice(Boolean inactivePrice) {
        this.inactivePrice = inactivePrice;
    }

    public String getVisibleWarehouse() {
        return visibleWarehouse;
    }

    public void setVisibleWarehouse(String visibleWarehouse) {
        this.visibleWarehouse = visibleWarehouse;
    }

    public Integer getClosedHistory() {
        return closedHistory;
    }

    public void setClosedHistory(Integer closedHistory) {
        this.closedHistory = closedHistory;
    }

    public List getImportedPlan() {
        return importedPlan;
    }

    public void setImportedPlan(List importedPlan) {
        this.importedPlan = importedPlan;
    }

    public String getOldDepartment() {
        return oldDepartment;
    }

    public void setOldDepartment(String oldDepartment) {
        this.oldDepartment = oldDepartment;
    }

    public String getSecondaryContract() {
        return secondaryContract;
    }

    public void setSecondaryContract(String secondaryContract) {
        this.secondaryContract = secondaryContract;
    }

    public List getLockedCoupon() {
        return lockedCoupon;
    }

    public void setLockedCoupon(List lockedCoupon) {
        this.lockedCoupon = lockedCoupon;
    }

    public String getImportedAgreement() {
        return importedAgreement;
    }

    public void setImportedAgreement(String importedAgreement) {
        this.importedAgreement = importedAgreement;
    }

    public Integer getPreviousCart() {
        return previousCart;
    }

    public void setPreviousCart(Integer previousCart) {
        this.previousCart = previousCart;
    }

    public Long getTypeFolder() {
        return typeFolder;
    }

    public void setTypeFolder(Long typeFolder) {
        this.typeFolder = typeFolder;
    }

    public String getUnpublishedShipment() {
        return unpublishedShipment;
    }

    public void setUnpublishedShipment(String unpublishedShipment) {
        this.unpublishedShipment = unpublishedShipment;
    }

    public Boolean getPrivateProject() {
        return privateProject;
    }

    public void setPrivateProject(Boolean privateProject) {
        this.privateProject = privateProject;
    }

    public String getVisibleTeam() {
        return visibleTeam;
    }

    public void setVisibleTeam(String visibleTeam) {
        this.visibleTeam = visibleTeam;
    }

    public Integer getPublishedMerchant() {
        return publishedMerchant;
    }

    public void setPublishedMerchant(Integer publishedMerchant) {
        this.publishedMerchant = publishedMerchant;
    }

    public Float getTotalDate() {
        return totalDate;
    }

    public void setTotalDate(Float totalDate) {
        this.totalDate = totalDate;
    }

    public String getDisabledHistory() {
        return disabledHistory;
    }

    public void setDisabledHistory(String disabledHistory) {
        this.disabledHistory = disabledHistory;
    }

    public Double getFlaggedPayment() {
        return flaggedPayment;
    }

    public void setFlaggedPayment(Double flaggedPayment) {
        this.flaggedPayment = flaggedPayment;
    }

    public Integer getUnflaggedMessage() {
        return unflaggedMessage;
    }

    public void setUnflaggedMessage(Integer unflaggedMessage) {
        this.unflaggedMessage = unflaggedMessage;
    }

    public Boolean getEncryptedSession() {
        return encryptedSession;
    }

    public void setEncryptedSession(Boolean encryptedSession) {
        this.encryptedSession = encryptedSession;
    }

    public BigDecimal getDeletedTeam() {
        return deletedTeam;
    }

    public void setDeletedTeam(BigDecimal deletedTeam) {
        this.deletedTeam = deletedTeam;
    }

    public Long getActiveDepartment() {
        return activeDepartment;
    }

    public void setActiveDepartment(Long activeDepartment) {
        this.activeDepartment = activeDepartment;
    }

    public Float getPrimarySubscription() {
        return primarySubscription;
    }

    public void setPrimarySubscription(Float primarySubscription) {
        this.primarySubscription = primarySubscription;
    }

    public Boolean getStatusTask() {
        return statusTask;
    }

    public void setStatusTask(Boolean statusTask) {
        this.statusTask = statusTask;
    }

    public Boolean getUnsecureDiscount() {
        return unsecureDiscount;
    }

    public void setUnsecureDiscount(Boolean unsecureDiscount) {
        this.unsecureDiscount = unsecureDiscount;
    }

    public Long getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(Long maxPrice) {
        this.maxPrice = maxPrice;
    }

    public Boolean getDraftMerchant() {
        return draftMerchant;
    }

    public void setDraftMerchant(Boolean draftMerchant) {
        this.draftMerchant = draftMerchant;
    }

    public Boolean getTemporaryDocument() {
        return temporaryDocument;
    }

    public void setTemporaryDocument(Boolean temporaryDocument) {
        this.temporaryDocument = temporaryDocument;
    }

    public Float getPrimaryName() {
        return primaryName;
    }

    public void setPrimaryName(Float primaryName) {
        this.primaryName = primaryName;
    }

    public Double getUnreviewedAsset() {
        return unreviewedAsset;
    }

    public void setUnreviewedAsset(Double unreviewedAsset) {
        this.unreviewedAsset = unreviewedAsset;
    }

    public Integer getPrimarySession() {
        return primarySession;
    }

    public void setPrimarySession(Integer primarySession) {
        this.primarySession = primarySession;
    }

    public Boolean getCountItem() {
        return countItem;
    }

    public void setCountItem(Boolean countItem) {
        this.countItem = countItem;
    }

    public Long getMainHistory() {
        return mainHistory;
    }

    public void setMainHistory(Long mainHistory) {
        this.mainHistory = mainHistory;
    }

    public Boolean getPreviousDepartment() {
        return previousDepartment;
    }

    public void setPreviousDepartment(Boolean previousDepartment) {
        this.previousDepartment = previousDepartment;
    }

    public String getMaxFolder() {
        return maxFolder;
    }

    public void setMaxFolder(String maxFolder) {
        this.maxFolder = maxFolder;
    }

    public Double getPrivateFolder() {
        return privateFolder;
    }

    public void setPrivateFolder(Double privateFolder) {
        this.privateFolder = privateFolder;
    }
}
