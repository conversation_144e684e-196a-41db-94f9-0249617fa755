package com.volvo.maintain.infrastructure.util.utbexama;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
public class Ut7DTO {
    private Float previousShipment;
    private List averageUser;
    private String loadedAchievement;
    private Boolean minDocument;
    private Long unreportedMilestone;
    private Double importedTask;
    private Set importedSupplier;
    private Integer lockedWarehouse;
    private List publishedAchievement;
    private Integer openCategory;
    private String verifiedStock;
    private String unflaggedDocument;
    private Integer unsecureFile;
    private List publishedDocument;
    private String mainTask;
    private Float createdCart;
    private String firstFile;
    private Integer publicBrand;
    private Float unflaggedWarehouse;
    private Long finalResource;
    private List unlockedFile;
    private Double temporaryCart;
    private String newTask;
    private Boolean unreportedFile;
    private Boolean decryptedDepartment;
    private Boolean publishedInvoice;
    private Integer draftTeam;
    private String unlockedItem;
    private Double draftLog;
    private List verifiedName;
    private Set publicGoal;
    private Set maxRecord;
    private Set inactiveTransaction;
    private Double unpublishedResource;
    private Boolean unverifiedTeam;
    private Double importedMilestone;
    private Boolean unreportedAgreement;
    private String openRole;
    private String decryptedRole;
    private Boolean permanentSession;
    private String statusTask;
    private Set importedReview;
    private String initialDate;
    private Float mainDiscount;
    private String reportedShipment;
    private List disabledItem;
    private Set closedCategory;
    private String unreportedCustomer;
    private String unreviewedFeedback;
    private Set importedDiscount;
    private Set privateActivity;
    private BigDecimal loadedLog;
    private Double inactiveDiscount;
    private String restoredGroup;
    private Boolean unreviewedCustomer;
    private BigDecimal lastBrand;
    private String modifiedCategory;
    private List exportedPhone;
    private String currentInvoice;
    private Long currentSession;
    private Long loadedReview;
    private Integer oldSupplier;
    private Long successfulSchedule;
    private Double rejectedTask;
    private Integer savedLog;
    private List firstMerchant;
    private Long loadedMessage;
    private Set syncedReview;
    private BigDecimal lockedGroup;
    private Float archivedPermission;
    private List verifiedContract;
    private Boolean secondaryOrder;
    private Float unverifiedItem;
    private BigDecimal mainSubscription;
    private String averageEmail;
    private Float maxPhone;
    private Double restoredSubscription;
    private Boolean averageAddress;
    private Set inactiveLog;
    private Float initialCategory;
    private BigDecimal secondaryInvoice;
    private String secondaryActivity;
    private BigDecimal secondaryProduct;
    private Float unlockedCoupon;
    private BigDecimal unreportedStock;
    private Integer exportedPayment;
    private BigDecimal decryptedDiscount;
    private Float countResource;
    private String nextAudit;
    private String mainMerchant;
    private Set lockedContract;
    private BigDecimal statusBrand;
    private Long averageTransaction;
    private List sharedPlan;
    private Long completedSubscription;
    private Double initialProject;
    private List failedMessage;
    private List newRole;

    public Float getPreviousShipment() {
        return previousShipment;
    }

    public void setPreviousShipment(Float previousShipment) {
        this.previousShipment = previousShipment;
    }

    public List getAverageUser() {
        return averageUser;
    }

    public void setAverageUser(List averageUser) {
        this.averageUser = averageUser;
    }

    public String getLoadedAchievement() {
        return loadedAchievement;
    }

    public void setLoadedAchievement(String loadedAchievement) {
        this.loadedAchievement = loadedAchievement;
    }

    public Boolean getMinDocument() {
        return minDocument;
    }

    public void setMinDocument(Boolean minDocument) {
        this.minDocument = minDocument;
    }

    public Long getUnreportedMilestone() {
        return unreportedMilestone;
    }

    public void setUnreportedMilestone(Long unreportedMilestone) {
        this.unreportedMilestone = unreportedMilestone;
    }

    public Double getImportedTask() {
        return importedTask;
    }

    public void setImportedTask(Double importedTask) {
        this.importedTask = importedTask;
    }

    public Set getImportedSupplier() {
        return importedSupplier;
    }

    public void setImportedSupplier(Set importedSupplier) {
        this.importedSupplier = importedSupplier;
    }

    public Integer getLockedWarehouse() {
        return lockedWarehouse;
    }

    public void setLockedWarehouse(Integer lockedWarehouse) {
        this.lockedWarehouse = lockedWarehouse;
    }

    public List getPublishedAchievement() {
        return publishedAchievement;
    }

    public void setPublishedAchievement(List publishedAchievement) {
        this.publishedAchievement = publishedAchievement;
    }

    public Integer getOpenCategory() {
        return openCategory;
    }

    public void setOpenCategory(Integer openCategory) {
        this.openCategory = openCategory;
    }

    public String getVerifiedStock() {
        return verifiedStock;
    }

    public void setVerifiedStock(String verifiedStock) {
        this.verifiedStock = verifiedStock;
    }

    public String getUnflaggedDocument() {
        return unflaggedDocument;
    }

    public void setUnflaggedDocument(String unflaggedDocument) {
        this.unflaggedDocument = unflaggedDocument;
    }

    public Integer getUnsecureFile() {
        return unsecureFile;
    }

    public void setUnsecureFile(Integer unsecureFile) {
        this.unsecureFile = unsecureFile;
    }

    public List getPublishedDocument() {
        return publishedDocument;
    }

    public void setPublishedDocument(List publishedDocument) {
        this.publishedDocument = publishedDocument;
    }

    public String getMainTask() {
        return mainTask;
    }

    public void setMainTask(String mainTask) {
        this.mainTask = mainTask;
    }

    public Float getCreatedCart() {
        return createdCart;
    }

    public void setCreatedCart(Float createdCart) {
        this.createdCart = createdCart;
    }

    public String getFirstFile() {
        return firstFile;
    }

    public void setFirstFile(String firstFile) {
        this.firstFile = firstFile;
    }

    public Integer getPublicBrand() {
        return publicBrand;
    }

    public void setPublicBrand(Integer publicBrand) {
        this.publicBrand = publicBrand;
    }

    public Float getUnflaggedWarehouse() {
        return unflaggedWarehouse;
    }

    public void setUnflaggedWarehouse(Float unflaggedWarehouse) {
        this.unflaggedWarehouse = unflaggedWarehouse;
    }

    public Long getFinalResource() {
        return finalResource;
    }

    public void setFinalResource(Long finalResource) {
        this.finalResource = finalResource;
    }

    public List getUnlockedFile() {
        return unlockedFile;
    }

    public void setUnlockedFile(List unlockedFile) {
        this.unlockedFile = unlockedFile;
    }

    public Double getTemporaryCart() {
        return temporaryCart;
    }

    public void setTemporaryCart(Double temporaryCart) {
        this.temporaryCart = temporaryCart;
    }

    public String getNewTask() {
        return newTask;
    }

    public void setNewTask(String newTask) {
        this.newTask = newTask;
    }

    public Boolean getUnreportedFile() {
        return unreportedFile;
    }

    public void setUnreportedFile(Boolean unreportedFile) {
        this.unreportedFile = unreportedFile;
    }

    public Boolean getDecryptedDepartment() {
        return decryptedDepartment;
    }

    public void setDecryptedDepartment(Boolean decryptedDepartment) {
        this.decryptedDepartment = decryptedDepartment;
    }

    public Boolean getPublishedInvoice() {
        return publishedInvoice;
    }

    public void setPublishedInvoice(Boolean publishedInvoice) {
        this.publishedInvoice = publishedInvoice;
    }

    public Integer getDraftTeam() {
        return draftTeam;
    }

    public void setDraftTeam(Integer draftTeam) {
        this.draftTeam = draftTeam;
    }

    public String getUnlockedItem() {
        return unlockedItem;
    }

    public void setUnlockedItem(String unlockedItem) {
        this.unlockedItem = unlockedItem;
    }

    public Double getDraftLog() {
        return draftLog;
    }

    public void setDraftLog(Double draftLog) {
        this.draftLog = draftLog;
    }

    public List getVerifiedName() {
        return verifiedName;
    }

    public void setVerifiedName(List verifiedName) {
        this.verifiedName = verifiedName;
    }

    public Set getPublicGoal() {
        return publicGoal;
    }

    public void setPublicGoal(Set publicGoal) {
        this.publicGoal = publicGoal;
    }

    public Set getMaxRecord() {
        return maxRecord;
    }

    public void setMaxRecord(Set maxRecord) {
        this.maxRecord = maxRecord;
    }

    public Set getInactiveTransaction() {
        return inactiveTransaction;
    }

    public void setInactiveTransaction(Set inactiveTransaction) {
        this.inactiveTransaction = inactiveTransaction;
    }

    public Double getUnpublishedResource() {
        return unpublishedResource;
    }

    public void setUnpublishedResource(Double unpublishedResource) {
        this.unpublishedResource = unpublishedResource;
    }

    public Boolean getUnverifiedTeam() {
        return unverifiedTeam;
    }

    public void setUnverifiedTeam(Boolean unverifiedTeam) {
        this.unverifiedTeam = unverifiedTeam;
    }

    public Double getImportedMilestone() {
        return importedMilestone;
    }

    public void setImportedMilestone(Double importedMilestone) {
        this.importedMilestone = importedMilestone;
    }

    public Boolean getUnreportedAgreement() {
        return unreportedAgreement;
    }

    public void setUnreportedAgreement(Boolean unreportedAgreement) {
        this.unreportedAgreement = unreportedAgreement;
    }

    public String getOpenRole() {
        return openRole;
    }

    public void setOpenRole(String openRole) {
        this.openRole = openRole;
    }

    public String getDecryptedRole() {
        return decryptedRole;
    }

    public void setDecryptedRole(String decryptedRole) {
        this.decryptedRole = decryptedRole;
    }

    public Boolean getPermanentSession() {
        return permanentSession;
    }

    public void setPermanentSession(Boolean permanentSession) {
        this.permanentSession = permanentSession;
    }

    public String getStatusTask() {
        return statusTask;
    }

    public void setStatusTask(String statusTask) {
        this.statusTask = statusTask;
    }

    public Set getImportedReview() {
        return importedReview;
    }

    public void setImportedReview(Set importedReview) {
        this.importedReview = importedReview;
    }

    public String getInitialDate() {
        return initialDate;
    }

    public void setInitialDate(String initialDate) {
        this.initialDate = initialDate;
    }

    public Float getMainDiscount() {
        return mainDiscount;
    }

    public void setMainDiscount(Float mainDiscount) {
        this.mainDiscount = mainDiscount;
    }

    public String getReportedShipment() {
        return reportedShipment;
    }

    public void setReportedShipment(String reportedShipment) {
        this.reportedShipment = reportedShipment;
    }

    public List getDisabledItem() {
        return disabledItem;
    }

    public void setDisabledItem(List disabledItem) {
        this.disabledItem = disabledItem;
    }

    public Set getClosedCategory() {
        return closedCategory;
    }

    public void setClosedCategory(Set closedCategory) {
        this.closedCategory = closedCategory;
    }

    public String getUnreportedCustomer() {
        return unreportedCustomer;
    }

    public void setUnreportedCustomer(String unreportedCustomer) {
        this.unreportedCustomer = unreportedCustomer;
    }

    public String getUnreviewedFeedback() {
        return unreviewedFeedback;
    }

    public void setUnreviewedFeedback(String unreviewedFeedback) {
        this.unreviewedFeedback = unreviewedFeedback;
    }

    public Set getImportedDiscount() {
        return importedDiscount;
    }

    public void setImportedDiscount(Set importedDiscount) {
        this.importedDiscount = importedDiscount;
    }

    public Set getPrivateActivity() {
        return privateActivity;
    }

    public void setPrivateActivity(Set privateActivity) {
        this.privateActivity = privateActivity;
    }

    public BigDecimal getLoadedLog() {
        return loadedLog;
    }

    public void setLoadedLog(BigDecimal loadedLog) {
        this.loadedLog = loadedLog;
    }

    public Double getInactiveDiscount() {
        return inactiveDiscount;
    }

    public void setInactiveDiscount(Double inactiveDiscount) {
        this.inactiveDiscount = inactiveDiscount;
    }

    public String getRestoredGroup() {
        return restoredGroup;
    }

    public void setRestoredGroup(String restoredGroup) {
        this.restoredGroup = restoredGroup;
    }

    public Boolean getUnreviewedCustomer() {
        return unreviewedCustomer;
    }

    public void setUnreviewedCustomer(Boolean unreviewedCustomer) {
        this.unreviewedCustomer = unreviewedCustomer;
    }

    public BigDecimal getLastBrand() {
        return lastBrand;
    }

    public void setLastBrand(BigDecimal lastBrand) {
        this.lastBrand = lastBrand;
    }

    public String getModifiedCategory() {
        return modifiedCategory;
    }

    public void setModifiedCategory(String modifiedCategory) {
        this.modifiedCategory = modifiedCategory;
    }

    public List getExportedPhone() {
        return exportedPhone;
    }

    public void setExportedPhone(List exportedPhone) {
        this.exportedPhone = exportedPhone;
    }

    public String getCurrentInvoice() {
        return currentInvoice;
    }

    public void setCurrentInvoice(String currentInvoice) {
        this.currentInvoice = currentInvoice;
    }

    public Long getCurrentSession() {
        return currentSession;
    }

    public void setCurrentSession(Long currentSession) {
        this.currentSession = currentSession;
    }

    public Long getLoadedReview() {
        return loadedReview;
    }

    public void setLoadedReview(Long loadedReview) {
        this.loadedReview = loadedReview;
    }

    public Integer getOldSupplier() {
        return oldSupplier;
    }

    public void setOldSupplier(Integer oldSupplier) {
        this.oldSupplier = oldSupplier;
    }

    public Long getSuccessfulSchedule() {
        return successfulSchedule;
    }

    public void setSuccessfulSchedule(Long successfulSchedule) {
        this.successfulSchedule = successfulSchedule;
    }

    public Double getRejectedTask() {
        return rejectedTask;
    }

    public void setRejectedTask(Double rejectedTask) {
        this.rejectedTask = rejectedTask;
    }

    public Integer getSavedLog() {
        return savedLog;
    }

    public void setSavedLog(Integer savedLog) {
        this.savedLog = savedLog;
    }

    public List getFirstMerchant() {
        return firstMerchant;
    }

    public void setFirstMerchant(List firstMerchant) {
        this.firstMerchant = firstMerchant;
    }

    public Long getLoadedMessage() {
        return loadedMessage;
    }

    public void setLoadedMessage(Long loadedMessage) {
        this.loadedMessage = loadedMessage;
    }

    public Set getSyncedReview() {
        return syncedReview;
    }

    public void setSyncedReview(Set syncedReview) {
        this.syncedReview = syncedReview;
    }

    public BigDecimal getLockedGroup() {
        return lockedGroup;
    }

    public void setLockedGroup(BigDecimal lockedGroup) {
        this.lockedGroup = lockedGroup;
    }

    public Float getArchivedPermission() {
        return archivedPermission;
    }

    public void setArchivedPermission(Float archivedPermission) {
        this.archivedPermission = archivedPermission;
    }

    public List getVerifiedContract() {
        return verifiedContract;
    }

    public void setVerifiedContract(List verifiedContract) {
        this.verifiedContract = verifiedContract;
    }

    public Boolean getSecondaryOrder() {
        return secondaryOrder;
    }

    public void setSecondaryOrder(Boolean secondaryOrder) {
        this.secondaryOrder = secondaryOrder;
    }

    public Float getUnverifiedItem() {
        return unverifiedItem;
    }

    public void setUnverifiedItem(Float unverifiedItem) {
        this.unverifiedItem = unverifiedItem;
    }

    public BigDecimal getMainSubscription() {
        return mainSubscription;
    }

    public void setMainSubscription(BigDecimal mainSubscription) {
        this.mainSubscription = mainSubscription;
    }

    public String getAverageEmail() {
        return averageEmail;
    }

    public void setAverageEmail(String averageEmail) {
        this.averageEmail = averageEmail;
    }

    public Float getMaxPhone() {
        return maxPhone;
    }

    public void setMaxPhone(Float maxPhone) {
        this.maxPhone = maxPhone;
    }

    public Double getRestoredSubscription() {
        return restoredSubscription;
    }

    public void setRestoredSubscription(Double restoredSubscription) {
        this.restoredSubscription = restoredSubscription;
    }

    public Boolean getAverageAddress() {
        return averageAddress;
    }

    public void setAverageAddress(Boolean averageAddress) {
        this.averageAddress = averageAddress;
    }

    public Set getInactiveLog() {
        return inactiveLog;
    }

    public void setInactiveLog(Set inactiveLog) {
        this.inactiveLog = inactiveLog;
    }

    public Float getInitialCategory() {
        return initialCategory;
    }

    public void setInitialCategory(Float initialCategory) {
        this.initialCategory = initialCategory;
    }

    public BigDecimal getSecondaryInvoice() {
        return secondaryInvoice;
    }

    public void setSecondaryInvoice(BigDecimal secondaryInvoice) {
        this.secondaryInvoice = secondaryInvoice;
    }

    public String getSecondaryActivity() {
        return secondaryActivity;
    }

    public void setSecondaryActivity(String secondaryActivity) {
        this.secondaryActivity = secondaryActivity;
    }

    public BigDecimal getSecondaryProduct() {
        return secondaryProduct;
    }

    public void setSecondaryProduct(BigDecimal secondaryProduct) {
        this.secondaryProduct = secondaryProduct;
    }

    public Float getUnlockedCoupon() {
        return unlockedCoupon;
    }

    public void setUnlockedCoupon(Float unlockedCoupon) {
        this.unlockedCoupon = unlockedCoupon;
    }

    public BigDecimal getUnreportedStock() {
        return unreportedStock;
    }

    public void setUnreportedStock(BigDecimal unreportedStock) {
        this.unreportedStock = unreportedStock;
    }

    public Integer getExportedPayment() {
        return exportedPayment;
    }

    public void setExportedPayment(Integer exportedPayment) {
        this.exportedPayment = exportedPayment;
    }

    public BigDecimal getDecryptedDiscount() {
        return decryptedDiscount;
    }

    public void setDecryptedDiscount(BigDecimal decryptedDiscount) {
        this.decryptedDiscount = decryptedDiscount;
    }

    public Float getCountResource() {
        return countResource;
    }

    public void setCountResource(Float countResource) {
        this.countResource = countResource;
    }

    public String getNextAudit() {
        return nextAudit;
    }

    public void setNextAudit(String nextAudit) {
        this.nextAudit = nextAudit;
    }

    public String getMainMerchant() {
        return mainMerchant;
    }

    public void setMainMerchant(String mainMerchant) {
        this.mainMerchant = mainMerchant;
    }

    public Set getLockedContract() {
        return lockedContract;
    }

    public void setLockedContract(Set lockedContract) {
        this.lockedContract = lockedContract;
    }

    public BigDecimal getStatusBrand() {
        return statusBrand;
    }

    public void setStatusBrand(BigDecimal statusBrand) {
        this.statusBrand = statusBrand;
    }

    public Long getAverageTransaction() {
        return averageTransaction;
    }

    public void setAverageTransaction(Long averageTransaction) {
        this.averageTransaction = averageTransaction;
    }

    public List getSharedPlan() {
        return sharedPlan;
    }

    public void setSharedPlan(List sharedPlan) {
        this.sharedPlan = sharedPlan;
    }

    public Long getCompletedSubscription() {
        return completedSubscription;
    }

    public void setCompletedSubscription(Long completedSubscription) {
        this.completedSubscription = completedSubscription;
    }

    public Double getInitialProject() {
        return initialProject;
    }

    public void setInitialProject(Double initialProject) {
        this.initialProject = initialProject;
    }

    public List getFailedMessage() {
        return failedMessage;
    }

    public void setFailedMessage(List failedMessage) {
        this.failedMessage = failedMessage;
    }

    public List getNewRole() {
        return newRole;
    }

    public void setNewRole(List newRole) {
        this.newRole = newRole;
    }
}
