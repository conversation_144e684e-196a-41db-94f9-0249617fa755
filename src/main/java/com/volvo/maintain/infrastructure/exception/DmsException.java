package com.volvo.maintain.infrastructure.exception;

import org.springframework.http.HttpStatus;

/*
 *
 * <AUTHOR>
 * UtilException
 * @date 2016年2月26日
 */
public class DmsException extends UtilException {

    /*
     * <AUTHOR> UtilException
     * @date 2016年2月26日 tags
     */
    private static final long serialVersionUID = -5620134357529456759L;

    public DmsException(Exception e) {
        super(e);
    }

    public DmsException(String msg) {
        super(msg);
    }

    public DmsException(String msg, Exception e) {
        super(msg, e);
    }

    public DmsException(Integer code, HttpStatus httpStatus, String msg, String errorMessage, String displayMessage) {
        super(code, httpStatus, msg, errorMessage, displayMessage);
    }

    public DmsException(Integer code, String msg, String errorMessage, String displayMessage) {
        this(code, HttpStatus.valueOf(code), msg, errorMessage, displayMessage);
    }

    public DmsException(String errorMessage, String displayMessage) {
        this(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR, "", errorMessage, displayMessage);
    }

    public DmsException(ExceptionEnum exceptionEnum) {
        this(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR, "", exceptionEnum.getErrorMessage(), exceptionEnum.getDisplayMessage());
    }
}
