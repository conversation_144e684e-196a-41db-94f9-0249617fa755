package com.volvo.maintain.infrastructure.exception;

import lombok.Data;
import org.springframework.http.HttpStatus;

/*
 *
 * <AUTHOR>
 * UtilException
 * @date 2016年2月26日
 */
@Data
public class UtilException extends RuntimeException {
	
    private final HttpStatus httpStatus;    
    private final Integer resultCode;
    private final Integer code;
    private final String msg;
    private final String errorMessage;
    private final String errMsg;
    private final String displayMessage;

    /*
     * <AUTHOR> UtilException
     * @date 2016年2月26日 tags
     */
    private static final long serialVersionUID = -5620134357529456759L;

    public UtilException(Exception e) {
    	super(e);
        this.code = 500;
        this.resultCode = 500;
        this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        this.msg = "";
        this.errorMessage = "";
        this.errMsg = "";
        this.displayMessage = "";
    }

    public UtilException(String msg) {
    	super(msg);
        this.code = 500;
        this.resultCode = 500;
        this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        this.msg = msg;
        this.errorMessage = "";
        this.errMsg = "";
        this.displayMessage = "";
    }

    public UtilException(String msg, Exception e) {
    	super(msg, e);
        this.code = 500;
        this.resultCode = 500;
        this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        this.msg = msg;
        this.errorMessage = "";
        this.errMsg = "";
        this.displayMessage = "";
    }

    public UtilException(Integer code, HttpStatus httpStatus, String msg, String errorMessage, String displayMessage) {
        super(errorMessage);
        this.code = code;
        this.resultCode = code;
        this.httpStatus = httpStatus;
        this.msg = msg;
        this.errorMessage = errorMessage;
        this.errMsg = errorMessage;
        this.displayMessage = displayMessage;
    }
    
    public UtilException(Integer code, String msg, String errorMessage, String displayMessage) {
        this(code, HttpStatus.valueOf(code), msg, errorMessage, displayMessage);
    }
    
    public UtilException(String errorMessage, String displayMessage) {
        this(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR, "", errorMessage, displayMessage);
    }
    
    public UtilException(ExceptionEnum exceptionEnum) {
        this(HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR, "", exceptionEnum.getErrorMessage(), exceptionEnum.getDisplayMessage());
    }
}
