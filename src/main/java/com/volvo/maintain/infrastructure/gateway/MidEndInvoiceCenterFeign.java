package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.interfaces.vo.InvoiceOwnerVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "mid-end-invoice-center", url = "${baseUrl.midEndInvoiceCenter}")
public interface MidEndInvoiceCenterFeign {

    @GetMapping(value = "/invoice/jdcfphz/invoice/owner/{vin}", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseDto<InvoiceOwnerVO> selectInvoiceByVin(@PathVariable("vin") String vin);
}
