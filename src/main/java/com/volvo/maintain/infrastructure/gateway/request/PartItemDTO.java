package com.volvo.maintain.infrastructure.gateway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 配件移库查询DTO
 */
@ApiModel(value = "PartItemDTO", description = "配件移库查询DTO")
@Data
public class PartItemDTO implements Serializable {

    private static final long serialVersionUID = 780008433065998852L;
    @ApiModelProperty(value = "仓库代码", name = "storageCode", required = false)
    private String storageCode;
    @ApiModelProperty(value = "品牌", name = "brand", required = false)
    private String brand;
    @ApiModelProperty(value = "配件代码", name = "partNo", required = false)
    private String partNo;
    @ApiModelProperty(value = "拼音", name = "spellCode", required = false)
    private String spellCode;
    @ApiModelProperty(value = "配件名称", name = "partName", required = false)
    private String partName;
    @ApiModelProperty(value = "类别", name = "partGroupCode", required = false)
    private Integer partGroupCode;
    @ApiModelProperty(value = "库位代码", name = "storagePositionCode", required = false)
    private String storagePositionCode;
    @ApiModelProperty(value = "配件车型组", name = "partModelGroupCodeSet", required = false)
    private String partModelGroupCodeSet;
    @ApiModelProperty(value = "库存是否大于0", name = "stockZero", required = false)
    private String stockZero;
    @ApiModelProperty(value = "售价是否大于0", name = "priceZero", required = false)
    private String priceZero;
    @ApiModelProperty(value = "备注", name = "description", required = false)
    private String description;
    
    @ApiModelProperty(value = "是否取值厂端建议销售价", name = "itemCode", required = false)
    private Integer itemCode;

    
    //新增零件属性类别
    @ApiModelProperty(value = "零件属性", name = "oilElectricityTpye", required = false)
    private Integer oilElectricityTpye;
    
    @ApiModelProperty(value = "零件类别", name = "partSubdivision", required = false)
    private Integer partSubdivision;

    @ApiModelProperty(value = "经销商代码", name = "ownerCode", required = false)
    private String ownerCode;
}
