package com.volvo.maintain.infrastructure.gateway;


import com.volvo.maintain.application.maintainlead.dto.VehicleOwnerDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(value = "mid-end-customer-center", url = "${baseUrl.midEndCustomerCenter}")
public interface MidEndCustomerCenterClient {

    @PostMapping(path = "/customer/customer-info/selectCustomerInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<VehicleOwnerDto>> selectCustomerInfo(@RequestBody VehicleOwnerDto vehicleOwnerDto);

    @PutMapping(path = "/customer/customer-info/saveList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<VehicleOwnerDto>> saveList(@RequestBody VehicleOwnerDto vehicleOwnerDto);

}