package com.volvo.maintain.infrastructure.gateway;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.CompanyDetailByCodeDto;
import com.volvo.maintain.application.maintainlead.dto.GroupDetailsConfigDTO;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsConfigDto;
import com.volvo.maintain.application.maintainlead.dto.clues.InsuranceLeadsConfigLogDto;
import com.volvo.maintain.application.maintainlead.dto.common.CommonConfigDTO;
import com.volvo.maintain.application.maintainlead.dto.white.WhiteListDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.clues.InsuranceLeadsConfigVo;
import com.volvo.maintain.interfaces.vo.white.VehicleHealthCheckWhiteListVo;

import io.swagger.annotations.ApiOperation;

/**
 * 权限服务
 */
@FeignClient(name = "domain-maintain-auth")
public interface DomainMaintainAuthFeign {

    /**
     * 查询白名单列表
     */
    @GetMapping(value = "/whitelistManagement/selectWhitelist")
    DmsResponse<Page<VehicleHealthCheckWhiteListVo>> selectWhitelist(@RequestParam("modType") String modType, @RequestParam("itemCode") String itemCode, @RequestParam("isDeleted") String isDeleted, @RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize);

    /**
     * 查询白名单明细
     */
    @GetMapping(value = "/whitelistManagement/selectWhitelistDetail")
    DmsResponse<List<VehicleHealthCheckWhiteListVo>> selectWhitelistDetail(@RequestParam("itemCode") String itemCode, @RequestParam("rosterType") String rosterType);

    @GetMapping(path = "/whitelistManagement/existBlackList/{modType}", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<VehicleHealthCheckWhiteListVo>> queryWhiteList(@RequestParam List<String> dealerList, @PathVariable Integer modType);

    @PostMapping(path = "/whitelistManagement/existBlackListNew/{modType}", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<VehicleHealthCheckWhiteListVo>> existBlackListNew(@RequestBody List<String> dealerList, @PathVariable Integer modType);

    /**
     * 新增白名单明细
     */
    @PostMapping(path = "/whitelistManagement/insertWhitelist",  produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Integer> insertWhitelist(@RequestBody WhiteListDto whiteListDto);

    /**
     * 启用/停用白名单
     */
    @GetMapping(value = "/whitelistManagement/toggleWhitelistActivation")
    DmsResponse<Integer> toggleWhitelistActivation(@RequestParam("itemCode") String whiteListDto, @RequestParam("isDeleted") String isDeleted, @RequestParam("createBy") String createBy, @RequestParam("rosterType") String rosterType);

    /**
     * 启用/停用白名单
     */
    @GetMapping(value = "/whitelistManagement/insertWhitelistImport")
    DmsResponse<Integer> insertWhitelistImport(WhiteListDto whiteListDto);
    @DeleteMapping(value = "/whitelistManagement/blackList",  produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> blackList();

    /**
     * 经销商白名单
     */
    @GetMapping(value = "/whitelist/checkWhitelist/interf")
    DmsResponse<Object> checkWhitelist(@RequestParam String ownerCode, @RequestParam Integer modType, @RequestParam Integer rosterType, @RequestParam String vin);

    /**
     * 经销商白名单
     */
    @GetMapping(value = "/whitelist/checkWhitelist")
    DmsResponse<Object> checkWhitelist(@RequestParam String ownerCode, @RequestParam Integer modType, @RequestParam Integer rosterType, @RequestParam String vin, @RequestParam String groupCode);

    @ApiOperation(value = " 查询白名单是否开启", notes = "查询白名单是否开启")
    @GetMapping(value = "/whitelist/checkWhitelist/enable/interf")
    DmsResponse<Boolean> checkIfWhitelistEnabled(@RequestParam(value = "modType") Integer modType);

    @ApiOperation(value = "查询查询白名单经销商", notes = "查询查询白名单经销商")
    @GetMapping(value = "/whitelist/checkWhitelist/getWhitelistedDealers/interf")
    DmsResponse<List<String>> getWhitelistedDealers(@RequestParam(value = "modType") Integer modType, @RequestParam(value = "rosterType") Integer rosterType);

    /**
     * 初始化经销商配置（单店保护，）
     * @param companyDetailByCode 经销商
     * @return 返回字符串 成功失败 文字提示
     */
    @PostMapping(value = "/renewalOfInsurance/initConfig/interf", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<String> init(@RequestBody List<CompanyDetailByCodeDto> companyDetailByCode, @RequestParam("flag") Boolean flag);

    /**
     *  根据参数查询配置信息
     * @param afterBigAreaId 大区Id
     * @param afterSmallAreaId 小区Id
     * @param ownerCode 经销商
     * @return 根据参数查询配置信息
     */
    @GetMapping(value = "/renewalOfInsurance/config/list", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Page<InsuranceLeadsConfigDto>> queryCompanyConfig(@RequestParam(value = "afterBigAreaId", required = false) String afterBigAreaId,@RequestParam(value = "afterSmallAreaId", required = false) String afterSmallAreaId,@RequestParam(value = "ownerCode", required = false) String ownerCode, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize);

    /**
     * 根据对应参数进行修改数据
     * @param InsuranceLeadsConfigVo 经销商信息
     * @return 返回修改的数据集合
     */
    @PostMapping(value = "/renewalOfInsurance/config/update", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<InsuranceLeadsConfigDto>> modifyOwnerCodeConfig(@RequestBody InsuranceLeadsConfigVo InsuranceLeadsConfigVo);
    /**
     *  查询经销商规则提前出单日期修改记录
     * @param ownerCode 经销商
     * @return 根据参数查询配置信息
     */
    @GetMapping(value = "/renewalOfInsurance/config/queryInsuranceLeadsModifyLog", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Page<InsuranceLeadsConfigLogDto>> queryInsuranceLeadsModifyLog(@RequestParam("ownerCode") String ownerCode, @RequestParam("flag") Integer flag, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize);

    @GetMapping(value = "/renewalOfInsurance/config/download/data", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<InsuranceLeadsConfigDto>> queryInsuranceLeadsExport(@RequestParam(value = "ownerCode", required = false) String ownerCode,
                                                                         @RequestParam(value = "currentPage", required = false) Integer currentPage,
                                                                         @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                         @RequestParam(value = "afterBigareaIds", required = false) String afterBigareaIds,
                                                                         @RequestParam(value = "afterSmallareaIds", required = false) String afterSmallareaIds);
    @GetMapping(value = "/common/config/interf")
    DmsResponse<CommonConfigDTO> getConfigByKey(@RequestParam(value = "configKey") String configKey, @RequestParam(value = "groupType") String groupType);



    @ApiOperation(value = "查询分组配置信息", notes = "查询分组配置信息", httpMethod = "POST")
    @PostMapping(path = "/groupConfig/v1/queryGroupDetailsConfigByBusinessType")
    DmsResponse<List<GroupDetailsConfigDTO>> queryGroupDetailsConfigByBusinessType(@RequestBody List<String> businessTypeList);


}
