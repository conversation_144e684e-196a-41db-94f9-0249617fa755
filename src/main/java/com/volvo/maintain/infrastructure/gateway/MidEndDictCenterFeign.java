package com.volvo.maintain.infrastructure.gateway;

import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.CompanyDetailByCodeDto;
import com.volvo.maintain.application.maintainlead.dto.IsExistByCodeDto;
import com.volvo.maintain.application.maintainlead.dto.RegionAllDto;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.infrastructure.gateway.request.CompanySelectDTO;
import com.volvo.maintain.infrastructure.gateway.response.CompanyDetailDTO;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.volvo.maintain.application.maintainlead.dto.PlateSortDictDto;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import java.util.List;

@FeignClient(name = "mid-end-dict-center", url = "${baseUrl.midEndDictCenter}")
public interface MidEndDictCenterFeign {

    @GetMapping(path = "/dictCode/getCodeDataType/{type}", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<PlateSortDictDto>> queryCodeDataType(@PathVariable("type") Integer firstBlockCode);

    /**
     * 根据经销商code查询经销商信息
     */
    @PostMapping(path = "/org/company/selectCompanyInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    RestResultResponse<List<CompanyDetailDTO>> selectByCompanyCode(@RequestBody CompanySelectDTO dto);
    /**
     * 获取全量省市区信息
     */
    @GetMapping(path = "/region/getAll", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<RegionAllDto>> queryRegionAll();


}
