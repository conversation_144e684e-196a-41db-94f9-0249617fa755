package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.coupon.QueryCouponDetailInfo1Dto;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.coupon.QueryCouponDto;
import com.volvo.maintain.interfaces.vo.coupon.CouponDetailInfoVO;
import com.volvo.maintain.interfaces.vo.coupon.CouponDetailVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "mid-end-coupon-center",url = "${baseUrl.midEndCouponCenter}")
public interface MidEndCouponCenterFeign {

    /**
     * 根据oneId或VIN查询绑定卡券
     *
     */
    @PostMapping(path = "/coupon/tt-coupon-detail/allEx", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseDto<List<CouponDetailVO>> allEx(@RequestBody QueryCouponDetailInfo1Dto queryCouponDetailInfo1DTO);

    /**
     * 根据oneId或VIN查询绑定卡券
     *
     */
    @PostMapping(path = "/coupon/tt-coupon-detail/allCouponAfter", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseDto<List<CouponDetailVO>> allCouponAfter(@RequestBody QueryCouponDto queryCouponDto);


}
