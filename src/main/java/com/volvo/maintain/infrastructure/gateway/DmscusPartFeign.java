package com.volvo.maintain.infrastructure.gateway;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.volvo.maintain.infrastructure.gateway.request.PartPurchaseOrderDTO;
import com.volvo.maintain.infrastructure.gateway.request.PartPurchaseOrderPushDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;

import io.swagger.annotations.ApiOperation;


@FeignClient(name = "dmscus-part")
public interface DmscusPartFeign {
	
    /**
     * 保存
     *
     * @param partPurchaseOrderDTO 需要保存的DTO
     * @return int
     * <AUTHOR>
     * @since 2020-03-26
     */
    @PostMapping("/partPurchaseOrder/save")
    @ApiOperation(value = "采购订单保存")
    DmsResponse<Long> save(@RequestBody PartPurchaseOrderDTO partPurchaseOrderDTO);
    
    /**
     * 订单上传
     *
     * @param orderPushDTO orderPushDTO
     * @return
     */
    @PostMapping("/partPurchaseOrder/purchaseOrderUpload")
    @ApiOperation(value = "订单上传")
    DmsResponse<Integer> purchaseOrderUpload(@RequestBody PartPurchaseOrderPushDTO orderPushDTO);
    
    /**
     * 获取经销商类型
     *
     * @return
     */
    @GetMapping("/partPurchaseOrder/getDealerType")
    @ApiOperation(value = "获取经销商类型")
    String getDealerType();
    
}



