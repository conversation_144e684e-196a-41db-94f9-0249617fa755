package com.volvo.maintain.infrastructure.gateway.request;



import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("配件相关信息查询Vo")
@Data
public class PartStockInfoParamsVo {
	
	@ApiModelProperty(value = "经销商代码",name = "ownerCode")
	private String ownerCode;
	
	@ApiModelProperty(value = "零件号列表", name = "partNo")
	private List<String> partNo;

}
