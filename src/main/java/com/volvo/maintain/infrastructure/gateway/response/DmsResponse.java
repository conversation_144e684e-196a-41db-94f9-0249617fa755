package com.volvo.maintain.infrastructure.gateway.response;

import com.volvo.maintain.infrastructure.constants.CommonConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * dms 数据返回
 */
@Data
public class DmsResponse<T> implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 返回代码
	 */
	private String resultCode;
	
	/**
	 * 返回代码
	 */
	private Integer code;

	/**
	 * 返回代码
	 */
	private String returnCode;

	/**
	 * 返回描述
	 */
	private String errMsg;

	/**
	 * 返回描述
	 */
	private String returnMessage;

	private  String result;

	/**
	 * 返回数据
	 */
	private T data;

	public boolean isSuccess() {
		return Objects.equals(this.getResultCode(), CommonConstant.SUCCESS_NEWBIE_CODE)
				|| Objects.equals(this.getResultCode(), CommonConstant.SUCCESS_CODE)
				|| Objects.equals(this.getReturnCode(), CommonConstant.SUCCESS_NEWBIE_CODE)
				|| Objects.equals(this.getReturnCode(), CommonConstant.SUCCESS_CODE)
				|| Objects.equals(this.getCode(), 200)
				|| Objects.equals(this.getCode(), 0);
	}

	public boolean isFail() {
		return !isSuccess();
	}
}
