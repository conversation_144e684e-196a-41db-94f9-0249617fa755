package com.volvo.maintain.infrastructure.gateway.request;

import com.volvo.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 
 * 虚拟手机号查询
 * 
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "虚拟手机号查询", description = "虚拟手机号查询")
public class VirtualPhoneSelectDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 1;
    /**
     * 响应code
     */
    private Integer code;
    /**
     * 响应描述
     */
    private String message;

    /**
     * 响应数据
     */
    private VirtualPhoneInfo data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VirtualPhoneInfo implements Serializable {
        private static final long serialVersionUID = 1454523534L;
        /**
         * 虚拟手机号
         */
        private String virtualPhone;
    }
}
