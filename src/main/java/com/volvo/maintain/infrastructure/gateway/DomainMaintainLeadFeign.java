package com.volvo.maintain.infrastructure.gateway;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.AccidentClueCrmInfoMqDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.AccidentCluesExportDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.AccidentCluesExportQueryDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.AccidentCluesSaNumberDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadOperationResultDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.LeadsLogRecordDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.OwnerRuleDto;
import com.volvo.maintain.application.maintainlead.dto.clues.*;
import com.volvo.maintain.application.maintainlead.dto.faultlight.FaultLightClueResponseDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.*;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceAssigPersonImportDTO;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceRecordImportDTO;
import com.volvo.maintain.application.maintainlead.dto.insurance.InviteInsuranceReqDto;
import com.volvo.maintain.application.maintainlead.dto.inviteClue.InvitationScripDto;
import com.volvo.maintain.application.maintainlead.dto.maintainLead.MaintainLeadParamsDto;
import com.volvo.maintain.application.maintainlead.vo.AccidentCluesVo;
import com.volvo.maintain.application.maintainlead.vo.CallDetailVo;
import com.volvo.maintain.application.maintainlead.vo.FullLeadVo;
import com.volvo.maintain.application.maintainlead.vo.FullLeadsTagVo;
import com.volvo.maintain.application.maintainlead.vo.InviteVehicleRecordVo;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.PushMessageRecordVo;
import com.volvo.maintain.interfaces.vo.TagInfoVo;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 功能描述：养修线索Feign接口
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@FeignClient(name = "domain-maintain-leads")
public interface DomainMaintainLeadFeign {
    /**
     * 功能描述：查询邀约线索列表
     *
     * @param paramDto 养修线索dto对象
     * @return IPage<MaintainLeadDto> 养修线索列表
     */
    @PostMapping("/invitationFollow/list")
    RestResultResponse<Page<MaintainLeadParamsDto>> getInviteVehicleRecord(@RequestBody InvitationFollowParamsDto paramDto);

    /**
     * 功能描述：保存跟进记录
     *
     * @param param 查询邀约跟进记录查询dto
     * @return int  返回保存结果
     */
    @PostMapping("/invitationFollow/saveInviteVehicleRecord")
    RestResultResponse<Boolean> saveInviteVehicleRecord(@RequestBody InviteVehicleRecordDetailDto param);

    @PostMapping(value = "/vipCustom/increasVip", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    RestResultResponse increasVip(@RequestBody List<VipImportDto> vipImportDtos);

    //根据vin查询是否VIP客户
    @GetMapping(path = "/vipCustom/queryVipByVin", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Boolean> queryVipByVin(@RequestParam String bizNo);

    //查询待发送记录
    @GetMapping(path = "/vipCustom/selectPushMessageRecord", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<PushMessageRecordVo> selectPushMessageRecord(@RequestParam int sinceType,@RequestParam String vin, @RequestParam String dealerCode);

    //查询待发送记录
    @GetMapping(path = "/vipCustom/queryPushMessageRecord", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<PushMessageRecordVo> queryPushMessageRecord(@RequestParam String vin, @RequestParam String dealerCode);

    //根据车架号获取vip
    @GetMapping(path = "/vipCustom/batchQueryVipByVin", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<VipVinDto>> batchQueryVipByVin(@RequestParam("bizNo") List<String> bizNo);


    /**
     * 批量vin或者车牌查询vip群组，更通用，适用更多场景
     */
    @PostMapping(path = "/vipCustom/batchQueryVipGroupByVinOrLicense", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<VipVinDto>> batchQueryVipGroupByVinOrLicense(@RequestBody VipGroupBatchQueryReqDTO batchQueryReqDTO);

    //根据车架号查询tag标签
    @PostMapping(path = "/tagLog/queryTagLog", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<TagLogDto>> queryTagLog(@RequestBody TagLogParamsDto tagLogParamsDto);

    //根据车架号获取邀约跟进
    @PostMapping(path = "/invitationFollow/queryInviteTypeByVins", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<InviteTypeVinDto>> queryInviteTypeByVins(@RequestBody InviteTypeParamsDto inviteTypeParamsDto);

    //根据车架号获取邀约跟进
    @PostMapping(path = "/invitationFollow/selectInviteTypeByVins", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<InviteTypeVinDto>> selectInviteTypeByVins(@RequestBody InviteTypeParamsDto inviteTypeParamsDto);

    //插入标签
    @PostMapping(path = "/tagLog/insertTagLogs", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse insertTagLogs(@RequestBody List<TagLogDto> tagLogDtos);

    //保存或更新发送记录
    @PostMapping(path = "/vipCustom/insertPushMessageRecord", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Integer> insertPushMessageRecord(@RequestBody PushMessageRecordVo pushMessageRecordVo);

    //保存或更新发送记录
    @PostMapping(path = "/vipCustom/updatePushMessageRecord", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse updatePushMessageRecord(@RequestBody PushMessageRecordVo pushMessageRecordVo);

    @PostMapping(path = "/vipCustom/selectPushMessageRecordList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<PushMessageRecordVo>> selectPushMessageRecordList();

    /**
     * 功能描述：查询邀约线索列表
     *
     * @param queryParams 全量线索查询dto对象
     * @return Page<FullLeadVo> 全量线索查询结果
     */
    @PostMapping("/fullLeads/list")
    @ApiOperation(value = "查询邀约线索列表")
    RestResultResponse<Page<FullLeadVo>> queryFullLeadsList(@RequestBody FullLeadQueryDto queryParams);
    /**
     * 功能描述：查询标签线索列表
     *
     * @param tagInfoDTO 标签信息dto对象
     * @return Page<TagInfoVO> 标签信息列表
     */
    @PostMapping(path = "/tag/queryTagInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Page<TagInfoVo>> queryTagInfo(@RequestBody TagInfoDto tagInfoDTO);

    /**
     * 功能描述：查询标签详情
     *
     * @param id 标签id
     * @return TagInfoVO 标签信息
     */
    @GetMapping(path = "/tag/queryTagInfoById", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<TagInfoVo> queryTagInfoById(@RequestParam("id") Long id);

    /**
     * 功能描述：更新标签信息
     *
     * @param changeTagInfoDTO 标签入参
     */
    @PostMapping(path = "/tag/updateTagInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Integer> updateTagInfo(@RequestBody ChangeTagInfoDto changeTagInfoDTO);
    /**
     * 功能描述：获取任务池是否存在对应任务
     *
    */
    @GetMapping(path = "/tag/queryExistTask", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<CdpTagTaskDto> queryExistTask();
    /**
     * 功能描述：插入当天任务到任务池
     *
     */
    @PostMapping(path = "/tag/insertCdpTagTask", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Integer> insertCdpTagTask(@RequestBody CdpTagTaskDto cdpTagTaskDto);
    /**
     * 功能描述：删除对应标签信息
     *
     * @return
     */
    @PostMapping(path = "/tag/delTagInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<TagInfoVo> delTagInfo(@RequestParam("tagId") String delTagId);
    /**
     * 功能描述：根据tagId获取标签信息
     *
     */
    @GetMapping(path = "/tag/queryCompleteData", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<TagInfoVo>> queryCompleteData();
    /**
     * 功能描述：新增标签信息
     *
     */
    @PostMapping(path = "/tag/insertTagInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> insertTagInfo(@RequestBody List<TagListDto> insertTagInfo);

    /**
     * 功能描述：修改标签信息
     *
     */
    @PostMapping(path = "/tag/updateTagInfoByCdp", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> updateTagInfoByCdp(@RequestBody List<TagListDto> updateTagInfo);
    /**
     * 功能描述：新增标签日志信息
     *
     */
    @PostMapping(path = "/tag/insertTagMetadataLogDto", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    void insertTagMetadataLogDto(@RequestBody List<TagMetadataLogDto> tagMetadataLogDto);

    /**
     * 功能描述：修改任务状态
     *
     */
    @PostMapping(path = "/tag/updateCdpTagTask", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    void updateCdpTagTask(@RequestBody CdpTagTaskDto cdpTagTaskDto);
    /**
     * 功能描述：根据一级板块查询标签
     *
     * @return TagInfoVO 标签信息
     */
    @GetMapping(path = "/tag/queryCompleteTagInfoByFirstCode", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<TagInfoVo>> queryCompleteTagInfoByFirstCode();


    /**
     * 全量线索tag
     *
     * @param id         线索id
     * @param inviteType 线索类型
     * @param vin        vin
     * @param dealerCode 经销商代码
     * @return FullLeadsTagVo
     */
    @GetMapping("/fullLeads/tagList")
    @ApiOperation(value = "查询全量线索tag列表")
    RestResultResponse<FullLeadsTagVo> queryTagList(@RequestParam("id") Long id, @RequestParam("inviteType") Integer inviteType,
                                                    @RequestParam("vin") String vin, @RequestParam("dealerCode") String dealerCode);

    /**
     * 功能描述：全量线索跟进
     *
     * @param followList 全量线索dto对象
     * @return IPage<FullLeadVo> 全量线索列表
     */
    @PostMapping("/fullLeads/saveFollowRecords")
    @ApiOperation(value = "全量线索跟进")
    RestResultResponse<List<StatusChangePushDto>> saveFollowRecords(@RequestBody List<FullLeadsFollowDto> followList);

    @PostMapping("/fullLeads/checkFollowAI")
    @ApiOperation(value = "全量线索通话校验")
    RestResultResponse<List<Integer>> checkFollowAI(List<FullLeadsFollowDto> followList);

    /**
     * 邀约通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    @GetMapping("/fullLeads/inviteCallList")
    @ApiOperation(value = "邀约类型线索通话记录查询")
    RestResultResponse<List<CallDetailVo>> inviteCallList(@RequestParam("detailId") Long detailId);

    /**
     * 续保通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    @GetMapping("/fullLeads/insuranceCallList")
    @ApiOperation(value = "续保类型线索通话记录查询")
    RestResultResponse<List<CallDetailVo>> insuranceCallList(@RequestParam("detailId") Long detailId);

    /**
     * 故障灯通话记录查询
     *
     * @param detailId 跟进记录id
     * @return 通话记录
     */
    @GetMapping("/fullLeads/faultLightCallList")
    @ApiOperation(value = "故障灯线索通话记录查询")
    RestResultResponse<List<CallDetailVo>> faultLightCallList(@RequestParam("detailId") Long detailId);

    /**
     * 功能描述：查询消息推送记录
     *
     * @param  sinceType 业务类型
     * @param  subBizNo 子业务编码
     * @param  bizNo 业务主键或业务编码
     * @return PushMessageRecordVo 推送记录
     */
    //查询待发送记录
    @GetMapping(path = "/vipCustom/queryPushMessageRecord", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<PushMessageRecordVo> queryPushMessageRecord(@RequestParam Integer sinceType, @RequestParam String subBizNo, @RequestParam String bizNo);
/**
     * 功能描述：查询时间范围内推送成功记录总条数
     *
     * @param  beginTime 开始时间
     * @param  endTime 结束时间
     * @param  bizNo 业务主键或业务编码
     */
    @GetMapping("/vipCustom/queryPushMessageRecordCount")
    @ApiOperation(value = "查询消息推送记录")
    DmsResponse<Integer> queryPushMessageRecordCount(@RequestParam String beginTime,@RequestParam String endTime,@RequestParam String bizNo);
    /**
     * 功能描述：查询时间范围内推送成功记录
     *
     * @param  currentPage 当前页
     * @param  pageSize 每页大小
     * @param  beginTime 开始时间
     * @param  endTime 结束时间
     * @param  bizNo 业务主键或业务编码
     * @return PushMessageRecordVo 推送记录
     */
    @GetMapping("/vipCustom/queryPushMessageRecordList")
    @ApiOperation(value = "查询消息推送记录")
    DmsResponse<Page<PushMessageRecordVo>> queryPushMessageRecordList(@RequestParam Integer currentPage,@RequestParam Integer pageSize,@RequestParam String beginTime,@RequestParam String endTime,@RequestParam String bizNo);

    /**
     * 查询所有不符合条件的记录进行补推
     * @return 记录
     */
    @PostMapping(value = "/vipCustom/queryPushMessageRecordList/notToken")
    DmsResponse<List<PushMessageRecordVo>> queryPushMessageRecordList(@RequestParam("bizNo") List<String> bizNo ,@RequestParam("minutes") String minutes);

    /**
     * 功能描述：查询保养灯线索数据
     *
     *  @param  inviteVehicleRecordDto  查询保养灯线索数据dto
     * @return List<InviteVehicleRecordVo>  返回查询结果
     */
    @PostMapping(value = "/invitationFollow/queryMaintenanceLightClues")
    DmsResponse<List<InviteVehicleRecordVo>> queryMaintenanceLightClues(@RequestBody InviteVehicleRecordDto inviteVehicleRecordDto);

    /**
     * 功能描述：获取已存在展示顺序
     *
     */
    @GetMapping(path = "/tag/queryShowSort", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<Integer>> queryShowSort();

    /**
     * 功能描述：清除预览配置
     * @return
     */
    @DeleteMapping(path = "/tag/previewConfiguration", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Integer> clearPreviewConfiguration();

    /**
     * 功能描述：清除配置
     * @return
     */
    @PutMapping(path = "/tag/configuration", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Integer> clearConfiguration(@RequestParam("tagId") String tagId);

    /**
     * 功能描述：预览&预览保存
     * @return
     */
    @PostMapping(path = "/tag/queryConfigList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<TagConfigTempDto>> queryConfigList();

    /**
     * 功能描述：预览&预览保存同步数据
     * @return
     */
    @PostMapping(path = "/tag/previewOrSaveSynchronizeData", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Integer> previewOrSaveSynchronizeData();

    /**
     * 功能描述：查询标签详情
     *
     * @param
     * @return TagInfoVO 标签信息
     */
    @GetMapping(path = "/tag/queryTagTempInfoById", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<TagInfoVo> queryTagTempInfoById();

    /**
     * 功能描述：查询标签信息
     * @return
     */
    @GetMapping(path = "/tag/queryTagTempInfoList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<TagInfoVo>> queryTagTempInfoList();


    @PostMapping(path = "/tag/synchronizeDataToTemp", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Integer> synchronizeDataToTemp();

    /**
     * 功能描述：根据标签code集合查询信息
     *
     * @param tagCodes 标签code
     * @return TagInfoVO 标签信息
     */
    @GetMapping(path = "/tag/queryTagInfoByTagCodes", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<TagInfoVo>> queryTagInfoByTagCodes(@RequestParam("tagCodes") List<String> tagCodes);
    /**
     * 功能描述：查询字段下拉框
     *
     * @param tagInfoDTO dto对象
     * @return List<TagInfoVO> 字段下拉框
     */
    @PostMapping(path = "/tag/queryFieldInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<TagInfoVo>> queryFieldInfo(@RequestBody TagInfoDto tagInfoDTO);
    /**
     * 功能描述：添加字段转译
     *
     * @param changeTagInfoDTO 字段更新修改Dto
     */
    @PostMapping(path = "/tag/updateFieldInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> updateFieldInfo(@RequestBody ChangeTagInfoDto changeTagInfoDTO);
    /**
     * 功能描述：获取字段转译列表信息
     *
     * @param showName 展示名称
     * @param currentPage 当前页
     * @param pageSize 每页条数
     * @return TagInfoVO 字段转译列表信息
     */
    @GetMapping(path = "/tag/queryFieldTranslation", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Page<TagInfoVo>> queryFieldTranslation(@RequestParam(value = "showName",required = false) String showName,
                                                       @RequestParam(value = "currentPage",required = true) Integer currentPage,
                                                       @RequestParam(value = "pageSize",required = true) Integer pageSize,
                                                       @RequestParam(value = "tagType",required = false) String tagType);
    /**
     * 功能描述：添加字段转译
     *
     * @param changeTagInfoDTO 字段更新修改Dto
     */
    @PostMapping(path = "/tag/insertTagRuleInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> insertTagRuleInfo(@RequestBody ChangeTagInfoDto changeTagInfoDTO);

    /**
     * 功能描述：邀约话术管理查询
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/invitation/invitationScriptManageQuery")
    DmsResponse<Page<InvitationScripDto>> invitationScriptManageQuery(@RequestParam("current") Integer current ,
                                                                      @RequestParam("size") Integer size,
                                                                      @RequestParam(value = "typeCodeId",required = false) List<Integer> typeCodeId,
                                                                      @RequestParam(value = "isEnable",required = false) Integer isEnable,
                                                                      @RequestParam(value = "title",required = false) String title,
                                                                      @RequestParam(value = "tagTalkskill",required = false) String tagTalkskill);

    /**
     * 邀约话术跟进页面渲染
     * @param followUpPageDto
     * @return
     */
    @PostMapping("/invitation/invitationScriptFollowUpPage")
    DmsResponse<List<InvitationScripDto>> invitationScriptFollowUpPage(@RequestBody FollowUpPageDto followUpPageDto);


    /**
     * 邀约话术跟进页面渲染
     * @param followUpPageDto 邀约话术跟进页面渲染
     * @return 邀约话术记录
     */
    @PostMapping("/invitation/queryInvitationScriptByTag")
    DmsResponse<List<InvitationScripDto>> queryInvitationScriptByTag(@RequestBody FollowUpPageDto followUpPageDto);

    /**
     * 邀约话术管理查询详情
     * @param id
     * @return
     */
    @GetMapping("/invitation/scriptManageDetail/{id}")
    DmsResponse<InvitationScripDto> invitationScriptManageDetail(@PathVariable("id") Integer id);
    /**
     * 功能描述：添加临时表字段转译
     *
     * @param changeTagInfoDTO 字段更新修改Dto
     */
    @PostMapping("/tag/insertTagConfigTemp")
    DmsResponse<Void> insertTagConfigTemp(@RequestBody ChangeTagInfoDto changeTagInfoDTO);

    /**
     * 邀约话术保存
     * @param invitationScripDto
     * @return
     */
    @PostMapping("/invitation/saveInvitationScript")
    DmsResponse<Integer> saveInvitationScript(InvitationScripDto invitationScripDto);

    /**
     * 邀约话术修改
     * @param invitationScripDto
     * @return
     */
    @PostMapping("/invitation/updateInvitationScript")
    DmsResponse<Integer> updateInvitationScript(@RequestBody InvitationScripDto invitationScripDto);

    /**
     * 邀约话术删除
     * @param id
     * @return
     */
    @GetMapping("/invitation/deleteInvitationScript")
    DmsResponse<Integer> deleteInvitationScript(@RequestParam("id") Integer id);
    /**
     * 批量删除本地标签字段配置
     * @param delTagInfo
     * @return
     */
    @PostMapping("/tag/batchDelTagInfo")
    DmsResponse<Void> batchDelTagInfo(@RequestBody List<String> delTagInfo);
    /**
     * 功能描述：查询标签详情（临时表）
     *
     * @param id 标签id
     * @return TagInfoVO 标签信息
     */
    @GetMapping(path = "/tag/queryTagInfoTempById", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<TagInfoVo> queryTagInfoTempById(@RequestParam("id") Long id);


    /**
     * 事故线索 ......start......
     */
    /**
     * 下发线索
     * @param dto 线索对象
     * @return 成功与失败
     */
    @PostMapping(path = "/accidentClues/api/v1/clues/interf", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<String> insertAccidentClues(@RequestBody LeadOperationResultDto dto);

    /**
     * Inserts a log record into the database for a given LeadsLogRecordDto. (基于给定的对象添加日志记录)
     * @param leadsLogRecordDto The LeadsLogRecordDto representing the log record to be inserted.
     * @return A DmsResponse object containing the ID of the inserted log record.
     */
    @PostMapping(path = "/accidentClues/api/v1/log/interf", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Long> insertLog(@RequestBody LeadsLogRecordDto leadsLogRecordDto);

    /**
     * Logs a record for the given LeadsLogRecordDto. （基于对象更新日志信息）
     * @param leadsLogRecordDto The LeadsLogRecordDto containing the record to be logged.
     * @return A DmsResponse object indicating the success or failure of the logging operation.
     */
    @PutMapping(path = "/accidentClues/api/v1/logs/interf", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> logRecord(@RequestBody LeadsLogRecordDto leadsLogRecordDto);

    /**
     * Updates the clues for a given accident ID and clue ID. (更新线索基于给定的 id 和 逐渐id )
     * @param dto The updated clues information provided as a {@link LeadOperationResultDto} object.
     * @param acId The accident ID.
     * @param id The clue ID.
     * @return A {@link DmsResponse} object with a {@code Void} response.
     */
    @PutMapping(path = "/accidentClues/api/v1/clues/interf/{acId}/{id}", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> updateClues(@RequestBody LeadOperationResultDto dto, @PathVariable("acId") Long acId, @PathVariable("id") Long id);

    /**
     * 事故线索-保存配置规则
     */
    @PostMapping("/accidentCluesOwnerRule/api/v1/save")
    DmsResponse<Long> acSave(@RequestBody OwnerRuleDto dto);

    /**
     * 事故线索-保存分配规则
     */
    @PostMapping("/accidentCluesOwnerRule/api/v1/saves")
    DmsResponse<Void> acSaves(@RequestBody List<OwnerRuleDto> dtos);

    /**
     * 事故线索-更改分配规则
     */
    @PostMapping("/accidentCluesOwnerRule/api/v1/updates")
    DmsResponse<Void> acUpdates(@RequestBody List<OwnerRuleDto> dtos);

    /**
     * 事故线索-根据经销商查询配置规则
     */
    @GetMapping("/accidentCluesOwnerRule/api/v1/selectOwnerRuleOwnerCode/{ownerCode}/{ruleType}")
    DmsResponse<List<OwnerRuleDto>> acSelectOwnerRuleOwnerCode(@PathVariable("ownerCode") String ownerCode, @PathVariable("ruleType") Integer ruleType);

    /**
     * 事故线索-根据经销商查询分配配置
     */
    @GetMapping("/accidentCluesOwnerRule/api/v1/selectAllotByOwnerCode/{ownerCode}")
    DmsResponse<List<OwnerRuleDto>> acSelectAllotByOwnerCode(@PathVariable("ownerCode") String ownerCode);

    /**
     * 事故线索-根据经销商查询消息配置
     */
    @GetMapping("/accidentCluesOwnerRule/api/v1/selectMessageByOwnerCode/interf/{ownerCode}")
    DmsResponse<List<OwnerRuleDto>> acSelectMessageByOwnerCode(@PathVariable("ownerCode") String ownerCode);

    /**
     * 事故线索-根据经销商查询即将分配人
     */
    @GetMapping("/accidentCluesOwnerRule/api/v1/selectAllocatedInfo/interf/{ownerCode}")
    DmsResponse<OwnerRuleDto> acSelectAllocatedInfo(@PathVariable("ownerCode") String ownerCode);

    /**
     * 事故线索-根据id删除
     */
    @GetMapping("/accidentCluesOwnerRule/api/v1/deleteById/{id}")
    DmsResponse<Integer> acDeleteById(@PathVariable("id") Long id);

    /**
     * 事故线索-根据ids删除
     */
    @PostMapping("/accidentCluesOwnerRule/api/v1/deleteByIds/{ids}")
    DmsResponse<Integer> acDeleteByIds(@PathVariable("ids") String ids);

    /**
     *
     * @param dto
     * 事故线索新增 超时未跟进状态
     * @return
     */
    @PutMapping(path = "/accidentClues/api/v1/updateAccidentCluesTimeOutStatusJob/interf", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<AccidentCluesDto>> updateAccidentCluesTimeOut(@RequestBody AccidentCluesFollowStatusChangeTaskDto dto);
    /**
     *
     * @param dto
     * 事故线索新增 关闭
     * @return
     */
    @PutMapping(path = "/accidentClues/api/v1/updateAccidentCluesCloseStatusJob/interf", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<AccidentCluesDto>> updateAccidentCluesClose(@RequestBody AccidentCluesFollowStatusChangeTaskDto dto);

    /**
     * 事故线索更新上报后的crm信息
     *
     */
    @PostMapping("/accidentClues/crmInfo/interf")
    DmsResponse<Void> updateAccidentClueCrmInfo(@RequestBody List<AccidentClueCrmInfoMqDto> crmInfo);


    /**
     * 事故线索集合
     */
    @PostMapping("/accidentClues/list/interf")
    DmsResponse<List<AccidentCluesVo>> accidentClueList(@RequestBody AccidentCluesDto query);
    /**
     *
     * @param acId
     * 事故线索新增 关闭
     * @return
     */
    @GetMapping(path = "/accidentClues/api/v1/queryCrmIdByAcId/{acId}")
    DmsResponse<Long> queryCrmIdByAcId(@PathVariable("acId") Long acId);
    /**
     *
     *
     */
    @PostMapping("/accidentClues/saveAccidentCluesSaNumber")
    DmsResponse<Void> saveAccidentCluesSaNumber(@RequestBody AccidentCluesSaNumberDto info);

    /**
     * 查询主线索
     */
    @PostMapping("/accidentClues/api/v1/clues/getParentCrmId/interf")
    DmsResponse<JSONObject> getParentCrmId(@RequestBody List<Long> idList);

    /**
     * 事故线索导出数据
     */
    @GetMapping("/accidentClues/export/oss")
    DmsResponse<List<AccidentCluesExportDto>> accidentClueExportOss(@SpringQueryMap AccidentCluesExportQueryDto dto);


    /*
     * 事故线索 ......end......
     */


    /**
     * Queries if clues exist for a given CRM id. (基于id查询是否存在线索)
     * @param id The CRM id for which the clues need to be checked.
     * @return Returns a DmsResponse indicating if clues exist (true) or not (false) for the given CRM id.
     */
    @GetMapping(value = "/accidentClues/api/v1/clues/interf/{id}")
    DmsResponse<AccidentCluesDto> queryCluesExistsByCrmId(@PathVariable(value = "id") @Param("id") Long id);


    /**
     * 根据车架号车牌号查询是否VIP客户
     */
    @GetMapping("/vipCustom/queryVipByVinLicense")
    DmsResponse<Boolean> queryVipByVinLicense(@RequestParam("vin") String vin,@RequestParam("license") String license);
    /**
     * 查询保客营销话术
     * @param tags 标签集合
     * @param typeCodeId 业务类型
     * @return 结果集
     */
    @GetMapping(value = "/invitation/scriptManageQuery/{typeCodeId}")
    DmsResponse<List<InvitationScripDto>> scriptManageQuery(@RequestParam List<String> tags, @PathVariable("typeCodeId") String typeCodeId);

    /**
     * 故障灯线索数据快照
     */
    @PostMapping("/faultLight/addClueCompensate")
    DmsResponse<FaultLightClueResponseDto> addClueCompensate(@RequestBody LeadOperationResultDto dto);

    /**
     * 故障灯线索数据添加
     */
    @PostMapping("/faultLight/addClueDataSynchro")
    DmsResponse<FaultLightClueResponseDto> addClueDataSynchro(@RequestBody LeadOperationResultDto dto);
    /**
     * 事故线索分配
     */
    @PostMapping("/accidentClues/api/v1/clues/allotDealer")
    DmsResponse<List<StatusChangePushDto>> allotDealer(@RequestBody List<AllotDealerDTO> params);
    /**
     * 根据线索ID,lirecrmId 查询线索是否下发过
     * @param type
     * @param id
     * @return
     */
    @GetMapping(value = "/comm/clues/api/v1/clues/interf/{type}/{id}")
    DmsResponse<Boolean> queryCluesExistsByCrmId(@PathVariable(value = "type") @Param("type") String type,@PathVariable(value = "id") @Param("id") Long id);

    /**
     * 下发线索通用接口
     * @param dto 线索对象
     * @return 成功与失败
     */
    @PostMapping(path = "/comm/clues/api/v1/clues/interf", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<DistributeClueResDto> insertClues(@RequestBody LeadOperationResultDto dto);

    /**
     * Inserts a log record into the database for a given LeadsLogRecordDto. (基于给定的对象添加日志记录)
     * @param leadsLogRecordDto The LeadsLogRecordDto representing the log record to be inserted.
     * @return A DmsResponse object containing the ID of the inserted log record.
     */
    @PostMapping(path = "/comm/clues/api/v1/log/interf", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Long> commInsertLog(@RequestBody LeadsLogRecordDto leadsLogRecordDto);

    /**
     * Logs a record for the given LeadsLogRecordDto. （基于对象更新日志信息）
     * @param leadsLogRecordDto The LeadsLogRecordDto containing the record to be logged.
     * @return A DmsResponse object indicating the success or failure of the logging operation.
     */
    @PutMapping(path = "/comm/clues/api/v1/logs/interf", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> commUpdateLog(@RequestBody LeadsLogRecordDto leadsLogRecordDto);

    /**
     * 根据车架号和经销商查询未完成线索和报单人信息
     * @param vin 车架号
     * @param ownerCode 经销商
     * @param b 标记 是否走 查询保单信息
     * @return 返回构建的报单人信息对象
     */
    @GetMapping("/renewalOfInsurance/policyholder")
    DmsResponse<InviteInsuranceVehicleRecordExtDto> queryPolicyholder(@RequestParam String vin, @RequestParam String ownerCode, @RequestParam Boolean tab);
    /**
     * 查询续保线索
     */
    @PostMapping("/fullLeads/queryRenewalInsurance")
    @ApiOperation(value = "查询续保线索")
    RestResultResponse<List<InsuranceVehicleRecordDTO>> queryRenewalInsurance(@RequestBody List<Long> followList);
    /**
     *  店端-保险跟进-导入线索
     * @param imporList
     * @return 解析数据
     */
    @PostMapping(value = "/renewalOfInsurance/import")
    DmsResponse<Void> importRenewalOfInsurance(@RequestBody List<InsuranceRecordImportDTO> imporList);
    /**
     * 店端-保险跟进-查询导入到临时表的数据
     * @return
     */
    @GetMapping("/renewalOfInsurance/importQuery")
    DmsResponse<Page<InsuranceRecordImportDTO>> importQuery(@RequestParam(value = "flag") Integer flag,
                                                            @RequestParam(value = "pageNum") Integer pageNum,
                                                            @RequestParam(value = "pageSize") Integer pageSize);

    /**
     * 续保关闭定时任务
     */
    @PostMapping("/renewalOfInsurance/closeRenewalLead/interf")
    void closeRenewalLead(@RequestBody InviteInsuranceReqDto inviteInsuranceReqDto);
    /**
     *  店端-保险跟进-执行线索导入
     * @return 解析数据
     */
    @PostMapping("/renewalOfInsurance/executeImport")
    DmsResponse<Void> executeImportRenewalOfInsurance();

    /**
     * 线索分配
     * @param dtos
     */
    @PostMapping("/renewalOfInsurance/assignClue")
    DmsResponse<Void> assignClue(@RequestBody List<DistributeClueDto> dtos);

    /**
     * 查询线索经销商
     */
    @GetMapping("/renewalOfInsurance/clueDealer")
    DmsResponse<List<String>> clueDealer();
    /**
     *  店端-续保任务分配-跟进人员导入
     * @param imporList
     * @return 解析数据
     */
    @PostMapping(value = "/renewalOfInsurance/importAssigPerson")
    DmsResponse<Void> importAssigPerson(@RequestBody List<InsuranceAssigPersonImportDTO> imporList, @RequestParam("dealerCode") String dealerCode);
    /**
     * 店端-续保分配-查询导入分配到临时表的数据
     * @return
     */
    @GetMapping("/renewalOfInsurance/queryImportAssigPerson")
    DmsResponse<Page<InsuranceAssigPersonImportDTO>> queryImportAssigPerson(@RequestParam(value = "flag", required = false) Integer flag,
                                                            @RequestParam(value = "pageNum") Integer pageNum,
                                                            @RequestParam(value = "pageSize") Integer pageSize,
                                                            @RequestParam(value = "dealerCode") String dealerCode);
    /**
     *  店端-续保分配-执行分配人导入
     * @return 解析数据
     */
    @PostMapping("/renewalOfInsurance/executeImportAssigPerson")
    DmsResponse<Void> executeImportAssigPerson(@RequestParam(value = "dealerCode") String dealerCode);

    @GetMapping(value = "/renewalOfInsurance/queryIsGuaranteeSlip")
    DmsResponse<Boolean> queryIsGuaranteeSlip(@RequestParam(value = "vin") String vin, @RequestParam(value = "dealerCode") String dealerCode);


    /**
     * 根据车架号获取bx车架号
     */
    @GetMapping(path = "/vipCustom/batchQueryBxInfoByVins", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<String>> batchQueryBxInfoByVins(@RequestParam("vins") List<String> vins);

    /**
     * vin或车牌查询vip群组
     */
    @GetMapping("/vipCustom/queryVipGroupByVinOrLicense")
    DmsResponse<List<String>> queryVipGroupByVinOrLicense(@RequestParam("vin") String vin, @RequestParam("license") String license);


    @PostMapping(value = "/vipCustom/vipGroup/import", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> importVipGroup(@RequestBody List<VipGroupImportDTO> importDTOList);

    /**
     * 重点客户组别维护列表查询
     */
    @PostMapping("/vipCustom/vipGroup/page")
    DmsResponse<Page<VipGroupImportDTO>> vipCustomPage(@RequestBody VipCustomPageReqDTO reqDTO);

    /**
     * 重点客户组别维护列表导出
     */
    @PostMapping("/vipCustom/vipGroup/export")
    DmsResponse<Void> vipCustomPageExport(@RequestBody VipCustomPageReqDTO reqDTO);
    /**
     * 事故线索分配
     */
    @PostMapping("/accidentClues/cluePool/getSumAccidentInfo")
    DmsResponse<AccidentCluesSumInfoDTO> getSumAccidentInfo(@RequestBody AccidentCluesExportQueryDto params);

    /**
     *  续保线索跟进查询接口
     */
    @PostMapping("/renewalOfInsurance/queryTransparency")
    DmsResponse<Page<InsuranceLeadsTransparencyDto>> queryTransparency(@RequestBody InsuranceLeadsTransparencyQueryDto queryDto);

    /**
     *  续保线索跟进透明表- 线索下发场景- 反查线索
     */
    @GetMapping("/renewalOfInsurance/getInsuranceVehicleRecordId")
    DmsResponse<List<Long>> getInsuranceVehicleRecordId(@RequestParam (value = "externalClueId") Long externalClueId);

    /**
     *  续保线索跟进透明表- 线索分配场景- 反查线索
     */
    @PostMapping("/renewalOfInsurance/getDistributionInsuranceVehicleRecordIds")
    DmsResponse<List<Long>> getDistributionInsuranceVehicleRecordIds(@RequestBody List<DistributeClueDto> dtos);

    @GetMapping("/tag/blockConfig/all")
    DmsResponse<List<BlockConfigDto>> getBlockConfig();
}
