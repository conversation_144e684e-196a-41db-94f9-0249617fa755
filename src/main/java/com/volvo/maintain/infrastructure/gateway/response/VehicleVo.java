package com.volvo.maintain.infrastructure.gateway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("车辆信息")
public class VehicleVo {
    /**
     * Vin
     */
    @ApiModelProperty(value = "Vin",name = "vin")
    private String vin;

    /**
     * 发动机代码
     */
    @ApiModelProperty(value = "发动机代码",name = "engineCode")
    private String engineCode;

    /**
     * 底盘编号
     */
    @ApiModelProperty(value = "底盘编号",name = "chassisNumberNo")
    private String chassisNumberNo;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号",name = "license")
    private String license;

    /**
     * 品牌id
     */
    @ApiModelProperty(value = "品牌id",name = "brand")
    private Integer brand;

    /**
     * 车系id
     */
    @ApiModelProperty(value = "车系id",name = "series")
    private Integer series;

    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色",name = "color")
    private Integer color;

    /**
     * 变速箱箱号
     */
    @ApiModelProperty(value = "变速箱" ,name = "gearBox")
    private String gearBox;

    /**
     * 内饰ID
     */
    @ApiModelProperty(value = "内饰ID",name = "innerColor")
    private Integer innerColor;

    /**
     * 车型ID
     */
    @ApiModelProperty(value = "车型ID",name = "model")
    private Integer model;

    /**
     * 配置年份 年款
     */
    @ApiModelProperty(value = "配置年份 年款",name = "modelYear")
    private String modelYear;

    /**
     * 配置id
     */
    @ApiModelProperty(value = "配置id",name = "apackage")
    private Integer apackage;

    /**
     *  品牌代码
     */
    @ApiModelProperty(value = "品牌代码",name = "brandCode")
    private String brandCode;

    /**
     *  车系代码
     */
    @ApiModelProperty(value = "车系代码",name = "seriesCode")
    private String seriesCode;//取MODEL_CODE的前3位字符

    /**
     *  车型代码
     */
    @ApiModelProperty(value = "车型代码",name = "modelCode")
    private String modelCode;

    /**
     * 配置代码 车款代码
     */
    @ApiModelProperty(value = "配置代码 车款代码",name = "apackageCode")
    private String apackageCode;

    /**
     * 发动机号
     */
    @ApiModelProperty(value = "发动机号",name = "engineNo")
    private String engineNo;

    /**
     * 排气量
     */
    @ApiModelProperty(value = "排气量",name = "exhaustQuantity")
    private String exhaustQuantity;

    /**
     * 排放标准（国Ⅲ、国Ⅳ、国Ⅲ+OBD、欧Ⅲ、欧Ⅳ、欧Ⅳ+OBD、国Ⅲ欧Ⅳ、国V）
     */
    @ApiModelProperty(value = "排放标准（国Ⅲ、国Ⅳ、国Ⅲ+OBD、欧Ⅲ、欧Ⅳ、欧Ⅳ+OBD、国Ⅲ欧Ⅳ、国V）",name = "dischargeStandard")
    private Integer dischargeStandard;

    /**
     * 燃料类型(4106)
     */
    @ApiModelProperty(value = "燃料类型(4106)",name = "dynamicCode")
    private Integer dynamicCode;

    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期",name = "invoiceDate")
    private String invoiceDate;

    /**
     * 保修起始日期
     */
    @ApiModelProperty(value = "保修起始日期",name = "wrtBeginDate")
    private String wrtBeginDate;

    /**
     * 保修结束日期
     */
    @ApiModelProperty(value = "保修结束日期",name = "wrtEndDate")
    private String wrtEndDate;

    /**
     * 上牌日期
     */
    @ApiModelProperty(value = "上牌日期",name = "licenseDate")
    private String licenseDate;

    /**
     * 行驶证号
     */
    @ApiModelProperty(value = "行驶证号",name = "drivingLicense")
    private String drivingLicense;

    /**
     * 里程
     */
    @ApiModelProperty(value = "里程",name = "mileage")
    private Integer mileage;

    /**
     * 车主上报销售日期
     */
    @ApiModelProperty(value = "车主上报销售日期",name = "salesDate")
    private String salesDate;

    /**
     * 制造日期
     */
    @ApiModelProperty(value = "制造日期",name = "productDate")
    private String productDate;

    /**
     * 出厂日期
     */
    @ApiModelProperty(value = "出厂日期",name = "factoryDate")
    private String factoryDate;

    /**
     * 水货车(0:否 1:是)
     */
    @ApiModelProperty(value = "水货车(0:否 1:是)",name = "smuggledGoodsVehicle")
    private String smuggledGoodsVehicle;

    @ApiModelProperty("经销商代码")
    private String dealerCode;

    @ApiModelProperty(value = "交车门店编号")
    private String orderOwnerCode;

    private String ownedCompanyName;
}
