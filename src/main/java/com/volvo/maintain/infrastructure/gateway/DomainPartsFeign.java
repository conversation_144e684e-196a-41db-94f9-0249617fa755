package com.volvo.maintain.infrastructure.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.PartsInfoDTO;
import com.volvo.maintain.application.maintainlead.dto.part.PartStockDTO;
import com.volvo.maintain.application.maintainlead.dto.workshop.*;
import com.volvo.maintain.application.maintainlead.vo.workshop.ShortPartItemVo;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "domain-parts")
public interface DomainPartsFeign {

    @PostMapping(value = "/workshop/queryShortPart", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Page<ShortPartItemDto>> queryShortPart(@RequestBody ShortPartItemVo shortPartItemVo);

    @PostMapping("/workshop/syncPartStatus")
    DmsResponse<List<SynPurchaseDetailsDto>> queryPurchaseDetail(@RequestBody ListPartBuyItemDto listPartBuyItemDto);
    
    /**
     * 查询零件的库存信息
     */
    @PostMapping(value = "/parts/queryPartInfo")
    DmsResponse<List<PartStockDTO>> queryPartInfo(@RequestBody List<String> partNos);
    
    /**
     * 查询零件主档数据
     */
    @PostMapping(value = "/partMasterFile/queryPartOrderInfoList")
    DmsResponse<List<PartsInfoDTO>> queryPartOrderInfoList(@RequestBody PartsInfoDTO partsInfo);

    /**
     * 工单号经销商查询缺料数据
     * @param toCRequestParamsDto 入参对象
     * @return 需要的数据
     */
    @PostMapping("/workshop/toc/etaTime")
    DmsResponse<List<OutboundOrderToCResponseDto>> toc(@RequestBody List<ToCRequestParamsDto> toCRequestParamsDto);

    /**
     * 记录通话记录
     * @param workshopCallRecordDto 通话记录
     * @return true false
     */
    @PostMapping(value = "/workshop/weCom/addCallLog", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Boolean> addCallLog(@RequestBody WorkshopCallRecordDto workshopCallRecordDto);

    /**
     * 查询通话记录
     * @param ownerCode 经销商
     * @param roNo 工单号
     * @param serviceAdvisor 服务顾问
     * @return 详情
     */
    @GetMapping("/workshop/weCom/callItem")
    DmsResponse<Page<WorkshopCallRecordDto>> queryCallItem(@RequestParam String ownerCode, @RequestParam String roNo, @RequestParam String serviceAdvisor, @RequestParam Integer pageNum, @RequestParam Integer pageSize);

    @PostMapping("/workshop/weCom/purchaseOrderDetailId")
    DmsResponse<List<ShortPartItemDto>> queryShortPartItem(@RequestBody List<Long> ids);

    @PostMapping("/workshop/weCome/queryShortPartCount")
    DmsResponse<List<Long>> queryShortPartCount(@RequestBody ShortPartItemVo shortPartItemVo);
}
