package com.volvo.maintain.infrastructure.gateway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "VehicleFuelVo", description = "车辆VIN")
public class VehicleFuelVo {
    @ApiModelProperty(value = "vin")
    private String vinCode;

    @ApiModelProperty(value = "vin")
    private String vinType;
}
