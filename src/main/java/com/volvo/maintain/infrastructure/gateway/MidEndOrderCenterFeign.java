package com.volvo.maintain.infrastructure.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.order.*;
import com.volvo.maintain.application.maintainlead.vo.order.*;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;


import java.math.BigDecimal;
import java.util.List;

@FeignClient(name = "mid-end-order-center",url = "${baseUrl.midEndOrderCenterCenter}")
public interface MidEndOrderCenterFeign {



    @PostMapping(path = "/vehicleDeliver/page", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<Page<VehicleOrderResponseDtoDto>> queryVehicleOrder(@RequestBody VehicleOrderPageDto vehicleOrderPageDto);


    //查询取送车订单详情(所有)
    @PostMapping(path = "/vehicleDeliver/allDetail", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<VehicleDeliverDTO>> allDetail(RequestDto data);
    //创建取送车订单
    @PostMapping(path = "/vehicleDeliver", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<TaskDeliverCarResponseDto> vehicleDeliver(@RequestBody RequestDto<TaskDeliverCarRequestDto> requestDtoRequestDto);
    //编辑取送车订单
    @PutMapping(path = "/vehicleDeliver", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<Integer> updateVehicleDeliver(@RequestBody RequestDto<TaskDeliverCarRequestDto> requestDtoRequestDto);

    //店端查询取送车订单(分页)
    @PostMapping(path = "/vehicleDeliver/shopSearchVehiicleDeliverPage", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<Page<ShopVehicleDeliverVo>> shopSearchVehiicleDeliverPage(@RequestBody RequestDto<ShopVehicleDeliverDto> shopVehicleDeliverDto);

    //取送车订单取消
    @PostMapping(path = "/vehicleDeliver/cancelVehicleOrder", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<Boolean> cancelVehicleOrder(@RequestBody RequestDto<CancelOrderDto> cancelOrderDtoRequestDto);

    //e代驾下单
    @PostMapping(path = "/vehicleDeliver/placeOrder", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<PlaceOrderVo> placeOrder(@RequestBody RequestDto<PlaceOrderDto> placeOrderDtoRequestDto);

    //店端查询取送车订单(无分页)
    @PostMapping(path = "/vehicleDeliver/shopSearchVehiicleDeliverList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<ShopVehicleDeliverVo>> shopSearchVehiicleDeliverList(@RequestBody RequestDto<ShopVehicleDeliverDto> shopVehicleDeliverDtoRequestDto);

    //查询取送车订单详情(所有)
    @PostMapping(path = "/vehicleDeliver/allDetail", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<VehicleDeliverDetailVo>> allDetails(@RequestBody RequestDto<QueryVehicleDeliverDto> queryVehicleDeliverDtoRequestDto);

    //获取订单详情
    @GetMapping(value = "/balance/customerId/{customerId}")
    MidResponse<BigDecimal> balanceCustomerId(@PathVariable("customerId") String customerId);

    //获取附近驻店司机列表
    @PostMapping(path = "/balance/getFixList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<DriverVo>> getFixList(@RequestBody RequestDto<DriverDto> driverDtoRequestDto);

    //获取预估价格
    @PostMapping(path = "/vehicleDeliver/price", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<PriceVo> price(@RequestBody RequestDto<PriceDto> priceDtoRequestDto);

    //获取订单详情
    @GetMapping(value = "/order/detail/orderId/{orderId}")
    MidResponse<DetailVo> detailOrderId(@PathVariable("orderId") Integer orderId);

    //获取司机代驾轨迹
    @GetMapping(value = "/order/trace/orderId/{orderId}/type/{type}")
    MidResponse<TraceVo> traceOrderId(@PathVariable("orderId") Integer orderId, @PathVariable("type") Integer type);

    //获取司机信息
    @GetMapping(value = "/order/driverInfo/orderId/{orderId}/type/{type}")
    MidResponse<DriverInfoVo> driverInfoOrderId(@PathVariable("orderId") Integer orderId, @PathVariable("type") Integer type);

    //获取车辆信息照片
    @GetMapping(value = "/order/getCarPhotos/orderId/{orderId}/daijiaType/{daijiaType}")
    MidResponse<CarPhotosVo> getCarPhotosOrderId(@PathVariable("orderId") Integer orderId, @PathVariable("daijiaType") Integer daijiaType);
}
