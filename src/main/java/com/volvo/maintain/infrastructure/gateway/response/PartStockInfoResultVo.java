package com.volvo.maintain.infrastructure.gateway.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 
 * <AUTHOR>
 *
 */
@Data
@ApiModel("配件相关信息查询返回VO")
public class PartStockInfoResultVo {

	@ApiModelProperty(value = "配件代码", name = "partNo")
	private String partNo;

	@ApiModelProperty(value = "配件名称", name = "partName")
	private String partName;

	@ApiModelProperty(value = "采购中数量", name = "purchaseQuantity")
	private Double purchaseQuantity;

	@ApiModelProperty(value = "原厂库账面库存", name = "oemstockQuantity")
	private Double oemstockQuantity;

	@ApiModelProperty(value = "外卖库账面库存", name = "takeawayStockQuantity")
	private Double takeawayStockQuantity;

	@ApiModelProperty(value = "原厂库可用库存", name = "oemUsableStock")
	private Double oemUsableStock;

	@ApiModelProperty(value = "外卖库可用库存", name = "takeawayUsableStock")
	private Double takeawayUsableStock;

	@ApiModelProperty(value = "在途数量", name = "deliveryQuantity")
	private Double deliveryQuantity;


}
