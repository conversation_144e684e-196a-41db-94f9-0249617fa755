package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.ConsultationReportDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.IcdResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "mid-end-tech-support-service",url = "${baseUrl.midEndTechSupportService}")
public interface MidEndTechSupportServiceFeign {

    @GetMapping(path = "/getConsultationReportForNb", consumes = "application/json", produces = "application/json")
    IcdResponse<List<ConsultationReportDto>> getConsultationReportForNb(@RequestParam("vin") String vin,
                                                                       @RequestParam("dealerCode") String dealerCode,
                                                                       @RequestParam("firstSubmissionTimeStart") String firstSubmissionTimeStart,
                                                                       @RequestParam("firstSubmissionTimeEnd") String firstSubmissionTimeEnd);


}
