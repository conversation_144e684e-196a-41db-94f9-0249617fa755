package com.volvo.maintain.infrastructure.gateway.response;


import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;


@Setter
@Getter
@ApiModel(value = "临时表导入返回实体")
public class ImportTempResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private int successCount;

    private List<T> errorList;

    private List<T> successList;
    
    private String errorMsg;

}
