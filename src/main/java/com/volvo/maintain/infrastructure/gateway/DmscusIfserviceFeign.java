package com.volvo.maintain.infrastructure.gateway;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.ContractPurchaseGiveDto;
import com.volvo.maintain.application.maintainlead.dto.MaintainActivityInfoRespDTO;
import com.volvo.maintain.application.maintainlead.dto.PurchaseGiveParamsDTO;
import com.volvo.maintain.application.maintainlead.dto.WarrantyUpdateStateDTO;
import com.volvo.maintain.application.maintainlead.dto.claimapply.ClaimApplyUseDTO;
import com.volvo.maintain.application.maintainlead.dto.rights.CustExtWarPurGiveVO;
import com.volvo.maintain.application.maintainlead.dto.rights.GiveStatusSyncDto;
import com.volvo.maintain.application.maintainlead.dto.rights.OrderActivationDetailsRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.OrderActivationDetailsResponseDto;
import com.volvo.maintain.application.maintainlead.dto.rights.WarrantyCXWYCheckRspDTO;
import com.volvo.maintain.application.maintainlead.dto.rights.WarrantyCXWYCheckRsqDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.ClaimApplyApproveRecordRespDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.ClaimApplyApproveReqDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.ClaimApplyPageReqDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.ClaimApplyPageRespDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.warrantyReturnDetailPageDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.warrantyReturnPageDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.warrantyReturnReqDTO;
import com.volvo.maintain.application.maintainlead.vo.AccidentCluesOrderVo;
import com.volvo.maintain.infrastructure.gateway.response.ApiResult;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.interfaces.vo.WarrantyUpdateStateResultVo;
import com.yonyou.cyx.framework.dto.ResponseDTO;

@FeignClient(name = "dmscus-ifservice")
public interface DmscusIfserviceFeign {

    /**
     * 记录下发日志
     */
    @PostMapping(value = "/warranty/approval/log")
    DmsResponse<Long> addApprovalLog(@RequestBody ClaimApplyUseDTO claimApplyUseDTO);

    /**
     * 延保状态更新
     */
    @PostMapping(path = "/extpurchase/updateWarrantyData", produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<WarrantyUpdateStateResultVo>> updateWarrantyData(@RequestBody WarrantyUpdateStateDTO query);

    /**
     * 新增审批
     */
    @PostMapping(value = "/warranty/approval/save")
    DmsResponse<Void> addApproval(@RequestBody ClaimApplyUseDTO claimApplyUseDTO);

    /**
     * 保养套餐(服务合同)状态更新
     */
    @PostMapping(path = "/maintainActivity/syncGiveStatus/interf")
    DmsResponse<List<GiveStatusSyncDto>> syncGiveStatus(@RequestBody List<GiveStatusSyncDto> giveStatusSyncDtos);

    /**
     * 更新审批
     */
    @PostMapping(value = "/warranty/approval/update")
    DmsResponse<Void> updateApproval(@RequestBody ClaimApplyUseDTO claimApplyUseDTO);

    /**
     * 服务合同产品购买
     */
    @PostMapping(path = "/maintainActivity/give/interf")
    DmsResponse<Void> give(@RequestBody ContractPurchaseGiveDto contractPurchaseGiveDto);

    /**
     * 获取申请信息      @RequestParam(value = "planFollowDateBegin", required = false) String planFollowDateBegin,
     */
    @GetMapping("/warranty/approval/detail")
    DmsResponse<ClaimApplyUseDTO> getApprovalDetail(@RequestParam("caseNo") String caseNo);

    /**
     * 延保产品购买
     */
    @PostMapping(path = "/extpurchase/addWarrantyPurchaseGive", produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<PurchaseGiveParamsDTO> addWarrantyPurchaseGive(@RequestBody ContractPurchaseGiveDto giveDto);

    /**
     * 获取审批数据明细
     */
    @GetMapping("/claim/apply/detail")
    DmsResponse<ClaimApplyUseDTO> getAllClaimApplyDetail(@RequestParam("caseNo") String caseNo);

    /**
     * 服务合同购买记录查询
     */
    @PostMapping(path = "/maintainActivity/serviceContractGiveRecordList/interf", produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<OrderActivationDetailsResponseDto>> serviceContractGiveRecordList(@RequestBody OrderActivationDetailsRequestDto orderActivationDetailsRequestDto);


    /**
     * 延保理赔申请列表查询
     * 根据VIN车架号查询延保使用购买记录
     */
    @PostMapping("/warranty/approval/list")
    DmsResponse<IPage<ClaimApplyPageRespDTO>> warrantyApprovalList(@RequestBody ClaimApplyPageReqDTO claimApplyPageReqDTO);

    /**
     * 根据VIN车架号查询延保使用购买记录
     * @param vin
     * @return
     */
    @GetMapping(path = "/extpurchase/getextwarpurgivedataForToC",produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<ApiResult<List<CustExtWarPurGiveVO>>> findCustExtWarPurGiveVOsByVinForToC(@RequestParam("vin") String vin,
                                                                                          @RequestParam("giveStatus") String giveStatus,
                                                                                          @RequestParam("orderCode") String orderCode,
                                                                                          @RequestParam("giveChannel") String giveChannel,
                                                                                          @RequestParam("source") Integer source);

    /**
     * 延保理赔申请审批
     */
    @PostMapping("/warranty/approval")
    DmsResponse<Void> approve(ClaimApplyApproveReqDTO reqDTO);

    @GetMapping(path = "/extpurchase/selectExtWarPurGive/v2/interf")
    DmsResponse<List<CustExtWarPurGiveVO>> selectExtWarPurGiveV2(@RequestParam(name = "vin" )String vin,
                                                                 @RequestParam(name = "orderCode" )String orderCode,
                                                                 @RequestParam(name = "productNos")String productNos);

    /**
     * 延保理赔申请列表导出
     */
    @GetMapping(value = "/warranty/approval/record")
    DmsResponse<List<ClaimApplyApproveRecordRespDTO>> approvalRecordList(@RequestParam("id") Long id);
    /**
     * 修改授权份额及金额
     */
    @PostMapping("/warranty/approval/modifyApprovalAmount")
    DmsResponse<Void> modifyApprovalAmount(@RequestBody ClaimApplyUseDTO dto);

    /**
     * 延保回款查询列表
     */
    @PostMapping(value = "/warranty/approval/returnList")
    DmsResponse<Page<warrantyReturnPageDTO>> returnList(@RequestBody warrantyReturnReqDTO dto);

    /**
     * 延保回款查询列表
     */
    @GetMapping("/warranty/approval/detailList")
    DmsResponse<List<warrantyReturnDetailPageDTO>> detailList(@RequestParam("returnId") Long returnId);
    /**
     * 反向关联工单
     */
    /**
     * 修改授权份额及金额
     */
    @PostMapping("/accidentClues/associationOrder/interf")
    DmsResponse<Void> associationOrder(@RequestBody AccidentCluesOrderVo dto);


    @GetMapping(path = "/extpurchase/queryExtWarPurGiveVByVin",produces = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<ApiResult<List<CustExtWarPurGiveVO>>> queryExtWarPurGiveVByVin(@RequestParam("vin") String vin);

    /**
     * 延保出险无忧-复购：check是否可以复购
     */
    @PostMapping(path = "/extpurchase/v1/warrantyCXWYCheck", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseDTO<List<WarrantyCXWYCheckRspDTO>> warrantyCXWYCheck(@RequestBody WarrantyCXWYCheckRsqDTO warrantyCXWYCheckDTOS);
    
	@PostMapping(path = "/maintainActivity/v1/queryMaintainActivityList", produces = MediaType.APPLICATION_JSON_VALUE)
	DmsResponse<List<MaintainActivityInfoRespDTO>> queryMaintainActivityList(@RequestBody List<String> maintainNos);
}
