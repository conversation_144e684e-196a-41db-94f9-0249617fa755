package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.vo.CarOwnerRelationVo;
import com.volvo.maintain.infrastructure.gateway.response.ImResponse;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(name = "vehicle-ownership-service",url = "${baseUrl.vehicleOwnershipService}")
public interface VehicleOwnershipServiceFeign {

    //根据vin获取所有绑定列表`
    @GetMapping(path = "/carowner/query/getBindInfobyVin", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<String> getBindInfoByVin(@RequestParam String vinCode);


    //根据vin获取所有绑定列表
    @GetMapping(path = "/dubbo/getBingRecodeByVinCode", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<CarOwnerRelationVo>> queryRelationList(@RequestParam("vinCode") String vinCode);


}
