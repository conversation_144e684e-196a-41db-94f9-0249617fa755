package com.volvo.maintain.infrastructure.gateway;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.volvo.maintain.application.maintainlead.dto.part.PartsDTO;
import com.volvo.maintain.application.maintainlead.dto.workshop.MissingPartsStatusDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.PartPurchaseOrderDTO;
import com.volvo.maintain.application.maintainlead.dto.workshop.SynPurchaseDetailsDto;
import com.volvo.maintain.application.maintainlead.vo.workshop.ETADocumentParamsVo;
import com.volvo.maintain.infrastructure.gateway.request.ETAOperationParamsDTO;
import com.volvo.maintain.infrastructure.gateway.request.PartPurchaseOrderDetaileRequestDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ETAMappingReturnDTO;
import com.volvo.maintain.infrastructure.gateway.response.PartPurchaseOrderDetaileDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 经销商贷款 feign 调用接口
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@FeignClient(value = "domain-purchase")
public interface DomainPurchaseFeign {
	
	/**
	 * 查询采购单号是否关联工单号
	 * @return
	 */
	@PostMapping("/purchase/order/checkPartPurchaseOrder")
	DmsResponse<Boolean> checkPartPurchaseOrder(@RequestParam("orderNo") String orderNo, @RequestParam("partNo") String partNo);


    @PostMapping("/purchase/order/queryPartPurchaseOrderDetaileListByPartNo")
    public List<PartPurchaseOrderDetaileDTO> queryPartPurchaseOrderDetaileListByPartNo(@RequestBody PartPurchaseOrderDetaileRequestDTO partPurchaseOrderDetaileRequest);

	@PostMapping(value = "/purchase/order/time/interf", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    void updateERATime(@RequestBody List<ETADocumentParamsVo> etaDocumentParamsVo);

	@GetMapping("/purchase/order/orderStatus/interf")
	DmsResponse<List<PartPurchaseOrderDetaileDTO>> queryPartPurchaseOrderDetailList();

	/**
	 * 根据主键idList 查询详情
	 * @param idList 采购明细主键id in 查询
	 * @return 明细详情
	 */
	@GetMapping("/purchase/order/idList/interf")
	DmsResponse<List<PartPurchaseOrderDetaileDTO>> queryPurchaseOrderById(@RequestParam Set<Long> idList);

	/**
	 * 同步状态
	 * @param data 数
	 */
	@PostMapping("/purchase/order/syncPartStatus")
	void syncPartStatus(@RequestBody List<SynPurchaseDetailsDto> data);

	/**
	 * 根据id 查询详情信息
	 * @param idList id list
	 * @return 详情信息
	 */
	@GetMapping("/purchase/order/ids/interf")
	DmsResponse<List<PartPurchaseOrderDTO>> queryPurchaseOrderByIds(@RequestParam Set<Long> idList);

	/**
	 * 查询所有无效零件的 缺料对应工单号
	 * @return 采购明细对象
	 */
	@PutMapping("/purchase/order/updatePartStatus")
	DmsResponse<List<PartPurchaseOrderDetaileDTO>> updatePartStatus();
	/**
	 * 查询零件在途数量
	 */
	@PostMapping("/purchase/order/queryPartDeliveryQuantity")
	DmsResponse<List<PartsDTO>> queryPartDeliveryQuantity(@RequestBody List<String> partNos);

	@PostMapping("/transparentEta/etaSceneHandle")
	DmsResponse<Boolean> etaSceneHandle(@RequestBody ETAOperationParamsDTO etaOperationParams);

	/**
	 * 查询经销商+零件映射对应的ETA时间
	 *
	 */
	@GetMapping("/transparentEta/queryEtaByOnwerCodeAndPartNo")
	DmsResponse<List<ETAMappingReturnDTO>> queryEtaByOnwerCodeAndPartNo(@RequestParam("ownerCode") String ownerCode, @RequestParam("partNo") String partNo);

	@GetMapping("/purchase/order/queryPurchasePart")
	DmsResponse<List<PartPurchaseOrderDetaileDTO>> queryPurchasePart(@RequestParam("ownerCode") String ownerCode, @RequestParam("purchaseNo") String purchaseNo);


	/**
	 * 根据idList 更新零件状态为 替换件
	 * @param idList 采购明细主键id list
	 * @return 无返回值
	 */
	@GetMapping("/purchase/order/byIdListUpdate/interf")
	DmsResponse<Void> updatePurchaseOrderById(@RequestParam Set<Long> idList);

}
