package com.volvo.maintain.infrastructure.gateway.response;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ApiModel(description = "接口返回结果")
public class ApiResult<T> {
    private final Logger logger = LoggerFactory.getLogger(ApiResult.class);

    /**
     * 请求失败常量
     */
    public static final int FAILD = 0;

    /**
     * 请求成功常量
     */
    public static final int SUCCESS = 1;

    /**
     * 返回结果 0表示失败     1  表示成功
     */
    @ApiModelProperty(value = "返回接口响应代码， 0表示失败     1  表示成功")
    private int result;

    /**
     * 业务代码：比如失败原因代码，处理成功后的不同状态等。
     */
    @ApiModelProperty(value = "返回业务代码，验证不通过返回的业务代码CODE")
    private String code;
    @ApiModelProperty(value = "返回业务代码，验证不通过返回的业务代码CODE")
    private String resultCode;

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    /**
     * 返回提示信息，比如失败原因描述，处理成功后的不同状态描述等。
     *
     */
    @ApiModelProperty(value = "返回描述")
    private String msg;

    @ApiModelProperty(value = "错误原因")
    private String cause;

    /** 操作成功后的返回对象 */
    @ApiModelProperty(value = "返回数据")
    private T object;

    /**
     * 构造函数，默认设置状态为： 1-成功
     */
    public ApiResult() {
        result = SUCCESS;
    }

    /**
     *
     * 功能描述: <br>
     * 设置result等于1-成功，并设置code和msg.
     *
     * @param code
     * @param msg
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public void setSuccess(String code, String msg) {
        this.code = code;
        this.msg = msg;
        this.result = SUCCESS;
    }

    /**
     *
     * 功能描述: <br>
     * 设置result等于0-失败，并设置code和msg.
     *
     * @param msg  失败原因
     * @param code 失败原因代码
     */
    public void setFail(String code, String msg) {
        this.code = code;
        this.msg = msg;
        this.result = FAILD;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    /**
     * @return result 调用结果
     */
    public int getResult() {
        return result;
    }

    /**
     * @param result 调用结果
     */
    public void setResult(int result) {
        this.result = result;
    }

    /**
     * @return msg 提示信息
     */
    public String getMsg() {
        return msg;
    }

    /**
     * @param msg 提示信息
     */
    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getObject() {
        return object;
    }

    public void setObject(T object) {
        this.object = object;
    }

    public static <T> ApiResult<T> success() {
        return new ApiResult<T>();
    }

    public static <T> ApiResult<T> success(T t) {
        ApiResult<T> ajaxResult = new ApiResult<T>();
        ajaxResult.setObject(t);

        return new ApiResult<T>();
    }

    /**
     *
     * 功能描述: <br>
     * 设置result等于1-成功，并设置code和msg.
     *
     * @param code
     * @param msg
     * @see [相关类/方法](可选)
     * @since [产品/模块版本](可选)
     */
    public static <T> ApiResult<T> success(String code, String msg) {
        ApiResult<T> ajaxResult = new ApiResult<T>();
        ajaxResult.setSuccess(code, msg);

        return ajaxResult;
    }

    /**
     *
     * 功能描述: <br>
     * 设置result等于0-失败，并设置code和msg.
     *
     * @param msg  失败原因
     * @param code 失败原因代码
     */
    public static <T> ApiResult<T> fail(String code, String msg) {
        ApiResult<T> ajaxResult = new ApiResult<T>();
        ajaxResult.setFail(code, msg);
        return ajaxResult;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    @JsonIgnore
    public boolean isSuccess() {
        return result == SUCCESS;
    }

    @JsonIgnore
    public boolean isFail() {
        return result == FAILD;
    }

    /**
     * @return the cause
     */
    public String getCause() {
        return cause;
    }

    /**
     * @param cause the cause to set
     */
    public void setCause(String cause) {
        this.cause = cause;
    }
}
