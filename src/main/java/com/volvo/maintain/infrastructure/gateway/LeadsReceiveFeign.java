package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.infrastructure.gateway.request.VirtualPhoneSelectDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient(name = "leads-receive",url = "${baseUrl.leadsReceiveUrl}")
public interface LeadsReceiveFeign {
    /**
     *查询虚拟手机号
     */
    @RequestMapping(value = "/leads/api/v1/virtualPhone", method = RequestMethod.GET)
    VirtualPhoneSelectDTO queryVirtualPhone(@RequestParam("id") Long id, @RequestParam("dealerPhone") String dealerPhone);


}
