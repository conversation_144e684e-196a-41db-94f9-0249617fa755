package com.volvo.maintain.infrastructure.gateway.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * QB车辆维护表车辆子表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QbVehicleInfoDTO {


    /**
     * 主键id
     */
    private Long id;

    /**
     * qb号表主键
     */
    private Long qbNumberId;

    /**
     * VIN
     */
    @ApiModelProperty(value = "VIN")
    private String vin;

    /**
     * 责任经销商
     */
    @ApiModelProperty(value = "责任经销商号")
    private String dealerCode;

    /**
     * 判断经销商不存在
     */
    private Integer isDealerCode;

    /**
     * 是否执行
     */
    private Integer isPerformed;

    /**
     * 执行时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date performedTime;

    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 是否删除:1,删除；0,未删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * QB号
     */
    @ApiModelProperty(value = "QB号")
    private String qbNumber;

    /**
     * 是否关闭
     */
    private Integer isClosed;

    /**
     * 关闭时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date closedDate;
    /**
     * 开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startingDate;

    private Date startingDateBegin;

    private Date startingDateEnd;


    /**
     * 结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date closingDate;

    private Date closingDateBegin;

    private Date closingDateEnd;

    /**
     * 经销商名称
     */
    private String dealerName;

}
