package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.MobileToOneIdDto;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.interfaces.vo.CustomerMobileListReturnVo;
import com.volvo.maintain.interfaces.vo.MemberInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "mid-end-member-center",url = "${baseUrl.midEndMemberCenter}")
public interface MIdEndMemberCenterFeign {

    //根据会员ids查询会员信息
    @PostMapping(path = "/member/tc-member-info/getMemberInfoByIdList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseDto<List<MemberInfoVo>> getMemberInfoByIdList(@RequestBody List<String> items);

    //根据会员ids查询会员信息
    @PostMapping(path = "/member/tc-member-info/getMemberInformationByMobile", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseDto<List<CustomerMobileListReturnVo>> getMemberInformationByMobile(@RequestBody MobileToOneIdDto requestDTO);

}
