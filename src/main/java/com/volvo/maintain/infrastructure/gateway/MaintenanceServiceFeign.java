package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.OrderMessageRequestDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcMessageRequestDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "2c-maintenance-service",url = "${baseUrl.maintenanceService}")
public interface MaintenanceServiceFeign {

    //根据vin获取所有绑定列表`
    @PostMapping(path = "/api/intra/appointment/overtimeMessageSendWechat", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse overtimeMessageSendWechat(@RequestBody OrderMessageRequestDto orderMessageRequestDto);

    //VHC消息推送
    @PostMapping(path = "/api/maintenance/examiningReportPush", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse examiningReportPush(@RequestBody VhcMessageRequestDto vhcMessageRequestDto);

}
