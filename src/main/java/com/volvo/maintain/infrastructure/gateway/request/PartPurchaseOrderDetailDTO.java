package com.volvo.maintain.infrastructure.gateway.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 零附件采购订单明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-01
 */
@Data
@ApiModel(value = "零附件采购订单明细表")
public class PartPurchaseOrderDetailDTO {

    /**
     * 自增序列
     */
    private Long id;

    /**
     * 采购订单主键id
     */
    private Long purchaseOrderId;

    /**
     * 零件编号
     */
    private String partNo;

    /**
     * 零件名称
     */
    private String partName;

    /**
     * 含税采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 含税零售价
     */
    private BigDecimal retailPrice;

    /**
     * 本次订货数量
     */
    private Integer orderQuantity;

    /**
     * 对应零件采购价*订货数量；保留两位小数
     */
    private BigDecimal purchaseAmount;

    /**
     * 需求日期，4非批售、4批售类型订单不要求维护；1，3类预约单需维护
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8" )
    private Date demandDate;

    /**
     * 是否超限
     */
    private Integer isLimit;

    /**
     * 返利类别
     */
    private Integer rebateType;

    /**
     * 预估返利总额
     */
    private BigDecimal rebateAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 活动编号
     */
    private String activityNo;

    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除，1：删除，0：未删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8" )
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8" )
    private Date updatedAt;

    /**
     * 采购单号
     */
    private String purchaseNo;

    private String binding;


    /**
     * vat 税率
     */
    private Double vat;

    /**
     * 限购数量
     */
    private Integer limitNum;

    /**
     * id
     */
    private Long itemId;
    /**
     * 返利比例
     */
    private Integer rebatePercent;

    private BigDecimal itemPurchaseAmount;


    /**
     * 采购中数量
     */
    private Double purchaseQuantity;

    /**
     * 账面库存
     */
    private Double stockQuantity;




    /**
     * 可用库存
     */
    private Double availableStock;

    /**
     * 在途数量
     */
    private Double deliveryQuantity;

    /**
     * 是否溯源件
     */
    private String isContainSource;

    /**
     *最小包装量
     */
    private BigDecimal minOrderQty;

    /**
     * 上传后修改次数
     */
    private int updateNum;
    

    /**
     * 金额
     */
    private BigDecimal taxAmount;
    

    /**
     * OG行状态
     */
    private String itemStatus;
    
}
