package com.volvo.maintain.infrastructure.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.StatusChangePushDto;
import com.volvo.maintain.application.maintainlead.dto.inviteClue.InviteClueParamDto;
import com.volvo.maintain.application.maintainlead.dto.inviteClue.InviteClueResultDto;
import com.volvo.maintain.application.maintainlead.vo.InviteVehicleRecordVo;
import com.volvo.maintain.infrastructure.gateway.request.InviteInsuranceVehicleCustomerNumberDTO;
import com.volvo.maintain.infrastructure.gateway.request.SaCustomerNumberDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


import java.util.List;

/**
 * 功能描述：协同客户feign接口
 *
 * <AUTHOR>
 * @since 2023/12/26
 */
import java.util.Map;

@FeignClient(name = "dmscus-customer")
public interface DmscusCustomerFeign {

    /**
     * 查询故障灯车主
     */
    @GetMapping(value = "/faultLight/queryFaultLight")
    DmsResponse<CustomerInfoDto> queryFaultLight(@RequestParam String vin, @RequestParam String ownerCode);

    /**
     * 邀约线索AI外呼接口
     *
     * @param params 参数
     * @return String 工作号
     */
    @PostMapping("/saCustomerNumber/fullLeadSaveSaCustomerNumber")
    RestResultResponse<String> saveInviteSaCustomerNumber(@RequestBody SaCustomerNumberDTO params);

    /**
     * 续保线索AI外呼接口
     *
     * @param params 参数
     * @return String 工作号
     */
    @PostMapping("/inviteInsuranceFollow/fullLeadSaveSaCustomerNumber")
    RestResultResponse<String> saveInsuranceSaCustomerNumber(@RequestBody InviteInsuranceVehicleCustomerNumberDTO params);

    /**
     * 故障灯线索AI外呼接口
     *
     * @param params 参数
     * @return String 工作号
     */
    @PostMapping("/faultLight/fullLeadSaveSaCustomerNumber")
    RestResultResponse<String> saveFaultLightSaCustomerNumber(@RequestBody SaCustomerNumberDTO params);

    @PostMapping("/inviteInsuranceFollow/fixSaCustomerNumber")
    RestResultResponse fixSaCustomerNumber(@RequestBody List<FullLeadsFollowDto> followList);

    /**
     * 查询是否大客户
     */
    @GetMapping(value = "/vehicleInfo/getFleetCodeByVin")
    DmsResponse<Integer> getFleetCodeByVin(@RequestParam String vin);

    /**
     * 客户画像线索查询
     */
    @PostMapping(value = "/inviteVehicleTag/selectInviteClueTag")
    DmsResponse<List<InviteClueResultDto>> selectInviteClueTag(@RequestBody InviteClueParamDto inviteClueParamDTO);

    /**
     *
     */
    @GetMapping("/invitationFollow/queryInviteVehicleByVin")
    DmsResponse<InviteVehicleRecordVo> queryInviteVehicleByVin(@RequestParam("ownerCode") String ownerCode,
                                                               @RequestParam("vin") String vin,
                                                               @RequestParam("inviteType") Integer inviteType);

    /**
     * 获取CDP的token
     */
    @GetMapping("/commonMethod/getCdpToken")
    DmsResponse<CdpTokenPortraitDto> getCdpToken();

    /**
     * 事故线索
     */
    @PostMapping(value = "/accidentClues/pushLiteCrmClueStatus/interf")
    DmsResponse<Void> acPushLiteCrmClueStatus(@RequestBody List<StatusChangePushDto> pushInfoList);
    /**
     * 事故线索
     */
    @PostMapping(value = "/accidentClues/getLiteCrmToken", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<String> getLiteCrmToken();
    /**
     *故障灯消费crm线索状态
     */
    @PostMapping(value = "/faultLight/faultLightConsumerStatus/interf", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> faultLightConsumerStatus(@RequestParam String msg);


    /**
     * 自建邀约线索删除功能
     */
    @PostMapping("/invitationManageVCDC/oem/delete")
    DmsResponse<Void> deleteSelfCreateInvitation(@RequestBody Map<String, Object> param);

    /**
     *查询迁移关系
     */
    @GetMapping( "/tm-clue-migrate-task/leadsTransfer/interf" )
    DmsResponse<Page<DealerMigrationRecordPo>> leadsTransfer(@RequestParam("currentPage")int currentPage,
                                                             @RequestParam("pageSize")int pageSize
            , @RequestParam(value = "migrationType",required = false)Integer migrationType);
}
