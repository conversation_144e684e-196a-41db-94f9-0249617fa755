package com.volvo.maintain.infrastructure.gateway;

import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.application.maintainlead.dto.workshop.EmpDetailsDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.UsedCarGetManagerByRoleCodeMapDto;
import com.volvo.maintain.application.maintainlead.vo.UserInfoVo;
import com.volvo.maintain.infrastructure.gateway.request.VinQueryDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import com.volvo.maintain.infrastructure.gateway.response.VehicleFuelVo;
import com.volvo.maintain.infrastructure.gateway.response.VehicleVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(name = "mid-end-auth-center", url = "${baseUrl.midEndAuthCenter}")
public interface MidEndAuthCenterFeign {

	//查询角色对应人员信息
	@PostMapping(path = "/role/dealer/user", consumes = "application/json", produces = "application/json")
	DmsResponse<List<EmpByRoleCodeDto>> queryDealerUser(@RequestBody ResponseDto<EmpByRoleCodeDto> empByRoleCodeDto);

	//发送邮件
	@GetMapping(path = "/users/id/{id}", consumes = "application/json", produces = "application/json")
	ResponseDto<UserInfoVo> queryUserInfoById(@PathVariable("id") String id);

	@PostMapping(path = "/empPermission/getUserInfoByUserIds", consumes = "application/json", produces = "application/json")
	List<EmpUserInfoDto> queryUserInfoByUserIds(@RequestBody UserInfoByUserIdsDto userInfoByUserIdsDto);

	@PostMapping(path = "/emp/list", consumes = "application/json", produces = "application/json")
	ResponseDto<List<UserListOutDto>> queryEmpList(@RequestBody JSONObject jsonObject);

	/**
	 * 批量查询用户信息
	 * @param userInfoByUserIdsDto
	 * @return
	 */
	@PostMapping(path = "/users/ids", consumes = "application/json", produces = "application/json")
	ResponseDto<List<UserInfoVo>> queryUserInfoByIds(@RequestBody RequestDto<UserInfoByUserIdsDto> userInfoByUserIdsDto);


	@PostMapping(value = "/role/dealer/user", produces = "application/json")
	@ResponseBody
	String getManagerByRoleCode(@RequestBody Map<String,Object> requestMap);

	@PostMapping(value = "/emp/ids", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	MidResponse<List<EmpDetailsDto>> queryEmpDetailsByIds(@RequestBody UsedCarGetManagerByRoleCodeMapDto requestDto);


	/**
	 * 组织角色查询员工
	 * @param getOrgUserDTO
	 * @return
	 */
	@PostMapping(path = "/users/org/role", consumes = "application/json", produces = "application/json")
	ResponseDto<List<UserOrgInfoDTO>> getOrgUser(@RequestBody RequestDto<GetOrgUserDTO> getOrgUserDTO);
	/**
	 * 根据经销商代码查询可用角色
	 * @param companyCode
	 * @return
	 */
	@GetMapping(path = "/role/companyCode/{companyCode}", consumes = "application/json", produces = "application/json")
	ResponseDto<List<RoleInfoDto>> queryRoleListByCompanyCode(@PathVariable("companyCode") String companyCode);

}
