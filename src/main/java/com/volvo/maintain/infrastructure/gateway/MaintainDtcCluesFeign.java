package com.volvo.maintain.infrastructure.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.*;
import com.volvo.maintain.application.maintainlead.dto.dtcclues.infoInherit.DtcCluesCategoryInheritDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "domain-maintain-leads")
public interface MaintainDtcCluesFeign {
    @PostMapping(path = "/dtcClues/generateCategoryList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "DTC线索生成类别列表")
    DmsResponse<Page<DtcCluesCategoryInheritDto>> generateCategoryList(@RequestBody DtcCluesCategoryListDto listRequestDto);

    @PostMapping(path = "/dtcClues/insertCluesCategory", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "新增线索种类")
    DmsResponse<Integer> insertCluesCategory(@RequestBody DtcCluesCategoryDto dtcCluesCategoryDto);

    @PostMapping(path = "/dtcClues/importCluesCategory", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "导入线索种类")
    DmsResponse<Integer> importCluesCategory(List<DtcCluesCategoryDto> categoryDtoList);

    @PostMapping(path = "/dtcClues/updateCluesCategory", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "编辑线索种类")
    DmsResponse<Integer> updateCluesCategory(@RequestBody DtcCluesCategoryDto dtcCluesCategoryDto);

    @GetMapping("/dtcClues/deleteCluesCategory")
    @ApiOperation(value = "删除线索种类")
    DmsResponse<Integer> deleteCluesCategory(@RequestParam(name = "ecu") String ecu, @RequestParam(name = "dtc") String dtc);

    @GetMapping("/dtcClues/queryCategoryPriority")
    @ApiOperation(value = "查询DTC生成类别中已被使用的优先级集合")
    DmsResponse<List<Integer>> queryCategoryPriority();

    @PostMapping(path = "/dtcClues/shieldCategoryList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "DTC线索屏蔽类别列表")
    DmsResponse<Page<DtcCluesCategoryInheritDto>> shieldCategoryList(@RequestBody DtcCluesCategoryListDto listRequestDto);

    @PostMapping(path = "/dtcClues/insertShieldCategory", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "新增屏蔽种类")
    DmsResponse<Integer> insertShieldCategory(@RequestBody DtcCluesCategoryDto dtcCluesCategoryDto);

    @PostMapping(path = "/dtcClues/importShieldCategory", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "导入屏蔽种类")
    DmsResponse<Integer> importShieldCategory(List<DtcCluesCategoryDto> dtcCluesCategoryDtoList);

    @PostMapping(path = "/dtcClues/updateShieldCategory", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "编辑屏蔽种类")
    DmsResponse<Integer> updateShieldCategory(@RequestBody DtcCluesCategoryDto dtcCluesCategoryDto);

    @GetMapping("/dtcClues/deleteShieldCategory")
    @ApiOperation(value = "删除屏蔽种类")
    DmsResponse<Integer> deleteShieldCategory(@RequestParam(name = "ecu") String ecu, @RequestParam(name = "dtc") String dtc);

    @GetMapping("/dtcClues/queryRelationEcuDtc")
    @ApiOperation(value = "ECU与DTC的联动下拉框")
    DmsResponse<Map<String, List<EcuDtcRelationDto>>> queryRelationEcuDtc(@RequestParam(name = "category") Integer category);
}
