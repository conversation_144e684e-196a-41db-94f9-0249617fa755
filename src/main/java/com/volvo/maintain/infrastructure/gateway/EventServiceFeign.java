package com.volvo.maintain.infrastructure.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.QueryCustomInfoDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.RemindRuleDetailDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.RemindRuleDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.RuleInfoDto;
import com.volvo.maintain.application.maintainlead.vo.workshop.CustomUserInfoVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.RemindRuleDetailVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.RemindRuleVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.SpecialVehicleConfigVo;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 事件服务Feign调用
 * @Date 2024/11/15 17:40
 */

@FeignClient(name = "eventcenter-service")
public interface EventServiceFeign {

    /**
     * 分页查询规则列表
     * @param remindRuleDto
     * @return
     */
    @PostMapping(value = "/remindRule/v1/queryRulePage")
    DmsResponse<Page<RemindRuleVo>> queryRulePage(@RequestBody RemindRuleDto remindRuleDto);


    /**
     * 查询规则详情
     * @param remindRuleDetailDto
     * @return
     */
    @PostMapping(value = "/remindRule/v1/queryRuleDetail")
    DmsResponse<RemindRuleDetailVo> queryRuleDetail(@RequestBody RemindRuleDetailDto remindRuleDetailDto);

    /**
     * 分页查询自定义人员信息
     * @param queryCustomInfoDto
     * @return
     */
    @PostMapping(value = "/remindRule/v1/queryCustomUserInfoList")
    DmsResponse<Page<CustomUserInfoVo>> queryCustomUserInfoList(@RequestBody QueryCustomInfoDto queryCustomInfoDto);

    /**
     * 分页查询特殊车辆配置信息
     * @param queryCustomInfoDto
     * @return
     */
    @PostMapping(value = "/remindRule/v1/querySpecialVehicleConfigList")
    DmsResponse<Page<SpecialVehicleConfigVo>> querySpecialVehicleConfigList(@RequestBody QueryCustomInfoDto queryCustomInfoDto);

    /**
     * 保存规则
     * @param ruleInfoDto
     * @return
     */
    @PostMapping(value = "/remindRule/v1/saveRuleInfo")
    DmsResponse<Void> saveRuleInfo(@RequestBody RuleInfoDto ruleInfoDto);

    /**
     *
     * 查询通用配置
     * @return
     */
    @GetMapping(value = "/remindRule/v1/queryCommonConfigList")
    DmsResponse<Map<String, List<CommonConfigDto>>> queryCommonConfigList();

}
