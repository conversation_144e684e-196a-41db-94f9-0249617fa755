package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.equityInfo.EmpowerDetailDTO;
import com.volvo.maintain.application.maintainlead.dto.equityInfo.EmpowerDetailQueryDTO;
import com.volvo.maintain.application.maintainlead.dto.rights.DealerCarRightsDto;
import com.volvo.maintain.interfaces.vo.rights.DealerCarRightsVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "car-rights-service",url = "${baseUrl.carRightsService}")
public interface CarRightsServiceFeign {

    @PostMapping(path = "/dealer/rights/list")
    ResponseDto<List<DealerCarRightsVo>> dealerRightsList(@RequestBody DealerCarRightsDto carDealerRightsDto);
    /**
     * 查询车辆权益信息(“质保”/“道路救援”/“二手车”/“保险”)
     */
    @PostMapping(path = "/userRightsManage/getEmpowerDetailList")
    ResponseDto<List<EmpowerDetailDTO>> getEmpowerDetailList(@RequestBody EmpowerDetailQueryDTO dto);
}
