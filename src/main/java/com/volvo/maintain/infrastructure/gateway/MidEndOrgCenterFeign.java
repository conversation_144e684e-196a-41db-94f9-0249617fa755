package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.CompanyDetailByCodeDto;
import com.volvo.maintain.application.maintainlead.dto.IsExistByCodeDto;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.company.CompanyNewSelectDto;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.DealerDTO;
import com.volvo.maintain.application.maintainlead.vo.DealerVO;
import com.volvo.maintain.infrastructure.gateway.request.CompanySelectDTO;
import com.volvo.maintain.infrastructure.gateway.response.CompanyDetailDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "mid-end-org-center", url = "${baseUrl.midEndOrgCenter}")
public interface MidEndOrgCenterFeign {


    /**
     * 根据经销商code查询经销商信息
     */
    @PostMapping(path = "/org/company/selectByCompanyCode", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseDto<List<CompanyDetailByCodeDto>> selectByCompanyCode(@RequestBody IsExistByCodeDto isExistByCodeDtO);

    /**
     * 根据经销商code查询经销商信息
     */
    @PostMapping(path = "/org/company/selectCompanyInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    RestResultResponse<List<CompanyDetailDTO>> selectByCompanyCode(@RequestBody CompanySelectDTO dto);

    /**
     * 根据经销商code查询经销商信息
     */
    @PostMapping(path = "/org/company/companyInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    RestResultResponse<List<CompanyDetailDTO>> companyInfo(@RequestBody CompanyNewSelectDto dto);


    /**
     * 分页查询经销商
     * @param dto
     * @return
     */
    @PostMapping(path = "/org/company/page", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    RestResultResponse<DealerVO.DealerPageInfo> getDealerList(@RequestBody DealerDTO dto);



    /**
     * 多条件查询经销商信息 （主要是用来查询所有非终止运营的经销商）
     */
    @PostMapping(path = "/org/company/companyInfo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseDto<List<CompanyDetailByCodeDto>> selectCompanyByStatus(@RequestBody CompanySelectDTO dto);

}
