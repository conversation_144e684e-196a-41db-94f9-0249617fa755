package com.volvo.maintain.infrastructure.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.RequestDto;
import com.volvo.maintain.application.maintainlead.dto.TmVehicleDto;
import com.volvo.maintain.application.maintainlead.dto.carebuy.SaveCareBuyedDto;
import com.volvo.maintain.application.maintainlead.dto.rights.UsageDetailsResponseDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.VehicleFuelVo;
import com.volvo.maintain.interfaces.vo.MidVehicleVo;
import com.volvo.maintain.interfaces.vo.CheckVehicleExistsVo;
import com.volvo.maintain.interfaces.vo.carebuy.CareBuyedVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(name = "mid-end-vehicle-center",url = "${baseUrl.midEndVehicleCenter}")
public interface MidEndVehicleCenterFeign {

    //车辆相关信息(查询ByVIN)
    @GetMapping(path = "/vehicle/vin/{vin}", consumes = "application/json", produces = "application/json")
    DmsResponse<TmVehicleDto> getVehicleByVIN(@PathVariable("vin") String vin);

    //权益购买记录+权益套餐(分页查询)==查询保养活动使用工单
    @PostMapping(path = "/careBuyed/all", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<SaveCareBuyedDto>> careBuyedAll(@RequestBody RequestDto requestDTO);

    //权益购买记录+权益套餐(分页查询)==查询保养活动使用工单
    @PostMapping(path = "/careBuyed/page", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<CareBuyedVo>> careBoughtPage(@RequestBody RequestDto requestDTO);

    //权益购买记录+权益套餐(分页查询)==查询保养活动使用工单
    @PostMapping(path = "/vehicle/listOwnerVehiclePage", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Page<MidVehicleVo>> listOwnerVehiclePage(@RequestBody RequestDto requestDTO);

    //中台 校验车辆是否存在CHECK_VEHICLE_EXISTS_URL
    @PostMapping(path = "/vehicle/vinList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<CheckVehicleExistsVo> vehicleVinList(@RequestBody RequestDto requestDTO);

    //车辆相关信息(查询ByVIN)
    @GetMapping(path = "/vehicle/getVehicleByVin/{vin}", consumes = "application/json", produces = "application/json")
    DmsResponse<TmVehicleDto> queryVehicleByVIN(@PathVariable("vin") String vin);

    //权益使用记录(所有查询)
    @PostMapping(path = "/careRecord/all", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<UsageDetailsResponseDto>> careRecordList(@RequestBody RequestDto requestDTO);

    @PostMapping("/vehicle/fuel/vehicleFuelType")
    DmsResponse<List<VehicleFuelVo>> vehicleFuelType(@RequestBody List<String> vin);
}
