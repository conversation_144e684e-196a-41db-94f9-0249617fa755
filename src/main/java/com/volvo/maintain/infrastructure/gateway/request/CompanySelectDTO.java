package com.volvo.maintain.infrastructure.gateway.request;

import com.volvo.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 经销商多条件查询（中台）
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-20
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "经销商查询条件", description = "经销商查询条件")
public class CompanySelectDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "经销商主键ID")
    private String companyId;

    @ApiModelProperty(value = "经销商代码")
    private String companyCode;

    @ApiModelProperty(value = "经销商名称中文")
    private String companyNameCn;

    @ApiModelProperty(value = "省份(可多个,用','分隔)")
    private String provinceId;

    @ApiModelProperty(value = "城市(可多个,用','分隔)")
    private String cityId;

    @ApiModelProperty(value = "区县(可多个,用','分隔)")
    private String countyId;

    @ApiModelProperty(value = "销售大区(可多个,用','分隔)")
    private String salesBigArea;

    @ApiModelProperty(value = "销售小区(可多个,用','分隔)")
    private String salesSmallArea;

    @ApiModelProperty(value = "售后大区(可多个,用','分隔)")
    private String afterBigArea;

    @ApiModelProperty(value = "售后小区(可多个,用','分隔)")
    private String afterSmallArea;

    @ApiModelProperty(value = "公司类型:15061001:销售公司;15061002:经销商集团;15061003:经销商公司")
    private String companyType;

    @ApiModelProperty(value = "组织id")
    private String orgId;

    private List<Integer> statusList;

    public CompanySelectDTO(String afterBigArea, String afterSmallArea, String companyCode) {
        this.companyCode = companyCode;
        this.afterBigArea = afterBigArea;
        this.afterSmallArea = afterSmallArea;
        this.companyType = "15061003";
    }
}
