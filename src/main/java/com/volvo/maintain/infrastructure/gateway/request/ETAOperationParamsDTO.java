package com.volvo.maintain.infrastructure.gateway.request;

import java.math.BigDecimal;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ETAOperationParamsDTO {

	/**
	 * 经销商
	 */
    private String ownerCode;
    /**
     * 采购订单号
     */
    private String purchaseNo;
    
    /**
     * 采购单明细ID
     */
    private Long purchaseOrderDetailId;

    /**
     * 零件号
     */
    private String partNo;
    
    /**
     * 替换零件号
     */
    private String replacePartNo;

    /**
     * 单据号
     */
    private String sheetNo;
    
    /**
     * 缺料id
     */
    private Long shortId;
    
    /**
     * 采购单入库关联单据双清数据
     */
    private List<Long> shortIds;
    
    /**
     * 非关联零件数量
     */
    private BigDecimal partQuantity;
    
    /**
     * 关联零件数
     */
    private BigDecimal linkedPartQuantity;
    
    
    /**
     * 是否关联
     */
    private String isLinked;
    
    /**
     * 场景code
     */
    private String sceneCode;
    
    
    /**
     * 发送时间戳
     */
    private Long msgTimeStamp;
}
