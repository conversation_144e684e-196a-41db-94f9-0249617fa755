package com.volvo.maintain.infrastructure.gateway.request;


import com.volvo.maintain.infrastructure.gateway.response.VehicleOwnerVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 接收前台打印参数
 *
 * <AUTHOR>
 * @date 2020-08-19
 */
@ApiModel("工单/结算单打印接收前台数据VO")
@Data
public class PrintParamVo {
    // 经销商信息
    @ApiModelProperty(value = "打印单-工单号", name = "roNo")
    private String roNo;
    @ApiModelProperty(value = "打印单-结算单号", name = "boNo")
    private String boNo;
    @ApiModelProperty(value = "打印单-估价单号", name = "estimateNo")
    private String estimateNo;
    @ApiModelProperty(value = "打印单-预约单号", name = "bookingOrderNo")
    private String bookingOrderNo;
    @ApiModelProperty(value = "打印单-收费对象", name = "payObj")
    private String payObj;
    @ApiModelProperty(value = "打印单-收费对象-去零金额", name = "paySub")
    private String paySub;
    @ApiModelProperty(value = "打印单-收费对象-圆整金额", name = "payYz")
    private String payYz;
    @ApiModelProperty(value = "打印单-打印类型(RO:工单 BO:结算单 YO:预约单 EO:估价单 EI:延保服务合同 US:使用服务合同打印 BS:服务合同购买)", name = "printType")
    private String printType;
    @ApiModelProperty(value = "打印单-工单组号", name = "repairGroupNoList")
    private List<String> repairGroupNoList;
    @ApiModelProperty(value = "打印单-结算单组号", name = "balanceGroupNoList")
    private List<String> balanceGroupNoList;
    @ApiModelProperty(value = "打印单-打印单地区", name = "printRegion")
    private String printRegion;
    @ApiModelProperty(value = "是否打印零件（1004）", name = "isPrintLj")
    private String isPrintLj;
    @ApiModelProperty(value = "质检单单号", name = "qcRoNo")
    private String qcRoNo;
    @ApiModelProperty(value = "经销商code", name = "ownerCode")
    private String ownerCode;

    /**
     * 构建查询条件
     * @param balanceNoList 结算单号
     * @param orderInfo 工单信息
     * @param dealerCode 经销商代码
     * @return 查询参数
     */
    public static PrintParamVo buildParams(List<String> balanceNoList, VehicleOwnerVO orderInfo, String dealerCode){

        PrintParamVo paramsVo = new PrintParamVo();
        paramsVo.setOwnerCode(dealerCode);
        paramsVo.setPaySub(orderInfo.getSubObbAmount());
        paramsVo.setPayYz(orderInfo.getYzAmaount());
        paramsVo.setPrintType("BO");
        paramsVo.setPayObj(orderInfo.getPaymentObjectCode());
        paramsVo.setPrintRegion("标准");
        paramsVo.setRepairGroupNoList(Collections.singletonList(orderInfo.getRoNo()));
        paramsVo.setBalanceGroupNoList(balanceNoList);

        return paramsVo;
    }
}
