package com.volvo.maintain.infrastructure.gateway.request;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <p>
 * 零附件采购订单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Data
public class PartPurchaseOrderDetaileRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 经销商代码
     */
    private List<String> partNos;
    
    /**
     * 经销商代码
     */
    private String dealerCode;
    
    
}
