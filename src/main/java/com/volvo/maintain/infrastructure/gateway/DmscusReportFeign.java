package com.volvo.maintain.infrastructure.gateway;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.FullLeadQueryDto;
import com.volvo.maintain.application.maintainlead.dto.VipCustomPageReqDTO;
import com.volvo.maintain.application.maintainlead.dto.VipGroupImportDTO;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CareBoughtQueryDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.*;
import com.volvo.maintain.application.maintainlead.dto.rights.GiveRecordRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.GiveRecordResponseDto;
import com.volvo.maintain.application.maintainlead.vo.CareBoughtVO;
import com.volvo.maintain.application.maintainlead.vo.FullLeadVo;
import com.volvo.maintain.application.maintainlead.vo.QueryVehicleOwnerDataVo;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 功能描述：报表服务
 *
 * <AUTHOR>
 * @since 2024/01/25
 */
@FeignClient(name = "dmscus-report")
public interface DmscusReportFeign {

    /**
     * 功能描述：查询邀约线索列表(厂端)
     *
     * @param queryParams 全量线索查询dto对象
     * @return Page<FullLeadVo> 全量线索查询结果
     */
    @PostMapping("/fullLeads/list")
    @ApiOperation(value = "查询邀约线索列表")
    RestResultResponse<Page<FullLeadVo>> queryFullLeadsList(@RequestBody FullLeadQueryDto queryParams);
    /**
     * <AUTHOR>
     * @ 根据vin集合   查询车主车辆信息
     */
    @PostMapping("/toolsStockQuery/selectVehicleOwnerInfo")
    DmsResponse<QueryVehicleOwnerDataVo> selectVehicleOwnerInfoByList(@RequestBody List<String> vinList);

    @PostMapping("/api/equity/resource")
    DmsResponse<Page<CareBoughtVO>> queryEquityDetail(@RequestBody CareBoughtQueryDTO queryCareBoughtDTO);

    /**
     * 导出数据 厂店端 共用（登录信息区分厂店端）
     * @param queryCareBoughtDTO 导出条件
     */
    @PostMapping("/api/equity/export/excel")
    DmsResponse<Void> exportCareBoughtData(@RequestBody CareBoughtQueryDTO queryCareBoughtDTO);

    /**
     * 延保理赔申请列表导出
     */
    @PostMapping("/warranty/approval/export")
    DmsResponse<Void> exportWarrantyApprovalList(@RequestBody ClaimApplyPageReqDTO claimApplyPageReqDTO);

    /**
     * 延保理赔申请列表查询
     */
    @PostMapping("/warranty/approval/list")
    DmsResponse<Page<ClaimApplyPageRespDTO>> warrantyApprovalList(ClaimApplyPageReqDTO claimApplyPageReqDTO);

    /**
     * 延保回款查询列表导出
     */
    @PostMapping(value = "/warranty/approval/reportList")
    DmsResponse<List<warrantyReturnPageDTO>> reportList(@RequestBody warrantyReturnReqDTO dto);

    /**
     * 延保回款明细导出
     */
    @GetMapping(value = "/warranty/approval/reportDetailList")
    DmsResponse<List<warrantyReturnDetailPageDTO>> reportDetailList(@RequestParam("returnIds") List<Long> returnIds);


    /**
     * 重点客户组别维护列表导出
     */
    @PostMapping("/vip/custom/export")
    DmsResponse<Void> vipCustomPageExport(@RequestBody VipCustomPageReqDTO reqDTO);

    /**
     * 重点客户组别维护列表查询
     */
    @PostMapping("/vip/custom/page")
    DmsResponse<Page<VipGroupImportDTO>> vipCustomPage(@RequestBody VipCustomPageReqDTO reqDTO);


    /**
     * 保养套餐购买记录
     * @param giveRecordRequestDto 查询条件
     */
    @PostMapping("/api/equity/give-list/interf")
    DmsResponse<Page<GiveRecordResponseDto>> queryGiveList(@RequestBody GiveRecordRequestDto giveRecordRequestDto);
}
