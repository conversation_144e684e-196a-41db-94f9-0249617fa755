package com.volvo.maintain.infrastructure.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.bookingOrder.BookingOrderDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.BookingOrderParamDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.MissingPartsStatusDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.ShortPartItemDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.ShortPartItemWeComDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.VehicleEntranceCountDto;
import com.volvo.maintain.application.maintainlead.service.workshop.VehicleEntranceParamsVO;
import com.volvo.maintain.application.maintainlead.vo.workshop.BookingOrderVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.ShortPartItemVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.VehicleEntranceParamsVo;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 功能描述：报表服务
 *
 */
@FeignClient(name = "dmscloud-report")
public interface DmscloudReportFeign {

    /**
     * 缺料明细导出查询
     */
    @GetMapping("/replenishment/queryShortPart")
    RestResultResponse<List<Map>> queryShortPart(@RequestParam Map<String, String> map);

    @PostMapping("/replenishment/weCom/queryShortPartItem")
    DmsResponse<Page<ShortPartItemWeComDto>> weComQueryShortPartItem(@RequestBody ShortPartItemVo shortPartItemVo);

    @PostMapping("/replenishment/weCom/queryShortPartItemCount")
    DmsResponse<List<MissingPartsStatusDto>> weComQueryShortPartStatusCount(@RequestBody ShortPartItemVo shortPartItemVo);

    @GetMapping("/replenishment/weCom/queryShortPartDetail")
    DmsResponse<List<ShortPartItemDto>> weComQueryShortPartDetail(@RequestParam String ownerCode, @RequestParam String sheetNo);

    /**
     * 获取 未分拨，已分拨 全部数量
     * @return 数量对象
     */
    @PostMapping(value = "/replenishment/weCom/queryAllocatedCount", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<VehicleEntranceCountDto> queryAllocatedCount(@RequestBody VehicleEntranceParamsVo vehicleEntranceVO);
    
    
    /**
     * 查询预约单（weCom）
     * @param bookingOrderVo 查询参数
     * @return 分页集
     */
    @PostMapping("/replenishment/queryBookingOrder")
    DmsResponse<Page<BookingOrderDto>> queryBookingWeCom(@RequestBody BookingOrderVo bookingOrderVo);

    @PostMapping("/replenishment/queryBookingOrderCount")
    DmsResponse<BookingOrderParamDto> queryBookingOrderCount(@RequestBody BookingOrderVo bookingOrderVo);

    @PostMapping("/weCom/queryVehicleEntranceList")
    Page<VehicleEntranceParamsVO> queryVehicleEntranceList(@RequestBody VehicleEntranceParamsVO vehicleEntranceVO);
}
