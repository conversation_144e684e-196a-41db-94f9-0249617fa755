package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.vo.CarOwnerRelationVo;
import com.volvo.maintain.infrastructure.gateway.response.ImResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "volvo-vehicle-car-owner",url = "${baseUrl.vehicleCarOwner}")
public interface VehicleCarOwnerFeign {

    //根据vin获取所有绑定列表
    @GetMapping(path = "/api/vehicle/owner/relation/list", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ImResponse<List<CarOwnerRelationVo>> getRelationList(@RequestParam String vin);

}