package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.accidentclue.InsuranceBillDto;
import com.volvo.maintain.application.maintainlead.dto.clues.InviteInsuranceVehicleRecordExtDto;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceBillListDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;

import com.volvo.maintain.interfaces.vo.InsuranceBillVo;
import io.swagger.annotations.ApiOperation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "domain-insurance-leads")
public interface DomainInsuranceLeadsFeign {

    @GetMapping("/insurance/leads/bill/interf")
    @ApiOperation("通过保险单号查询续保的保单信息")
    DmsResponse<List<InsuranceBillDto>> getInsuranceBill(@RequestParam(value = "insuranceNo", required = false) String insuranceNo,
        @RequestParam("vin") String vin);

    /**
     * 根据车架号和经销商查询未完成线索和报单人信息
     * @param vin 车架号
     * @param ownerCode 经销商
     * @param tab 标记 是否走 查询保单信息
     * @return 返回构建的报单人信息对象
     */
    @GetMapping("/renewalOfInsurance/policyholder")
    DmsResponse<InviteInsuranceVehicleRecordExtDto> queryPolicyholder(@RequestParam String vin, @RequestParam String ownerCode, @RequestParam Boolean tab);


    /**
     * 查询商业险起止日期
     */
    @GetMapping(value = "/insurance/leads/selectInsureHistory")
    DmsResponse<List<InsuranceBillListDto>> selectInsureHistory(@RequestParam String vin);

    /**
     * 根据vin和ownerCode 查询最新的保单信息(本地+易保)
     * @param vin
     * @param ownerCode
     * @return
     */
    @GetMapping("/insurance/leads/lastInsureInfo/interf")
    DmsResponse<InsuranceBillListDto> lastInsureInfo(@RequestParam(value = "vin") String vin, @RequestParam(value = "ownerCode") String ownerCode);


    /**
     * 查询交强险
     * @param vin 车架号
     * @return 返回最近一次交强险日期
     */
    @GetMapping("/insurance/leads/clivtaInsurance")
    DmsResponse<InsuranceBillVo> queryClivtaInsuranceByVin(@RequestParam String vin);
}
