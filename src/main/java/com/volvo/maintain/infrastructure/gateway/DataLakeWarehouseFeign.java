package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.icup.DataLakeWarehouseIcupMileageDto;
import com.volvo.maintain.infrastructure.gateway.response.ImResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

//湖仓
@FeignClient(name = "dataLakeWarehouse", url = "${baseUrl.dataLakeWarehouseUrl}")
public interface DataLakeWarehouseFeign {


    @GetMapping(value = "/dl/v1/api/getVehMileage")
    ImResponse<DataLakeWarehouseIcupMileageDto> getVehMileage(@RequestParam("veh_vin") String veh_vin);




}
