package com.volvo.maintain.infrastructure.gateway.response;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 配件缺料记录DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-11
 */
@Data
public class ShortPartDto implements Serializable{

    private static final long serialVersionUID = 1L;
	
    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 缺料记录ID
     */
    private Long shortId;

    /**
     * 仓库代码
     */
    private String storageCode;
    /**
     * 配件代码
     */
    private String partNo;
    /**
     * 配件名称
     */
    private String partName;

    /**
     * 库位代码
     */
    private String storagePositionCode;

    /**
     * 出入库类型
     */
    private Integer inOutType;

    /**
     * 缺料类型
     */
    private Integer shortType;

    /**
     * 单据号码
     */
    private String sheetNo;

    /**
     * 是否已结案
     */
    private Integer closeStatus;

    /**
     * 是否急件
     */
    private Integer isUrgent;

    /**
     * 车牌号
     */
    private String license;
    /**
     * 缺件数量
     */
    private BigDecimal shortQuantity;
    /**
     * 经手人
     */
    private String handler;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 电话
     */
    private String phone;
    
    private String itemUpdateStatus;//A新增 U修改或 D删除 S未修改
    private Date sendTime;

    /**
     * 是否BO订单
     */
    private Integer isBo;

    /**
     * ELINK单号
     */
    private String elinkNo;

    /**
     * 是否上报
     */
    private Integer isUpload;

    /**
     * 订货状态
     */
    private Integer orderGoodsStatus;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 运单号
     */
    private String transNo;

    /**
     * 是否完成
     */
    private Integer isAchieve;  
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 是否关联
     */
    private Integer isLinked;
    
    /**
     * 是否关联
     */
    private Long purchaseOrderDetailId;
    
    /**
     * 是否删除
     */
    private Integer isDeleted;
}
