package com.volvo.maintain.infrastructure.gateway.request;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.volvo.dto.BaseDTO;
import com.volvo.maintain.application.maintainlead.dto.FullLeadsCallDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * <p>
 * SA呼叫登记
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@Slf4j
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "邀约线索AI外呼接口参数", description = "邀约线索AI外呼接口参数")
public class SaCustomerNumberDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 邀约ID
     */
    private Long inviteId;

    /**
     * call_id
     */
    private String callId;

    /**
     * 服务顾问ID
     */
    private String saId;

    /**
     * 客户名称
     */
    private String cusName;

    /**
     * 客户电话
     */
    private String cusNumber;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 服务顾问姓名
     */
    private String saName;

    /**
     * 服务顾问手机号
     */
    private String saNumber;

    /**
     * AI语音工作号
     */
    private String workNumber;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 功能描述：构建邀约通话记录保存参数
     *
     * @param dto 全量线索保存参数
     * @return 保存参数
     */
    public static SaCustomerNumberDTO buildSaveParam(FullLeadsCallDto dto) {

        SaCustomerNumberDTO param = new SaCustomerNumberDTO()
                .setInviteId(dto.getId())
                .setSaId(dto.getSaId())
                .setCusName(dto.getCusName())
                .setCusNumber(dto.getCusNumber())
                .setBatchNo(dto.getBatchNo())
                .setCallId(dto.getCallId());

        log.info("构建邀约通话记录保存参数：{}", JSONObject.toJSONString(param));
        return param;
    }
}
