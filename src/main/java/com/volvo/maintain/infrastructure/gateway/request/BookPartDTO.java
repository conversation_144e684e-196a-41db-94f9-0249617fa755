package com.volvo.maintain.infrastructure.gateway.request;

import java.util.Date;
import java.util.List;

public class BookPartDTO {
    private String obligatedNo;//零件预留单号

    private String roNo;//单据号

    private String partNo; //零件编号

    private String applicant;//预留人

    private String ownerName;//客户名称/车主

    private String license;//车牌号

    private String obligatedDateBegin;//预留日期-开始
    private String obligatedDateEnd;//预留日期-结束

    private String owenNo;

    private String biaoji;

    private String isObligated;

    private String itemId;

    private String part;

    private String obligatedReason;

    private String stor;

    private String deliverer;

    private String delivererMobile;

    private String delivererPhone;

    private String serviceAdvisor;

    private Date obligatedCloseDate;

    private String remark;

    private String itemIds;

    private String isYuLiu;

    private List tables;

    private String pageNum;
    private String limit;
    private String pageSize;


    public String getPartNo() {
        return partNo;
    }

    public void setPartNo(String partNo) {
        this.partNo = partNo;
    }

    public String getObligatedDateBegin() {
        return obligatedDateBegin;
    }

    public void setObligatedDateBegin(String obligatedDateBegin) {
        this.obligatedDateBegin = obligatedDateBegin;
    }

    public String getObligatedDateEnd() {
        return obligatedDateEnd;
    }

    public void setObligatedDateEnd(String obligatedDateEnd) {
        this.obligatedDateEnd = obligatedDateEnd;
    }

    public String getPageNum() {
        return pageNum;
    }

    public void setPageNum(String pageNum) {
        this.pageNum = pageNum;
    }

    public String getLimit() {
        return limit;
    }

    public void setLimit(String limit) {
        this.limit = limit;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getPart() {
        return part;
    }

    public void setPart(String part) {
        this.part = part;
    }

    public String getStor() {
        return stor;
    }

    public void setStor(String stor) {
        this.stor = stor;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getBiaoji() {
        return biaoji;
    }

    public void setBiaoji(String biaoji) {
        this.biaoji = biaoji;
    }

    public String getIsObligated() {
        return isObligated;
    }

    public void setIsObligated(String isObligated) {
        this.isObligated = isObligated;
    }

    public String getOwenNo() {
        return owenNo;
    }

    public void setOwenNo(String owenNo) {
        this.owenNo = owenNo;
    }

    public String getItemIds() {
        return itemIds;
    }

    public void setItemIds(String itemIds) {
        this.itemIds = itemIds;
    }

    public String getObligatedNo() {
        return obligatedNo;
    }

    public void setObligatedNo(String obligatedNo) {
        this.obligatedNo = obligatedNo;
    }


    public String getRoNo() {
        return roNo;
    }


    public void setRoNo(String roNo) {
        this.roNo = roNo;
    }


    public String getApplicant() {
        return applicant;
    }


    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }


    public String getOwnerName() {
        return ownerName;
    }


    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }


    public String getLicense() {
        return license;
    }


    public void setLicense(String license) {
        this.license = license;
    }


    public String getDeliverer() {
        return deliverer;
    }


    public void setDeliverer(String deliverer) {
        this.deliverer = deliverer;
    }


    public String getDelivererMobile() {
        return delivererMobile;
    }


    public void setDelivererMobile(String delivererMobile) {
        this.delivererMobile = delivererMobile;
    }


    public String getDelivererPhone() {
        return delivererPhone;
    }


    public void setDelivererPhone(String delivererPhone) {
        this.delivererPhone = delivererPhone;
    }


    public String getServiceAdvisor() {
        return serviceAdvisor;
    }


    public void setServiceAdvisor(String serviceAdvisor) {
        this.serviceAdvisor = serviceAdvisor;
    }


    public Date getObligatedCloseDate() {
        return obligatedCloseDate;
    }


    public void setObligatedCloseDate(Date obligatedCloseDate) {
        this.obligatedCloseDate = obligatedCloseDate;
    }


    public String getRemark() {
        return remark;
    }


    public void setRemark(String remark) {
        this.remark = remark;
    }


    public List getTables() {
        return tables;
    }


    public void setTables(List tables) {
        this.tables = tables;
    }

    public String getObligatedReason() {
        return obligatedReason;
    }

    public void setObligatedReason(String obligatedReason) {
        this.obligatedReason = obligatedReason;
    }

    public String getIsYuLiu() { return isYuLiu; }

    public void setIsYuLiu(String isYuLiu) { this.isYuLiu = isYuLiu; }
}
