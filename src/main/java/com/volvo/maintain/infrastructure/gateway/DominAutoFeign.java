package com.volvo.maintain.infrastructure.gateway;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.CarPickupCarPhotosResDto;
import com.volvo.maintain.application.maintainlead.dto.CarPickupDeliveryBalanceResDto;
import com.volvo.maintain.application.maintainlead.dto.RequestDto;
import com.volvo.maintain.application.maintainlead.dto.TaskDeliverCarRequestDto;
import com.volvo.maintain.application.maintainlead.dto.TaskDeliverCarResponseDto;
import com.volvo.maintain.application.maintainlead.dto.VehicleDeliverDTO;
import com.volvo.maintain.application.maintainlead.dto.VehicleOrderPageDto;
import com.volvo.maintain.application.maintainlead.dto.VehicleOrderResponseDtoDto;
import com.volvo.maintain.application.maintainlead.dto.order.CancelOrderDto;
import com.volvo.maintain.application.maintainlead.dto.order.DriverDto;
import com.volvo.maintain.application.maintainlead.dto.order.FindMerchantPriceDTO;
import com.volvo.maintain.application.maintainlead.dto.order.PlaceOrderDto;
import com.volvo.maintain.application.maintainlead.dto.order.PriceDto;
import com.volvo.maintain.application.maintainlead.dto.order.QueryVehicleDeliverDto;
import com.volvo.maintain.application.maintainlead.dto.order.ShopVehicleDeliverDto;
import com.volvo.maintain.application.maintainlead.vo.order.DetailVo;
import com.volvo.maintain.application.maintainlead.vo.order.DriverInfoVo;
import com.volvo.maintain.application.maintainlead.vo.order.DriverVo;
import com.volvo.maintain.application.maintainlead.vo.order.PlaceOrderVo;
import com.volvo.maintain.application.maintainlead.vo.order.PriceVo;
import com.volvo.maintain.application.maintainlead.vo.order.ShopVehicleDeliverVo;
import com.volvo.maintain.application.maintainlead.vo.order.TraceVo;
import com.volvo.maintain.application.maintainlead.vo.order.VehicleDeliverDetailVo;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;

@FeignClient(name = "domin-auto",url = "${baseUrl.dominAuto}")
public interface DominAutoFeign {

    @PostMapping(path = "/vehicleDeliver/page", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<Page<VehicleOrderResponseDtoDto>> queryVehicleOrder(@RequestBody VehicleOrderPageDto vehicleOrderPageDto);

    //查询取送车订单详情(所有)
    @PostMapping(path = "/vehicleDeliver/allDetail", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<VehicleDeliverDTO>> allDetail(RequestDto data);
    //创建取送车订单
    @PostMapping(path = "/vehicleDeliver", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<TaskDeliverCarResponseDto> vehicleDeliver(@RequestBody RequestDto<TaskDeliverCarRequestDto> requestDtoRequestDto);
    //编辑取送车订单
    @PutMapping(path = "/vehicleDeliver", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<Integer> updateVehicleDeliver(@RequestBody RequestDto<TaskDeliverCarRequestDto> requestDtoRequestDto);

    //店端查询取送车订单(分页)
    @PostMapping(path = "/vehicleDeliver/shopSearchVehiicleDeliverPage", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<Page<ShopVehicleDeliverVo>> shopSearchVehiicleDeliverPage(@RequestBody RequestDto<ShopVehicleDeliverDto> shopVehicleDeliverDto);

    //取送车订单取消
    @PostMapping(path = "/vehicleDeliver/cancelVehicleOrder", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<Boolean> cancelVehicleOrder(@RequestBody RequestDto<CancelOrderDto> cancelOrderDtoRequestDto);

    //e代驾下单
    @PostMapping(path = "/vehicleDeliver/placeOrder", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<PlaceOrderVo> placeOrder(@RequestBody RequestDto<PlaceOrderDto> placeOrderDtoRequestDto);

    //店端查询取送车订单(无分页)
    @PostMapping(path = "/vehicleDeliver/shopSearchVehiicleDeliverList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<ShopVehicleDeliverVo>> shopSearchVehiicleDeliverList(@RequestBody RequestDto<ShopVehicleDeliverDto> shopVehicleDeliverDtoRequestDto);

    //查询取送车订单详情(所有)
    @PostMapping(path = "/vehicleDeliver/allDetail", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<VehicleDeliverDetailVo>> allDetails(@RequestBody RequestDto<QueryVehicleDeliverDto> queryVehicleDeliverDtoRequestDto);

    //获取订单详情
    @GetMapping(value = "/business/balance/customerId/{customerId}")
    MidResponse<BigDecimal> balanceCustomerId(@PathVariable("customerId") String customerId);

    //获取附近驻店司机列表
    @PostMapping(path = "/business/getFixList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<DriverVo>> getFixList(@RequestBody RequestDto<DriverDto> driverDtoRequestDto);

    //获取预估价格
    @PostMapping(path = "/business/price", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<PriceVo> price(@RequestBody RequestDto<PriceDto> priceDtoRequestDto);

    //获取订单详情
    @GetMapping(value = "/order/detail/orderId/{orderId}")
    MidResponse<DetailVo> detailOrderId(@PathVariable("orderId") String orderId);

    //获取司机代驾轨迹
    @GetMapping(value = "/order/trace/orderId/{orderId}/type/{type}")
    MidResponse<TraceVo> traceOrderId(@PathVariable("orderId") String orderId, @PathVariable("type") Integer type);

    //获取司机信息
    @GetMapping(value = "/order/driverInfo/orderId/{orderId}/type/{type}")
    MidResponse<DriverInfoVo> driverInfoOrderId(@PathVariable("orderId") String orderId, @PathVariable("type") Integer type);

    //获取车辆信息照片
    @GetMapping(value = "/order/getCarPhotos/orderId/{orderId}/daijiaType/{daijiaType}")
    MidResponse<Map<String, Object>> getCarPhotosOrderId(@PathVariable("orderId") String orderId, @PathVariable("daijiaType") Integer daijiaType);
    
    // 获取滴滴订单预估价格
    @PostMapping(value = "/business/order/estimatePrice", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<PriceVo>> estimatePrice(@RequestBody PriceDto priceDtoRequestDto);

    // 获取滴滴商户金额
    @PostMapping(value = "/business/balance/findMerchantPrice", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<List<CarPickupDeliveryBalanceResDto>> findMerchantPrice(FindMerchantPriceDTO priceDtoRequestDto);
    
    // 验车图片
    @GetMapping(value = "/order/getCarPhotos/{orderId}", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidResponse<CarPickupCarPhotosResDto> getCarPhotos(@PathVariable("orderId") String orderId);
    
}
