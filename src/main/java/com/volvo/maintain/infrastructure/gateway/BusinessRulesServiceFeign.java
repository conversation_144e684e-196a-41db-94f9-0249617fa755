package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.RequestDto;
import com.volvo.maintain.application.maintainlead.dto.ResponseDto;
import com.volvo.maintain.application.maintainlead.dto.equityInfo.*;
import com.volvo.maintain.application.maintainlead.dto.rights.DealerCarRightsDto;
import com.volvo.maintain.interfaces.vo.rights.DealerCarRightsVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 终身权益信息查询
 */
@FeignClient(value = "business-rules-service",url = "${baseUrl.businessRulesService}")
public interface BusinessRulesServiceFeign {


    /**
     * 查询车辆是否是终身保养权益
     */
    @PostMapping(path = "/business/equities/queryList")
    ResponseDto<RewardSendResponseVO> getLifetimeMaintenance(@RequestBody RequestDto<RecordDetailedListDTO> dto);
}
