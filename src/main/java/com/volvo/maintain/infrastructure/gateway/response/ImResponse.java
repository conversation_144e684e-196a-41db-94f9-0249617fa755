package com.volvo.maintain.infrastructure.gateway.response;

import com.volvo.maintain.infrastructure.constants.CommonConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * im 数据返回
 */
@Data
public class ImResponse<T>  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 返回代码，0表示成功，其他表示失败
     */
    private String code;

    /**
     * 返回描述
     */
    private String msg;

    private String msgId;

    /**
     * 返回数据
     */
    private T data;

    public boolean isSuccess() {
        return Objects.equals(this.getCode(), CommonConstant.SUCCESS_CODE);
    }

    public boolean isFail() {
        return !isSuccess();
    }
}
