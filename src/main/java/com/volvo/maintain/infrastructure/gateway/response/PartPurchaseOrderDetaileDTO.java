package com.volvo.maintain.infrastructure.gateway.response;

import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 零附件采购订单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Data
public class PartPurchaseOrderDetaileDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 零件号
     */
    private String partNo;

    /**
     * 订单数量（零件）
     */
    private BigDecimal orderQuantity;

    /**
     * 采购单主键
     */
    private Long purchaseOrderId;
    
    
    /**
     * 关联工单号
     */
    private String linkedNumber;
    
    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 经销商
     */
    private String ownerCode;

    /**
     * 采购订单明细主键ID;
     */
    private Long id;

    /**
     * 经销商  (eta使用)
     */
    private String outCustomerNo;

    /**
     * 采购单  (eta使用)
     */
    private String nbCode;


    /**
     * 缺件状态
     */
    private Long missingPartsStatus;

    /**
     * 预计到货时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    private String expectedDeliveryTime;

    /**
     * 零件状态,替换件;block
     */
    private Long partsStatus;

    /**
     * 替换件号
     */
    private String replaceParts;

    /**
     * 缺料日期
     */
    private String createdAt;

    private String partName;

    /**
     * 缺件数量
     */
    private BigDecimal shortQuantity;
    
}
