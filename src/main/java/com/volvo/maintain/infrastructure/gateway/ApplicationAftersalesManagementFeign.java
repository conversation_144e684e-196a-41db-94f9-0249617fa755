package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.PushMessageRecordDto;
import com.volvo.maintain.application.maintainlead.dto.QwJSSDKDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(name = "application-aftersales-management")
public interface ApplicationAftersalesManagementFeign {
    /**
     * 功能描述：推送EM90专属管家
     *
     * @param content 推送内容
     */
    @GetMapping(value = "/qw/pushQwExclusiveButler/notToken")
    DmsResponse<QwJSSDKDto> pushQwExclusiveButler(@RequestParam String vin,@RequestParam String content);
    /**
     * 功能描述：查询服务顾问id
     *
     * @param vin
     */
    @GetMapping(value = "/qw/getVehicleByUserId/notToken")
    DmsResponse<String> getVehicleByUserId(@RequestParam String vin);

    /**
     * 公共方法 发送记录
     * @param recordDto 消息记录
     */
    @PostMapping(value = "/EM90/commonPushMessage/notToken")
    DmsResponse<Void> pushMessage(@RequestBody PushMessageRecordDto recordDto);
}
