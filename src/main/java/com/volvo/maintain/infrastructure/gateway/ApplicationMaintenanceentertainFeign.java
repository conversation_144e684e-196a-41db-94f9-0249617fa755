package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.workshop.VehicleEntranceCountDto;
import com.volvo.maintain.application.maintainlead.vo.workshop.VehicleEntranceVO;
import com.volvo.maintain.infrastructure.gateway.response.ImResponse;
import com.volvo.maintain.interfaces.vo.VehicleEntranceVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient(name = "application-maintenanceentertain",url = "${baseUrl.applicationMaintenanceentertainUrl}")
public interface ApplicationMaintenanceentertainFeign {
    /**
     * 查询未分拨车辆
     * @param dealerCode 经销商
     * @param licensePlate 车牌号
     *
     */
    @GetMapping(value = "/vehicle-reception-entrance/api/v1/queryUnallocatedVehicleList")
    ImResponse<VehicleEntranceVo> queryUnallocatedVehicleList(@RequestParam String dealerCode, @RequestParam String licensePlate,@RequestParam String entryTime);

    /**
     * 获取 未分拨，已分拨 全部数量
     * @return 数量对象
     */
    @PostMapping("/api/v1/queryAllocatedCount-weCom")
    ImResponse<VehicleEntranceCountDto> queryAllocatedCount(@RequestBody VehicleEntranceVO vehicleEntranceVO);
}
