package com.volvo.maintain.infrastructure.gateway;

import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.dto.RestResultResponse;
import com.volvo.maintain.application.maintainlead.dto.BalanceAccountsExportTempDTO;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigDto;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigQueryDto;
import com.volvo.maintain.application.maintainlead.dto.CustomerInfoDto;
import com.volvo.maintain.application.maintainlead.dto.ExtWarPurgiveDataVO;
import com.volvo.maintain.application.maintainlead.dto.PadVehiclePreviewDto;
import com.volvo.maintain.application.maintainlead.dto.PageDto;
import com.volvo.maintain.application.maintainlead.dto.PurchaseConditionsParamDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderDto;
import com.volvo.maintain.application.maintainlead.dto.SettlementEventDto;
import com.volvo.maintain.application.maintainlead.dto.WarrantyMaintenanceReminderListDTO;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CareBuyedExtDto;
import com.volvo.maintain.application.maintainlead.dto.carebuy.CheckedCareBuyeDUseScopeVo;
import com.volvo.maintain.application.maintainlead.dto.coupon.LoyalCouponDto;
import com.volvo.maintain.application.maintainlead.dto.reminder.WarrantyMaintenanceReminderDTO;
import com.volvo.maintain.application.maintainlead.dto.repairAssign.TtRoAssignDTO;
import com.volvo.maintain.application.maintainlead.dto.rights.OrderActivationDetailsResponseDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcNoDto;
import com.volvo.maintain.application.maintainlead.vo.DeliverVo;
import com.volvo.maintain.application.maintainlead.vo.PadVehiclePreviewResultVo;
import com.volvo.maintain.application.maintainlead.vo.PurchaseConditionsResultVo;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderVO;
import com.volvo.maintain.infrastructure.annotation.ErrorCodeConversion;
import com.volvo.maintain.infrastructure.gateway.request.BookPartDTO;
import com.volvo.maintain.infrastructure.gateway.request.PartItemDTO;
import com.volvo.maintain.infrastructure.gateway.request.PrintParamVo;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.PartMasterFileParamsVo;
import com.volvo.maintain.infrastructure.gateway.response.PrintDataVo;
import com.volvo.maintain.infrastructure.gateway.response.VehicleOwnerVO;
import com.volvo.maintain.interfaces.vo.CommonConfigVO;
import com.volvo.maintain.interfaces.vo.ModelVO;
import com.volvo.maintain.interfaces.vo.OwnerInfoResultsVo;
import com.volvo.maintain.interfaces.vo.OwnerQueryParamsVo;
import com.volvo.maintain.interfaces.vo.RepairOrderHistoryParamsVo;
import com.volvo.maintain.interfaces.vo.RepairOrderHistoryResultVo;
import com.volvo.maintain.interfaces.vo.VehicleConnectingOwnersInfoVo;
import com.volvo.maintain.interfaces.vo.booking.BookingOrderResponseVo;

import io.swagger.annotations.ApiOperation;


@FeignClient(name = "dmscloud-service")
public interface DmscloudServiceFeign {

	/**
	 * 根据VIN查询车辆信息
	 */
	@GetMapping(value = "/CustomerReception/queryVehicleInfoByParams/interf")
	DmsResponse<PageDto> queryVehicleInfoByParam(@RequestParam String vin,@RequestParam String ownerCode);

	/**
	 * 查询全部车型
	 */
	@GetMapping(value = "/midEnd/basicData/model/interf")
	DmsResponse<List<ModelVO>> model();

	/**
	 * 经销商白名单
	 */
	@GetMapping(value = "/whitelist/checkWhitelist")
	DmsResponse<Boolean> checkWhitelist(@RequestParam String ownerCode, @RequestParam Integer modType, @RequestParam Integer rosterType, @RequestParam String vin);

	/**
	 * 经销商白名单
	 */
	@GetMapping(value = "/vehicleHealthCheck/selectWhitelistByOwnerCode/customerInterAspect")
	DmsResponse<Boolean> customerInterAspect(@RequestParam String ownerCode, @RequestParam Integer modType, @RequestParam Integer rosterType);


	/**
	 * 查询自店车主信息
	 */
	@GetMapping(value = "/customerId/queryCustomerInfo")
	DmsResponse<CustomerInfoDto> queryCustomerInfo(@RequestParam String vin, @RequestParam String ownerCode);

	/**
	 * 查询送修人
	 */
	@GetMapping(value = "/CustomerReception/getVehicleConnectInfoByVin")
	DmsResponse<VehicleConnectingOwnersInfoVo> getVehicleConnectInfoByVin(OwnerQueryParamsVo paramsVo);

	/**
	 * 查询忠诚守候卡券
	 */
	@GetMapping(value = "/loyalCoupon/selectAllLoyalCoupon")
	DmsResponse<List<LoyalCouponDto>> selectAllLoyalCoupon();


	@GetMapping(value = "order/repair/queryRepairOrderInfoByVinAndOwnerCode")
	DmsResponse<List<RepairOrderDto>> queryRepairOrderInfoByVinAndOwnerCode(@RequestParam Map<String, String> queryParam);

    /**
     * 故障灯工单查询
     *
     * @param ownerCode 经销商代码
     * @param vin       vin
     * @param roNo      工单号
     * @return 工单信息
     */
    @GetMapping("/faultLightApi/queryRepairOrder")
    RestResultResponse<List<VehicleOwnerVO>> queryRepairOrder(@RequestParam(value = "ownerCode") String ownerCode,
                                                              @RequestParam(value = "vin") String vin,
                                                              @RequestParam(value = "roNo") String roNo);

    /**
     * 工单金额查询
     */
    @PostMapping("/printBalanceRo/balancePrintDataInner")
	RestResultResponse<PrintDataVo> balancePrintData(@RequestBody PrintParamVo paramsVo);
	/**
     * 查询EM90预约单
     */
	@GetMapping(value = "/BookingRegister/queryEm90AppointmentOrder/interf")
    DmsResponse<BookingOrderResponseVo> queryEm90AppointmentOrder(@RequestParam String startTime, @RequestParam String endTime, @RequestParam int modelCode);

	/**
	 * 根据分组和KEY获取配置信息
	 */
	@GetMapping(value = "/common/config")
	DmsResponse<CommonConfigVO> getConfigByKey(@RequestParam CommonConfigQueryDto query);


	/**
	 * 根据分组和KEY获取配置信息
	 */
	@GetMapping(value = "/common/config/list")
	DmsResponse<List<CommonConfigVO>> getConfigList(@RequestParam String groupType, @RequestParam String configKey);

	/**
	 * 车主信息查询
	 */
	@PostMapping(value = "/padVehiclePreviewApi/queryOwnerVehicle/interf")
	DmsResponse<PadVehiclePreviewResultVo> queryOwnerVehicle(@RequestBody PadVehiclePreviewDto padVehiclePreviewDto);
	/**
	 * 功能描述：查询当前车辆在本店中是否有未结算的维修工单（非PDS）
	 *
	 * @param ownerCode 经销商信息
	 * @param vin 车架号
	 * @return RepairOrderVO 工单信息
	 */
	@GetMapping(value = "/order/repair/queryUnsettledRepairOrder")
	DmsResponse<RepairOrderVO> queryUnsettledRepairOrder(@RequestParam String ownerCode, @RequestParam String vin);
	/**
	 * 查询未上工位的维修工单
	 *
	 */
	@GetMapping("/repairAssign/order/notOnWorkShop")
    DmsResponse<List<RepairOrderVO>> notOnWorkShopOrder();

	/**
	 * 查询DMS默认参数
	 */
	@GetMapping(value = "order/repair/queryDmsDefaultParam")
	DmsResponse<CustomerInfoDto> queryDmsDefaultParam(@RequestParam String cdpTagIdKey);

	@ApiOperation("获取客户旅程")
	@GetMapping("order/repair/queryCustomerJourney")
	DmsResponse<Page<RepairOrderHistoryResultVo>> queryCustomJourney(@RequestParam(value = "current", required = false) Integer current, @RequestParam(value = "size", required = false) Integer size, @SpringQueryMap RepairOrderHistoryParamsVo paramsVo);

	@ApiOperation("获取客户旅程")
	@GetMapping("/common/config")
	DmsResponse<CommonConfigDto> selectCommonConfig(@RequestParam String configKey,@RequestParam String groupType);

	/**
	 * 经销商白名单
	 */
	@GetMapping(value = "/protectingCustomersMarket/judgeIsMasterAndExchangePurchase")
	DmsResponse<Boolean> judgeIsMasterAndExchangePurchase(@RequestParam String vin, @RequestParam String delivererMobile, @RequestParam String roNo);
	/**
	 * 根据分组和KEY获取配置信息
	 */
	@GetMapping(value = "/common/config")
	DmsResponse<CommonConfigDto> getConfigByKey(@RequestParam String configKey,@RequestParam String groupType);
	/**
	 * 插入打印表数据
	 */
	@GetMapping(value = "/balanceAccountsPrint/rinseBalanceAccountsPrint")
	DmsResponse rinseBalanceAccountsPrint(@RequestBody SettlementEventDto eventDto);


	/**
	 * 维修业务主查询
	 */
	@GetMapping(value = "/repair/business/findAll")
	@ApiOperation(value="维修业务查询", notes="获取系统中满足查询条件的维修业务数据")
	DmsResponse<Page<Map<String,String>>> findAll(@RequestParam Map<String,String> queryParams);


	@ApiOperation(value = "入库打印temp表数据", notes = "入库打印temp表数据")
	@PostMapping("/balanceAccountsPrint/rinseBalanceAccountsTemp")
	void rinseBalanceAccountsTemp(@RequestBody List<BalanceAccountsExportTempDTO> list);


	@ApiOperation(value = "下载中心下载", notes = "下载中心下载")
	@GetMapping("/balanceAccountsPrint/balanceAccountsDownload")
	void balanceAccountsDownload(@RequestParam String exportId,@RequestParam String ownerCode);

	/**
	 * 记录送修人异常工单
	 * @param roNo 工单号
	 * @param traceTime 回访时间类型
	 * @return map 返回结果
	 */
	@PutMapping("/GiveCar/alterTraceTime/{roNo}/{traceTime}")
    DmsResponse<Map<String, Object>> saveReceiveMoney(@PathVariable("roNo") String roNo, @PathVariable("traceTime") String traceTime);

	/**
	 * 添加记录
	 * @param deliverVo 送修人异常记录
	 * @return 返回影响行数
	 */
	@PostMapping("/deliverOrder/insertBoundVehicleOrder")
	DmsResponse<Integer> saveBoundVehicleOrder(@RequestBody DeliverVo deliverVo);

	/**
	 * 添加记录
	 * @param deliverVo 送修人异常记录
	 * @return 返回影响行数
	 */
	@PostMapping("/deliverOrder/deleteBoundVehicleOrder")
	DmsResponse<Integer> deleteBoundVehicleOrder(@RequestBody DeliverVo deliverVo);


	/**
	 * 取消交车
	 * @param roNo 工单号
	 * @return map
	 */
	@PutMapping("/GiveCar/getVehicle/{roNo}")
	DmsResponse<Map<String, Object>> getVehicle(@PathVariable("roNo") String roNo);

	/**
	 * 根据时间查询送修人手机号次数
	 */
	@PostMapping(value = "/deliverOrder/queryDeliverMobileNumber",produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	DmsResponse<Integer> queryDeliverMobileNumber(@RequestBody DeliverVo deliverVo);

	/**
	 * 根据时间查询送修人手机号次数
	 */
	@GetMapping(value = "/collaborativeInterfaceApi/getOwnerInfo")
	DmsResponse<OwnerInfoResultsVo> getOwnerInfo(@RequestParam("vin") String vin, @RequestParam("ownerCode") String ownerCode);

	/**
	 * 校验服务合同权益
	 */
	@GetMapping(value = "/CustomerReception/checkCareBuyedUseScope/{id}")
	DmsResponse<CheckedCareBuyeDUseScopeVo> checkUseScope(@PathVariable Long id);

	@PostMapping(value = "/CustomerReception/careBuyedUseScope/list")
	DmsResponse<List<CareBuyedExtDto>> careBuyedUseScopeList(@RequestBody List<CareBuyedExtDto> careBuyedExtDtos);
	/**
	 * 延保消息提醒查询
	 */
	@GetMapping(value = "/reminders/getWarrantyMaintenanceReminder")
	DmsResponse<WarrantyMaintenanceReminderDTO> getReminder(@RequestParam(value = "ownerCode", required = false) String ownerCode, @RequestParam(value = "vin") String vin, @RequestParam(value = "roNo") String roNo);;

	@PostMapping("CustomerReception/purchaseConditions/interAspect")
	DmsResponse<List<PurchaseConditionsResultVo>> purchaseConditions(@RequestBody PurchaseConditionsParamDto purchaseConditionsParamDTO);

	/**
	 * 服务合同购买记录查询
	 * @param
	 * @return
	 */
	@PostMapping(path = "/CustomerReception/getGiveStatusByGiveId/interAspect")
	DmsResponse<List<OrderActivationDetailsResponseDto>> getGiveStatusByGiveId(@RequestBody List<String> giveIds);


    /**
     * 批量查询零件主档属性
     */
    @GetMapping("/partMasterFile/getInfoByPartNo")
    DmsResponse<List<PartMasterFileParamsVo>> getInfoByPartNo(@RequestParam("partNo") String partNo);


    /**
     * 预留
     */
    @PutMapping(value="/bookpart/hold")
    DmsResponse<Map<String,Object>> performExecutes(@RequestBody BookPartDTO bookPartDTO);


    @GetMapping(value = "/bookpart/queryPartItem")
    DmsResponse<PageDto> queryPartItem(PartItemDTO partItemDTO);


	/**
	 * 生成检查单或报价单号
	 */
	@ApiOperation(value = "生成检查单或报价单号", notes = "生成检查单或报价单号")
	@GetMapping(value = "/vhc/createVhcNo")
	DmsResponse<VhcNoDto> createVhcNo(
			@RequestParam(value = "ownerCode") String ownerCode,
			@RequestParam(value = "code") String code);

	/**
	 * 车辆燃料类型查询
	 */
	@ApiOperation(value = "车辆燃料类型查询", notes = "车辆燃料类型查询" )
	@RequestMapping(value = "/vhc/getFuelTypeByVin", method = RequestMethod.GET)
	DmsResponse<VhcNoDto> getFuelTypeByVin(@RequestParam("vin") String vin);

	/**
	 * 车辆燃料类型查询
	 */
	@ApiOperation(value = "主页面质检按钮校验", notes = "主页面质检按钮校验" )
	@RequestMapping(value = "/repairAssign/Verification/{id}/{isPad}", method = RequestMethod.GET)
	@ErrorCodeConversion
	DmsResponse<Void> verification(@PathVariable(value = "id") String id,@PathVariable(value = "isPad") String isPad);


	@GetMapping(value = "/lucencyWorkshop/endTimeSupposedMsg")
	@ApiOperation(value = "修改交车时间通知 ", notes = "修改交车时间通知")
	void endTimeSupposedMsg(@RequestParam(value = "ownerCode",required = false)String ownerCode,
								   @RequestParam(value = "roNo",required = false)String roNo,
								   @RequestParam(value = "endTimeSupposed",required = false)String endTimeSupposed);

	/**
	 * 查询车辆延保提醒消息
	 */
	@ApiOperation(value = "查询车辆延保提醒消息", notes = "查询车辆延保提醒消息")
	@GetMapping(value = "/reminders/getWarrantyMaintenanceReminderList")
	DmsResponse<List<WarrantyMaintenanceReminderListDTO>> getWarrantyMaintenanceReminderList(
			@RequestParam(value = "vin") String vin);
	
	
    @ApiOperation(value = "车主车辆页面延保信息", notes = "车主车辆页面延保信息 ", httpMethod = "GET")
    @RequestMapping(value = "/customerCare/ownerVehicleManagement/FindExtrusiveData",method = RequestMethod.GET)
    DmsResponse<List<ExtWarPurgiveDataVO>> findExtrusiveData(@RequestParam("vin") String vin);
    
    /**
     * 竣工,整單派工
     */
    @PutMapping(value = "/repairAssign/v2/maintainRepair")
    DmsResponse<Void> maintainRepairAssignComplete(@RequestBody List<TtRoAssignDTO> dtoList);
    
    @GetMapping(value = "/repairAssign/queryRepairAssignzj")
    DmsResponse<PageDto<Map>> queryRepairAssignzj(@SpringQueryMap Map<String, String> queryParam);
    
}



