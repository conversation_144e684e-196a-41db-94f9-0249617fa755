package com.volvo.maintain.infrastructure.gateway;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.CommonConfigDto;
import com.volvo.maintain.application.maintainlead.dto.CustomerInfoDto;
import com.volvo.maintain.application.maintainlead.dto.HandoverInspectionDetailDto;
import com.volvo.maintain.application.maintainlead.dto.MessageConfirmDTO;
import com.volvo.maintain.application.maintainlead.dto.MessageResultDTO;
import com.volvo.maintain.application.maintainlead.dto.OrderTagSnapshotDto;
import com.volvo.maintain.application.maintainlead.dto.RecentRepairOrderDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderAndClaimTypeReqDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderAndClaimTypeRespDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderEntryTimeDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderExtDto;
import com.volvo.maintain.application.maintainlead.dto.RepairOrderReqDTO;
import com.volvo.maintain.application.maintainlead.dto.ReportReasonDto;
import com.volvo.maintain.application.maintainlead.dto.SettlementDocConfirmCompensateDto;
import com.volvo.maintain.application.maintainlead.dto.VehicleCheckReminderDto;
import com.volvo.maintain.application.maintainlead.dto.VehicleCheckReminderReqDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.BeginOrderDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.DeliveryDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.QueryParamDto;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.SignQuantityDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.StatusCountDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.WorkShopBalanceOrderDTO;
import com.volvo.maintain.application.maintainlead.dto.LucencyWorkshop.WorkShopRepairOrderDTO;
import com.volvo.maintain.application.maintainlead.dto.accidentclue.AccidentCluesExportDto;
import com.volvo.maintain.application.maintainlead.dto.bookingOrder.BookingOrderDto;
import com.volvo.maintain.application.maintainlead.dto.customermarketing.OwnerInfoDTO;
import com.volvo.maintain.application.maintainlead.dto.healthcheck.HealthCheckDto;
import com.volvo.maintain.application.maintainlead.dto.order.RepairOrderBatchQueryDto;
import com.volvo.maintain.application.maintainlead.dto.part.PartDto;
import com.volvo.maintain.application.maintainlead.dto.part.PartOrderDto;
import com.volvo.maintain.application.maintainlead.dto.part.RoRepairOrderPartUpdateStatusDto;
import com.volvo.maintain.application.maintainlead.dto.part.ShortPartDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.NoRepairItemDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.QueryVhcDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VehicleHealthCheckDetailDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VehicleHealthCheckDetailParamDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VehicleHealthCheckInfoDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcConfirmRepairDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcDetailsDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcInfoDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcInfoPoDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcItemConfigInfoDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcItemPoDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcMaintanceReqDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcNotRepairReqDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcPricesheetDetailsDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcQueryLabourDto;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcQueryLabourVo;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcQuotedDTO;
import com.volvo.maintain.application.maintainlead.dto.vhc.VhcRemindDTO;
import com.volvo.maintain.application.maintainlead.dto.workshop.BookingOrderParamDto;
import com.volvo.maintain.application.maintainlead.vo.FinalInspectionContentVO;
import com.volvo.maintain.application.maintainlead.vo.FinalInspectionTreeVO;
import com.volvo.maintain.application.maintainlead.vo.RepairOrderV2VO;
import com.volvo.maintain.application.maintainlead.vo.RoHandRepairProjectVO;
import com.volvo.maintain.application.maintainlead.vo.healthcheck.VehicleHealthRecordInfoVo;
import com.volvo.maintain.application.maintainlead.vo.healthcheck.VehicleHealthVo;
import com.volvo.maintain.application.maintainlead.vo.order.OrderServiceInfoVO;
import com.volvo.maintain.application.maintainlead.vo.workshop.BookingOrderVo;
import com.volvo.maintain.application.maintainlead.vo.workshop.ETADocumentParamsVo;
import com.volvo.maintain.infrastructure.annotation.ErrorCodeConversion;
import com.volvo.maintain.infrastructure.gateway.request.UpdateETAMappingDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ShortPartDto;
import com.volvo.maintain.interfaces.vo.CheckFinalInspectionReqVO;
import com.volvo.maintain.interfaces.vo.CheckFinalInspectionRespVO;
import com.volvo.maintain.interfaces.vo.FinalInspectionAttVO;
import com.yonyou.cyx.framework.dto.ResponseDTO;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@FeignClient(name = "domain-maintain-orders")
public interface DomainMaintainOrdersFeign {

    /**
     * 通过VIN查询最近一次创建的工单（除PDS、零售以外）的经销商信息
     */
    @GetMapping("/maintain/order/recent/interf")
    @ApiOperation("通过VIN查询最近一次创建的工单（除PDS、零售以外）的经销商")
    DmsResponse<RecentRepairOrderDto> getRecentMaintainOrder(@RequestParam String vin);

    /**
     * 查询默认参数
     */
    @GetMapping("/maintain/order/queryDmsDefaultParam/interf")
    DmsResponse<CustomerInfoDto> queryDmsDefaultParam(@RequestParam("cdpTagIdKey") String cdpTagIdKey);

    /**
     * 查询默认参数
     */
    @GetMapping("/maintain/order/queryDmsDefaultParam2/interf")
    DmsResponse<CommonConfigDto> queryDmsDefaultParam2(@RequestParam("cdpTagIdKey") String cdpTagIdKey, @RequestParam("groupType") String groupType);

    /**
     * 批量查询养修工单
     */
    @PostMapping("/maintain/order/batch")
    DmsResponse<List<RepairOrderDto>> batchQueryMaintainOrder(@RequestBody List<AccidentCluesExportDto> query);

    /**
     * 批量查询预约单信息
     */
    @PostMapping("/maintain/order/booking/order/batch")
    DmsResponse<List<BookingOrderDto>> batchQueryBookingOrder(@RequestBody List<AccidentCluesExportDto> query);


    @GetMapping(value = "/maintain/order/queryRepairOrderInfoByVinAndOwnerCode")
    DmsResponse<List<RepairOrderDto>> queryRepairOrderInfoByVinAndOwnerCode(@RequestParam Map<String, String> queryParam);

    @GetMapping(path = "/vehicleCheckReminder/isDataByOwnerCodeRoNo", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Boolean> isEm90Delivery(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo);

    @GetMapping(path = "/vehicleCheckReminder/selectRepairOrderEntryTime", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<RepairOrderEntryTimeDto> selectRepairOrderEntryTime(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo);

    //新增交车校验报告记录表
    @PostMapping(value = "/vehicleCheckReminder/insertHandoverInspectionDetail", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Long> insertHandoverInspectionDetail(@RequestBody HandoverInspectionDetailDto handoverInspectionDetailDto);

    //新增交车校验提醒
    @PostMapping(value = "/vehicleCheckReminder/insertVehicleCheckReminder", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Long> insertVehicleCheckReminder(@RequestBody VehicleCheckReminderDto vehicleCheckReminderDto);

    //批量新增交车校验提醒
    @PostMapping(value = "/vehicleCheckReminder/batchInsertVehicleCheckReminder", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> batchInsertVehicleCheckReminder(@RequestBody List<VehicleCheckReminderDto> vehicleCheckReminderDto);

    //提交未提交报告原因
    @PostMapping(value = "/vehicleCheckReminder/submitReportReason", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse submitReportReason(@RequestBody ReportReasonDto reportReasonDto);

    //查询重点客户标签提醒
    @GetMapping("/vehicleCheckReminder/queryEmphasisClientRemind")
    DmsResponse<Boolean> queryEmphasisClientRemind(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo);
    @PostMapping("/vehicleCheckReminder/queryEmphasisClientRemind")
    DmsResponse<List<VehicleCheckReminderDto>> queryEmphasisClientRemind(@RequestBody VehicleCheckReminderReqDTO reqDTO);

    /**
     * 根据手机号查询 意向记录
     *
     * @param mobile 手机号
     * @return 意向结果集
     */
    @GetMapping(value = "/market/clue/mobile")
    DmsResponse<List<OrderTagSnapshotDto>> queryOrderTagSnapshotList(@RequestParam(required = false) String mobile,
                                                                     @RequestParam(required = false) String vin);

    /**
     * 编辑时是否重点客户弹窗-前置条件：环检单转工单
     */
    @GetMapping("/vehicleCheckReminder/whetherPopUpVipCustom")
    DmsResponse<Boolean> whetherPopUpVipCustom(@RequestParam("roNo") String roNo);
    /**
     * 批量查询未入库工单的零件总数
     */
    @PostMapping("/maintain/order/queryPartTotal")
    DmsResponse<List<PartOrderDto>> queryPartTotal(@RequestBody List<String> parts);
    /**
     * 根据itemId查询维修零件明细修改前数量
     */
    @PostMapping("/maintain/order/queryPartInfo")
    DmsResponse<PartOrderDto> queryPartInfo(@RequestParam("itemId") Long itemId);
    /**
     * 批量查询未入库工单的零件总数
     */
    @PostMapping("/maintain/order/queryPartClaimPrice")
    DmsResponse<List<PartOrderDto>> queryPartClaimPrice(@RequestBody List<String> parts);
    /**
     * 缺料明细处理业务
     */
    @PostMapping("/maintain/order/saveOrUpdateShortPart")
    DmsResponse<Void> saveOrUpdateShortPart(@RequestBody List<ShortPartDTO> shortPartDTOList, @RequestParam(value = "roNo",required = false) String roNo);

    @GetMapping("/maintain/order/userIds")
    DmsResponse<List<String>> queryServiceAdvisorList(@RequestParam("ownerCode") String ownerCode,@RequestParam("roNoList") List<String> roNoList);

    @GetMapping("/maintain/order/queryShortInfoBySheetNo")
    DmsResponse<List<ShortPartDto>> queryShortInfoBySheetNo(@RequestParam("ownerCode") String ownerCode, @RequestParam("sheetNo") String sheetNo,  @RequestParam("partNo") String partNo, @RequestParam("isDelete") Integer isDelete);


    /**
     * 非关联映射数据关联缺料映射关系更新
     * @param etaMappingReturn
     * @return
     */
    @PostMapping("/maintain/order/updateShortInfoLinked")
    DmsResponse<Boolean> updateShortETAMappingLinked(@RequestBody UpdateETAMappingDTO updateETAMappingDTO);

    @PostMapping("/maintain/order/batchShortInfoBySheetNo")
    DmsResponse<List<ShortPartDto>> batchShortInfoBySheetNo(@RequestBody List<ShortPartDto> shortPartDtoList);

    /**
     * 批量查询缺料信息通过关联采购单id集合
     * @param purchaseDetaileIds
     * @return
     */
    @PostMapping("/maintain/order/queryShortInfoByPurchaseDetaileIds")
    DmsResponse<List<ShortPartDto>> queryShortInfoByPurchaseDetaileIds(@RequestBody List<Long> purchaseDetaileIds);

    /**
     * VHC-报价-维修项信息查询
     */
    @GetMapping(value = "/workOrder/queryMaintenanceItems")
    DmsResponse<VhcQuotedDTO> queryMaintenanceItems(@RequestParam(value = "vhcNo") String vhcNo, @RequestParam(value = "ownerCode") String ownerCode);

    /**
     * VHC-报价-检查零件是否缺料
     *
     * @return 解析数据
     */
    @GetMapping(value = "/workOrder/checkedPartShort", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Integer> checkedPartShort(@RequestParam(value = "partNo") String partNo, @RequestParam(value = "practicalQuantity") BigDecimal practicalQuantity, @RequestParam(value = "ownerCode") String ownerCode, @RequestParam(value = "storageCode") String storageCode);

    /**
     * VHC-报价-检查零件是否在工单
     *
     * @return 解析数据
     */
    @GetMapping(value = "/workOrder/checkedWorkOrderHavePart", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Integer> checkedWorkOrderHavePart(@RequestParam(value = "partNo", required = false) String partNo, @RequestParam(value = "labourCode", required = false) String labourCode, @RequestParam(value = "roNo") String roNo, @RequestParam(value = "ownerCode") String ownerCode, @RequestParam(value = "vhcCode") String vhcCode, @RequestParam(value = "vhcLabourCode") String vhcLabourCode);

    /**
     * VHC-报价-保存草稿&报价完成
     *
     * @return 解析数据
     */
    @PostMapping(value = "/workOrder/saveMaintenanceItems", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> saveMaintenanceItems(@RequestBody VhcMaintanceReqDTO dto);

    /**
     * VHC-报价-推送用户
     *
     * @return 解析数据
     */
    @PostMapping(value = "/workOrder/pushCustomer")
    DmsResponse<Void> pushCustomer(@RequestParam(value = "vhcNo") String vhcNo, @RequestParam(value = "roNo") String roNo, @RequestParam(value = "ownerCode") String ownerCode, @RequestParam(value = "flag") Integer flag, @RequestParam(value = "kgyIds", required = false) String kgyIds, @RequestParam(value = "itemIds", required = false) String itemIds);


    /**
     * 创建车辆健康检查
     */
    @PostMapping("/vhcRepair/createdVhcInfo")
    @ApiOperation(value = "创建车辆健康检查")
    void createdVhcInfo(@RequestBody VhcInfoDTO vhcInfoDTO);

    /**
     * 校验工单组是否存在vhc套餐code
     */
    @GetMapping("/vhcRepair/verifyOrderGroupVHC")
    @ApiOperation(value = "校验工单组是否存在vhc套餐code")
    DmsResponse<Boolean> verifyOrderGroupVHC(
            @RequestParam(value = "ownerCode") String ownerCode,
            @RequestParam(value = "roNo") String roNo,
            @RequestParam(value = "setCode") String setCode,
            @RequestParam(value = "vin") String vin,
            @RequestParam(value = "labourCode") String labourCode);

    /**
     * VHC-报价-健康检查消息提醒查询
     *
     * @return 解析数据
     */
    @GetMapping(value = "/workOrder/queryVhcRemind")
    DmsResponse<List<VhcRemindDTO>> queryVhcRemind(@RequestParam(value = "userId") Long userId, @RequestParam(value = "ownerCode") String ownerCode);

    /**
     * VHC-健康检查消息已读删除
     *
     * @return 解析数据
     */
    @GetMapping(value = "/workOrder/removeInfoById")
    DmsResponse<Void> removeInfoById(@RequestParam(value = "id") Integer id);

    /**
     * VHC-报价-保存草稿&报价完成
     *
     * @return 解析数据
     */
    @PostMapping(value = "/workOrder/confirmRepair", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<Void> confirmRepair(@RequestBody VhcConfirmRepairDTO dto);

    @PostMapping(value = "/workOrder/saveNotRepair")
    @ApiOperation(value = "保存不修原因")
    DmsResponse<Void> saveNotRepair(@RequestBody VhcNotRepairReqDTO dto);

    /**
     * 检查页面查询
     */
    @PostMapping("/vhcRepair/selectVhcList")
    @ApiOperation(value = "检查页面查询")
    DmsResponse<Page<VhcDetailsDTO>> selectVhcList(@RequestBody QueryVhcDto queryVhcDto);


    /**
     * 报价页面查询
     */
    @PostMapping("/vhcRepair/selectVhcPricesheetList")
    @ApiOperation(value = "报价页面查询")
    DmsResponse<Page<VhcPricesheetDetailsDTO>> selectVhcPricesheetList(@RequestBody QueryVhcDto queryVhcDto);


    /**
     * 查询车辆健康检查详情
     */
    @PostMapping("/vehicleHealthCheck/v1/getVehicleHealthCheckDetail")
    @ApiOperation(value = "查询车辆健康检查详情")
    @ErrorCodeConversion
    DmsResponse<VehicleHealthCheckDetailDto> getVehicleHealthCheckDetail(@RequestBody VehicleHealthCheckDetailParamDto vehicleHealthCheckDetailParamDto);

    /**
     * 根据大类id查询小类信息
     */
    @GetMapping("/vehicleHealthCheck/v1/getVhcItemInfoByClassId")
    @ApiOperation(value = "根据大类id查询小类信息")
    @ErrorCodeConversion
    DmsResponse<List<VhcItemConfigInfoDto>> getVhcItemInfoByClassId(@RequestParam("classId") Integer classId, @RequestParam("configClassId") String configClassId);

    /**
     * 保存车辆健康检查信息
     */
    @PostMapping("/vehicleHealthCheck/v1/saveVehicleHealthCheckInfo")
    @ApiOperation(value = "保存车辆健康检查信息")
    @ErrorCodeConversion
    DmsResponse<Void> saveVehicleHealthCheckInfo(@RequestBody VehicleHealthCheckInfoDto vehicleHealthCheckInfoDto);


    /**
     * 根据车架号经销商查询最近一次检查未修项
     */
    @GetMapping("/vhcRepair/selectNoRepairItem")
    @ApiOperation(value = "根据车架号经销商查询最近一次检查未修项")
    @ErrorCodeConversion
    DmsResponse<List<NoRepairItemDto>> selectNoRepairItem(@RequestParam(value = "ownerCode")String ownerCode,
                                                           @RequestParam(value = "vin") String vin,
                                                          @RequestParam(value = "roNo",required = false) String roNo);

    /**
     * 360客户画像车辆健康检查
     */
    @GetMapping("/vhcRepair/select360VhcItem")
    @ApiOperation(value = "360客户画像车辆健康检查")
    DmsResponse<Map<String, List<VhcItemPoDTO>>> select360VhcItem(@RequestParam(value = "ownerCode",required = false)String ownerCode,
                                                                   @RequestParam(value = "vin") String vin);

    /**
     * 查询工时信息
     */
    @PostMapping("/vhcRepair/queryLabourList")
    @ApiOperation(value = "查询工时信息")
    @ErrorCodeConversion
    DmsResponse<Page<VhcQueryLabourVo>> queryLabourList(@RequestBody VhcQueryLabourDto vhcQueryLabourDto);

    /**
     * 关闭车辆健康检查小类不修原因
     */
    @GetMapping("/vehicleHealthCheck/v1/closeVhcItemInfoByRoNo")
    @ApiOperation(value = "关闭车辆健康检查小类不修原因")
    @ErrorCodeConversion
    DmsResponse<Void> closeVhcItemInfoByRoNo(@RequestParam("roNo") String roNo);

    /**
     * 根据检查单号或者工单号查询检查单信息
     */
    @GetMapping("/vhcRepair/selectVhcInfo")
    @ApiOperation(value = "根据检查单号或者工单号查询检查单信息")
    DmsResponse<VhcInfoPoDTO> selectVhcInfo(
            @RequestParam(value = "ownerCode")String ownerCode,
            @RequestParam(value = "roNo",required = false)String roNo,
            @RequestParam(value="vhcNo",required = false) String vhcNo);

    /**
     * 校验是否推送车辆健康检查报价单
     */
    @GetMapping("/vehicleHealthCheck/v1/checkPushVhcPricesheet")
    @ApiOperation(value = "校验是否推送车辆健康检查报价单")
    @ErrorCodeConversion
    DmsResponse<Boolean> checkPushVhcPricesheet(@RequestParam("roNo") String roNo, @RequestParam("ownerCode") String ownerCode);

    /**
     * 质检确认维修状态
     */
    @GetMapping("/vhcRepair/confirmClassResult")
    @ApiOperation(value = "质检确认维修状态")
    DmsResponse confirmClassResult(
            @RequestParam(value = "ownerCode")String ownerCode,
            @RequestParam(value = "roNo",required = false)String roNo,
            @RequestParam(value="vhcLabourCode",required = false) String vhcLabourCode,
            @RequestParam(value="vhcSetCode",required = false) String vhcSetCode);


    /**
     * 预约单关联未修项目
     */
    @GetMapping("/vhcRepair/vhcConnectBookOrder")
    @ApiOperation(value = "预约单关联未修项目")
    void vhcConnectBookOrder(@RequestParam(value = "ownerCode")String ownerCode,
                                    @RequestParam(value = "bookingOrderNo")String bookingOrderNo,
                                    @RequestParam(value = "vin")String vin);

    /**
     * 查询预约单关联未修项目
     */
    @GetMapping("/vhcRepair/selectVhcConnectBookOrder")
    @ApiOperation(value = "预约单关联未修项目")
    DmsResponse<List<NoRepairItemDto>> selectVhcConnectBookOrder(@RequestParam(value = "ownerCode")String ownerCode,
                                                           @RequestParam(value = "bookingOrderNo")String bookingOrderNo,
                                                           @RequestParam(value = "vin")String vin);

    /**
     * 根据车架号查询健康检查数据分页
     * @param vin 车架号
     * @param pageNum 分页参数
     * @param pageSize 分页参数
     * @return 分页数据
     */
    @GetMapping("/vehicleHealthCheck/list/interf")
    IPage<VehicleHealthVo> queryHealthByVin(@RequestParam("vin") String vin, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize);

    /**
     * 批量vin 查询健康检查单 C端使用
     * @param dto 入参对象
     * @return 要反回的数据
     */
    @PostMapping(value = "/vehicleHealthCheck/details/interf", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse<List<VehicleHealthRecordInfoVo>> queryVehicleHealthCheckDetails(@RequestBody HealthCheckDto dto);

    /**
     * 查询弹窗记录
     * @param ownerCode 经销商code
     * @param businessType 业务类型
     * @param businessId 业务id
     * @return
     */
    @GetMapping("/message/popup/selectPopupRecord")
    DmsResponse<MessageResultDTO> selectPopupRecord(@RequestParam("ownerCode")String ownerCode,
                                                          @RequestParam("businessType")String businessType,
                                                          @RequestParam("businessId")String businessId,
                                                          @RequestParam("vin")String vin);

    /**
     * 记录确认弹窗记录
     * @param dto
     * @return
     */
    @PostMapping("/message/popup/confirm")
    DmsResponse<Void> confirmMessage(@RequestBody MessageConfirmDTO dto);

    /**
     * 批量查询工单信息 基于经销商和工单
     * @param dto ownerCode And roNo
     * @return 工单信息
     */
    @PostMapping("/maintain/order/batch-v2")
    DmsResponse<List<RepairOrderDto>> queryRepairOrderInfo(@RequestBody List<RepairOrderBatchQueryDto> dto);

    /**
     * 修改预计交车时间
     * @param ownerCode
     * @param roNo
     * @return
     */
    @GetMapping("/maintain/order/roStatus")
    DmsResponse<Void> updateRepairOrderStatus(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo, @RequestParam("endTimeSupposed") String endTimeSupposed);


    /**
     * 开单页面查询
     * @param queryParamDto
     * @return
     */
    @PostMapping(value = "/lucencyWorkShop/selectBeginOrderList")
    @ResponseBody
    DmsResponse<Page<BeginOrderDTO>> selectBeginOrderList(@RequestBody QueryParamDto queryParamDto) ;

    /**
     * 查询派工数量
     * @param queryParamDto
     * @return
     */
    @PostMapping(value = "/lucencyWorkShop/selectAssignStatusCount")
    @ResponseBody
    DmsResponse<List<StatusCountDTO>> selectAssignStatusCount(@RequestBody QueryParamDto queryParamDto);
    /**
     * 查询维修页面
     * @param queryParamDto
     * @return
     */
    @PostMapping(value = "/lucencyWorkShop/selectWorkshopRepairOrder")
    @ResponseBody
    DmsResponse<Page<WorkShopRepairOrderDTO>> selectWorkshopRepairOrder(@RequestBody QueryParamDto queryParamDto);

    /**
     * 查询打卡数量
     * @param queryParamDto
     * @return
     */
    @PostMapping(value = "/lucencyWorkShop/selectIsPunchCount")
    @ResponseBody
    DmsResponse<List<StatusCountDTO>> selectIsPunchCount(@RequestBody QueryParamDto queryParamDto);

    /**
     * 查询结算页面
     */
    @PostMapping(value = "/lucencyWorkShop/selectWorkShopBalanceOrder")
    @ResponseBody
    DmsResponse<Page<WorkShopBalanceOrderDTO>> selectWorkShopBalanceOrder(@RequestBody QueryParamDto queryParamDto);

    /**
     * 查询交车数量
     */
    @PostMapping(value = "/lucencyWorkShop/selectDeliveryTagCount")
    @ResponseBody
    DmsResponse<List<StatusCountDTO>> selectDeliveryTagCount(@RequestBody QueryParamDto queryParamDto);


    /**
     * 看版交车列表
     */
    @PostMapping(value = "/lucencyWorkShop/selectDeliveryList")
    @ResponseBody
    DmsResponse<Page<DeliveryDTO>> selectDeliveryList(@RequestBody QueryParamDto queryParamDto);


    /**
     * 查询预约单（weCom）
     * @param bookingOrderVo 查询参数
     * @return 分页集
     */
    @PostMapping("/maintain/order/queryBookingOrder")
    DmsResponse<Page<BookingOrderDto>> queryBookingWeCom(@RequestBody BookingOrderVo bookingOrderVo);

    @PostMapping("/maintain/order/queryBookingOrderCount")
    DmsResponse<BookingOrderParamDto> queryBookingOrderCount(@RequestBody BookingOrderVo bookingOrderVo);

    @GetMapping(value = "/lucencyWorkShop/signQuantity")
    DmsResponse<List<SignQuantityDTO>> signQuantity(@RequestParam(value = "ownerCode",required = false)String ownerCode,
                                                     @RequestParam(value = "beginDate",required = false)String beginDate,
                                                     @RequestParam(value = "endDate",required = false)String endDate);


    @GetMapping(value = "/lucencyWorkShop/menuOrderQuantity")
    DmsResponse<List<SignQuantityDTO>> menuOrderQuantity(@RequestParam(value = "ownerCode",required = false)String ownerCode,
                                                   @RequestParam(value = "beginDate",required = false)String beginDate,
                                                   @RequestParam(value = "endDate",required = false)String endDate);

    /**
     * 批量查询养修工单
     */
    @GetMapping("/repair-order/v1/getOrderByVin/interf")
    DmsResponse<RepairOrderDto> getOrderByVin(@RequestParam("vin") String vin, @RequestParam(value = "accidentDate", required = false) String accidentDate);

    /**
     * 查询工单扩展信息
     */
    @GetMapping("/repair-order/v1/getOrderExtByOwnerCodeAndRoNo/interf")
    DmsResponse<RepairOrderExtDto> getOrderExtByOwnerCodeAndRoNo(@RequestParam("ownerCode") String ownerCode, @RequestParam(value = "roNo") String roNo);
    /**
     * 结算单确认补偿
     * @param parseObject
     * @return
     */
    @PostMapping(value = "/repair-order/v1/updateSettlementDocConfirmReason/interf")
    DmsResponse<Void> updateSettlementDocConfirmReason(@RequestBody SettlementDocConfirmCompensateDto parseObject);

    @PostMapping("/maintain/order/synchronousEta/interf")
    DmsResponse<Void> synchronousEta(@RequestBody List<ETADocumentParamsVo> paramsVoList);

    /**
     * 查询工单零件信息
     */
    @GetMapping("/maintain/order/queryOrderPartsInfo")
    DmsResponse<List<PartDto>> queryOrderPartsInfo(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo);

    /**
     * 修改操作人（工单标识）
     */
    @PutMapping("/maintain/order/updateRepairOrderExtOperate")
    DmsResponse<Void> updateRepairOrderExtOperate(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo, @RequestParam("operateBy") Integer operateBy);
    /**
     * 查询工单零件埋点标识
     */
    @GetMapping("/repair-order/v1/getPartBuryingPoint")
    DmsResponse<List<RoRepairOrderPartUpdateStatusDto>> getPartBuryingPoint(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo, @RequestParam("partNos") String partNos);
    /**
     * 查询工单零件埋点标识
     */
    @PutMapping("/repair-order/v1/updatePartBuryingPoint")
    DmsResponse<Void> updatePartBuryingPoint(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo, @RequestParam("partNos") String partNos);
    
    // 单据数据变更
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "ownerCode", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "beginDate", value = ""),
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "endDate", value = ""),
            @ApiImplicitParam(paramType = "type", dataType = "string", name = "type", value = "")
    })
    @ApiOperation(value = "角标&单据查询", notes = "角标&单据查询", httpMethod = "GET")
    @GetMapping(value = "/lucencyWorkShop/v1/queryMenuOrderQuantityAndSignQuantity")
    DmsResponse<SignQuantityDTO> queryMenuOrderQuantityAndSignQuantity(@RequestParam(value = "ownerCode",required = false)String ownerCode,
                                        @RequestParam(value = "beginDate",required = false)String beginDate,
                                        @RequestParam(value = "endDate",required = false)String endDate,
                                        @RequestParam(value = "type",required = false)String type);

    @GetMapping("/market/clue/queryOwnerInfo")
    DmsResponse<OwnerInfoDTO> queryOwnerInfo(@RequestParam("ownerCode") String ownerCode,
                                             @RequestParam("vin") String vin);
    /**
     * 查询服务过程信息
     * @param shortPartDtoList
     * @return
     */
    @PostMapping("/orderServiceInfo/v1/queryServiceInfomation")
    DmsResponse<List<OrderServiceInfoVO>> queryServiceInfomation(@RequestBody List<ShortPartDto> shortPartDtoList);
    
    
    /**
     * 查询工单组根据工单
     *
     */
    @PostMapping("/repairAssign/v2/queryRepairOrderList")
    DmsResponse<List<RepairOrderV2VO>> queryRepairOrders(@RequestBody RepairOrderReqDTO repairOrderReq);
    
    
    /**
     * 根据经销商+工单号批量查询工单信息
     *
     */
    @PostMapping("/repairAssign/v2/qualityInspection")
    DmsResponse<List<RoHandRepairProjectVO>> qualityInspection(@RequestBody List<RepairOrderReqDTO> repairOrderListReq);
    
    
    /**
     * 根据维修类型 筛选出 维修部位&维修现象下拉选
     *
     */
    @GetMapping("/repairAssign/v2/queryRepairAnalysisByType")
    DmsResponse<FinalInspectionTreeVO> queryRepairAnalysisByType(@RequestParam("repairTypeCode") String repairTypeCode);
    
    
    /**
     * 新增根据交修项目维修类型集合反查终检内容清单
     *
     */
    @PostMapping("/repairAssign/v2/queryFinalInspectionContentByType")
    DmsResponse<List<FinalInspectionContentVO>> queryFinalInspectionContentByType(@RequestBody List<String> repairTypeCodeList);
    
    /**
     * 维修项目校验
     *
     */
    @ApiOperation(value = "维修项目校验", notes = "维修项目校验", httpMethod = "POST")
    @PostMapping("/repairAssign/v2/checkFinalInspection")
    DmsResponse<List<CheckFinalInspectionRespVO>> checkFinalInspection(@RequestBody List<CheckFinalInspectionReqVO> checkFinalInspectionReqList);
    
    /**
     * 查询附加项目 根据 经销商+工单号
     *
     */
    @GetMapping("/repairAssign/v2/queryFinalInspectionAttByRoNo")
    DmsResponse<List<FinalInspectionAttVO>> queryFinalInspectionAttByRoNo(@RequestParam("ownerCode") String ownerCode, @RequestParam("roNo") String roNo);
    
	@PostMapping("/repairOrder/v1/queryRepairOrderAndClaimTypeByVin")
	DmsResponse<List<RepairOrderAndClaimTypeRespDto>> queryRepairOrderAndClaimTypeByVin(@RequestBody RepairOrderAndClaimTypeReqDto repairOrderAndClaimTypeReq);
}
