package com.volvo.maintain.infrastructure.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.volvo.maintain.application.maintainlead.dto.insurance.InsuranceBillListDto;
import com.volvo.maintain.infrastructure.gateway.request.PartStockInfoParamsVo;
import com.volvo.maintain.application.maintainlead.dto.rights.PurchaseEligibilityCheckRequestDto;
import com.volvo.maintain.application.maintainlead.dto.rights.PurchaseEligibilityCheckResponseDto;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.warrantyReturnDetailPageDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.warrantyReturnPageDTO;
import com.volvo.maintain.application.maintainlead.dto.warrantyApproval.warrantyReturnReqDTO;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.PartStockInfoResultVo;
import com.volvo.maintain.interfaces.vo.ExtendedWarrantyVo;
import com.volvo.maintain.interfaces.vo.InsuranceBillVo;
import com.volvo.maintain.interfaces.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;
import java.util.Map;

@FeignClient(name = "dmscus-repair")
public interface DmscusRepairFeign {

    /**
     * 查询有效延保购买记录
     */
    @GetMapping(value = "/extendedWarrantyPurchaseGive/queryExtendedWarrantyInfo")
    DmsResponse<List<ExtendedWarrantyVo>> queryExtendedWarrantyInfo(@RequestParam String vin);

    /**
     * 查询商业险起止日期
     */
    @GetMapping(value = "/insuranceBill/selectInsureHistory")
    DmsResponse<List<InsuranceBillListDto>> selectInsureHistory(@RequestParam String vin);

    /**
     * 查询商业险起止日期
     */
    @GetMapping(value = "/insuranceBill/queryInsuranceDateByVin")
    DmsResponse<InsuranceBillVo> queryInsuranceDateByVin(@RequestParam String vin);

    /**
     * 查询延保产品列表
     */
    @GetMapping(value = "/extendedWarrantyProduct/queryExtendedWarrantyProductInfo")
    DmsResponse<Page<ExtendedWarrantyProductVo>> queryExtendedWarrantyProductInfo(@RequestParam Map<String, String> queryParam,
                                                                                  @RequestParam("currentPage")int currentPage,
                                                                                  @RequestParam("pageSize")int pageSize);


    @GetMapping(value = "/maintainActivity/page/get")
    DmsResponse<Page<MaintainActivityVo>> maintainActivityGetByPage(
                                               @RequestParam(value = "maintainName", required = false) String maintainName,
                                               @RequestParam(value = "partNo", required = false) String partNo,
                                               @RequestParam(value = "activityType", required = false) Integer activityType,
                                               @RequestParam(value = "isValidMaintain", required = false) Integer isValidMaintain,
                                               @RequestParam(value = "isActive", required = false) Integer isActive,
                                               @RequestParam(value = "isMall", required = false) Integer isMall,
                                               @RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize);

    @PostMapping("/contents-part/partNos/interAspect" )
    DmsResponse<List<ContentsPartVo>> selectByPartNos(@RequestBody List<String> partNos);

    @PostMapping("/contents-part/v1/buyValid/interAspect")
    DmsResponse<List<PurchaseEligibilityCheckResponseDto>> serveContractBuyValid(@RequestBody PurchaseEligibilityCheckRequestDto purchaseEligibilityCheckRequestDto);

    /**
     * @param vo
     * @return
     */
    @PostMapping("/collaborativeInterfaceApi/getPartStockInfo")
    DmsResponse<List<PartStockInfoResultVo>> getPartStockInfo(PartStockInfoParamsVo vo);
    /**
     * 查询非车险
     */
    @GetMapping(value = "/extendedWarrantyPurchaseGive/queryNonCarInsuranceInfo")
    DmsResponse<List<NonCarInsuranceVo>> queryNonCarInsuranceInfo(@RequestParam("vin") String vin, @RequestParam("dealerCode") String dealerCode);

}
