package com.volvo.maintain.infrastructure.gateway.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 零附件采购订单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-01
 */
@Data
@ApiModel(value = "零附件采购订单主表")
public class PartPurchaseOrderDTO {
    private static final long serialVersionUID = 1L;


    /**
     * 主键id自增序列
     */
    private Long id;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 采购单号，生成规则YV+YY+MM+四位自增长数字；批量采购订单号，生成规则YI+YY+MM+四位自增长数字
     */
    private String purchaseNo;

    /**
     * 订单类型：LDC经销商：1、3；VMI经销商：1、4非批售；其中4-批售订单根据经销商是否被授权批售
     */
    private Integer orderLevel;
    
    /**
     * 订单支付类型
     */
    private Integer purchasePaymentType;

    /**
     * 订单状态，未上传、已上传
     */
    private Integer orderStatus;

    /**
     * 提交日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8" )
    private Date submitDate;

    /**
     * 批售审核状态，待审核、审核不通过、审核通过
     */
    private Integer wholesaleApproval;

    /**
     * 超额审批状态，待审核、审核不通过、审核通过
     */
    private Integer overfulfilApproval;

    /**
     * 根据订单类型，1类：17-空运；3类：43-紧急陆运；4类批售/非批售：20-公路
     */
    private Integer transportMode;

    /**
     * 总采购项次
     */
    private Integer purchaseItem;

    /**
     * 预估采购金额
     */
    private BigDecimal purchaseAmount;

    /**
     * 关联单号
     */
    private String linkedNumber;

    /**
     * 车主/外卖对象
     */
    private String ownerObject;

    /**
     * 备注
     */
    private String remark;

    /**
     * 超额申请理由
     */
    private String overfulfilRemark;

    /**
     * 超额审批意见
     */
    private String overfulfilOpinion;

    /**
     * 批售审核意见
     */
    private String wholesaleOpinion;

    /**
     * 是否锁定，1：锁定，0：未锁定，锁定后只允许锁定人进行操作
     */
    private Integer isLock;

    /**
     * 锁定人
     */
    private Long lockBy;

    /**
     * 锁定人姓名
     */
    private String lockByName;


    /**
     * 是否LDC，1：是，0：否
     */
    private Integer isLdc;

    /**
     * VIPS下发验收单后，更新到此字段
     */
    private String vvNo;

    /**
     * 默认为导入日期+3天，批量采购订单使用该字段
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8" )
    private Date demandDate;


    /**
     * 批售审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date wholesaleCheckTime;

    /**
     * 批售审核接口获取时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date wholesaleApiTime;


    /**
     * 系统ID
     */
    private String appId;

    /**
     * 所有者代码
     */
    private String ownerCode;

    /**
     * 所有者的父组织代码
     */
    private String ownerParCode;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 数据来源
     */
    private Integer dataSources;

    /**
     * 是否删除，1：删除，0：未删除
     */
    private Boolean isDeleted;

    /**
     * 是否有效
     */
    private Integer isValid;

    /**
     * 是否新店首铺
     */
    private Integer isFirstStore;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date createdAt;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date updatedAt;

    /**
     * 已收到实物
     */
    private Boolean isGetGoods;


    private String createdAtStart;

    private String createdAtEnd;
    private Integer orderPullStats;
    private Integer orderSource;
    private Integer orderTo;
    private Integer isUpload;


    private List<PartPurchaseOrderDetailDTO> partList;

    /**
     * JD补货订单号
     */
    private String replenishmentNo;

    /**
     * 补货时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date replenishmentDate;

    /*采购单号*/
    private List<String> purchaseNoList;

    /**
     * 页面类型 1：原采购单 2：动力电池
     */
    private int pageType;

    private String vin;

    /**
     * 关联工单号
     */
    private String relevancyRoNo;

    /**
     * 关联技术支持单号
     */
    private String relevancyTechnicalSupportNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date uploadTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date affirmTime;

    private String affirmBy;

    private String uploadTimeStart;

    private String uploadTimeEnd;
    
    /**
     * OG订单状态
     */
    private String ogOrderStatus;
    
    /**
     * OG采购金额（含税）
     */
    private BigDecimal ogTotalTaxAmount;
    
    /**
     * 原因
     */
    private String reason;
    
    /**
     * OG单号
     */
    private String ogOrderNo;
}
