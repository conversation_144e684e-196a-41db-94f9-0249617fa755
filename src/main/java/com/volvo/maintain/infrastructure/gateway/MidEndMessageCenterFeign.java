package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.EmailInfoDto;
import com.volvo.maintain.application.maintainlead.dto.message.AppPushWithTemplateDto;
import com.volvo.maintain.application.maintainlead.dto.message.MessageSendDto;
import com.volvo.maintain.application.maintainlead.dto.workshop.SendPushNoticeDto;
import com.volvo.maintain.infrastructure.gateway.response.DmsResponse;
import com.volvo.maintain.infrastructure.gateway.response.ImResponse;
import com.volvo.maintain.infrastructure.gateway.response.MidResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "mid-end-message-center",url = "${baseUrl.midEndMessageCenter}")
public interface MidEndMessageCenterFeign {

    //发送邮件
    @PostMapping(path = "/push/v1/emails", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse pushEmails(@RequestHeader(name = "appId") String appId, @RequestBody EmailInfoDto emailInfoDto);

    //发送短信
    @PostMapping(path = "/push/v1/smss", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    ImResponse pushSms(@RequestHeader(name = "appId") String appId, @RequestBody MessageSendDto messageSendDto);

    @PostMapping(value = "/push/v1/apps/pushNewbieWithTemplate", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    DmsResponse pushMails(@RequestBody AppPushWithTemplateDto appPushDTO);

    @PostMapping(value = {"/push/v1/apps/template"})
    MidResponse<Void> sendAppsPushWithTemplate(@RequestHeader(required = false) String appId, @RequestBody SendPushNoticeDto sendPushNoticeDTO);
}
