package com.volvo.maintain.infrastructure.gateway;

import com.volvo.maintain.application.maintainlead.dto.DownloadDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @since 2024/01/15
 */
@FeignClient(name = "download-service")
public interface DownloadServiceFeign {

    /**
     * 下载中心导出
     * @param dto 导出参数
     */
    @PostMapping("/download/exportExcel")
    void downloadExportExcel(@RequestBody DownloadDto dto);
}
