package com.volvo.maintain.infrastructure.gateway.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 自建邀约 查询车主车辆
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "车主车辆信息", description = "车主车辆信息")
public class VehicleOwnerVO {

    /**
     * 车主姓名
     */
    private String name;

    /**
     * 车主电话
     */
    private String mobile;

    /**
     * 客户唯一Id
     */
    private Long oneId;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactorMobile;

    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 工单号
     */
    private String roNo;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 车系id
     */
    private String seriesId;

    /**
     * 车型id
     */
    private String modelId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 车系名称
     */
    private String seriesName;

    /**
     * 车型名称
     */
    private String modelName;

    /**
     * 销售日期
     */
    private Date invoiceDate;

    /**
     * 工单开单时间
     */
    private Date roCreateDate;


    /**
     * 工单结算日期
     */
    private Date deliveryDate;

    /**
     * 出厂里程
     */
    private Double mileage;

    /**
     * 上次维修日期
     */
    private Date lastMaintainDate;

    /**
     * 上次维修里程
     */
    private Double lastMaintainMileage;


    /**
     * 建议进厂时间
     */
    private Date adviseInDate;


    /**
     * 日均行驶里程
     */
    private Double dailyAverageMileage;


    /**
     * 性别
     */
    private String sex;

    /**
     * 年龄
     */
    private String age;


    /**
     * 经销商
     */
    private String dealerCode;


    /**
     * 邀约类型：首保、定保、保险、客户流失
     */
    private Integer inviteType;


    /**
     * 邀约规则
     */
    private Integer inviteRule;

    /**
     * 邀约规则值
     */
    private Integer ruleValue;

    /**
     * 提前N天邀约
     */
    private Integer dayInAdvance;

    /**
     * 再提醒间隔（月）
     */
    private Integer remindInterval;


    /**
     * 超时关闭时间间隔（月）
     */
    private Integer closeInterval;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer isUse;


    /**
     * 车型 code
     */
    private String modelCode;


    /**
     * 年款
     */
    private String configYear;

    /**
     * 发动机号
     */
    private String engineNo;

    /**
     * 变速箱号
     */
    private String transMission;

    /**
     * 项目类型
     */
    private Integer itemType;

    /**
     * 易损 code
     */
    private String itemCode;

    /**
     * 易损 name
     */
    private String itemName;


    /**
     * 建议进厂里程
     */
    private Integer adviseInMileage;


    /**
     * 出厂里程
     */
    private Integer outMileage;


    /**
     * qb号
     */
    private String qbNumber;


    /**
     * 是否voc车辆 10041001是 10041002 否
     */
    private Integer isVoc;

    /**
     * 维修类型代码
     */
    private String repairTypeCode;

    /**
     * 工单类型
     */
    private String orType;

    /**
     * 结算时间
     */
    @ApiModelProperty(value = "结算时间", name = "forBalanceTime")
    private Date forBalanceTime;

    /**
     * 结算单号
     */
    @ApiModelProperty(value = "结算单号", name = "balanceNo")
    private String balanceNo;

    /**
     * 收费对象代码
     */
    @ApiModelProperty(value = "收费对象代码", name = "paymentObjectCode")
    private String paymentObjectCode;

    /**
     * 去零金额
     */
    @ApiModelProperty(value = "去零金额 ", name = "subObbAmount")
    private String subObbAmount;

    /**
     * 圆整金额
     */
    @ApiModelProperty(value = "圆整金额 ", name = "yzAmaount")
    private String yzAmaount;

    /**
     * 状态 deliveryTag
     */
    @ApiModelProperty(value = "状态 ", name = "deliveryTag")
    private String deliveryTag;

    /**
     * 工单创建时间
     */
    private Date createdAt;

    /**
     * customerId
     */
    private Long customerId;
}
