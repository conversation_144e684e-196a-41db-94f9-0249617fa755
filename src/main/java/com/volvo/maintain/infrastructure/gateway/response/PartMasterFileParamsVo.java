package com.volvo.maintain.infrastructure.gateway.response;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020年7月14日
 */
@ApiModel("零件主信息下发接口入参Vo")
@Data
public class PartMasterFileParamsVo {
    @ApiModelProperty(value = "零件号", name = "partNo")
    @JSONField(name = "partsNo")
    private String partNo;

    @ApiModelProperty(value = "品牌", name = "partBrand")
    @JSONField(name = "brandName")
    private String partBrand;

    @ApiModelProperty(value = "零件名称", name = "partName")
    @JSONField(name = "partsName")
    private String partName;

    @ApiModelProperty(value = "零件英文名称", name = "partEname")
    @JSONField(name = "partsEname")
    private String partEname;

    @ApiModelProperty(value = "AGE CODE", name = "ageCode")
    @JSONField(name = "ageCode")
    private String ageCode;

    @ApiModelProperty(value = "cls12成本价", name = "CLS12成本价(常规订货价)")
    @JSONField(name = "cls12")
    private BigDecimal cls12;

    @ApiModelProperty(value = "cls4成本价", name = "cls4")
    @JSONField(name = "cls4")
    private BigDecimal cls4;

    @ApiModelProperty(value = "折扣码", name = "discountCode")
    @JSONField(name = "discountCode")
    private String discountCode;

    @ApiModelProperty(value = "功能码", name = "functionCode")
    @JSONField(name = "functionCode")
    private String functionCode;

    @ApiModelProperty(value = "采购束包装", name = "purchasePackage")
    @JSONField(name = "purchasePackage")
    private BigDecimal purchasePackage;

    @ApiModelProperty(value = "国别码", name = "countryCode")
    @JSONField(name = "countryCode")
    private String countryCode;

    @ApiModelProperty(value = "产品类别", name = "productCode")
    @JSONField(name = "productCode")
    private String productCode;

    @ApiModelProperty(value = "销售价(厂端建议销售价)", name = "unitPrice")
    @JSONField(name = "unitPrice")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "ABC", name = "abcCode")
    @JSONField(name = "abcCode")
    private String abcCode;

    @ApiModelProperty(value = "被取代码", name = "replacedCode")
    @JSONField(name = "replacedCode")
    private String replacedCode;

    @ApiModelProperty(value = "取代码", name = "replaceCode")
    @JSONField(name = "replaceCode")
    private String replaceCode;

    @ApiModelProperty(value = "EUC", name = "euc")
    @JSONField(name = "euc")
    private String euc;

    @ApiModelProperty(value = "配件分类(DDLS)", name = "ddls")
    @JSONField(name = "ddls")
    private String ddls;

    @ApiModelProperty(value = "weight(重量)", name = "weight")
    @JSONField(name = "weight")
    private String weight;

    @ApiModelProperty(value = "volume(体积)", name = "volume")
    @JSONField(name = "volume")
    private BigDecimal volume;

    @ApiModelProperty(value = "unit", name = "unit")
    @JSONField(name = "unit")
    private String unit;

    @ApiModelProperty(value = "BSPR_code", name = "bsprCode")
    @JSONField(name = "bsprCode")
    private String bsprCode;

    @ApiModelProperty(value = "PAG_price", name = "pagPrice")
    @JSONField(name = "pagPrice")
    private BigDecimal pagPrice;

    @ApiModelProperty(value = "保修成本(索赔价)", name = "warCost")
    @JSONField(name = "warCost")
    private BigDecimal warCost;

    @ApiModelProperty(value = "CCC", name = "CCC")
    @JSONField(name = "cccFlag")
    private String ccc;

    @ApiModelProperty(value = "DFS", name = "DFS")
    @JSONField(name = "dfsFlag")
    private String dfs;

    @ApiModelProperty(value = "SurCharge", name = "surCharge")
    @JSONField(name = "surchargehge")
    private String surCharge;

    @ApiModelProperty(value = "define1", name = "define1")
    @JSONField(name = "define1")
    private String define1;

    @ApiModelProperty(value = "define2", name = "define2")
    @JSONField(name = "define2")
    private BigDecimal define2;

    @ApiModelProperty(value = "min_order_qty", name = "minOrderQty")
    @JSONField(name = "minOrderQty")
    private BigDecimal minOrderQty;

    @ApiModelProperty(value = "TAX_CODE", name = "taxCode")
    @JSONField(name = "taxCode")
    private String taxCode;

    @ApiModelProperty(value = "UNIT_OF_MEASURE", name = "unitOfMeasure")
    @JSONField(name = "unitOfMeasure")
    private String unitOfMeasure;

    @ApiModelProperty(value = "SUPPLIER_NUMBER", name = "supplierNumber")
    @JSONField(name = "supplierNumber")
    private String supplierNumber;

    @ApiModelProperty(value = "PASSIVE", name = "passive")
    @JSONField(name = "passive")
    private String passive;

    @ApiModelProperty(value = "SALES_BLOCKING_CODE", name = "salesBlockingCode")
    @JSONField(name = "salesBlockingCode")
    private String salesBlockingCode;

    @ApiModelProperty(value = "DDLS_CODE", name = "ddlsCode")
    @JSONField(name = "ddlsCode")
    private String ddlsCode;

    @ApiModelProperty(value = "REPLACING", name = "replacing")
    @JSONField(name = "replacing")
    private String replacing;

    @ApiModelProperty(value = "REPLACED", name = "replaced")
    @JSONField(name = "replaced")
    private String replaced;

    @ApiModelProperty(value = "是否可辅料批量出库", name = "IS_SUBMATERTAL_OUT")
    @JSONField(name = "isSubmaterialOut")
    private Integer isSubmatertalOut;

    @ApiModelProperty(value = "是否积分件", name = "isIntegral")
    @JSONField(name = "isIntegral")
    private String isIntegral;

    @ApiModelProperty(value = "是否溯源件", name = "isTraceable")
    @JSONField(name = "isTraceable")
    private String isTraceable;

    @ApiModelProperty(value = "产地", name = "source")
    @JSONField(name = "source")
    private String source;


}
