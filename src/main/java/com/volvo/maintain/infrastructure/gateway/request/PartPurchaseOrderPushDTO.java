package com.volvo.maintain.infrastructure.gateway.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 采购订单上传DTO
 */
@Data
public class PartPurchaseOrderPushDTO {

    @ApiModelProperty(value = "采购单id",name = "id")
    private Long id;
    
    /**
     * 订单支付类型
     */
    private String purchasePaymentType;

    /**
     * 页面类型 1：原采购订单 2：动力电池
     */
    @ApiModelProperty(value = "页面类型",name = "pageType")
    private int pageType;

    private List<PartSourceCodes> partSourceCodes;

}
