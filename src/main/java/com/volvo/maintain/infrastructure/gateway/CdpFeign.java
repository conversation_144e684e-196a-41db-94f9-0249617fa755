package com.volvo.maintain.infrastructure.gateway;

import com.alibaba.fastjson.JSONObject;
import com.volvo.maintain.application.maintainlead.dto.*;
import com.volvo.maintain.infrastructure.gateway.response.CdpResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(name = "CDP", url = "${baseUrl.cdpUrl}")
public interface CdpFeign {
    /**
     * 功能描述：获取cdp TOKEN
     */
    @PostMapping(path = "/attr-tag/api/getAppTokenId", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    CdpResponse<JSONObject> postForObject(@RequestBody CdpTokenDto tokenDto);
    /**
     * 功能描述:根据vin获取cdp标签信息
     *
     * @param param 入参
     */
    @PostMapping(path = "/attr-tag/api/getCustomerTags", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    CdpResponse<JSONObject> queryTagListByVin(@RequestBody JSONObject param);
    /**
     * 功能描述:批量根据vin获取cdp标签信息
     *
     * @param param 入参
     */
    @PostMapping(path = "/attr-tag/api/getVehicleLabels", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    CdpResponse<List<CustomerTagsDto>> getVehicleLabels(@RequestBody JSONObject param);

    @PostMapping(path = "/attr-tag/api/profile/getRelatedProfiles", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    CdpResponse<JSONObject> getRelatedProfiles(@RequestBody CdpCustomerParamsDto cdpCustomerParamsDto);

    @PostMapping(path = "/attr-tag/api/v2/profile/getRelatedProfiles", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    CdpResponse<JSONObject> getRelatedProfilesV2(@RequestBody CdpCustomerParamsDto cdpCustomerParamsDto);

    /**
     * 功能描述:获取cdp全量标签信息
     *
     * @param param 入参
     */
    @PostMapping(path = "/attr-tag/api/getTagList", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    CdpResponse<List<SyncTagInfoDTo>> queryTagList(@RequestBody JSONObject param);


    /**
     * 功能描述:MOBILE批量查询车辆标签
     * @param param
     * @return
     */
    @PostMapping(path = "attr-tag/api/getBatchAttAndTagByMobile", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    CdpResponse<List<CustomerTagsDto>> queryBatchTagList(@RequestBody CdpCustomerParamsDto cdpCustomerParamsDto);

    @PostMapping(path = "/attr-tag/api/platform/v2/checkInSegmentsBaseByAuth", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    CdpResponse<JSONObject> checkInSegmentsBaseByAuth(@RequestBody JSONObject param);

    @PostMapping(path = "/attr-tag/api/platform/v2/checkInSegmentsBaseByVin", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    CdpResponse<JSONObject> checkInSegmentsBaseByVin(@RequestBody JSONObject param);
}
