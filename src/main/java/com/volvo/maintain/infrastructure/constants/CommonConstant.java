package com.volvo.maintain.infrastructure.constants;

public class CommonConstant {
    public static final String APP_ID = "volvo";

    /**
     * BLACK_HEADERS
     */
    public static final String BLACK_HEADERS_CONTENT_LENGTH = "content-length";
    public static final String WHITE_HEADERS_GRAYID = "grayid";
    public static final String WHITE_HEADERS_AUTHORIZATION = "Authorization";

    /**
     * REPLACE_HEADER
     */
    public static final String REPLACE_HEADER_APPKEY = "Appkey";
    public static final String REPLACE_HEADER_SIGNATURE = "Signature";
    public static final String REPLACE_HEADER_TIMESTAMP = "Timestamp";
    public static final String REPLACE_HEADER_NONCE = "Nonce";
    public static final String REPLACE_HEADER_AUTHORIZATION = "Authorization";

    /**
     * CDP SUCCESS
     */
    public static final String SUCCESS_CODE = "0";
    public static final String SUCCESS_MSG = "成功";

    public static final String SUCCESS = "success";

    /**
     * NEWBIE SUCCESS
     */
    public static final String SUCCESS_NEWBIE_CODE = "200";
    public static final String SUCCESS_NEWBIE_MSG = "成功";
    /**
     * FAIL
     */
    public static final String FAIL_CODE = "500";
    public static final String FAIL_MSG = "失败";

    /**故障灯线索使用常量-start-*/
    public static final int NOTIFY_LENGTH = 50;
    /**故障灯线索使用常量-end-*/

    /**
     * 创建来源参数
     */
    public static final Integer PC_CREATE = 82641001;            //PC端创建
    public static final Integer PAD_CREATE = 82641002;            //PAD创建
    public static final Integer APPOINTMENT_CREATE = 82641003;    //预约单创建
    public static final Integer EVALUATION_CREATE = 82641004;    //估价单创建

    /**
     * 白名单
     */
    //客户画像
    public static final Integer CLIENT_PORTRAIT = 91111023;
    public static final Integer PRODUCTION_LABEL = 91111045;
    public static final Integer PRODUCTION_LABEL_FAULT = 91111046;
    public static final Integer PRODUCTION_LABEL_INSURANCE = 91111047;
    public static final Integer PRODUCTION_LABEL_ACCIDENT = 91111048;


    /**
     * 标签类型
     */
    //环检接车
    public static final Integer TAG_PRECHECK = 1;

    /**
     * 产值标签（cdp授权标签待定）
     */
    public static final String PRODUCTION_LABEL_TAG = "production_label_tag";

    /**
     * vip客户导入条数
     */
    public static final Integer VIP_CUSTOM_INCREAS_SIZE = 1000;
    public static final Integer VIP_CUSTOM_INCREAS_MAX_SIZE = 10000;

    /**
     * CDP客户标签（cdp授权标签待定）
     */
    public static final String CDP_TAG_WHETHER_EMPOWER = "";
    /**
     * VOLVO 发送邮箱
     */
    public static final String VOLVO_EMAIL = "<EMAIL>";

    /**
     * 参数邮箱
     */
    public static final String FIND_EMAIL = "5100";

    /**
     * 排序
     */
    public static final Integer SHOW_SORT = 999;

    /**
     * 标签来源
     */
    public static final Integer CDP_TAG_SOURCE = 35251001;

    public static final Integer NEWBIE_TAG_SOURCE = 35251002;
    /**
     * 展示优先级(显示)
     */
    public static final Integer SHOW_LEVEL_SHOW = 35211003;

    /**
     * 展示优先级(不显示)
     */
    public static final Integer SHOW_LEVEL_DONT_SHOW = 35211005;

    /**
     * 标签类型
     */
    public static final String TAG_VEHICLE_PROFILEBASE = "vehicle_profilebase";

    public static final String TAG_CUSTOMER_PROFILEBASE = "customer_profilebase";
    /**
     * cdp 数据类别
     */
    public static final String CDP_VIN_ID_NAME = "id_vin_crypto";

    public static final String CDP_MOBILE_ID_NAME = "id_mobile_crypto";

    public static final String CDP_CAR_ONE_ID = "car_oneid";

    /**
     * cdp 客户数据缓存时间
     */
    public static final Integer CDP_CUSTOMER_CACHE_TIME = 10;

    /**
     * cdp 客户数据缓存key前缀
     */
    public static final String CDP_CUSTOMER_PROFILE_TOKEN = "cdp:customerProfile:token:";
    public static final String CDP_CUSTOMER_OR_VEHICLE_PREFIX = "cdp:customerOrVehicle:";
    public static final String CDP_VALUE_UTF8 = "utf-8";
    public static final String CDP_TOKEN = "cdp:TOKEN-VALUE";
    public static final int CDP_TOKEN_TIME = 235;

    /**
     * 是否状态
     */
    /**
     * 是否状态
     */
    public static final int DICT_IS_YES = 10041001; // 是
    public static final int DICT_IS_NO = 10041002; // 否

    /**重试次数*/
    public static final Integer RETRY_COUNT_TWO = 2;

    /**重试次数*/
    public static final Integer RETRY_COUNT_THREE = 3;

    /**重试最大次数异常*/
    public static final Integer TASK_STATUS_MAX = -1;
    /**重试失败*/
    public static final Integer TASK_STATUS_CLOSE = 2;

    /**标签同步重试成功*/
    public static final Integer TASK_STATUS_SUCCESS = 1;

    /**一级板块code*/
    public static final Integer FIRST_BLOCK_CODE = 3522;

    /**一级板块类型*/
    public static final Integer FIRST_BLOCK_TYPE = 1;

    /**二级板块类型*/
    public static final Integer SECOND_BLOCK_TYPE = 2;

    /**三级板块类型*/
    public static final Integer THIRD_BLOCK_TYPE = 3;

    /**文本显示类型*/
    public static final Integer TEXT_SHOW_TYPE = 35291001;

    /**板块展现形式(上下)*/
    public static final Integer UP_AND_DOWN = 35271001;

    public static final Integer BLOCK_TYPE_ONE = 1;

    /**板块展现形式(tab)*/
    public static final Integer TAB = 35271002;

    /**值获取类型(客户端自行处理)*/
    public static final Integer CLIENT_SELF_PROCESSING= 35241004;

    /**
     * 默认规则
     */
    public static final String TAG_VALUE_RULE = "{\"getType\":35241001,\"showType\":35291001}";

    /**值获取类型(本地接口查询)*/
    public static final Integer LOCAL_REAL_TIME_QUERY = 35241002;
    //在职状态
    // 10081001:在职
    // 10081002:离职
    public static final Integer IS_ON_JOB_DIMISSION = 10081002;
    public static final Integer IS_ON_JOB_IN = 10081001;

    //角色 取送车专员：QSCZY、服务经理：FWJL、店总：DZ
    public static final String ROLE_CODE_QSCZY = "QSCZY";
    public static final String ROLE_CODE_FWJL = "FWJL";
    public static final String ROLE_CODE_DZ = "ZJL";

    //em90
    public static final String GROUP_TYPE_EM90 = "EM90";
    public static final String MODEL_CODE_EM90 = "em90-modelCode";
    //em90取送车电话号码
    public static final String GROUP_TYPE_EM90_MODELCODE = "em90-mobile";
    //em90 短信模板
    public static final String GROUP_TYPE_EM90_TEMPLATEID = "EM90-TemplateId";
    //取送车专员短信通知-养修预约
    public static final String EM90_TEMPLATEID_QSCZY_1 = "em90-TemplateId-QSCZY-1";
    //取送车专员短信通知-不存在养修预约
    public static final String EM90_TEMPLATEID_QSCZY_2 = "em90-TemplateId-QSCZY-2";
    //服务经理,店总短信通知
    public static final String EM90_TEMPLATEID_FWJL = "em90-TemplateId-FWJL";
    //出发地址和到达地址不在同一地级市消息通知
    public static final String EM90_TEMPLATEID_DZBYZ = "em90-TemplateId-DZBYZ";


    /**
     * 参数邮箱
     */
    public static final Integer VEHICLE_ORDER_PAGE_SIZE = 200;
    public static final Integer VEHICLE_ORDER_PAGE = 1;
    public static final Integer VEHICLE_ORDER_ORDER_STATUS = 82721001;
    /**
     * EM90系统配置
     */
    public static final String EM90_TAKE_DELIVER_CAT_CONFIG_KEY = "em90-TakeDeliverCarPhone";
    public static final String EM90_GROUP_TYPE = "EM90";
    //EM90-取送车未确认提醒
    public static final String EM90_VEHICLE_DELIVER_UNCONFIRMED_REMINDER = "EM90_VEHICLE_DELIVER_UNCONFIRMED_REMINDER";
    //EM90-取送车下单提醒
    public static final String EM90_VEHICLE_DELIVER_CREATE_REMINDER = "EM90_VEHICLE_DELIVER_CREATE_REMINDER";
    //EM90-取送车已确认&跨店提醒
    public static final String EM90_VEHICLE_DELIVER_CONFIRM_REMINDER = "EM90_VEHICLE_DELIVER_CONFIRM_REMINDER";

    public static final String EM90_DELAY_TIME = "em90-DELAYTime";





    /**
     * EM90车辆进店提醒
     */
    public static final String EM90_VEHICLE_ENTRY_REMINDER = "EM90_VEHICLE_ENTRY_REMINDER";


    /**
     * EM90保养线索提醒
     */
    public static final String EM90_MAINTAIN_CLUE_REMINDER = "EM90_MAINTAIN_CLUE_REMINDER";

    /**
     * EM90-进店后15分钟未分拨异常提醒
     */
    public static final String EM90_GT15M_NOT_ALLOCATION_REMINDER = "EM90_GT15M_NOT_ALLOCATION_REMINDER";

    /**
     * EM90-开单后15分钟未上工位异常提醒
     */
    public static final String EM90_GT15M_NOT_STATION_REMINDER = "EM90_GT15M_NOT_STATION_REMINDER";

    /**
     * EM90系统配置
     */
    public static final String EM90_CONFIG_KEY = "em90-modelCode";
    /**
     * 查询批次
     */
    public static final Integer EM90_BATCH = 200;

    /**
     * em90取送车类型
     */
    public static final Integer EM90_TASK_CAR = 82711001;
    public static final Integer EM90_DELIVER_CAR = 82711001;

    /**
     * 取送车状态
     */
    //待确认
    public static final String EM90_82721001 = "82721001";
    //已下单
    public static final String EM90_82721002 = "82721002";

    // 待确认 redis key 前缀
    public static final String EM90_TO_BE_CONFIRMED_PREFIX = "EM90_TO_BE_CONFIRMED&";


    /**
     * 店端。支持全量线索 ps：360客户画像需求变更部分 on 2024-03-14
     */
    public static final String STORY = "2";

    /**
     * PUSH公共配置
     */
    public static final Integer PUSH_MESSAGE_SINCE_TYPE = 2;
    public static final String EM90_NOTE_TEMPLATE_ID = "a66447c4-739e-4570-9d90-fdcfcbe49e2c";
    /**
     * 保客营销CDP配置参数
     */
    public static final String PROTECTING_CUSTOMER_MARKET_CONFIG_KEY = "cdpTagCodeKey";
    public static final String PROTECTING_CUSTOMER_MARKET_GROUP_TYPE = "cdpTagCode";
    public static final String PROTECTING_CUSTOMER_MARKET_TAG_NAME = "增换购-用户";

    public static final String TALKSKILL_WEIGHT_KEY = "talkskill-weight";

    public static final String CUSTOMERMARKETING = "customerMarketing";

    public static final String CUSTOMERMARKETING_MODEL = "customer-marketing";



    public static final String BUSINESSTYPE = "10000000";

    // 每次批量插入最大条数
    public static final Integer INSERT_BATCH_LIMIT_COUNT = 100;

    /**
     * 保客营销CDP配置参数
     */
    public static final String PROTECTING_VIN_MARKET_CONFIG_KEY = "cdpTagCodeKey1";
    public static final String PROTECTING_VIN_MARKET_GROUP_TYPE = "cdpTagCode1";
    public static final String PROTECTING_VIN_MARKET_TAG_NAME = "增换购-vin";


    public static final String REGION_CROWN_CODE = "3333";

    public static final String REPAIRTYPECODE = "P";

    //送修人屏蔽
    public static final Integer DELIVER_SHIELD = 91111030;
    //送修人屏蔽状态正常
    public static final String DELIVER_SHIELD_NORMAL = "0";
    //送修人屏蔽状态异常 abnormal
    public static final String DELIVER_SHIELD_ABNORMAL = "1000";
    //送修人屏蔽配置参数
    public static final String DELIVER_PARAM = "Deliver-Param";
    //送修人屏蔽次数
    public static final String DELIVER_NUMBER = "Deliver-number";
    //送修人屏蔽天数
    public static final String DELIVER_DAY = "Deliver-day";

    public static final Integer PARTSVOUCHER = 82381013;

    /**
     * 企微事故线索类型（白名单）
     */
    public static final Integer WECOM_ACCIDENT_LEAD_TYPE = 91111029;
    /**
     * 企微事故线索类型（白名单）
     */
    public static final String WECOM_ACCIDENT_LEAD_TYPE_9129 = "9129";
    /**
     * (以前)事故线索类型（白名单）
     */
    public static final Integer ACCIDENT_LEAD_TYPE_1013 = 91111013;

    /**
     * 企微事故线索类型（白名单）
     */
    public static final Integer WECOM_ACCIDENT_MODE_TYPE = 91111013;

    /**
     * 企微事故线索code （黑名单）
     */
    public static final Integer WECOM_ACCIDENT_LEAD_CODE = 1;

    /**
     * 企微事故线索code （白名单）
     */
    public static final Integer WECOM_ACCIDENT_ROSTER_TYPE_WHITE = 0;
    /**
     * 企微事故线索code （白名单）
     */
    public static final String WECOM_ACCIDENT_ROSTER_TYPE_WHITE_0 = "0";

    public static final Integer ERRORDATA = 0;

    public static final String FAIL_DATA = "failed to add data！";

    public static final Integer SUCCESSFULLY_PROCESSED = 1;

    public static final Integer PROCESSING_FAILED = 2;

    public static final Integer BLACKLIST_TYPE = 1;

    public static final Integer SINCE_TYPE_WECOM = 6;


    public static final int NUM_5 = 5;
    public static final int NUM_1 = 1;
    public static final int NUM_0 = 0;
    public static final int NUM_2 = 2;
    public static final int NUM_3 = 3;
    public static final int NUM_4 = 4;
    public static final int NUM_6 = 6;

    public static final String LEADS_TYPE_103 = "103";
    public static final String GET_VIRTUAL_PHONE_FAIL = "获取虚拟号失败";

    /**
     * 检测报告类型
     */
    //1: em90车辆检测报告提醒
    public static final String REMIND_TYPE_1 = "1";
    //2: 重点客户提醒'
    public static final String REMIND_TYPE_2 = "2";

    /**
     * G:保修 I:事故 M:保养 N:机电维修 P:PDS S:零售
     */
    public static final String REPAIR_TYPE_G = "G";
    public static final String REPAIR_TYPE_I = "I";
    public static final String REPAIR_TYPE_M = "M";
    public static final String REPAIR_TYPE_N = "N";
    public static final String REPAIR_TYPE_P = "P";
    public static final String REPAIR_TYPE_S = "S";


    public static final String TAG100 = "TAG100";

    public static final String TAG101 = "TAG101";

    public static final String ALLOCATION_TYPE_ONE = "38041001";

    public static final String ALLOCATION_TYPE_TWO = "38041002";
    /**
     * 续保线索白名单字典
     */
    public static final Integer RENEWAL_INSURANCE_WHITE = 91111033;
    /**
     * 厂端下发
     */
    public static final Integer RENEWAL_INSURANCE_CLUEISSUANCE_TYPE_96171001 = 96171001;
    /**
     * 自店导入
     */
    public static final Integer RENEWAL_INSURANCE_CLUEISSUANCE_TYPE_96171002 = 96171002;
    /**
     * 参数配置——续保线索分组
     */
    public static final String RENEWAL_INSURANCE_GROUP = "Renewal_of_insurance";
    /**
     * 参数配置——续保线索KEY
     */
    public static final String RENEWAL_INSURANCE_KEY = "Renewal_of_insurance_1001";
    /**
     * 续保线索-续保到期日期修改次数限制天数
     */
    public static final Long RENEWAL_INSURANCE_MODIFY_DAYS = 365L;

    // 投保人
    public static final String INSURED_CODE = "109900002";

    // 工单送修人
    public static final String REPAIRER_CODE = "109900013";

    public static final String ROLE_CODE_BXZY = "BXZY";

    public static final Integer INVITEINSURANCE_ORDER_STATUS_83681001 =  83681001;
    //已完成
    public static final Integer INVITEINSURANCE_ORDER_STATUS_83681002 =  83681002;
    //流失客户
    public static final Integer INVITEINSURANCE_ORDER_STATUS_83681003 =  83681003;
    //他店进厂
    public static final Integer INVITEINSURANCE_ORDER_STATUS_83681004 =  83681004;
    //关闭
    public static final Integer INVITEINSURANCE_ORDER_STATUS_83681005 =  83681005;
    //续保关闭
    public static final Integer INVITEINSURANCE_ORDER_STATUS_83681006 =  83681006;
    //续保关闭
    public static final Integer INVITEINSURANCE_ORDER_STATUS_83681007 =  83681007;
    public static final int DATA_SOURCE_DEALER = 13; //自店导入

    /**
     * 取送车订单类型：取车
     */
    public static final Integer PICK_UP_CAR_CODE = 82711001;
    /**
     * 参数配置——续保线索KEY
     */
    public static final String SELECT_OWNER_RULE = "SELECT_OWNER_RULE:";

    /**
     * EM90工单交车时有报告
     */
    public static final String REMAIND_HAVE_REPORT = "有报告";

    /**
     *  35021001：里程数小于最近一次非作废的工单填写的里程数
     *  35021002：查询不到开票关键信息
     *  35021003：车辆的新旧车类型不否符合产品购买范围
     *  35021004：车辆已有正在生效的延保不具备任何延保产品的购买资格
     *  35021005：车辆的车型代码不否符合产品购买范围
     *  35021006：车辆的发动机代码不否符合产品的购买范围
     *  35021007：总车龄过长不符、每两次保养间隔过久不符
     *  35021008：总里程过多不符、每两次保养间隔过多不符
     *  35021009：不存在产品零件配置
     *  35021010：不符合上架和生效范围
     *  35021011：限定未做首保不符合购买范围
     *  35021012：限制单次最大购买数量
     */
    public static final String ERROR_TYPE_35021001 = "35021001";
    public static final String ERROR_TYPE_35021002 = "35021002";
    public static final String ERROR_TYPE_35021003 = "35021003";
    public static final String ERROR_TYPE_35021004 = "35021004";
    public static final String ERROR_TYPE_35021005 = "35021005";
    public static final String ERROR_TYPE_35021006 = "35021006";
    public static final String ERROR_TYPE_35021007 = "35021007";
    public static final String ERROR_TYPE_35021008 = "35021008";
    public static final String ERROR_TYPE_35021009 = "35021009";
    public static final String ERROR_TYPE_35021010 = "35021010";
    public static final String ERROR_TYPE_35021011 = "35021011";
    public static final String ERROR_TYPE_35021012 = "35021012";
    public static final String ERROR_TYPE_35021013 = "35021013";



    public static final Integer SUCCESS_ETA_CODE = 200;

    public static final String MAIL_NOTICE_TEMPLATE_CODE = "Tem_1_MQR13ZLF";

    public static final String MAIL_NOTICE_TEMPLATE_CODE_block = "Tem_1_25XQYDUG";

    public static final String USED_KGY_PUSH_ROLE_CODE = "KGY";

    public static final String USED_LJJL_PUSH_ROLE_CODE = "LJJL";

    public static final Integer SHORT_PART_STATUS_2 = 81341002;

    public static final Integer SHORT_PART_STATUS_8 = 81341008;

    public static final Long MISSING_PART_STATUS_8 = 81181005L;

    public static final Long MISSING_PART_STATUS_6 = 81181006L;

    public static final String MISSING_PARTS_REMIND = "70131004";

    public static final String PHONE_PARTS_REMIND = "70131011";

    public static final String WORK_ORDER_MATERIAL_SHORTAGE = "70131115";

    public static final String WORK_SHOP_VHC_INSPECT = "70131204"; // VHC检查

    public static final String  WORK_SHOP_VHC_QUOTATION = "70131205"; // VHC报价

    public static final String  WORK_SHOP_VHC_CONFIRM = "70131214"; // VHC检查确认

    public static final String DICT_RO_STATUS_TYPE_ON_REPAIR = "80491001"; // 在修

    public static final Integer DATA_SOURCES_83751005 =  83751005; // 背靠背

    public static final Integer USER_RIGHTS_STATUS = 1;

    public static final Double  ZERO= 0.0;

    public static final Integer LJKG_83261002 = 83261002;
    public static final String DEFAULT_STORE = "DEFAULT";

    public static final Long WARRANTY_PERIOD = 1005L;
    // 保险
    public static final Long INSURANCE_PERIOD = 1002L;
    // 二手车
    public static final Long USED_CAR_PERIOD = 1184L;
    // 道路救援
    public static final Long ROAD_RESCUE_PERIOD = 1006L;
    // 终身保养
    public static final Long LIFETIME_MAINTENANCE_PERIOD = 1187L;
    // 保养套餐
    public static final Long MAINTENANCE_PACKAGE_PERIOD = 1185L;
    // 延保
    public static final Long EXTENDED_WARRANTY_PERIOD = 1186L;
    // 非车险
    public static final Long NON_CAR_INSURANCE_PERIOD = 1188L;

}
