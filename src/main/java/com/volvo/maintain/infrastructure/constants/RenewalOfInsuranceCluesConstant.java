package com.volvo.maintain.infrastructure.constants;

public class RenewalOfInsuranceCluesConstant {

    /**
     * 经销商状态：开业准备
     */
    public static final Integer DEALER_STATUS_PREPARATION = 16031001;

    /**
     * 经销商状态：营业中
     */
    public static final Integer DEALER_STATUS_OPEN = 16031002;

    /**
     * 经销商状态：暂停营业
     */
    public static final Integer DEALER_STATUS_SUSPENDED = 16031003;

    /**
     * 经销商状态：终止运营
     */
    public static final Integer DEALER_STATUS_TERMINATED = 16031004;

    /**
     * 经销商状态：过度销售
     */
    public static final Integer DEALER_STATUS_OVERSELLING = 16031005;

    /**
     * 查询接口路径 ----> 导出下载中心回调
     */
    public static final String EXPORT_URL = "http://application-maintain-management/renewalOfInsurance/download/data";
    /**
     * 查询接口路径 ----> 导出下载中心回调---->缺料明细导出
     */
    public static final String SHORTPART_EXPORT = "http://application-maintain-management/replenishment/download/shortpartExport";

    public static final Integer SINGLE_STORE_PROTECTION = 96061001;

    public static final Integer UNPROTECT = 96061002;


}
