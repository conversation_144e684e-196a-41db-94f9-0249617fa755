package com.volvo.maintain.infrastructure.constants;

/**
 * 功能描述：redis常量类
 *
 * <AUTHOR>
 * @since 2024/01/05
 */
public class RedisConstants {

    /**
     * redis base key
     */
    private static final String BASE_KEY = "domain-maintain-leads:";

    /**
     * 全量线索跟进分布式锁key
     */
    public static final String FULL_LEADS_FOLLOW_KEY = BASE_KEY + "fullLeads:follow:{0}";
    /**
     * 事故线索推送LiteCrm token key
     */
    public static final String KEY_AC_PUSH_LITE_CRM_TOKEN = "dmscus-customer:accident:clue:liteCrm:token";
    public static final String KEY_AC_PUSH_LITE_CRM_TOKEN_LOCK = "accident:clue:liteCrm:lock:getToken";
    /**
     * 事故线索推送LiteCrm token 过期时间(小时)
     */
    public static final Long EXPIRE_AC_PUSH_LITE_CRM_TOKEN = 230L;
    /**
     * 续保线索--续保到期日期修改次数
     */
    public static final String RENEWAL_INSURANCE_REDIS_KEY = BASE_KEY + "Renewal:insurance:adviseInDateModifyCount:";

    public static final String KEY_REPLENISHMENT_LOCK = "replenishment:lock:";
    /**
     * 事故线索配置信息
     */
    public static final String ACCIDENT_CLUES_REDIS_KEY = "application:accident:clues:config:";

    /**
     * 透明车间签到数量缓存相关
     */
    public static final String SIGN_QUANTITY_CACHE_KEY = "workshop:sign:quantity:cache:";
    public static final String SIGN_QUANTITY_LOCK_KEY = "workshop:sign:quantity:lock:";
    public static final String SIGN_QUANTITY_REFRESH_FLAG_KEY = "workshop:sign:quantity:refresh:flag:";

    /**
     * 缓存时间配置（分钟）
     */
    public static final long SIGN_QUANTITY_REFRESH_THRESHOLD_MINUTES = 5L;
}
