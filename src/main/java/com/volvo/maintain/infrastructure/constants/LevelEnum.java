package com.volvo.maintain.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LevelEnum {
    TAG001(1005L, 8),
    TAG002(1006L, 4),
    TAG003(1184L, 5),
    TAG004(1002L, 6);
    //"质保1005”/“道路救援1006”/“二手车1184”/“保险1002"
    private final Long code;
    private final Integer level;
    public static Integer getCodeByLevel(Long code) {
        for (LevelEnum clueType : LevelEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getLevel();
            }
        }
        return null;
    }



}
