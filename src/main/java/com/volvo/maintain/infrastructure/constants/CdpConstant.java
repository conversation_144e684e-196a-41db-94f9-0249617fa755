package com.volvo.maintain.infrastructure.constants;

public class CdpConstant {

    //cdp查询类型
    public static final String CDP_TAG_VIN="vin";

    public static final String CDP_TAG_MOBILE="id_mobile_crypto";

    //cdp查询返回类型
    public static final String ID_MEMBER_ID="id_member_id";

    public static final String ONE_ID="one_id";
    public static final String CUSTOMER_ONEID="customer_oneid";
    public static final String ID_MOBILE="id_mobile";

    //cdp查询范围
    public static final String BIND_MEMBER_OWNER="bind_member_owner";

    public static final String CUSTOMER_PROFILEBASE="customer_profilebase";

    public static final String VEHICLE_PROFILEBASE="vehicle_profilebase";

    public static final String CDP_CAR_OWNER="cdp_car_owner";



    /**
     * 车主类型
     */
    //中台车主
    public static final Integer CUSTOMER_TYPE_MID =35301001;
    //自店车主
    public static final Integer CUSTOMER_TYPE_OWNER=35301002;
    //送修人
    public static final Integer CUSTOMER_TYPE_SEND_REPAIR_A=35301003;

    public static final Integer CUSTOMER_TYPE_SEND_REPAIR_B=35301006;

    public static final Integer CUSTOMER_TYPE_SEND_REPAIR_C=35301007;
    //故障灯客户
    public static final Integer CUSTOMER_TYPE_FAULT_LIGHT=35301004;
    //cdp
    public static final Integer CUSTOMER_TYPE_CDP =35301005;

    public static final Integer CUSTOMER_TYPE_INVITE =35301008;

    public static final Integer CUSTOMER_TYPE_POLICYHOLDER =35301009;

    public static final Integer CUSTOMER_TYPE_OWNER_POLICYHOLDER =35301010;


    /**
     * dmscloud-service 配置参数
     */

    public static final String CDPTAGIDKEY = "cdpTagIdKey";

    public static final String FULLNAMEAFTERSALE = "fullnameAftersale";


    public static final String DEFAULTGRADE = "3";


}
