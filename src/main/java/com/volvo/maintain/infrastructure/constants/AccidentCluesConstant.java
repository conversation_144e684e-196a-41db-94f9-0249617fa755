package com.volvo.maintain.infrastructure.constants;

public class AccidentCluesConstant {
	public static final Integer OWNER_RULE_TYPE_ALLOT = 1;
	public static final Integer OWNER_RULE_TYPE_MESSAGE = 2;

	public static final Integer OWNER_RULE_SELECT_YES = 10041001;
	public static final Integer OWNER_RULE_SELECT_NO = 10041002;

	public static final String ACCIDENT_CLUES_GROUPTYPE = "Accident_Clues";
	public static final String OWNER_RULE_RULE_CODE_SELECT_DEF = "rule_code_select_def";
	public static final String OWNER_RULE_RULE_MOBILE_MAX_COUNT = "rule_mobile_max_count";
	public static final String OWNER_RULE_SELECT_YSE_ROLECODE = "FWJL";
	public static final int OWNER_RULE_MOBILE_MAX_COUNT = 3;
	public static final int OWNER_RULE_SELECT_USER_MAX_SIZE = 100;
	
	 /**
     *  事故线索-线索创建/推送后超过30min未跟进提醒
     */
    public static final String ACCIDENT_CLUES_30M_TIMEOUT_NOT_FOLLOW = "ACCIDENT_CLUES_30M_TIMEOUT_NOT_FOLLOW";
    /**
     *  场景类型:6.推企微push
     */
    public static final Integer PUSH_WE_COM = 6;
    /**
     *  场景类型:2.短信
     */
    public static final Integer PUSH_SMS = 2;
    /**
     *  事故线索-线索创建/推送后超过30min未跟进提醒 短息模板id
     */
    public static final String ACCIDENT_CLUES_30M_TIMEOUT_NOT_FOLLOW_TEMPLATEID = "160d8ff3-ba68-428c-a05a-2b2ec37c2847";
    /**
     * 事故线索，企微推送模板
     * 
     */
    public static final String PUSH_WE_COM_TEMPLATE = "【沃尔沃汽车中国】${createdAt} 车辆${license}，车型：${modelName} 客户：${contacts}，在${accidentAddress}发生事故，线索超时未跟进，请尽快去Newbie或沃尔沃企业微信端处理推修任务";
    /**
     * 服务经理
     */
    public static final String SERVICE_MANAGE = "SM";
    /**
     * 员工
     */
    public static final String FOLLOW_PEOPER = "FP";

	/**
	 * 	事故线索状态：83531001(未跟进)
	 */
	public static final int NOT_FOLLOW = 83531001;
	/**
	 * 	事故线索状态：83531002(继续跟进)
	 */
	public static final int KEEP_ON_FOLLOW = 83531002;
	/**
	 * 	事故线索状态：83531003(跟进成功)
	 */
	public static final int FOLLOW_SUCCESS = 83531003;
	/**
	 * 	事故线索状态：83531004(跟进失败)
	 */
	public static final int FOLLOW_FAIL = 83531004;
	/**
	 * 	事故线索状态：83531005(超时未跟进)
	 */
	public static final int FOLLOW_TIME_OUT = 83531005;
	/**
	 * 	事故线索状态：83531006(超时关闭)
	 */
	public static final int FOLLOW_TIME_OUT_CLOSE = 83531006;


	/**
	 * This is a constant variable that represents the key for the accident clues factory to store.
	 * (业务号。 bizNO)
	 * The value is "ACCIDENT_CLUES_FACTORY_TO_STORE".
	 */
	public static final String ACCIDENT_CLUES_FACTORY_TO_STORE = "ACCIDENT_CLUES_FACTORY_TO_STORE";

	/**
	 * 消息模板
	 */
	public static final String MESSAGE_TEMPLATE = "【沃尔沃汽车中国】%s 车辆：%s，车型：%s 客户：%s，在%s，线索分配失败，请尽快处理线索任务。";

	/**
	 * 公共配置 获取 配置的userId
	 */
	public static final String FACTORY_BIZ_MANAGE = "factory_biz_manage";

	/**
	 * 消息模板
	 */
	public static final String messageTemplate = "3a859b81-115e-4bb2-a5c9-9062dcac69a8";


	public static final String MESSAGE_ASSIGNOR = "【沃尔沃汽车中国】%s 车辆：%s，车型：%s 客户：%s，在%s，发生事故，请尽快去 Newbie或沃尔沃企业微信端处理推修任务。";

	public static final String MESSAGE_ASSIGNOR_SMS = "64949cfa-93b1-48fd-8b95-d2b4ad7d68fa";
	/**
	 * 库管员
	 */
	public static final String KGY = "KGY";
	/**
	 *
	 */
	public static final String ACCIDENT_CLUES_PICC = "PICC";

	public static final String ACCIDENT_ALLOTDEALER = "accident_allotDealer";

	public static final String ORDER_FOLLOW_STATUS_MAX_DAY = "order_follow_status_max_day";
}
