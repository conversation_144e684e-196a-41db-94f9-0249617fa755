package com.volvo.maintain.infrastructure.constants;

/**
 * 车辆健康检查
 */
public class VhcConstants {

    //车辆健康检查白名单code
    public static final Integer VHC_CODE_WHITE_LIST=91111041;
    //车辆健康检查code
    public static final String VHC_CODE ="vhcCode";
    //车辆健康检查套餐code
    public static final String VHC_SET_CODE ="vhcSetCode";
    //车辆健康检查工时code
    public static final String VHC_LABOUR_CODE ="vhcLabourCode";
    //车辆健康检查编号
    public static final String VHC_JC_NO ="VC";
    //车辆健康检查报价编号
    public static final String VHC_BJ_NO ="VJ";
    /*燃油类型vhc_type
        电车：89701001
        油车：89701002
     */
    public static final String VHC_TYPE_EV ="89701001";
    public static final String VHC_TYPE_YC ="89701002";
    //中台油车标识
    public static final String FUEL_TYPE_A = "A";
    //中台电车标识
    public static final String FUEL_TYPE_C = "C";

    /**
     * 车辆健康检查配置
     */
    public static final String VHC_CONFIG_CLASS = "vhcConfigClass";

    /**
     * 车辆健康检查非正常配置大类id的配置code
     */
    public static final String VHC_NOT_NORMAL_CONFIG_CLASS_ID ="vhcConfigClassId";

    /**
     * 车辆健康检查类目排序code
     */
    public static final String VHC_VHC_CLASS_TYPE_ORDER ="vhcClassTypeOrder";

    public static final String VHC_CONFIG_CLASS_KEY_OTHER ="OTHER";

}
