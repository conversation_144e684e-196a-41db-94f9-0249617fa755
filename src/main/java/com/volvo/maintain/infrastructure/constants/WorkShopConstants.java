package com.volvo.maintain.infrastructure.constants;

//透明车间常量
public class WorkShopConstants {

    //进店提醒
    public final static String WORK_SHOP_70131001 = "70131001";
    //接待提醒
    public final static String WORK_SHOP_70131002 = "70131002";
    //派工提醒
    public final static String WORK_SHOP_70131003 = "70131003";
    //缺件到货日期提醒
    public final static String WORK_SHOP_70131004 = "70131004";
    //预交车提醒
    public final static String WORK_SHOP_70131005 = "70131005";
    //超时提醒
    public final static String WORK_SHOP_70131006 = "70131006";
    //已离场未交车提醒
    public final static String WORK_SHOP_70131007 = "70131007";

    /**
     * 终止提示mq
     */
    //进场接待
    public final static String WORK_SHOP_70131008 = "70131008";
    //全部派工完成
    public final static String WORK_SHOP_70131009 = "70131009";
    //已交车
    public final static String WORK_SHOP_70131010 = "70131010";

    /**
     * 菜单id
     */
    public final static String WORK_SHOP_MENU_ID_117746="117746";//交车
    public final static String WORK_SHOP_MENU_ID_117745="117745";//结算
    public final static String WORK_SHOP_MENU_ID_117744="117744";//维修
    public final static String WORK_SHOP_MENU_ID_117743="117743";//缺件查询
    public final static String WORK_SHOP_MENU_ID_117742="117742";//开单
    public final static String WORK_SHOP_MENU_ID_117741="117741";//到店
    public final static String WORK_SHOP_MENU_ID_117740="117740";//预约

    /**
     * 前缀
     */
    public static final String SUBSCRIPT = "subscript"; // 角标
    public static final String ORDERS = "orders"; // 订单号

}
