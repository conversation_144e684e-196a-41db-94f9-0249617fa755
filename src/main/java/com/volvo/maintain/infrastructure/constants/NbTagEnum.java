package com.volvo.maintain.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * description 邀约线索类型枚举类
 *
 * <AUTHOR>
 * @date 2023/8/22 14:44
 */
@Getter
@AllArgsConstructor
public enum NbTagEnum {
    TAG001("TAG001", "VIP",null,null),
    TAG002("TAG002", "客户姓名",null,null),
    TAG003("TAG003", "联系电话",null,null),
    TAG004("TAG004", "客户有效卡券数量",null,null),
    TAG005("TAG005", "30/60天内即将到期V值",null,null),
    TAG006("TAG006", "VIN",null,null),
    TAG007("TAG007", "车牌号",null,null),
    TAG008("TAG008", "车辆可用卡券数量",null,null),
    TAG009("TAG009", "忠诚守候卡券数量",null,null),
    TAG010("TAG010", "取送车卡券数量",null,null),
    TAG011("TAG011", "低车龄保养套餐抵扣券数量",null,null),
    TAG012("TAG012", "流失客户保养抵扣券数量",null,null),
    TAG013("TAG013", "保养灯线索",null,null),
    TAG014("TAG014", "流失客户标签",null,null),
    TAG015("TAG015", "是否大客户",null,null),
    TAG016("TAG016", "保修起止日期",null,null),
    TAG017("TAG017", "保养套餐",null,null),
    TAG018("TAG018", "生效中延保产品",null,null),
    TAG019("TAG019", "保险起止日期",null,null),
    TAG020("TAG020", "最近一次商业险保险起止日期",null,null),
    TAG021("TAG021", "最近一次交强险保险起止日期",null,null),
    TAG022("TAG022", "优惠减免卡券数量",83311001,"31081005"),
    TAG023("TAG023", "优惠打折卡券数量",83311002,"31081005"),
    TAG024("TAG024", "兑换券卡券数量",null,"31081006"),
    TAG025("TAG025", "储值卡卡券数量",null,"31081004"),
    TAG026("TAG026", "经销商权益",null,null),
    TAG027("TAG027", "非车险",null,null),
    TAG028("TAG028", "质保、道路救援、二手车、保险权益信息、终身权益",null,null);

    private final String code;
    private final String name;
    private final Integer offerType;
    private final String couponType;
    public static String getCodeByName(String name) {
        for (NbTagEnum clueType : NbTagEnum.values()) {
            if (clueType.getName().equals(name)) {
                return clueType.getCode();
            }
        }
        return null;
    }
    /*
    获取10,11,12集合
    * */
    public static List<NbTagEnum> queryNbTagEnum10_11_12() {

        return Arrays.asList(TAG010,TAG011,TAG012);
    }

    /*
    获取10,11,12集合
    * */
    public static List<NbTagEnum> queryNbTagEnum9_10_11_12() {

        return Arrays.asList(TAG009,TAG010,TAG011,TAG012);
    }

    /*
    获取22,23,24,25集合
    * */
    public static List<NbTagEnum> queryNbTagEnum22_23_24_25() {

        return Arrays.asList(TAG022,TAG023,TAG024,TAG025);
    }

    public String getNameByCode(String code) {
        for (NbTagEnum clueType : NbTagEnum.values()) {
            if (clueType.getCode().equals(code)) {
                return clueType.getName();
            }
        }
        return null;
    }


}
