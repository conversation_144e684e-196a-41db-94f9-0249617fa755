/*
 * Copyright (c) Volvo CAR Distribution (SHANGHAI) Co., Ltd. 2023. All rights reserved.
 */

package com.volvo.maintain;

import org.springframework.retry.annotation.EnableRetry;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

import java.util.HashMap;
import java.util.Map;


/**
 * 功能描述：启动类
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.volvo"})
@ComponentScan(basePackages = {"com.volvo"})
@EnableSwagger2
@EnableRetry
public class AppMaintainLeadApplication {
    public static void main(String[] args){
        Map<String, Object> props = new HashMap<>();
        props.put("spring.main.allow-bean-definition-overriding", true);
        SpringApplication app = new SpringApplication(AppMaintainLeadApplication.class);
        app.setDefaultProperties(props);
        app.run(args);
    }
}
