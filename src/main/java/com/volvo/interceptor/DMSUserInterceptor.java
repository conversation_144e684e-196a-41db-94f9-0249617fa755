package com.volvo.interceptor;
import com.volvo.dto.CurrentLoginInfoDto;
import com.volvo.exception.FrameworkException;
import com.volvo.feign.MidUserDataService;
import com.volvo.utils.ApplicationContextHolder;
import com.volvo.utils.BeanCovertUtlils;
import com.volvo.utils.ThreadLocalUtil;

import java.util.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

public class DMSUserInterceptor extends HandlerInterceptorAdapter {

    private Logger log = LoggerFactory.getLogger(DMSUserInterceptor.class);
    @Resource
    MidUserDataService userDataService;

    private static final List<String> urls = Lists.newArrayList();

    static {
        urls.add("/product/v1/mall-items");
        urls.add("/product/v1/sync-give-status");
        urls.add("/product/v1/give");
        urls.add("/product/v1/give-act-list");
        urls.add("/product/v1/use-list");
        urls.add("/product/v1/give-list");
        urls.add("/ebaotechClaim/Issue/approval");
        urls.add("/workshop/shortageEta/toc");
    }

    public DMSUserInterceptor() {
    }

    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse response, Object handler) throws Exception {
        Enumeration<String> stringIterator = httpServletRequest.getHeaderNames();
        Map<String, String> headerMap = new HashMap(16);

        String path;
        while(stringIterator.hasMoreElements()) {
            path = (String)stringIterator.nextElement();
            headerMap.put(path, httpServletRequest.getHeader(path));
        }

        LoginInfoThreadLocal.HEADER_INFO.set(headerMap);
        path = httpServletRequest.getRequestURI();
        this.log.debug("into LoginFilter请求的地址: {} ,请求方法: {}", path, httpServletRequest.getMethod());
        if (this.judgePath(path)) {
            return true;
        } else {
            this.setThreadLocal(httpServletRequest);
            this.log.info("获取登录人httpServletRequest" + httpServletRequest);
            Object userIdObject = this.getUserIdObject(httpServletRequest);
            this.log.info("DMS LOGIN USERID {}", userIdObject);
            this.fillCurrentLoginInfoDto(userIdObject);
            return true;
        }
    }

    private Object getUserIdObject(HttpServletRequest httpServletRequest) {
        Object userIdObject = httpServletRequest.getHeader("userId");
        if (Objects.isNull(userIdObject)) {
            userIdObject = httpServletRequest.getHeader("Authorization");
            if (Objects.nonNull(userIdObject)) {
                userIdObject = userIdObject.toString().substring(7);
                this.log.info("获取登录人userIdObject" + BeanCovertUtlils.obj2Json(userIdObject));
            }
        }

        return userIdObject;
    }

    private void fillCurrentLoginInfoDto(Object userIdObject) {
        if (Objects.isNull(userIdObject)) {
            this.log.error("获取登录人ID信息失败，请确认Eureka，redis配置是否正确！！！！！！");
        }

        if (Objects.nonNull(userIdObject)) {
            CurrentLoginInfoDto logininfovo = this.userDataService.getUserInfo(String.valueOf(userIdObject));
            CurrentLoginInfoDto loginInfo = (CurrentLoginInfoDto)ApplicationContextHolder.getBeanByType(CurrentLoginInfoDto.class);
            this.log.info("loginInfo-----------{}", loginInfo);

            try {
                if (Objects.nonNull(logininfovo) && Objects.isNull(loginInfo)) {
                    loginInfo = new CurrentLoginInfoDto();
                    BeanCovertUtlils.copyProperties(logininfovo, loginInfo);
                    this.log.info("loginInfo info: {}", loginInfo);
                } else {
                    BeanCovertUtlils.copyProperties(logininfovo, loginInfo);
                    this.log.info("loginInfo info: {}", loginInfo);
                }
            } catch (Exception var5) {
                Exception e = var5;
                throw new FrameworkException("获取登录信息异常:{}", e);
            }
        }

    }

    private boolean judgePath(String path) {
        return path.endsWith("/login")
                || path.endsWith("/login/initUserData")
                || path.endsWith("/login/code")
                || path.endsWith("/dealers")
                || path.endsWith("/interAspect")
                || path.endsWith("/login/appLogin")
                || path.endsWith("/auth/getUserInfo")
                || path.contains("/swagger-resources")
                || path.contains("/swagger-ui.html")
                || path.contains("/springfox-swagger")
                || path.contains("/csrf")
                || path.contains("/error")
                || path.contains("/interf")
                || urls.contains(path);
    }

    private void setThreadLocal(HttpServletRequest request) {
        String current = request.getParameter("current");
        String size = request.getParameter("size");
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        String pageNum = request.getParameter("pageNum");
        String limit = request.getParameter("limit");
        String sortColumn = request.getParameter("sort");
        ThreadLocalUtil.set("current", current);
        ThreadLocalUtil.set("size", size);
        ThreadLocalUtil.set("currentPage", currentPage);
        ThreadLocalUtil.set("pageSize", pageSize);
        ThreadLocalUtil.set("pageNum", pageNum);
        ThreadLocalUtil.set("limit", limit);
        ThreadLocalUtil.set("sort", sortColumn);
    }
}
