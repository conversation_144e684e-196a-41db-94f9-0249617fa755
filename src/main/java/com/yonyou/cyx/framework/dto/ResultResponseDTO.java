package com.yonyou.cyx.framework.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("ResultResponseDTO")
public class ResultResponseDTO<T> extends ResponseDTO {
	@ApiModelProperty(value = "返回代码")
	private Integer resultCode;
	@ApiModelProperty(value = "返回描述")
	private String errMsg;
	@ApiModelProperty(value = "返回时长")
	private long elapsedMilliseconds;
	@ApiModelProperty(value = "返回标识")
	private boolean success;

	// 老代码抽取并新增了 setCode
	public ResultResponseDTO data(T data) {
		this.setResultCode(200);
		this.setCode(200);
		this.setSuccess(true);
		this.setData(data);
		return this;
	}
}
