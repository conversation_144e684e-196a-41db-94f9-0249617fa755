package com.yonyou.cyx.framework.interceptor;

import com.yonyou.cyx.framework.bean.dto.framework.RestResultResponse;
import com.yonyou.cyx.framework.dto.ResponseDTO;
import com.yonyou.cyx.framework.dto.ResultResponseDTO;
import com.yonyou.cyx.function.utils.jsonserializer.JSONUtil;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@ControllerAdvice
public class ResponseAdvisor implements ResponseBodyAdvice<Object> {
	public ResponseAdvisor() {
	}

	public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
		return true;
	}

	public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
		// 如果返回的是 ResponseDTO 则直接进行返回；
		if (body instanceof ResponseDTO) {
			return body;
		}

		// 如果返回的是 ResultResponseDTO 则直接进行返回；
		// ps：原则上不允许直接返回 ResultResponseDTO；
		if (body instanceof ResultResponseDTO) {
			return body;
		}

		// 如果返回的是 RestResultResponse 我们需要转换成新定义的 ResultResponseDTO 且进行完全兼容同时返回 code、msg、data；
		// ps：这样做的目的是为了少重写一次 RestResultResponse 和 他的父类 ResultBean （因为要新老兼容必须得加上 code、msg 等字段）；
		if (body instanceof RestResultResponse) {
			RestResultResponse<?> rsp = (RestResultResponse<?>) body;
			ResultResponseDTO resultResponseDTO = new ResultResponseDTO();
			resultResponseDTO.setCode(rsp.getResultCode());
			resultResponseDTO.setMsg(rsp.getErrMsg());
			resultResponseDTO.setResultCode(rsp.getResultCode());
			resultResponseDTO.setErrMsg(rsp.getErrMsg());
			resultResponseDTO.setElapsedMilliseconds(rsp.getElapsedMilliseconds());
			resultResponseDTO.setSuccess(rsp.isSuccess());
			resultResponseDTO.setData(rsp.getData());
			return resultResponseDTO;
		}

		// 这里沿用了老逻辑没有做任何改动
		if (returnType.getMethod().getReturnType().equals(String.class)) {
			return JSONUtil.objectToJson((new ResultResponseDTO<>()).data(body));
		}

		// 这里沿用了老逻辑并进行兼容转换和 RestResultResponse 转换成 ResultResponseDTO 等同
		if (!request.getURI().toString().contains("/swagger")
				&& !request.getURI().toString().contains("/v2/api-docs")
				&& !request.getURI().toString().contains("/actuator")) {
			return new ResultResponseDTO().data(body);
		}

		// 这里沿用了老逻辑没有做任何改动
		return body;
	}
}