/*
 * Copyright (c) Volvo CAR Distribution (SHANGHAI) Co., Ltd. 2023. All rights reserved.
 */

package com.volvo.maintain;

import static org.mockito.Mockito.mock;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.jupiter.api.Test;
import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.scanners.TypeAnnotationsScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;


class SupperObjTest {

    @Test
    public void testAllClasses() {
        // 验证动态字段的值

        Collection<URL> urls = ClasspathHelper.forPackage("com.volvo");
        if(CollectionUtils.isEmpty(urls)){
            return;
        }
        // 使用 Reflections 配置，扫描当前目录及其子目录
        Reflections reflections = new Reflections(
                new ConfigurationBuilder()
                        .setUrls(urls) // 空字符串表示扫描所有包
                        .setScanners(new SubTypesScanner(false)) // 扫描子类和接口
                        .filterInputsBy(input -> input != null
                                && input.endsWith(".class")
//                                && input.endsWith("AccidentInfo")
                                && (input.contains("com/volvo/maintain/infrastructure/gateway/request") || input.contains("com/volvo/maintain/infrastructure/gateway/response") || input.toUpperCase().contains("PO") || input.toUpperCase().contains("VO") || input.toUpperCase().contains("DTO") || input.toUpperCase().contains("ENTITY"))
                                && !input.contains("Test")) // 过滤掉非类文件
        );

        // 扫描所有的类
        Set<Class<? extends Object>> basePoClass = reflections.getSubTypesOf(Object.class);
        Set<Class<? extends Serializable>> serializableClass = reflections.getSubTypesOf(Serializable.class);


        Set<Class<?>> allClasses = reflections.getSubTypesOf(Object.class);
        allClasses.addAll(basePoClass);
        allClasses.addAll(serializableClass);
        callMethod(allClasses);
//
        callService(urls);
    }

    private void callService(Collection<URL> urls) {
        Reflections reflections = new Reflections(
                new ConfigurationBuilder()
                        .setUrls(urls) // 空字符串表示扫描所有包
                        .setScanners(new TypeAnnotationsScanner(),new SubTypesScanner(false)) // 扫描子类和接口
                        .filterInputsBy(input -> input != null
                                && input.endsWith(".class")
                                && !input.contains("Test")
                                && !input.contains("LucencyWorkShop")
                                && !input.contains("CustomerServiceImpl")) // 过滤掉非类文件
        );
        //所有接口实现
        List<Class<?>> allService = Stream.of(
                        reflections.getTypesAnnotatedWith(Controller.class),
                        reflections.getTypesAnnotatedWith(Component.class),
                        reflections.getTypesAnnotatedWith(Service.class),
                        reflections.getTypesAnnotatedWith(SpringBootApplication.class),
                        reflections.getTypesAnnotatedWith(RestController.class))
                .flatMap(Set::stream)
                .collect(Collectors.toList());
        // 增加工具类的扫描
        Set<Class<?>> utilClasses = reflections.getSubTypesOf(Object.class).stream()
                .filter(cls -> (cls.getSimpleName().endsWith("Utils") || cls.getSimpleName().endsWith("Util") || cls.getSimpleName().endsWith("Util")) &&
                        !cls.getName().contains("HttpJsonUtil")
                        )
                .collect(Collectors.toSet());
        Set<String> mockType = new HashSet<>();
        allService.addAll(utilClasses);
        List<String> methods = new ArrayList<>();
        methods.add("queryCustomInfoList");
        methods.add("selectOwnerRule_");
        methods.add("queryCustomizedLabel");
        methods.add("queryCustomInfoList");
        methods.add("queryCustomInfoListByMobile");
        methods.add("queryCdpTagInfoAndConfigure");
        allService.forEach(service -> {
            try {
                List<Object> obj = createObj(service);
                Object o = obj.get(0);
                //所有字段并mock代理实例
                Field[] declaredFields = service.getDeclaredFields();
                if(declaredFields.length>0){
                    //初始化所有字段
                    Arrays.stream(declaredFields).forEach(field -> {
                        try {
                            field.setAccessible(true);
                            int modifiers = field.getModifiers();
                            if(!Modifier.isFinal(modifiers) &&
                                    !Modifier.isStatic(modifiers)){
                                mockType.add(field.getType().getName());
                                field.set(o,createMockInstance(field.getType()));

                            }
                        } catch (Exception e) {
                            //                        System.out.println(e.getMessage());
                        }
                    });
                }

                //执行方法
                Method[] declaredMethods = service.getDeclaredMethods();
                Arrays.stream(declaredMethods).forEach(method -> {
                    System.out.println("service"+service.getName()+" method"+method.getName());
                    method.setAccessible(true);
                    Class<?>[] paramTypes = method.getParameterTypes();
                    Type[] genericParameterTypes = method.getGenericParameterTypes();
                    Object[] params = new Object[paramTypes.length];
                    if(paramTypes.length > 0){
                        for (int i = 0; i < paramTypes.length; i++) {
                            Type genericSuperclass = null;
                            if (genericParameterTypes[i] instanceof ParameterizedType) {
                                ParameterizedType parameterizedType = (ParameterizedType) genericParameterTypes[i];

                                // 获取实际类型参数
                                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                                for (Type actualTypeArgument : actualTypeArguments) {
                                    if (actualTypeArgument instanceof Class<?>) {
                                        genericSuperclass = actualTypeArgument;
                                    }
                                }
                            }
                            params[i] = getMockValue(paramTypes[i],genericSuperclass);
                            mockType.add(paramTypes[i].getName());
                        }
                    }
                    try {
                        int modifiers = method.getModifiers();
                        if(!methods.contains(method.getName())){
                            if (Modifier.isStatic(modifiers)) {
                                method.invoke(null,params);
                            } else {
                                method.invoke(o,params);
                            }
                        }

                    } catch (Exception e) {
//                        e.printStackTrace();
                    }
                });
            } catch (Exception e) {
//                System.out.println("service exception"+service.getName());
            }
        });
    }

    private static Object createMockInstance(Class<?> clazz) {
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(clazz);
        enhancer.setCallback(new MethodInterceptor() {
            @Override
            public Object intercept(Object obj, Method method, Object[] args, MethodProxy proxy) throws Throwable {
                Type genericSuperclass = method.getGenericReturnType();
                return getMockValue(method.getReturnType(),genericSuperclass);
            }
        });
        // 创建代理对象
        return enhancer.create();
    }

    // 根据返回类型生成mock值
    private static Object getMockValue(Class<?> returnType,Type genericSuperclass) {
        int modifiers = returnType.getModifiers();
//        if(Modifier.isInterface(modifiers)){
//           return null;
//        }
        if (returnType == String.class) {
            return "mockString";
        }
        if (returnType == BigDecimal.class) {
            return BigDecimal.ZERO;
        }
        if (returnType == Date.class) {
            return new Date();
        }else if (returnType == Integer.class || returnType == int.class) {
            return 0;
        } else if (returnType == Boolean.class || returnType == boolean.class) {
            return false;
        } else if (returnType == Double.class || returnType == double.class) {
            return 0.0;
        } else if (returnType == Long.class || returnType == long.class) {
            return 0L;
        } else if (returnType == Float.class || returnType == float.class) {
            return 0.0f;
        } else if (returnType == Character.class || returnType == char.class) {
            return 'a';
        }else if (returnType == Map.class) {
            return new HashMap<>();
        }else if (returnType == MultipartFile.class) {
            return new MockMultipartFile(
                    "file",                   // Name of the file
                    "test.txt",               // Original filename
                    "text/plain",             // Content type
                    "This is the file content".getBytes()  // File content
            );
        }else if (returnType == List.class) {
            return new ArrayList<>();
        } else if (returnType == Throwable.class  || returnType == Object.class || returnType == ProceedingJoinPoint.class || returnType == HttpServletResponse.class || returnType == BindingResult.class || returnType == HttpServletRequest.class || returnType == JoinPoint.class) {
            return null;
        }else {
            try {
                return mock(returnType); // 使用Mockito生成其他类型的mock对象
            } catch (Exception e) {
//                System.out.println(returnType.getName()+" error"+e.getMessage());
                return null;
            }
        }
    }

    private  void callMethod(Set<Class<?>> allClasses) {
        for (Class<?> clazz : allClasses) {
            // 这里可以添加针对每个类的具体测试逻辑
            try {
                List<Object> obj1 = createObj(clazz);
                List<Object> obj2 = createObj(clazz);
                callMethod(clazz, obj1.get(0), obj2.get(0));
                //build注解
                Method builder1 = clazz.getMethod("builder");
                if(Objects.nonNull(builder1)){
                    Object buildObj1 = builder1.invoke(obj1);
                    Object buildObj2 = builder1.invoke(obj2);
                    callMethod(buildObj1.getClass(), buildObj1, buildObj2);
                    Method buildMethod = buildObj1.getClass().getMethod("build");
                    buildMethod.setAccessible(true);
                    buildMethod.invoke(buildObj1);
                    buildMethod.invoke(buildObj2);
                }
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
    }

    private void callMethod(Class<?> clazz, Object o1, Object o2) {
        o1.toString();
        o1.hashCode();
        BeanUtils.copyProperties(o1, o2);
        o1.equals(o2);
        try {
            Method canEqual = clazz.getMethod("canEqual");
            canEqual.setAccessible(true);
            canEqual.invoke(o1, o2);
        } catch (Exception e) {
//            log.error("method invoke error", e.getMessage());
        }


        Method[] declaredMethods = clazz.getDeclaredMethods();
        Arrays.stream(declaredMethods).forEach(method -> {
            int parameterCount = method.getParameterCount();
            if(parameterCount == 0){
                try{
                    method.setAccessible(true);
                    method.invoke(o1);
//                    log.info("method invoke success"+method.getName());
                }catch (Exception e) {
//                    log.error("method invoke error",method.getName()+ e.getMessage());
                }
            }else{
                Object[] args = new Object[parameterCount];
                IntStream.range(0,parameterCount).forEach(index -> args[index] = null );
                try {
                    method.setAccessible(true);
                    method.invoke(o1,args);
//                    log.info("method invoke success"+method.getName());
                } catch (Exception e) {
//                    log.error("method invoke error", method.getName()+e.getMessage());
                }
            }
        });
    }

    private List<Object> createObj(Class<?> clazz) {
        try {
            Constructor<?>[] constructors = clazz.getConstructors();
            return Arrays.stream(constructors).map(c -> {
                int parameterCount = c.getParameterCount();
                if (parameterCount == 0) {
                    try {
                        c.setAccessible(true);
                        return c.newInstance();
                    } catch (Throwable e) {
    //                    log.error("method invoke error", e);
                    }
                } else {
                    Object[] args = new Object[parameterCount];
                    IntStream.range(0, parameterCount).forEach(index -> args[index] = null);
                    try {
                        c.setAccessible(true);
                        return c.newInstance(args);
                    } catch (Throwable e) {
    //                    log.error("method invoke error", e.getMessage());
                    }
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Throwable e) {

        }
        return Lists.newArrayList();
    }
}
