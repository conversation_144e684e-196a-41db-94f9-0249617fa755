# SmartCache 智能缓存注解使用说明

## 概述

`@SmartCache` 是基于 `signQuantity` 接口缓存机制设计的通用缓存注解，提供了以下核心特性：

1. **永久缓存策略** - 数据永久存储在 Redis 中，不会自动过期
2. **智能异步刷新** - 超过阈值时间后自动触发异步刷新
3. **分布式锁控制** - 确保同一缓存键只有一个线程执行实时查询
4. **降级策略** - 获取锁失败时可返回过期缓存数据
5. **双重检查机制** - 获取锁后再次检查缓存，避免重复查询

## 核心机制分析

### 原始 signQuantity 接口的缓存流程

```java
// 1. 构建缓存键
String cacheKey = RedisConstants.SIGN_QUANTITY_CACHE_KEY + ownerCode;
String lockKey = RedisConstants.SIGN_QUANTITY_LOCK_KEY + ownerCode;
String flagKey = RedisConstants.SIGN_QUANTITY_REFRESH_FLAG_KEY + ownerCode;

// 2. 尝试从缓存获取
SignQuantityCacheDTO cacheData = getFromCache(cacheKey);

// 3. 缓存命中 -> 检查是否需要异步刷新
if (cacheData != null) {
    tryAsyncRefresh(ownerCode, beginDate, endDate, lockKey, cacheData, flagKey);
    return cacheData.getSignQuantityList();
}

// 4. 缓存未命中 -> 分布式锁 + 实时查询
return getDataWithLock(ownerCode, beginDate, endDate, lockKey, cacheKey);
```

### 注解化后的使用方式

```java
@SmartCache(
    keyPrefix = "workshop:sign:quantity:cache:",
    keyExpression = "#ownerCode",
    refreshThresholdMinutes = 8L,
    enableFallback = true
)
public List<SignQuantityDTO> getSignQuantity(String ownerCode, String beginDate, String endDate) {
    // 只需要编写业务逻辑，缓存由注解自动处理
    return executeSignQuantityQuery(ownerCode, beginDate, endDate);
}
```

## 注解参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `keyPrefix` | String | "" | 缓存键前缀 |
| `keyExpression` | String | "" | 缓存键表达式（支持SpEL） |
| `refreshThresholdMinutes` | long | 8L | 缓存刷新阈值（分钟） |
| `lockWaitTime` | long | 1L | 分布式锁等待时间 |
| `lockTimeUnit` | TimeUnit | SECONDS | 锁等待时间单位 |
| `refreshFlagExpireMinutes` | long | 1L | 异步刷新标记过期时间 |
| `threadPoolName` | String | "threadSignQuantity" | 异步刷新使用的线程池 |
| `enableFallback` | boolean | true | 是否启用降级策略 |
| `cacheType` | Class<?> | Object.class | 缓存数据类型 |

## SpEL 表达式支持

### 基本用法

```java
// 使用参数名
@SmartCache(keyExpression = "#ownerCode")
public Data getData(String ownerCode) { ... }

// 使用参数索引
@SmartCache(keyExpression = "#p0")
public Data getData(String ownerCode) { ... }

// 组合多个参数
@SmartCache(keyExpression = "#ownerCode + ':' + #dateRange")
public Data getData(String ownerCode, String dateRange) { ... }

// 使用参数数组
@SmartCache(keyExpression = "#args[0] + ':' + #args[1]")
public Data getData(String param1, String param2) { ... }
```

### 复杂表达式示例

```java
// 条件表达式
@SmartCache(keyExpression = "#type == 'VIP' ? 'vip:' + #userId : 'normal:' + #userId")
public UserData getUserData(String userId, String type) { ... }

// 方法调用
@SmartCache(keyExpression = "#userId + ':' + #dateRange.replace('-', '')")
public ReportData getReport(String userId, String dateRange) { ... }
```

## 使用示例

### 1. 基础用法 - 单参数缓存

```java
@SmartCache(
    keyPrefix = "user:profile:",
    keyExpression = "#userId",
    refreshThresholdMinutes = 30L
)
public UserProfile getUserProfile(String userId) {
    return userService.queryUserProfile(userId);
}
```

### 2. 多参数组合缓存

```java
@SmartCache(
    keyPrefix = "dealer:report:",
    keyExpression = "#dealerCode + ':' + #reportType + ':' + #dateRange",
    refreshThresholdMinutes = 15L,
    lockWaitTime = 2L
)
public ReportData getDealerReport(String dealerCode, String reportType, String dateRange) {
    return reportService.generateReport(dealerCode, reportType, dateRange);
}
```

### 3. 自定义线程池

```java
@SmartCache(
    keyPrefix = "heavy:computation:",
    keyExpression = "#taskId",
    refreshThresholdMinutes = 60L,
    threadPoolName = "thread360PoolNew"  // 使用自定义线程池
)
public ComputationResult getComputationResult(String taskId) {
    return heavyComputationService.compute(taskId);
}
```

### 4. 禁用降级策略

```java
@SmartCache(
    keyPrefix = "critical:data:",
    keyExpression = "#dataId",
    refreshThresholdMinutes = 5L,
    enableFallback = false  // 获取锁失败时不返回过期数据
)
public CriticalData getCriticalData(String dataId) {
    return criticalDataService.query(dataId);
}
```

## 最佳实践

### 1. 缓存键设计

- 使用有意义的前缀，便于管理和监控
- 确保键的唯一性，避免冲突
- 考虑键的长度，避免过长

```java
// 好的设计
@SmartCache(keyPrefix = "workshop:sign:quantity:", keyExpression = "#ownerCode")

// 避免的设计
@SmartCache(keyPrefix = "cache:", keyExpression = "#param")  // 前缀太通用
```

### 2. 刷新阈值设置

- 根据数据的实时性要求设置合适的阈值
- 考虑数据源的负载能力
- 平衡缓存命中率和数据新鲜度

```java
// 实时性要求高的数据
@SmartCache(refreshThresholdMinutes = 5L)

// 相对稳定的数据
@SmartCache(refreshThresholdMinutes = 60L)
```

### 3. 线程池选择

- 根据任务特性选择合适的线程池
- IO密集型任务使用较大的线程池
- CPU密集型任务使用较小的线程池

```java
// IO密集型任务
@SmartCache(threadPoolName = "thread360PoolNew")

// 特定业务的线程池
@SmartCache(threadPoolName = "threadSignQuantity")
```

### 4. 错误处理

- 合理设置降级策略
- 监控缓存命中率和异常情况
- 考虑缓存穿透和雪崩问题

## 注意事项

1. **方法必须是 public** - AOP 代理只能拦截 public 方法
2. **避免自调用** - 同一个类内部的方法调用不会触发 AOP
3. **异常处理** - 方法抛出异常时不会缓存结果
4. **序列化** - 缓存的对象必须实现 Serializable 接口
5. **内存使用** - 永久缓存可能导致内存增长，需要监控

## 监控和运维

### Redis 键命名规范

```
{keyPrefix}{keyExpression计算结果}           # 缓存数据
{keyPrefix}{keyExpression计算结果}:lock      # 分布式锁
{keyPrefix}{keyExpression计算结果}:refresh:flag  # 异步刷新标记
```

### 监控指标

- 缓存命中率
- 异步刷新频率
- 锁竞争情况
- 方法执行时间

### 故障排查

1. 检查 Redis 连接状态
2. 查看线程池状态
3. 监控锁的获取和释放
4. 检查 SpEL 表达式是否正确

## 迁移指南

### 从原始实现迁移到注解

1. **提取业务逻辑**
   ```java
   // 原始方法
   public List<SignQuantityDTO> signQuantity(String ownerCode, String beginDate, String endDate) {
       // 缓存逻辑 + 业务逻辑
   }
   
   // 迁移后
   @SmartCache(keyPrefix = "workshop:sign:quantity:cache:", keyExpression = "#ownerCode")
   public List<SignQuantityDTO> signQuantity(String ownerCode, String beginDate, String endDate) {
       // 只保留业务逻辑
       return executeSignQuantityQuery(ownerCode, beginDate, endDate);
   }
   ```

2. **配置参数映射**
   - `RedisConstants.SIGN_QUANTITY_CACHE_KEY` → `keyPrefix`
   - `ownerCode` → `keyExpression = "#ownerCode"`
   - 8分钟刷新阈值 → `refreshThresholdMinutes = 8L`
   - `threadSignQuantity` → `threadPoolName = "threadSignQuantity"`

3. **测试验证**
   - 验证缓存键生成正确
   - 测试异步刷新机制
   - 确认降级策略工作正常
