<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.volvo</groupId>
        <artifactId>hypnos-parent</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.volvo</groupId>
    <artifactId>application-maintain-management</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>application-maintain-management</name>
    <description>application-maintain-management</description>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tomcat-embed-websocket</artifactId>
                    <groupId>org.apache.tomcat.embed</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--hutool-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!-- nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>
        <!-- hystrix -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <!-- ribbon -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
        </dependency>
        <!-- 单元测试需要引入的包，主要是为了走测试流程和生成测试覆盖率报告-->
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.9.10</version>
            <scope>test</scope>
        </dependency>
        <!--mock单元测试数据，主要是为了跳过数据真实查询,这里之所以引入是因为需要使用静态内联方法，否则不需要引入-->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.9.0</version>
            <scope>test</scope>
        </dependency>
        <!--生成单元测试报告-->
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <version>0.8.7</version>
            <classifier>runtime</classifier>
        </dependency>
        <!-- mock静态方法需要引入这个依赖 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- xxl-job -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.0</version>
        </dependency>

        <!-- MQ方法需要引入这个依赖 -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.2</version>
        </dependency>

        <!-- 异常重试需要引入这个依赖 -->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.2.4.RELEASE</version>
            <scope>compile</scope>
        </dependency>

        <!--工具类和认证信息-->
        <dependency>
            <groupId>com.volvo</groupId>
            <artifactId>volvo-hypnos-common-utils</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.volvo</groupId>
            <artifactId>volvo-hypnos-authentication-2b</artifactId>
            <version>1.0.9</version>
        </dependency>
        <!-- 引用function 包 -->
        <dependency>
            <groupId>com.yonyou.cyx</groupId>
            <artifactId>volvo-framework-starter</artifactId>
            <version>2.7.56-RELEASE</version>
        </dependency>
		<dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.12</version>
        </dependency>

        <dependency>
            <groupId>com.volvo</groupId>
            <artifactId>datalake-tools</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.lop</groupId>
            <artifactId>IntegratedSupplyChain_ISC_JAVA</artifactId>
            <version>4.3</version>
        </dependency>
        <dependency>
            <groupId>com.lop</groupId>
            <artifactId>lop-opensdk-support</artifactId>
            <version>1.0.28-SNAPSHOT</version>
        </dependency>

    </dependencies>



    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <configuration>
                    <excludes>
<!--                        <exclude>com/volvo/maintain/application/maintainlead/dto/**</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/job/**</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/vo/**</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/customerProfile/BookingRegisterService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/customerProfile/CdpTagInfoService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/customerProfile/CommonMethodService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/customerProfile/CouponDetailService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/customerProfile/CustomerService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/customerProfile/CustomizedLabelService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/customerProfile/InvitationService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/customerProfile/PrecheckOrderTagService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/customerProfile/TagInfoService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/customerProfile/VipCustomService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/protectingCustomersMarket/ProtectingCustomersMarketService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/AccidentCluesOwnerRuleService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/AccidentCluesService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/BalanceAccountsPrintService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/DeliverOrderService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/EM90ServiceLeadService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/Em90VehicleDeliverService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/FullLeadsService</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/JobTaskGenerator</exclude>-->
<!--                        <exclude>com/volvo/maintain/application/maintainlead/service/MaintainLeadService</exclude>-->
<!--                        <exclude>com/volvo/maintain/infrastructure/**</exclude>-->
<!--                        <exclude>com/volvo/maintain/interfaces/**/**</exclude>-->
<!--                        <exclude>com/volvo/maintain/MaintainLeadApplication</exclude>-->
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <propertyName>surfireArgLine</propertyName>
                            <includes>
                                <include>**/*</include>
                            </includes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--生成单元测试报告-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <useSystemClassLoader>false</useSystemClassLoader>
                    <forkMode>once</forkMode>
                    <reuseForks>true</reuseForks>
                    <!--suppress UnresolvedMavenProperty -->
                    <argLine>-Dfile.encoding=UTF-8 ${surfireArgLine}</argLine>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.0</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>